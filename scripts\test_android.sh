#!/bin/bash

# Clipsy Android Test Script
# This script runs tests for the Android application

set -e

echo "=== Clipsy Android Test Script ==="
echo "Starting Android tests..."

# Change to Android directory
cd "$(dirname "$0")/../android"

# Check if Android SDK is available
if ! command -v adb &> /dev/null; then
    echo "Error: Android SDK not found. Please install Android SDK and add it to PATH."
    exit 1
fi

# Check if Gradle wrapper exists
if [ ! -f "./gradlew" ]; then
    echo "Error: Gradle wrapper not found. Please ensure you're in the Android project directory."
    exit 1
fi

# Make gradlew executable
chmod +x ./gradlew

echo "Running unit tests..."
./gradlew test

echo "Running instrumented tests (requires connected device or emulator)..."
if adb devices | grep -q "device$"; then
    echo "Device/emulator detected, running instrumented tests..."
    ./gradlew connectedAndroidTest
else
    echo "Warning: No device/emulator detected. Skipping instrumented tests."
    echo "To run instrumented tests, connect a device or start an emulator."
fi

echo "Running lint checks..."
./gradlew lint

echo "Generating test reports..."
./gradlew jacocoTestReport

echo ""
echo "=== Test Results ==="
echo "Unit test results: app/build/reports/tests/testDebugUnitTest/index.html"
echo "Instrumented test results: app/build/reports/androidTests/connected/index.html"
echo "Lint results: app/build/reports/lint-results.html"
echo "Coverage report: app/build/reports/jacoco/jacocoTestReport/html/index.html"

echo ""
echo "Android tests completed successfully!"
