1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.clipsy.android.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10
11    <!-- Network permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->N:\new project\android\app\src\main\AndroidManifest.xml:6:5-67
12-->N:\new project\android\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->N:\new project\android\app\src\main\AndroidManifest.xml:7:5-79
13-->N:\new project\android\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->N:\new project\android\app\src\main\AndroidManifest.xml:8:5-76
14-->N:\new project\android\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
15-->N:\new project\android\app\src\main\AndroidManifest.xml:9:5-86
15-->N:\new project\android\app\src\main\AndroidManifest.xml:9:22-83
16
17    <!-- Foreground service permission (Android 9+) -->
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->N:\new project\android\app\src\main\AndroidManifest.xml:12:5-77
18-->N:\new project\android\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
19-->N:\new project\android\app\src\main\AndroidManifest.xml:13:5-87
19-->N:\new project\android\app\src\main\AndroidManifest.xml:13:22-84
20
21    <!-- Boot receiver permission -->
22    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
22-->N:\new project\android\app\src\main\AndroidManifest.xml:16:5-81
22-->N:\new project\android\app\src\main\AndroidManifest.xml:16:22-78
23
24    <!-- Notification permission (Android 13+) -->
25    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
25-->N:\new project\android\app\src\main\AndroidManifest.xml:19:5-77
25-->N:\new project\android\app\src\main\AndroidManifest.xml:19:22-74
26
27    <!-- Wake lock for background operations -->
28    <uses-permission android:name="android.permission.WAKE_LOCK" />
28-->N:\new project\android\app\src\main\AndroidManifest.xml:22:5-68
28-->N:\new project\android\app\src\main\AndroidManifest.xml:22:22-65
29
30    <!-- Battery optimization bypass -->
31    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
31-->N:\new project\android\app\src\main\AndroidManifest.xml:25:5-95
31-->N:\new project\android\app\src\main\AndroidManifest.xml:25:22-92
32
33    <permission
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\345ccf188a6f49c4c91ca3120313e197\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
34        android:name="com.clipsy.android.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\345ccf188a6f49c4c91ca3120313e197\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\345ccf188a6f49c4c91ca3120313e197\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.clipsy.android.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\345ccf188a6f49c4c91ca3120313e197\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\345ccf188a6f49c4c91ca3120313e197\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
38    <!--
39 Note: Android restricts clipboard access for background services.
40         Clipboard updates will only work when app is in foreground or recently used.
41    -->
42    <application
42-->N:\new project\android\app\src\main\AndroidManifest.xml:30:5-77:19
43        android:name="com.clipsy.android.ClipsyApplication"
43-->N:\new project\android\app\src\main\AndroidManifest.xml:31:9-42
44        android:allowBackup="true"
44-->N:\new project\android\app\src\main\AndroidManifest.xml:32:9-35
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\345ccf188a6f49c4c91ca3120313e197\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
46        android:dataExtractionRules="@xml/data_extraction_rules"
46-->N:\new project\android\app\src\main\AndroidManifest.xml:33:9-65
47        android:debuggable="true"
48        android:extractNativeLibs="false"
49        android:fullBackupContent="@xml/backup_rules"
49-->N:\new project\android\app\src\main\AndroidManifest.xml:34:9-54
50        android:icon="@mipmap/ic_launcher"
50-->N:\new project\android\app\src\main\AndroidManifest.xml:35:9-43
51        android:label="@string/app_name"
51-->N:\new project\android\app\src\main\AndroidManifest.xml:36:9-41
52        android:networkSecurityConfig="@xml/network_security_config"
52-->N:\new project\android\app\src\main\AndroidManifest.xml:41:9-69
53        android:roundIcon="@mipmap/ic_launcher"
53-->N:\new project\android\app\src\main\AndroidManifest.xml:37:9-48
54        android:supportsRtl="true"
54-->N:\new project\android\app\src\main\AndroidManifest.xml:38:9-35
55        android:theme="@style/Theme.Clipsy"
55-->N:\new project\android\app\src\main\AndroidManifest.xml:39:9-44
56        android:usesCleartextTraffic="true" >
56-->N:\new project\android\app\src\main\AndroidManifest.xml:40:9-44
57
58        <!-- Main Activity -->
59        <activity
59-->N:\new project\android\app\src\main\AndroidManifest.xml:45:9-53:20
60            android:name="com.clipsy.android.ui.MainActivity"
60-->N:\new project\android\app\src\main\AndroidManifest.xml:46:13-44
61            android:exported="true"
61-->N:\new project\android\app\src\main\AndroidManifest.xml:47:13-36
62            android:theme="@style/Theme.Clipsy" >
62-->N:\new project\android\app\src\main\AndroidManifest.xml:48:13-48
63            <intent-filter>
63-->N:\new project\android\app\src\main\AndroidManifest.xml:49:13-52:29
64                <action android:name="android.intent.action.MAIN" />
64-->N:\new project\android\app\src\main\AndroidManifest.xml:50:17-69
64-->N:\new project\android\app\src\main\AndroidManifest.xml:50:25-66
65
66                <category android:name="android.intent.category.LAUNCHER" />
66-->N:\new project\android\app\src\main\AndroidManifest.xml:51:17-77
66-->N:\new project\android\app\src\main\AndroidManifest.xml:51:27-74
67            </intent-filter>
68        </activity>
69
70        <!-- Simple Test Activity (backup) -->
71        <activity
71-->N:\new project\android\app\src\main\AndroidManifest.xml:56:9-59:51
72            android:name="com.clipsy.android.ui.SimpleMainActivity"
72-->N:\new project\android\app\src\main\AndroidManifest.xml:57:13-50
73            android:exported="false"
73-->N:\new project\android\app\src\main\AndroidManifest.xml:58:13-37
74            android:theme="@style/Theme.Clipsy" />
74-->N:\new project\android\app\src\main\AndroidManifest.xml:59:13-48
75
76        <!-- Settings Activity -->
77        <activity
77-->N:\new project\android\app\src\main\AndroidManifest.xml:62:9-66:61
78            android:name="com.clipsy.android.ui.SettingsActivity"
78-->N:\new project\android\app\src\main\AndroidManifest.xml:63:13-48
79            android:exported="false"
79-->N:\new project\android\app\src\main\AndroidManifest.xml:64:13-37
80            android:parentActivityName="com.clipsy.android.ui.MainActivity"
80-->N:\new project\android\app\src\main\AndroidManifest.xml:66:13-58
81            android:theme="@style/Theme.Clipsy" />
81-->N:\new project\android\app\src\main\AndroidManifest.xml:65:13-48
82
83        <!-- Clipboard Sync Service -->
84        <service
84-->N:\new project\android\app\src\main\AndroidManifest.xml:69:9-73:56
85            android:name="com.clipsy.android.service.ClipboardSyncService"
85-->N:\new project\android\app\src\main\AndroidManifest.xml:70:13-57
86            android:enabled="true"
86-->N:\new project\android\app\src\main\AndroidManifest.xml:71:13-35
87            android:exported="false"
87-->N:\new project\android\app\src\main\AndroidManifest.xml:72:13-37
88            android:foregroundServiceType="dataSync" />
88-->N:\new project\android\app\src\main\AndroidManifest.xml:73:13-53
89
90        <provider
90-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a91f452df0000e700c370ac1a193c5b7\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
91            android:name="androidx.startup.InitializationProvider"
91-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a91f452df0000e700c370ac1a193c5b7\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
92            android:authorities="com.clipsy.android.debug.androidx-startup"
92-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a91f452df0000e700c370ac1a193c5b7\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
93            android:exported="false" >
93-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a91f452df0000e700c370ac1a193c5b7\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
94            <meta-data
94-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a91f452df0000e700c370ac1a193c5b7\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
95                android:name="androidx.emoji2.text.EmojiCompatInitializer"
95-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a91f452df0000e700c370ac1a193c5b7\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
96                android:value="androidx.startup" />
96-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a91f452df0000e700c370ac1a193c5b7\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
97            <meta-data
97-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
98                android:name="androidx.work.WorkManagerInitializer"
98-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
99                android:value="androidx.startup" />
99-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
100            <meta-data
100-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13bfa49b324cc449865639e3d8511ac3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
101                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
101-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13bfa49b324cc449865639e3d8511ac3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
102                android:value="androidx.startup" />
102-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\13bfa49b324cc449865639e3d8511ac3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
103            <meta-data
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
104                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
105                android:value="androidx.startup" />
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
106        </provider>
107
108        <service
108-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
109            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
109-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
110            android:directBootAware="false"
110-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
111            android:enabled="@bool/enable_system_alarm_service_default"
111-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
112            android:exported="false" />
112-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
113        <service
113-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
114            android:name="androidx.work.impl.background.systemjob.SystemJobService"
114-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
115            android:directBootAware="false"
115-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
116            android:enabled="@bool/enable_system_job_service_default"
116-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
117            android:exported="true"
117-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
118            android:permission="android.permission.BIND_JOB_SERVICE" />
118-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
119        <service
119-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
120            android:name="androidx.work.impl.foreground.SystemForegroundService"
120-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
121            android:directBootAware="false"
121-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
122            android:enabled="@bool/enable_system_foreground_service_default"
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
123            android:exported="false" />
123-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
124
125        <receiver
125-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
126            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
126-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
127            android:directBootAware="false"
127-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
128            android:enabled="true"
128-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
129            android:exported="false" />
129-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
130        <receiver
130-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
131            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
132            android:directBootAware="false"
132-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
133            android:enabled="false"
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
134            android:exported="false" >
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
135            <intent-filter>
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
136                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
137                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
138            </intent-filter>
139        </receiver>
140        <receiver
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
141            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
142            android:directBootAware="false"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
143            android:enabled="false"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
144            android:exported="false" >
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
145            <intent-filter>
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
146                <action android:name="android.intent.action.BATTERY_OKAY" />
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
147                <action android:name="android.intent.action.BATTERY_LOW" />
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
148            </intent-filter>
149        </receiver>
150        <receiver
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
151            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
152            android:directBootAware="false"
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
153            android:enabled="false"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
154            android:exported="false" >
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
155            <intent-filter>
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
156                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
157                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
158            </intent-filter>
159        </receiver>
160        <receiver
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
161            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
162            android:directBootAware="false"
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
163            android:enabled="false"
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
164            android:exported="false" >
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
165            <intent-filter>
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
166                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
167            </intent-filter>
168        </receiver>
169        <receiver
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
170            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
171            android:directBootAware="false"
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
172            android:enabled="false"
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
173            android:exported="false" >
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
174            <intent-filter>
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
175                <action android:name="android.intent.action.BOOT_COMPLETED" />
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
176                <action android:name="android.intent.action.TIME_SET" />
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
177                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
178            </intent-filter>
179        </receiver>
180        <receiver
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
181            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
182            android:directBootAware="false"
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
183            android:enabled="@bool/enable_system_alarm_service_default"
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
184            android:exported="false" >
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
185            <intent-filter>
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
186                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
187            </intent-filter>
188        </receiver>
189        <receiver
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
190            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
191            android:directBootAware="false"
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
192            android:enabled="true"
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
193            android:exported="true"
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
194            android:permission="android.permission.DUMP" >
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
195            <intent-filter>
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
196                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f700c8ce43ef7fdd065ac16f1abca4c\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
197            </intent-filter>
198        </receiver>
199
200        <uses-library
200-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\980a306dbdc1b6f9a209a99e37884eb6\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
201            android:name="androidx.window.extensions"
201-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\980a306dbdc1b6f9a209a99e37884eb6\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
202            android:required="false" />
202-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\980a306dbdc1b6f9a209a99e37884eb6\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
203        <uses-library
203-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\980a306dbdc1b6f9a209a99e37884eb6\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
204            android:name="androidx.window.sidecar"
204-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\980a306dbdc1b6f9a209a99e37884eb6\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
205            android:required="false" />
205-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\980a306dbdc1b6f9a209a99e37884eb6\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
206
207        <service
207-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\66faa70639eda926d6580574ce67041c\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
208            android:name="androidx.room.MultiInstanceInvalidationService"
208-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\66faa70639eda926d6580574ce67041c\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
209            android:directBootAware="true"
209-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\66faa70639eda926d6580574ce67041c\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
210            android:exported="false" />
210-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\66faa70639eda926d6580574ce67041c\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
211
212        <receiver
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
213            android:name="androidx.profileinstaller.ProfileInstallReceiver"
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
214            android:directBootAware="false"
214-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
215            android:enabled="true"
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
216            android:exported="true"
216-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
217            android:permission="android.permission.DUMP" >
217-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
218            <intent-filter>
218-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
219                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
219-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
219-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
220            </intent-filter>
221            <intent-filter>
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
222                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
223            </intent-filter>
224            <intent-filter>
224-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
225                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
225-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
225-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
226            </intent-filter>
227            <intent-filter>
227-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
228                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
228-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
228-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28b834cf3805eb11b70fb9e7e8de04db\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
229            </intent-filter>
230        </receiver>
231    </application>
232
233</manifest>
