1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.clipsy.android.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10
11    <!-- Network permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->N:\new project\android\app\src\main\AndroidManifest.xml:6:5-67
12-->N:\new project\android\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->N:\new project\android\app\src\main\AndroidManifest.xml:7:5-79
13-->N:\new project\android\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->N:\new project\android\app\src\main\AndroidManifest.xml:8:5-76
14-->N:\new project\android\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
15-->N:\new project\android\app\src\main\AndroidManifest.xml:9:5-86
15-->N:\new project\android\app\src\main\AndroidManifest.xml:9:22-83
16
17    <!-- Foreground service permission (Android 9+) -->
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->N:\new project\android\app\src\main\AndroidManifest.xml:12:5-77
18-->N:\new project\android\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
19-->N:\new project\android\app\src\main\AndroidManifest.xml:13:5-87
19-->N:\new project\android\app\src\main\AndroidManifest.xml:13:22-84
20
21    <!-- Boot receiver permission -->
22    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
22-->N:\new project\android\app\src\main\AndroidManifest.xml:16:5-81
22-->N:\new project\android\app\src\main\AndroidManifest.xml:16:22-78
23
24    <!-- Notification permission (Android 13+) -->
25    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
25-->N:\new project\android\app\src\main\AndroidManifest.xml:19:5-77
25-->N:\new project\android\app\src\main\AndroidManifest.xml:19:22-74
26
27    <!-- Wake lock for background operations -->
28    <uses-permission android:name="android.permission.WAKE_LOCK" />
28-->N:\new project\android\app\src\main\AndroidManifest.xml:22:5-68
28-->N:\new project\android\app\src\main\AndroidManifest.xml:22:22-65
29
30    <!-- Battery optimization bypass -->
31    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
31-->N:\new project\android\app\src\main\AndroidManifest.xml:25:5-95
31-->N:\new project\android\app\src\main\AndroidManifest.xml:25:22-92
32
33    <!-- Clipboard access permission -->
34    <uses-permission android:name="android.permission.READ_CLIPBOARD_IN_BACKGROUND" />
34-->N:\new project\android\app\src\main\AndroidManifest.xml:28:5-87
34-->N:\new project\android\app\src\main\AndroidManifest.xml:28:22-84
35    <uses-permission android:name="android.permission.WRITE_CLIPBOARD_IN_BACKGROUND" />
35-->N:\new project\android\app\src\main\AndroidManifest.xml:29:5-88
35-->N:\new project\android\app\src\main\AndroidManifest.xml:29:22-85
36
37    <permission
37-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d22882521834a86e542cf7cc54c4b22\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.clipsy.android.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d22882521834a86e542cf7cc54c4b22\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d22882521834a86e542cf7cc54c4b22\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.clipsy.android.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d22882521834a86e542cf7cc54c4b22\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d22882521834a86e542cf7cc54c4b22\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
42
43    <application
43-->N:\new project\android\app\src\main\AndroidManifest.xml:31:5-78:19
44        android:name="com.clipsy.android.ClipsyApplication"
44-->N:\new project\android\app\src\main\AndroidManifest.xml:32:9-42
45        android:allowBackup="true"
45-->N:\new project\android\app\src\main\AndroidManifest.xml:33:9-35
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d22882521834a86e542cf7cc54c4b22\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
47        android:dataExtractionRules="@xml/data_extraction_rules"
47-->N:\new project\android\app\src\main\AndroidManifest.xml:34:9-65
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:fullBackupContent="@xml/backup_rules"
50-->N:\new project\android\app\src\main\AndroidManifest.xml:35:9-54
51        android:icon="@mipmap/ic_launcher"
51-->N:\new project\android\app\src\main\AndroidManifest.xml:36:9-43
52        android:label="@string/app_name"
52-->N:\new project\android\app\src\main\AndroidManifest.xml:37:9-41
53        android:networkSecurityConfig="@xml/network_security_config"
53-->N:\new project\android\app\src\main\AndroidManifest.xml:42:9-69
54        android:roundIcon="@mipmap/ic_launcher"
54-->N:\new project\android\app\src\main\AndroidManifest.xml:38:9-48
55        android:supportsRtl="true"
55-->N:\new project\android\app\src\main\AndroidManifest.xml:39:9-35
56        android:theme="@style/Theme.Clipsy"
56-->N:\new project\android\app\src\main\AndroidManifest.xml:40:9-44
57        android:usesCleartextTraffic="true" >
57-->N:\new project\android\app\src\main\AndroidManifest.xml:41:9-44
58
59        <!-- Main Activity -->
60        <activity
60-->N:\new project\android\app\src\main\AndroidManifest.xml:46:9-54:20
61            android:name="com.clipsy.android.ui.MainActivity"
61-->N:\new project\android\app\src\main\AndroidManifest.xml:47:13-44
62            android:exported="true"
62-->N:\new project\android\app\src\main\AndroidManifest.xml:48:13-36
63            android:theme="@style/Theme.Clipsy" >
63-->N:\new project\android\app\src\main\AndroidManifest.xml:49:13-48
64            <intent-filter>
64-->N:\new project\android\app\src\main\AndroidManifest.xml:50:13-53:29
65                <action android:name="android.intent.action.MAIN" />
65-->N:\new project\android\app\src\main\AndroidManifest.xml:51:17-69
65-->N:\new project\android\app\src\main\AndroidManifest.xml:51:25-66
66
67                <category android:name="android.intent.category.LAUNCHER" />
67-->N:\new project\android\app\src\main\AndroidManifest.xml:52:17-77
67-->N:\new project\android\app\src\main\AndroidManifest.xml:52:27-74
68            </intent-filter>
69        </activity>
70
71        <!-- Simple Test Activity (backup) -->
72        <activity
72-->N:\new project\android\app\src\main\AndroidManifest.xml:57:9-60:51
73            android:name="com.clipsy.android.ui.SimpleMainActivity"
73-->N:\new project\android\app\src\main\AndroidManifest.xml:58:13-50
74            android:exported="false"
74-->N:\new project\android\app\src\main\AndroidManifest.xml:59:13-37
75            android:theme="@style/Theme.Clipsy" />
75-->N:\new project\android\app\src\main\AndroidManifest.xml:60:13-48
76
77        <!-- Settings Activity -->
78        <activity
78-->N:\new project\android\app\src\main\AndroidManifest.xml:63:9-67:61
79            android:name="com.clipsy.android.ui.SettingsActivity"
79-->N:\new project\android\app\src\main\AndroidManifest.xml:64:13-48
80            android:exported="false"
80-->N:\new project\android\app\src\main\AndroidManifest.xml:65:13-37
81            android:parentActivityName="com.clipsy.android.ui.MainActivity"
81-->N:\new project\android\app\src\main\AndroidManifest.xml:67:13-58
82            android:theme="@style/Theme.Clipsy" />
82-->N:\new project\android\app\src\main\AndroidManifest.xml:66:13-48
83
84        <!-- Clipboard Sync Service -->
85        <service
85-->N:\new project\android\app\src\main\AndroidManifest.xml:70:9-74:56
86            android:name="com.clipsy.android.service.ClipboardSyncService"
86-->N:\new project\android\app\src\main\AndroidManifest.xml:71:13-57
87            android:enabled="true"
87-->N:\new project\android\app\src\main\AndroidManifest.xml:72:13-35
88            android:exported="false"
88-->N:\new project\android\app\src\main\AndroidManifest.xml:73:13-37
89            android:foregroundServiceType="dataSync" />
89-->N:\new project\android\app\src\main\AndroidManifest.xml:74:13-53
90
91        <provider
91-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c74d51dd8ba1d13cb10da58acf9f9165\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
92            android:name="androidx.startup.InitializationProvider"
92-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c74d51dd8ba1d13cb10da58acf9f9165\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
93            android:authorities="com.clipsy.android.debug.androidx-startup"
93-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c74d51dd8ba1d13cb10da58acf9f9165\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
94            android:exported="false" >
94-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c74d51dd8ba1d13cb10da58acf9f9165\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
95            <meta-data
95-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c74d51dd8ba1d13cb10da58acf9f9165\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.emoji2.text.EmojiCompatInitializer"
96-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c74d51dd8ba1d13cb10da58acf9f9165\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
97                android:value="androidx.startup" />
97-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c74d51dd8ba1d13cb10da58acf9f9165\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
98            <meta-data
98-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
99                android:name="androidx.work.WorkManagerInitializer"
99-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
100                android:value="androidx.startup" />
100-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
101            <meta-data
101-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d3eae4473fed4855fb2e543272d5eb7\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
102-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d3eae4473fed4855fb2e543272d5eb7\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
103                android:value="androidx.startup" />
103-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d3eae4473fed4855fb2e543272d5eb7\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
106                android:value="androidx.startup" />
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
107        </provider>
108
109        <service
109-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
110            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
110-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
111            android:directBootAware="false"
111-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
112            android:enabled="@bool/enable_system_alarm_service_default"
112-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
113            android:exported="false" />
113-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
114        <service
114-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
115            android:name="androidx.work.impl.background.systemjob.SystemJobService"
115-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
116            android:directBootAware="false"
116-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
117            android:enabled="@bool/enable_system_job_service_default"
117-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
118            android:exported="true"
118-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
119            android:permission="android.permission.BIND_JOB_SERVICE" />
119-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
120        <service
120-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
121            android:name="androidx.work.impl.foreground.SystemForegroundService"
121-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
122            android:directBootAware="false"
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
123            android:enabled="@bool/enable_system_foreground_service_default"
123-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
124            android:exported="false" />
124-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
125
126        <receiver
126-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
127            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
127-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
128            android:directBootAware="false"
128-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
129            android:enabled="true"
129-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
130            android:exported="false" />
130-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
131        <receiver
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
132            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
132-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
133            android:directBootAware="false"
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
134            android:enabled="false"
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
135            android:exported="false" >
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
136            <intent-filter>
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
137                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
138                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
139            </intent-filter>
140        </receiver>
141        <receiver
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
142            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
143            android:directBootAware="false"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
144            android:enabled="false"
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
145            android:exported="false" >
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
146            <intent-filter>
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
147                <action android:name="android.intent.action.BATTERY_OKAY" />
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
148                <action android:name="android.intent.action.BATTERY_LOW" />
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
149            </intent-filter>
150        </receiver>
151        <receiver
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
152            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
153            android:directBootAware="false"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
154            android:enabled="false"
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
155            android:exported="false" >
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
156            <intent-filter>
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
157                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
158                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
159            </intent-filter>
160        </receiver>
161        <receiver
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
162            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
163            android:directBootAware="false"
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
164            android:enabled="false"
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
165            android:exported="false" >
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
166            <intent-filter>
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
167                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
168            </intent-filter>
169        </receiver>
170        <receiver
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
171            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
172            android:directBootAware="false"
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
173            android:enabled="false"
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
174            android:exported="false" >
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
175            <intent-filter>
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
176                <action android:name="android.intent.action.BOOT_COMPLETED" />
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
177                <action android:name="android.intent.action.TIME_SET" />
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
178                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
179            </intent-filter>
180        </receiver>
181        <receiver
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
182            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
183            android:directBootAware="false"
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
184            android:enabled="@bool/enable_system_alarm_service_default"
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
185            android:exported="false" >
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
186            <intent-filter>
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
187                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
188            </intent-filter>
189        </receiver>
190        <receiver
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
191            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
192            android:directBootAware="false"
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
193            android:enabled="true"
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
194            android:exported="true"
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
195            android:permission="android.permission.DUMP" >
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
196            <intent-filter>
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
197                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2883f64cc5639033a119a701496ac8aa\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
198            </intent-filter>
199        </receiver>
200
201        <uses-library
201-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4f8536da2d52f3e1a612e86a72156c6\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
202            android:name="androidx.window.extensions"
202-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4f8536da2d52f3e1a612e86a72156c6\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
203            android:required="false" />
203-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4f8536da2d52f3e1a612e86a72156c6\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
204        <uses-library
204-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4f8536da2d52f3e1a612e86a72156c6\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
205            android:name="androidx.window.sidecar"
205-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4f8536da2d52f3e1a612e86a72156c6\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
206            android:required="false" />
206-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4f8536da2d52f3e1a612e86a72156c6\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
207
208        <service
208-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92bd09925f80d2e8991cb1402094b346\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
209            android:name="androidx.room.MultiInstanceInvalidationService"
209-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92bd09925f80d2e8991cb1402094b346\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
210            android:directBootAware="true"
210-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92bd09925f80d2e8991cb1402094b346\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
211            android:exported="false" />
211-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92bd09925f80d2e8991cb1402094b346\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
212
213        <receiver
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
214            android:name="androidx.profileinstaller.ProfileInstallReceiver"
214-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
215            android:directBootAware="false"
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
216            android:enabled="true"
216-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
217            android:exported="true"
217-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
218            android:permission="android.permission.DUMP" >
218-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
219            <intent-filter>
219-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
220                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
220-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
220-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
221            </intent-filter>
222            <intent-filter>
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
223                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
223-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
223-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
224            </intent-filter>
225            <intent-filter>
225-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
226                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
226-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
226-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
227            </intent-filter>
228            <intent-filter>
228-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
229                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
229-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
229-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\204c2e37a34c54f09fe0e2bbd7ce4f8c\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
230            </intent-filter>
231        </receiver>
232    </application>
233
234</manifest>
