@echo off
REM Clipsy Windows Startup Removal Script
REM This script removes C<PERSON><PERSON> from Windows startup configuration
REM Run as Administrator to remove system-wide configurations

echo ========================================
echo   Clipsy Windows Startup Removal
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    set "IS_ADMIN=true"
    echo [INFO] Running with Administrator privileges
    echo [INFO] Can remove system-wide and user-specific startup
) else (
    set "IS_ADMIN=false"
    echo [INFO] Running as regular user
    echo [INFO] Will remove user-specific startup only
)
echo.

echo [INFO] Removing all Clipsy startup configurations...
echo.

REM Remove registry entries (Current User)
echo [STEP 1] Removing Current User registry entries...
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "Clipsy" /f >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Removed: HKCU Clipsy entry
) else (
    echo [INFO] No HKCU Clipsy entry found
)

reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" /f >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Removed: HKCU ClipsyPC entry
) else (
    echo [INFO] No HKCU ClipsyPC entry found
)

echo.

REM Remove registry entries (All Users) - Admin only
if "%IS_ADMIN%"=="true" (
    echo [STEP 2] Removing All Users registry entries...
    reg delete "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v "Clipsy" /f >nul 2>&1
    if %errorLevel% == 0 (
        echo [OK] Removed: HKLM Clipsy entry
    ) else (
        echo [INFO] No HKLM Clipsy entry found
    )
    
    reg delete "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" /f >nul 2>&1
    if %errorLevel% == 0 (
        echo [OK] Removed: HKLM ClipsyPC entry
    ) else (
        echo [INFO] No HKLM ClipsyPC entry found
    )
    echo.
) else (
    echo [STEP 2] Skipping All Users registry (requires Administrator)
    echo.
)

REM Remove startup folder shortcuts
echo [STEP 3] Removing startup folder shortcuts...
set "STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"

del "%STARTUP_FOLDER%\Clipsy*.lnk" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Removed: Clipsy shortcuts from startup folder
) else (
    echo [INFO] No Clipsy shortcuts found in startup folder
)

echo.

REM Remove Windows service - Admin only
if "%IS_ADMIN%"=="true" (
    echo [STEP 4] Removing Windows service...
    
    REM Check if service exists
    sc query "ClipsyService" >nul 2>&1
    if %errorLevel% == 0 (
        echo [INFO] Found ClipsyService, removing...
        
        REM Stop service first
        sc stop "ClipsyService" >nul 2>&1
        if %errorLevel% == 0 (
            echo [OK] Stopped: ClipsyService
        )
        
        REM Wait a moment for service to stop
        timeout /t 2 /nobreak >nul
        
        REM Delete service
        sc delete "ClipsyService" >nul 2>&1
        if %errorLevel% == 0 (
            echo [OK] Removed: ClipsyService
        ) else (
            echo [ERROR] Failed to remove ClipsyService
        )
    ) else (
        echo [INFO] No ClipsyService found
    )
    echo.
) else (
    echo [STEP 4] Skipping Windows service removal (requires Administrator)
    echo.
)

REM Remove Task Scheduler entries (if any)
if "%IS_ADMIN%"=="true" (
    echo [STEP 5] Removing Task Scheduler entries...
    
    schtasks /query /tn "Clipsy*" >nul 2>&1
    if %errorLevel% == 0 (
        echo [INFO] Found Clipsy scheduled tasks, removing...
        schtasks /delete /tn "Clipsy*" /f >nul 2>&1
        if %errorLevel% == 0 (
            echo [OK] Removed: Clipsy scheduled tasks
        ) else (
            echo [ERROR] Failed to remove some scheduled tasks
        )
    ) else (
        echo [INFO] No Clipsy scheduled tasks found
    )
    echo.
) else (
    echo [STEP 5] Skipping Task Scheduler (requires Administrator)
    echo.
)

REM Verify removal
echo [STEP 6] Verifying removal...
echo.

set "FOUND_STARTUP=false"

REM Check registry entries
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" >nul 2>&1
if %errorLevel% == 0 (
    echo [WARNING] Current User registry entry still exists
    set "FOUND_STARTUP=true"
)

reg query "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" >nul 2>&1
if %errorLevel% == 0 (
    echo [WARNING] All Users registry entry still exists
    set "FOUND_STARTUP=true"
)

REM Check startup folder
if exist "%STARTUP_FOLDER%\Clipsy*.lnk" (
    echo [WARNING] Startup folder shortcuts still exist
    set "FOUND_STARTUP=true"
)

REM Check Windows service
sc query "ClipsyService" >nul 2>&1
if %errorLevel% == 0 (
    echo [WARNING] Windows service still exists
    set "FOUND_STARTUP=true"
)

if "%FOUND_STARTUP%"=="false" (
    echo [SUCCESS] All Clipsy startup configurations have been removed.
) else (
    echo [WARNING] Some startup configurations may still exist.
    echo [INFO] You may need to remove them manually or run as Administrator.
)

echo.

REM Kill any running Clipsy processes
echo [OPTIONAL] Stop running Clipsy processes:
choice /C YN /M "Do you want to stop any currently running Clipsy processes?"
if errorlevel 1 if not errorlevel 2 (
    echo [INFO] Stopping Clipsy processes...
    
    taskkill /f /im "Clipsy.exe" >nul 2>&1
    if %errorLevel% == 0 (
        echo [OK] Stopped: Clipsy.exe
    )
    
    taskkill /f /im "ClipsyCleanedCode.exe" >nul 2>&1
    if %errorLevel% == 0 (
        echo [OK] Stopped: ClipsyCleanedCode.exe
    )
    
    taskkill /f /im "python.exe" /fi "WINDOWTITLE eq *clipsy*" >nul 2>&1
    if %errorLevel% == 0 (
        echo [OK] Stopped: Python Clipsy processes
    )
    
    echo [INFO] Clipsy processes stopped.
)

echo.
echo ========================================
echo    Startup Removal Complete!
echo ========================================
echo.
echo [SUCCESS] Clipsy startup configurations have been removed.
echo.
echo Clipsy will no longer start automatically with Windows.
echo.
echo To re-enable startup, run: setup-autostart.bat
echo.
pause
