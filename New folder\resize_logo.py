#!/usr/bin/env python3
"""
Logo resizer script for Clipsy application
Resizes the logo to various sizes needed for Android and Windows applications
"""

import os
from PIL import Image
import argparse

def create_android_icons(input_image_path, output_dir="android/app/src/main/res"):
    """Create Android app icons in various densities"""
    
    # Android icon sizes (in pixels)
    android_sizes = {
        "mipmap-mdpi": 48,
        "mipmap-hdpi": 72,
        "mipmap-xhdpi": 96,
        "mipmap-xxhdpi": 144,
        "mipmap-xxxhdpi": 192
    }
    
    # Load the original image
    try:
        original = Image.open(input_image_path)
        print(f"Original image size: {original.size}")
        
        # Ensure output directories exist
        for density in android_sizes.keys():
            os.makedirs(f"{output_dir}/{density}", exist_ok=True)
        
        # Create resized versions
        for density, size in android_sizes.items():
            resized = original.resize((size, size), Image.Resampling.LANCZOS)
            output_path = f"{output_dir}/{density}/ic_launcher.png"
            resized.save(output_path, "PNG", optimize=True)
            print(f"Created {output_path} ({size}x{size})")
            
            # Also create foreground version for adaptive icons
            foreground_path = f"{output_dir}/{density}/ic_launcher_foreground.png"
            resized.save(foreground_path, "PNG", optimize=True)
            print(f"Created {foreground_path} ({size}x{size})")
        
        # Create notification icon (should be white/transparent, smaller)
        notification_sizes = {
            "drawable-mdpi": 24,
            "drawable-hdpi": 36,
            "drawable-xhdpi": 48,
            "drawable-xxhdpi": 72,
            "drawable-xxxhdpi": 96
        }
        
        for density, size in notification_sizes.items():
            os.makedirs(f"{output_dir}/{density}", exist_ok=True)
            resized = original.resize((size, size), Image.Resampling.LANCZOS)
            output_path = f"{output_dir}/{density}/ic_notification.png"
            resized.save(output_path, "PNG", optimize=True)
            print(f"Created {output_path} ({size}x{size})")
            
    except Exception as e:
        print(f"Error processing Android icons: {e}")

def create_windows_icons(input_image_path, output_dir="windows"):
    """Create Windows app icons"""
    
    # Windows icon sizes
    windows_sizes = [16, 24, 32, 48, 64, 128, 256]
    
    try:
        original = Image.open(input_image_path)
        os.makedirs(output_dir, exist_ok=True)
        
        # Create individual PNG files
        for size in windows_sizes:
            resized = original.resize((size, size), Image.Resampling.LANCZOS)
            output_path = f"{output_dir}/icon_{size}x{size}.png"
            resized.save(output_path, "PNG", optimize=True)
            print(f"Created {output_path} ({size}x{size})")
        
        # Create a multi-size ICO file
        ico_images = []
        for size in windows_sizes:
            resized = original.resize((size, size), Image.Resampling.LANCZOS)
            ico_images.append(resized)
        
        ico_path = f"{output_dir}/app_icon.ico"
        ico_images[0].save(ico_path, format='ICO', sizes=[(img.width, img.height) for img in ico_images])
        print(f"Created {ico_path} (multi-size ICO)")
        
    except Exception as e:
        print(f"Error processing Windows icons: {e}")

def create_web_icons(input_image_path, output_dir="web"):
    """Create web/PWA icons"""
    
    # Web icon sizes
    web_sizes = [16, 32, 48, 72, 96, 144, 192, 256, 384, 512]
    
    try:
        original = Image.open(input_image_path)
        os.makedirs(output_dir, exist_ok=True)
        
        for size in web_sizes:
            resized = original.resize((size, size), Image.Resampling.LANCZOS)
            output_path = f"{output_dir}/icon_{size}x{size}.png"
            resized.save(output_path, "PNG", optimize=True)
            print(f"Created {output_path} ({size}x{size})")
            
        # Create favicon
        favicon = original.resize((32, 32), Image.Resampling.LANCZOS)
        favicon.save(f"{output_dir}/favicon.ico", format='ICO')
        print(f"Created {output_dir}/favicon.ico (32x32)")
        
    except Exception as e:
        print(f"Error processing web icons: {e}")

def main():
    parser = argparse.ArgumentParser(description='Resize logo for different platforms')
    parser.add_argument('input_image', help='Path to the input logo image')
    parser.add_argument('--android', action='store_true', help='Create Android icons')
    parser.add_argument('--windows', action='store_true', help='Create Windows icons')
    parser.add_argument('--web', action='store_true', help='Create web icons')
    parser.add_argument('--all', action='store_true', help='Create icons for all platforms')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_image):
        print(f"Error: Input image '{args.input_image}' not found")
        return
    
    if args.all or args.android:
        print("Creating Android icons...")
        create_android_icons(args.input_image)
    
    if args.all or args.windows:
        print("Creating Windows icons...")
        create_windows_icons(args.input_image)
    
    if args.all or args.web:
        print("Creating web icons...")
        create_web_icons(args.input_image)
    
    if not any([args.android, args.windows, args.web, args.all]):
        print("No platform specified. Use --android, --windows, --web, or --all")
        print("Creating icons for all platforms by default...")
        create_android_icons(args.input_image)
        create_windows_icons(args.input_image)
        create_web_icons(args.input_image)

if __name__ == "__main__":
    main()
