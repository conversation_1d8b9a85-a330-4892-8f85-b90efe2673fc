package com.clipsy.android.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Data model for discovered devices.
 */
@Parcelize
data class Device(
    val ip: String,
    val deviceName: String,
    val deviceType: String, // "windows" or "android"
    val websocketPort: Int = 8766,  // Default to Windows server port for connections
    val lastSeen: Long = System.currentTimeMillis(),
    val manual: Boolean = false,
    val deviceId: String? = null
) : Parcelable {

    companion object {
        const val TYPE_WINDOWS = "windows"
        const val TYPE_ANDROID = "android"
        const val DEFAULT_WEBSOCKET_PORT = 8766  // Windows server port
        const val ANDROID_SERVER_PORT = 8765     // Android server port
    }
    
    /**
     * Get the WebSocket URL for this device.
     */
    fun getWebSocketUrl(): String {
        return "ws://$ip:$websocketPort"
    }
    
    /**
     * Check if the device is considered online based on last seen time.
     */
    fun isOnline(timeoutMs: Long = 30000): Boolean {
        return System.currentTimeMillis() - lastSeen < timeoutMs
    }
    
    /**
     * Get formatted last seen time.
     */
    fun getLastSeenFormatted(): String {
        val now = System.currentTimeMillis()
        val diff = now - lastSeen
        
        return when {
            diff < 1000 -> "Just now"
            diff < 60000 -> "${diff / 1000}s ago"
            diff < 3600000 -> "${diff / 60000}m ago"
            diff < 86400000 -> "${diff / 3600000}h ago"
            else -> "${diff / 86400000}d ago"
        }
    }
    
    /**
     * Get device type icon resource name.
     */
    fun getTypeIcon(): String {
        return when (deviceType) {
            TYPE_WINDOWS -> "ic_computer"
            TYPE_ANDROID -> "ic_phone_android"
            else -> "ic_device_unknown"
        }
    }
    
    /**
     * Create a copy with updated last seen time.
     */
    fun updateLastSeen(): Device {
        return copy(lastSeen = System.currentTimeMillis())
    }
    
    /**
     * Get unique identifier for this device.
     * Use device name for deduplication instead of IP to handle multiple network interfaces.
     */
    fun getUniqueId(): String {
        return deviceId ?: deviceName.replace(" ", "_").lowercase()
    }

    /**
     * Get connection identifier (IP:port) for actual connections.
     */
    fun getConnectionId(): String {
        return "$ip:$websocketPort"
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as Device
        
        return ip == other.ip && websocketPort == other.websocketPort
    }
    
    override fun hashCode(): Int {
        var result = ip.hashCode()
        result = 31 * result + websocketPort
        return result
    }
}
