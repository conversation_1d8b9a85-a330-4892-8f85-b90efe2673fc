# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.11.0"
  }
  digests {
    sha256: "\316\223\322\207\334\315\254\317s\033\2117\033\v\255\345\333\206y\375y\265Y3/\332\364EN\2637\033"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "`\261\v^\365v\233yW\001r\340\025\270\025\224\005\311/\003K\250\213\223\221\251wX\234\235\353N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.22"
  }
  digests {
    sha256: "j\276\024l\'\206A8\270t\314\314\376_SN>\271#\311\232\033{]EIN\345iO>\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.12.0"
  }
  digests {
    sha256: "\225\263\355\257x\"{|\310\330\002\321\037\207\223\251\256\322\222\345+\217\277\214\b\326\023L\313\027\026\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.12.0"
  }
  digests {
    sha256: "B\377\247\312G\327\272\217\341\330t\305~\371\307\021\033\304\032+\f\f!Q\2129\340}\"-\355\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.3.0"
  }
  digests {
    sha256: "\253\375)\310Un[\3202Z\237v\232\271\351\321T\377JU\025\304v\315\325\242\250([\033\031\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\222\375\017\002\273YO\a\264\017\202p\3533**\341\345\030\313\246\376\307\320b\363\366\004<\231\315\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.7.0"
  }
  digests {
    sha256: "\343\033\320\351+\320\263\0336\004H\312Z[\200\374\037cP{\264\356\216\233\323\000CtI\321x\233"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.0"
  }
  digests {
    sha256: "\323\246vp\235\352\004\362\250Pn*\350PR\377\367c\333Rj\307\361k\004\336P\375\320[\a "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.0"
  }
  digests {
    sha256: "\277\356\022\301\310\214?t\225O\277ngf\274\0309V\363tx\267\300$\372\347\365\263\204\223\327\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.2"
  }
  digests {
    sha256: "\337{\247?7}\275|\315(\261\022\257\320\215(\266\016\302\306\274\221\354e\206\t\310ED*\316\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.2"
  }
  digests {
    sha256: "F\f\2206\266M\247\316z\006j\f\261\204\340\024Q\317\304I)\252\033b\376\006\211Y\177#C\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.10.0"
  }
  digests {
    sha256: "- N\344\361\026\271\210\246\246\265\333\214\364=\343\370\247M\n\341\272\027Xo\220\252\343\216>\032-"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.4.1"
  }
  digests {
    sha256: "6\322\215\236\303:\214d18B\274\351\234\225sm\245\262zk:Q69\005\r\350/\aW&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-fragment-ktx"
    version: "2.7.5"
  }
  digests {
    sha256: "\254PI\020x\344\354\363\"\022\034\314p\327\b\240o\302\344Qi\250V\n\274\006\307nA\240\241m"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-fragment"
    version: "2.7.5"
  }
  digests {
    sha256: "\004\235\a\374\340^\213\037\314\362\030\211d\b2\037Q\265x0\234\206\255|L\034)-?ocn"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.7.5"
  }
  digests {
    sha256: "\274rxZ\374g\"\241\371]O\210oK\213+h\027\252@\241\177\301\236}\354\244:I\322Z\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.7.5"
  }
  digests {
    sha256: "\216l\207(\030\000\301\354F1\2769(\230\273mh\306\202\027D\222\314N2\a\305\357y\224\245\265"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-ktx"
    version: "2.7.5"
  }
  digests {
    sha256: "\275|\353\247]&\261\362\354\372\223\035\256hFx\360|\243\272\036\331\006\247,W\261\242\320i\332\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-ktx"
    version: "2.7.5"
  }
  digests {
    sha256: "\b7Kp\242L)\273\037\341\026\341d\272\f\006L\017\373\237\364\274\207\177\264\310\307&\206eg\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-ui"
    version: "2.7.5"
  }
  digests {
    sha256: "\303\326\023>\264\312\310\300y\327\306\222\363q1\323\267\207\237%f\377\223\002\350\3263\226(\020C:"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-ui-ktx"
    version: "2.7.5"
  }
  digests {
    sha256: "\312\204\347|d\321\351\035\307\312\026=p\215\345I\r$\316\v\361\322q\021\333\016\353\227g\027a\327"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0"
  }
  digests {
    sha256: "2\022\230[\344\022ss\312M\016\247\370\270\032%\n\342\020^\222Oy@\020]\006z\017\232\3010"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.1.0"
  }
  digests {
    sha256: ",\347\220l\321\336\240Z\354\201\227]\262-T8#Y\300Z!\262Rz\330H\274`\366\262r\223"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime-ktx"
    version: "2.8.1"
  }
  digests {
    sha256: "uV\rO^z\034T\21612\253\362s\317\016\332\217+\336f=PM\r\324*B\\25Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.8.1"
  }
  digests {
    sha256: "&\331\325b*\ri\302\266\001\313f\036\241u\366\340\265M\262u7\021\261\214\2416\276K\232\351\302"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.5.0"
  }
  digests {
    sha256: "\230\202\204\030w\244C\177 \002\355\2355?uzTR\341;J\260\353\361\225\306<\317\016.\bG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.5.0"
  }
  digests {
    sha256: "\002b\342\240\342\242\351\307&{\237z@XG\316cj\006\304\301lR\227l\266\215\367xf\270}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.3.0"
  }
  digests {
    sha256: "\323\323~$\003\305#\245\316\341\230;\'\246\336z\330\334\273P/Dl?v\374\245\016\327<\345b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.3.0"
  }
  digests {
    sha256: "\213[\323\254\357\001\352x\032\205E\275\367\020\261\300a\202Avi\241\246\214\320\256JSl\332\350\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.9.0"
  }
  digests {
    sha256: "2\252 k\232)\311\337^\332\223\240\222\317\263\260\271\023>#,\006+\252\210/\003\031\360\347\237\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.java-websocket"
    artifactId: "Java-WebSocket"
    version: "1.5.4"
  }
  digests {
    sha256: "Jp7\275\021\005\b\332\356kq\214~\0342O7g\243\262\202qXE\025(\366\266\206\f6W"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.slf4j"
    artifactId: "slf4j-api"
    version: "2.0.6"
  }
  digests {
    sha256: "/*\222\324\020\262h\023\235}c\267^\322^!\231\\\376A\000\301\233\3625w\317\333\310\a{\332"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\207*^>\a\300\270\322\034\267wi\253/F\213 \232\266\354\b#\030t@\032\354\376\243\004\250\034"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.2.1"
  }
  digests {
    sha256: "@\312\212\337\333~\377\266\037\254\263\233\331\312./:@\321\006t;\f\326\334\236!\350\276\335O\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jmdns"
    artifactId: "jmdns"
    version: "3.5.8"
  }
  digests {
    sha256: "]Epk\357i\370\027\264~\021\316e\220}\022d4\367OCW\203_\370|q \335~\234\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\a\1775\354\202\216\024\212\311-\fj\241A\2472\265\353zJ9\026y\256\357\245\237\251\241\245\314"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\0057\307\002>`I3\366\251V;\033\263\270>a\373\375,\303\320\351\220\rak\253hb\244"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 7
  library_dep_index: 6
}
library_dependencies {
  library_index: 5
  library_dep_index: 3
  library_dep_index: 6
}
library_dependencies {
  library_index: 6
  library_dep_index: 3
}
library_dependencies {
  library_index: 7
  library_dep_index: 3
}
library_dependencies {
  library_index: 8
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 3
  library_dep_index: 9
}
library_dependencies {
  library_index: 9
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 38
  library_dep_index: 3
  library_dep_index: 8
}
library_dependencies {
  library_index: 10
  library_dep_index: 3
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
}
library_dependencies {
  library_index: 12
  library_dep_index: 1
  library_dep_index: 13
}
library_dependencies {
  library_index: 14
  library_dep_index: 1
}
library_dependencies {
  library_index: 15
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 37
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 24
}
library_dependencies {
  library_index: 16
  library_dep_index: 1
}
library_dependencies {
  library_index: 17
  library_dep_index: 1
  library_dep_index: 16
}
library_dependencies {
  library_index: 18
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 15
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 19
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 5
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
}
library_dependencies {
  library_index: 21
  library_dep_index: 4
  library_dep_index: 22
  library_dep_index: 7
  library_dep_index: 5
}
library_dependencies {
  library_index: 22
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
}
library_dependencies {
  library_index: 23
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 18
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 15
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 24
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 15
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 25
  library_dep_index: 24
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 15
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 26
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 15
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 30
  library_dep_index: 18
  library_dep_index: 34
  library_dep_index: 24
}
library_dependencies {
  library_index: 27
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 26
  library_dep_index: 15
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 18
  library_dep_index: 34
  library_dep_index: 24
}
library_dependencies {
  library_index: 28
  library_dep_index: 15
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 15
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 30
  library_dep_index: 34
  library_dep_index: 18
  library_dep_index: 24
}
library_dependencies {
  library_index: 29
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 15
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 24
}
library_dependencies {
  library_index: 30
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 31
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 15
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 31
  library_dep_index: 1
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 1
}
library_dependencies {
  library_index: 33
  library_dep_index: 29
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 26
  library_dep_index: 15
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 18
  library_dep_index: 34
  library_dep_index: 24
}
library_dependencies {
  library_index: 34
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 24
  library_dep_index: 29
  library_dep_index: 35
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 18
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 15
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
}
library_dependencies {
  library_index: 35
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 3
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 35
  library_dep_index: 3
  library_dep_index: 35
}
library_dependencies {
  library_index: 37
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 13
}
library_dependencies {
  library_index: 38
  library_dep_index: 1
  library_dep_index: 11
}
library_dependencies {
  library_index: 39
  library_dep_index: 40
  library_dep_index: 1
  library_dep_index: 42
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 8
  library_dep_index: 45
  library_dep_index: 46
  library_dep_index: 48
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 15
  library_dep_index: 29
  library_dep_index: 55
  library_dep_index: 35
  library_dep_index: 3
  library_dep_index: 42
}
library_dependencies {
  library_index: 40
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 15
  library_dep_index: 29
  library_dep_index: 34
  library_dep_index: 37
  library_dep_index: 35
  library_dep_index: 32
  library_dep_index: 3
  library_dep_index: 41
}
library_dependencies {
  library_index: 41
  library_dep_index: 40
  library_dep_index: 8
  library_dep_index: 27
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 3
  library_dep_index: 40
}
library_dependencies {
  library_index: 42
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 39
}
library_dependencies {
  library_index: 43
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 11
}
library_dependencies {
  library_index: 44
  library_dep_index: 43
  library_dep_index: 14
  library_dep_index: 11
}
library_dependencies {
  library_index: 45
  library_dep_index: 1
}
library_dependencies {
  library_index: 46
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 11
}
library_dependencies {
  library_index: 48
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 49
  library_dep_index: 11
  library_dep_index: 9
  library_dep_index: 48
}
library_dependencies {
  library_index: 50
  library_dep_index: 40
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 8
  library_dep_index: 24
  library_dep_index: 15
  library_dep_index: 29
  library_dep_index: 34
  library_dep_index: 51
  library_dep_index: 37
  library_dep_index: 35
  library_dep_index: 52
  library_dep_index: 3
  library_dep_index: 53
}
library_dependencies {
  library_index: 51
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 23
  library_dep_index: 29
}
library_dependencies {
  library_index: 52
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 47
}
library_dependencies {
  library_index: 53
  library_dep_index: 41
  library_dep_index: 54
  library_dep_index: 8
  library_dep_index: 50
  library_dep_index: 25
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 3
  library_dep_index: 50
}
library_dependencies {
  library_index: 54
  library_dep_index: 3
  library_dep_index: 11
}
library_dependencies {
  library_index: 55
  library_dep_index: 1
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
  library_dep_index: 58
  library_dep_index: 40
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 59
  library_dep_index: 60
  library_dep_index: 61
  library_dep_index: 9
  library_dep_index: 46
  library_dep_index: 63
  library_dep_index: 10
  library_dep_index: 50
  library_dep_index: 15
  library_dep_index: 68
  library_dep_index: 55
  library_dep_index: 69
  library_dep_index: 43
  library_dep_index: 70
}
library_dependencies {
  library_index: 57
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 7
  library_dep_index: 6
}
library_dependencies {
  library_index: 59
  library_dep_index: 1
}
library_dependencies {
  library_index: 60
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 47
  library_dep_index: 11
}
library_dependencies {
  library_index: 61
  library_dep_index: 39
  library_dep_index: 9
  library_dep_index: 62
}
library_dependencies {
  library_index: 63
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 64
}
library_dependencies {
  library_index: 64
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 65
  library_dep_index: 51
  library_dep_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 65
  library_dep_index: 1
}
library_dependencies {
  library_index: 66
  library_dep_index: 1
}
library_dependencies {
  library_index: 67
  library_dep_index: 1
}
library_dependencies {
  library_index: 68
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 47
  library_dep_index: 11
}
library_dependencies {
  library_index: 69
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 11
}
library_dependencies {
  library_index: 70
  library_dep_index: 1
  library_dep_index: 50
  library_dep_index: 68
  library_dep_index: 9
  library_dep_index: 11
}
library_dependencies {
  library_index: 71
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 78
  library_dep_index: 77
  library_dep_index: 73
  library_dep_index: 75
  library_dep_index: 74
}
library_dependencies {
  library_index: 72
  library_dep_index: 53
  library_dep_index: 73
  library_dep_index: 79
  library_dep_index: 3
  library_dep_index: 71
  library_dep_index: 73
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 75
  library_dep_index: 74
}
library_dependencies {
  library_index: 73
  library_dep_index: 41
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 33
  library_dep_index: 74
  library_dep_index: 3
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 72
  library_dep_index: 71
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 74
  library_dep_index: 1
  library_dep_index: 54
  library_dep_index: 8
  library_dep_index: 18
  library_dep_index: 27
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 37
  library_dep_index: 36
  library_dep_index: 3
  library_dep_index: 75
  library_dep_index: 72
  library_dep_index: 71
  library_dep_index: 73
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 75
  library_dep_index: 74
  library_dep_index: 74
  library_dep_index: 72
  library_dep_index: 71
  library_dep_index: 73
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 76
  library_dep_index: 75
  library_dep_index: 73
  library_dep_index: 75
  library_dep_index: 72
  library_dep_index: 71
  library_dep_index: 73
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 74
}
library_dependencies {
  library_index: 77
  library_dep_index: 10
  library_dep_index: 47
  library_dep_index: 46
  library_dep_index: 73
  library_dep_index: 69
  library_dep_index: 56
  library_dep_index: 75
  library_dep_index: 72
  library_dep_index: 71
  library_dep_index: 73
  library_dep_index: 76
  library_dep_index: 78
  library_dep_index: 74
}
library_dependencies {
  library_index: 78
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 72
  library_dep_index: 71
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 73
  library_dep_index: 75
  library_dep_index: 74
}
library_dependencies {
  library_index: 79
  library_dep_index: 1
  library_dep_index: 47
  library_dep_index: 9
  library_dep_index: 80
  library_dep_index: 69
}
library_dependencies {
  library_index: 80
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 9
}
library_dependencies {
  library_index: 81
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 14
}
library_dependencies {
  library_index: 82
  library_dep_index: 83
  library_dep_index: 3
  library_dep_index: 19
}
library_dependencies {
  library_index: 83
  library_dep_index: 10
  library_dep_index: 9
  library_dep_index: 9
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 84
  library_dep_index: 87
  library_dep_index: 31
  library_dep_index: 13
  library_dep_index: 3
}
library_dependencies {
  library_index: 84
  library_dep_index: 10
  library_dep_index: 17
  library_dep_index: 85
  library_dep_index: 86
  library_dep_index: 87
}
library_dependencies {
  library_index: 85
  library_dep_index: 1
  library_dep_index: 5
}
library_dependencies {
  library_index: 86
  library_dep_index: 1
  library_dep_index: 3
}
library_dependencies {
  library_index: 87
  library_dep_index: 1
  library_dep_index: 86
  library_dep_index: 3
}
library_dependencies {
  library_index: 88
  library_dep_index: 89
}
library_dependencies {
  library_index: 89
  library_dep_index: 90
  library_dep_index: 5
}
library_dependencies {
  library_index: 90
  library_dep_index: 91
}
library_dependencies {
  library_index: 91
  library_dep_index: 5
  library_dep_index: 7
}
library_dependencies {
  library_index: 92
  library_dep_index: 88
  library_dep_index: 93
}
library_dependencies {
  library_index: 94
  library_dep_index: 89
  library_dep_index: 5
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
}
library_dependencies {
  library_index: 97
  library_dep_index: 98
  library_dep_index: 8
  library_dep_index: 53
  library_dep_index: 3
}
library_dependencies {
  library_index: 98
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 9
  library_dep_index: 41
  library_dep_index: 53
  library_dep_index: 68
  library_dep_index: 79
  library_dep_index: 11
}
library_dependencies {
  library_index: 99
  library_dep_index: 96
}
library_dependencies {
  library_index: 100
  library_dep_index: 3
  library_dep_index: 101
}
library_dependencies {
  library_index: 101
  library_dep_index: 3
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 3
  dependency_index: 8
  dependency_index: 39
  dependency_index: 56
  dependency_index: 61
  dependency_index: 27
  dependency_index: 33
  dependency_index: 26
  dependency_index: 28
  dependency_index: 53
  dependency_index: 71
  dependency_index: 78
  dependency_index: 81
  dependency_index: 82
  dependency_index: 88
  dependency_index: 92
  dependency_index: 89
  dependency_index: 94
  dependency_index: 95
  dependency_index: 19
  dependency_index: 97
  dependency_index: 93
  dependency_index: 99
  dependency_index: 100
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
