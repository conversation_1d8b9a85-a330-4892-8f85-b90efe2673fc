#!/usr/bin/env python3
"""
Test WebSocket connection to verify it's actually the Clipsy service
"""

import asyncio
import websockets
import json

async def test_clipsy_websocket(host, port):
    """Test WebSocket connection to verify Clipsy service."""
    try:
        uri = f"ws://{host}:{port}"
        print(f"Connecting to {uri}...")
        
        async with websockets.connect(uri, timeout=5) as websocket:
            print("✅ WebSocket connection established!")
            
            # Send a ping message to test Clipsy protocol
            ping_message = {
                "type": "ping",
                "timestamp": "2025-07-04T13:20:00Z"
            }
            
            await websocket.send(json.dumps(ping_message))
            print("📤 Sent ping message")
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                response_data = json.loads(response)
                print(f"📥 Received response: {response_data}")
                
                if response_data.get("type") == "pong":
                    print("🎉 CONFIRMED: This is a Clipsy service!")
                    return True
                else:
                    print("❌ Not a Clipsy service (unexpected response)")
                    return False
                    
            except asyncio.TimeoutError:
                print("⏰ No response received (might not be Clipsy)")
                return False
                
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

async def test_all_discovered_devices():
    """Test all discovered devices to identify which ones are Clipsy."""
    print("Testing All Discovered Devices")
    print("==============================")
    
    # Test the known devices
    devices = [
        ("************", 8766),
        ("*************", 8766),  # Expected Windows PC
        ("*************", 8766)
    ]
    
    clipsy_devices = []
    
    for host, port in devices:
        print(f"\nTesting {host}:{port}")
        print("-" * 30)
        
        is_clipsy = await test_clipsy_websocket(host, port)
        if is_clipsy:
            clipsy_devices.append(f"{host}:{port}")
    
    print(f"\n🎯 SUMMARY")
    print("=" * 40)
    print(f"Total devices tested: {len(devices)}")
    print(f"Clipsy devices found: {len(clipsy_devices)}")
    
    if clipsy_devices:
        print("Confirmed Clipsy devices:")
        for device in clipsy_devices:
            print(f"  ✅ {device}")
        
        if "*************:8766" in clipsy_devices:
            print("\n🚀 SUCCESS: Windows PC Clipsy service confirmed!")
            print("   Android devices should now be able to connect.")
        else:
            print("\n⚠️  Windows PC not confirmed as Clipsy service")
    else:
        print("❌ No Clipsy devices found")

if __name__ == "__main__":
    asyncio.run(test_all_discovered_devices())
