#!/usr/bin/env python3
"""
Test SyncServer first, then add <PERSON><PERSON><PERSON>iscovery to see when it breaks
"""

import asyncio
import threading
import logging
import time
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from network.sync_server import SyncServer
from network.discovery import DeviceDiscovery
from pairing.pairing_manager import SimpleConnectionManager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SyncServerThenDiscoveryTest:
    def __init__(self):
        self.loop = None
        self.loop_thread = None
        self.sync_server = None
        self.device_discovery = None
        self.pairing_manager = None
        self.initialization_ready = threading.Event()
        self.config = {
            "device_name": "Test-PC-Sequential",
            "websocket_port": 8779,
            "discovery_port": 8780
        }
    
    def start_async_loop(self):
        """Start the asyncio event loop in a separate thread."""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        logger.info(f"🔄 Event loop started in thread: {threading.current_thread().name}")
        self.loop.run_forever()
    
    async def start_syncserver_only(self):
        """Start only SyncServer first"""
        logger.info(f"🚀 Starting SyncServer only...")
        
        try:
            # Create SimpleConnectionManager
            logger.info("📋 Creating SimpleConnectionManager...")
            self.pairing_manager = SimpleConnectionManager()
            
            # Create SyncServer
            logger.info("📋 Creating SyncServer...")
            self.sync_server = SyncServer(self.config, self.pairing_manager)
            
            # Start SyncServer
            logger.info("📋 Starting SyncServer...")
            await self.sync_server.start()
            
            logger.info(f"✅ SyncServer started: {self.sync_server.host}:{self.sync_server.port}")
            
            # Signal that initialization is complete
            self.initialization_ready.set()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start SyncServer: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.initialization_ready.set()
            return False
    
    async def add_discovery(self):
        """Add DeviceDiscovery after SyncServer is working"""
        logger.info(f"🚀 Adding DeviceDiscovery...")
        
        try:
            # Create DeviceDiscovery
            logger.info("📋 Creating DeviceDiscovery...")
            self.device_discovery = DeviceDiscovery(self.config, self.pairing_manager)
            
            # Update discovery with server port
            logger.info("📋 Updating discovery with server port...")
            self.device_discovery.update_server_port(self.sync_server.port)
            
            # Start DeviceDiscovery
            logger.info("📋 Starting DeviceDiscovery...")
            await self.device_discovery.start()
            
            logger.info(f"✅ DeviceDiscovery started")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start DeviceDiscovery: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def run_test(self):
        """Run the sequential test"""
        logger.info("🚀 Starting sequential test...")
        
        try:
            # Start async event loop in background thread
            self.loop_thread = threading.Thread(target=self.start_async_loop, daemon=True)
            self.loop_thread.start()
            
            # Wait for loop to be ready
            time.sleep(0.5)
            
            # Start SyncServer only
            if self.loop:
                logger.info("🔄 Step 1: Starting SyncServer only...")
                future = asyncio.run_coroutine_threadsafe(
                    self.start_syncserver_only(), self.loop
                )
                
                # Wait for SyncServer initialization
                logger.info("⏳ Waiting for SyncServer initialization...")
                if self.initialization_ready.wait(timeout=15):
                    logger.info("✅ SyncServer initialization completed")
                    
                    # Test SyncServer alone
                    logger.info("🔄 SyncServer is running alone - test connections now!")
                    logger.info(f"Test with: python test_minimal_websocket_client.py (port {self.config['websocket_port']})")
                    logger.info("Press Enter to add DeviceDiscovery...")
                    input()
                    
                    # Add DeviceDiscovery
                    logger.info("🔄 Step 2: Adding DeviceDiscovery...")
                    future2 = asyncio.run_coroutine_threadsafe(
                        self.add_discovery(), self.loop
                    )
                    
                    # Wait for DeviceDiscovery
                    try:
                        result = future2.result(timeout=15)
                        if result:
                            logger.info("✅ DeviceDiscovery added successfully")
                            logger.info("🔄 Both components running - test connections again!")
                            logger.info(f"Test with: python test_minimal_websocket_client.py (port {self.config['websocket_port']})")
                            logger.info("Press Ctrl+C to stop")
                            
                            try:
                                while True:
                                    time.sleep(1)
                            except KeyboardInterrupt:
                                logger.info("Stopping components...")
                        else:
                            logger.error("❌ Failed to add DeviceDiscovery")
                            return False
                    except Exception as e:
                        logger.error(f"❌ DeviceDiscovery addition failed: {e}")
                        return False
                        
                else:
                    logger.error("❌ SyncServer initialization timeout")
                    return False
            else:
                logger.error("❌ Failed to start event loop")
                return False
                
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            return False
        finally:
            # Cleanup
            if self.sync_server:
                try:
                    asyncio.run_coroutine_threadsafe(
                        self.sync_server.stop(), self.loop
                    )
                except:
                    pass
            
            if self.device_discovery:
                try:
                    asyncio.run_coroutine_threadsafe(
                        self.device_discovery.stop(), self.loop
                    )
                except:
                    pass
            
            if self.loop and not self.loop.is_closed():
                self.loop.call_soon_threadsafe(self.loop.stop)
            if self.loop_thread and self.loop_thread.is_alive():
                self.loop_thread.join(timeout=5)
        
        return True

if __name__ == "__main__":
    test = SyncServerThenDiscoveryTest()
    success = test.run_test()
    sys.exit(0 if success else 1)
