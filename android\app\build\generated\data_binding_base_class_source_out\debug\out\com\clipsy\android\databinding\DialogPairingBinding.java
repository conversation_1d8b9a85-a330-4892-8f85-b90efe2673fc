// Generated by view binder compiler. Do not edit!
package com.clipsy.android.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.clipsy.android.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogPairingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCancelPairing;

  @NonNull
  public final TextView tvPairingCode;

  @NonNull
  public final TextView tvPairingInstruction;

  @NonNull
  public final TextView tvPairingTitle;

  private DialogPairingBinding(@NonNull LinearLayout rootView, @NonNull Button btnCancelPairing,
      @NonNull TextView tvPairingCode, @NonNull TextView tvPairingInstruction,
      @NonNull TextView tvPairingTitle) {
    this.rootView = rootView;
    this.btnCancelPairing = btnCancelPairing;
    this.tvPairingCode = tvPairingCode;
    this.tvPairingInstruction = tvPairingInstruction;
    this.tvPairingTitle = tvPairingTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogPairingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogPairingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_pairing, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogPairingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel_pairing;
      Button btnCancelPairing = ViewBindings.findChildViewById(rootView, id);
      if (btnCancelPairing == null) {
        break missingId;
      }

      id = R.id.tv_pairing_code;
      TextView tvPairingCode = ViewBindings.findChildViewById(rootView, id);
      if (tvPairingCode == null) {
        break missingId;
      }

      id = R.id.tv_pairing_instruction;
      TextView tvPairingInstruction = ViewBindings.findChildViewById(rootView, id);
      if (tvPairingInstruction == null) {
        break missingId;
      }

      id = R.id.tv_pairing_title;
      TextView tvPairingTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvPairingTitle == null) {
        break missingId;
      }

      return new DialogPairingBinding((LinearLayout) rootView, btnCancelPairing, tvPairingCode,
          tvPairingInstruction, tvPairingTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
