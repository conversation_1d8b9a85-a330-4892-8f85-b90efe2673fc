{"logs": [{"outputFile": "com.clipsy.android.app-mergeReleaseResources-55:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c7cbaeefdc37cd24560b8fc7fa3163c1\\transformed\\appcompat-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,9586", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,9661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0d22882521834a86e542cf7cc54c4b22\\transformed\\core-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3442,3544,3641,3740,3848,3954,9666", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3437,3539,3636,3735,3843,3949,4069,9762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d1724a1e2b468b75777ff8300e82e0e6\\transformed\\material-1.10.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1030,1122,1190,1250,1337,1401,1463,1527,1595,1660,1714,1823,1881,1943,1997,2072,2192,2274,2354,2488,2566,2646,2769,2857,2935,2989,3040,3106,3174,3248,3338,3414,3485,3563,3633,3703,3803,3892,3970,4058,4148,4220,4292,4376,4427,4505,4571,4652,4735,4797,4861,4924,4993,5093,5197,5290,5390,5448,5503", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,122,87,77,53,50,65,67,73,89,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77", "endOffsets": "256,333,406,493,581,661,760,879,961,1025,1117,1185,1245,1332,1396,1458,1522,1590,1655,1709,1818,1876,1938,1992,2067,2187,2269,2349,2483,2561,2641,2764,2852,2930,2984,3035,3101,3169,3243,3333,3409,3480,3558,3628,3698,3798,3887,3965,4053,4143,4215,4287,4371,4422,4500,4566,4647,4730,4792,4856,4919,4988,5088,5192,5285,5385,5443,5498,5576"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3268,4074,4173,4292,4444,4595,4687,4755,4815,4902,4966,5028,5092,5160,5225,5279,5388,5446,5508,5562,5637,5757,5839,5919,6053,6131,6211,6334,6422,6500,6554,6605,6671,6739,6813,6903,6979,7050,7128,7198,7268,7368,7457,7535,7623,7713,7785,7857,7941,7992,8070,8136,8217,8300,8362,8426,8489,8558,8658,8762,8855,8955,9013,9366", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,122,87,77,53,50,65,67,73,89,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77", "endOffsets": "306,3015,3088,3175,3263,3343,4168,4287,4369,4503,4682,4750,4810,4897,4961,5023,5087,5155,5220,5274,5383,5441,5503,5557,5632,5752,5834,5914,6048,6126,6206,6329,6417,6495,6549,6600,6666,6734,6808,6898,6974,7045,7123,7193,7263,7363,7452,7530,7618,7708,7780,7852,7936,7987,8065,8131,8212,8295,8357,8421,8484,8553,8653,8757,8850,8950,9008,9063,9439"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\983aed2efb51dcef9549ad462abafc61\\transformed\\preference-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,482,651,730", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "170,257,335,477,646,725,801"}, "to": {"startLines": "48,50,111,113,116,117,118", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4374,4508,9288,9444,9767,9936,10015", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "4439,4590,9361,9581,9931,10010,10086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bed6289f91d7b29bf75bbeb68ea27ae0\\transformed\\navigation-ui-2.7.5\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,113", "endOffsets": "156,270"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "9068,9174", "endColumns": "105,113", "endOffsets": "9169,9283"}}]}]}