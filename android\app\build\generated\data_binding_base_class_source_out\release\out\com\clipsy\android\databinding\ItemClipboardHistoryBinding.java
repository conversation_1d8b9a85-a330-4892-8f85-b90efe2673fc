// Generated by view binder compiler. Do not edit!
package com.clipsy.android.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.clipsy.android.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemClipboardHistoryBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton buttonDelete;

  @NonNull
  public final ImageView iconContentType;

  @NonNull
  public final TextView textContent;

  @NonNull
  public final TextView textDeviceInfo;

  @NonNull
  public final TextView textSource;

  @NonNull
  public final TextView textTimestamp;

  private ItemClipboardHistoryBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton buttonDelete, @NonNull ImageView iconContentType,
      @NonNull TextView textContent, @NonNull TextView textDeviceInfo, @NonNull TextView textSource,
      @NonNull TextView textTimestamp) {
    this.rootView = rootView;
    this.buttonDelete = buttonDelete;
    this.iconContentType = iconContentType;
    this.textContent = textContent;
    this.textDeviceInfo = textDeviceInfo;
    this.textSource = textSource;
    this.textTimestamp = textTimestamp;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemClipboardHistoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemClipboardHistoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_clipboard_history, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemClipboardHistoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_delete;
      ImageButton buttonDelete = ViewBindings.findChildViewById(rootView, id);
      if (buttonDelete == null) {
        break missingId;
      }

      id = R.id.icon_content_type;
      ImageView iconContentType = ViewBindings.findChildViewById(rootView, id);
      if (iconContentType == null) {
        break missingId;
      }

      id = R.id.text_content;
      TextView textContent = ViewBindings.findChildViewById(rootView, id);
      if (textContent == null) {
        break missingId;
      }

      id = R.id.text_device_info;
      TextView textDeviceInfo = ViewBindings.findChildViewById(rootView, id);
      if (textDeviceInfo == null) {
        break missingId;
      }

      id = R.id.text_source;
      TextView textSource = ViewBindings.findChildViewById(rootView, id);
      if (textSource == null) {
        break missingId;
      }

      id = R.id.text_timestamp;
      TextView textTimestamp = ViewBindings.findChildViewById(rootView, id);
      if (textTimestamp == null) {
        break missingId;
      }

      return new ItemClipboardHistoryBinding((MaterialCardView) rootView, buttonDelete,
          iconContentType, textContent, textDeviceInfo, textSource, textTimestamp);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
