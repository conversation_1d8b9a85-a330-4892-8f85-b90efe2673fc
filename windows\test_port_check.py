#!/usr/bin/env python3
"""
Port Check Test Script
Tests if the PC WebSocket server port is actually open and listening
"""

import socket
import logging
import sys

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_port_open(host, port):
    """Test if a port is open and listening"""
    logger.info(f"Testing if {host}:{port} is open...")
    
    try:
        # Create a socket and try to connect
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5 second timeout
        
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            logger.info(f"✅ Port {host}:{port} is OPEN and accepting connections")
            return True
        else:
            logger.error(f"❌ Port {host}:{port} is CLOSED or not accepting connections (error code: {result})")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing port {host}:{port}: {e}")
        return False

def test_all_interfaces():
    """Test the WebSocket port on different interfaces"""
    port = 8766
    
    logger.info("🔍 Testing WebSocket server port accessibility...")
    logger.info("="*60)
    
    # Test different interfaces
    interfaces = [
        ("127.0.0.1", "localhost"),
        ("*************", "PC LAN IP"),
        ("0.0.0.0", "all interfaces")
    ]
    
    results = []
    for ip, description in interfaces:
        logger.info(f"\nTesting {description} ({ip}):")
        if ip == "0.0.0.0":
            # Can't connect to 0.0.0.0, skip this test
            logger.info("⚠️ Skipping 0.0.0.0 (can't connect to this address)")
            continue
            
        is_open = test_port_open(ip, port)
        results.append((ip, description, is_open))
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("PORT TEST SUMMARY")
    logger.info("="*60)
    
    any_open = False
    for ip, description, is_open in results:
        status = "✅ OPEN" if is_open else "❌ CLOSED"
        logger.info(f"{description} ({ip}): {status}")
        if is_open:
            any_open = True
    
    if any_open:
        logger.info("🎉 At least one interface is accepting connections!")
    else:
        logger.error("💥 No interfaces are accepting connections!")
        logger.error("This suggests the WebSocket server is not actually running or is blocked by firewall.")
    
    return any_open

if __name__ == "__main__":
    success = test_all_interfaces()
    sys.exit(0 if success else 1)
