#!/usr/bin/env python3
"""
Test network scanning on Wi-Fi network (192.168.1.x) to find Windows Clipsy app.
"""

import socket
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

def scan_device_at(ip_address, timeout=2):
    """Scan a specific IP address for Clipsy service on port 8766."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((ip_address, 8766))
        sock.close()
        
        if result == 0:
            return ip_address
        else:
            return None
    except Exception:
        return None

def scan_wifi_network():
    """Scan Wi-Fi network (192.168.1.x) for Clipsy devices."""
    print("Wi-Fi Network Scanning Test")
    print("===========================")
    print("Scanning network: 192.168.1.x")
    print("Looking for Clipsy service on port 8766...")
    print()
    
    found_devices = []
    
    # Use ThreadPoolExecutor for concurrent scanning
    with ThreadPool<PERSON>xecutor(max_workers=50) as executor:
        # Submit scan tasks for all IPs in the Wi-Fi network
        future_to_ip = {}
        for i in range(1, 255):
            target_ip = f"192.168.1.{i}"
            future = executor.submit(scan_device_at, target_ip, 2)
            future_to_ip[future] = target_ip
        
        # Collect results
        completed = 0
        for future in as_completed(future_to_ip):
            completed += 1
            ip = future_to_ip[future]
            result = future.result()
            
            if result:
                found_devices.append(result)
                print(f"Found Clipsy device at {result}")
            
            # Show progress every 50 scans
            if completed % 50 == 0:
                print(f"   Scanned {completed}/254 addresses...")
    
    print(f"\nScan completed! Checked {completed} addresses.")
    print()
    
    # Results
    if found_devices:
        print("Discovered Clipsy Devices:")
        for device_ip in found_devices:
            print(f"   - {device_ip}:8766")
        print()
        print("SUCCESS: Network scanning approach works!")
        print("Android app should be able to discover these devices.")
        
        # Test WebSocket connection
        print("\nTesting WebSocket connection...")
        for device_ip in found_devices:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect((device_ip, 8766))
                sock.close()
                print(f"   WebSocket connection to {device_ip}:8766 - OK")
            except Exception as e:
                print(f"   WebSocket connection to {device_ip}:8766 - FAILED: {e}")
        
    else:
        print("No Clipsy devices found on 192.168.1.x network")
        print("Make sure:")
        print("   1. Windows Clipsy app is running")
        print("   2. Both devices are on the same Wi-Fi network")
        print("   3. Windows Firewall allows port 8766")

if __name__ == "__main__":
    scan_wifi_network()
