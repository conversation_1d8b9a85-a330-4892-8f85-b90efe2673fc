('N:\\new project\\windows\\build\\ClipsyProtocolFixed\\PYZ-00.pyz',
 [('__future__', 'C:\\Users\\<USER>\\miniconda3\\Lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Users\\<USER>\\miniconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Users\\<USER>\\miniconda3\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Users\\<USER>\\miniconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'C:\\Users\\<USER>\\miniconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Users\\<USER>\\miniconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log', 'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'C:\\Users\\<USER>\\miniconda3\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Users\\<USER>\\miniconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Users\\<USER>\\miniconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Users\\<USER>\\miniconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('clipboard',
   'N:\\new project\\windows\\src\\clipboard\\__init__.py',
   'PYMODULE'),
  ('clipboard.monitor',
   'N:\\new project\\windows\\src\\clipboard\\monitor.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Users\\<USER>\\miniconda3\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\Users\\<USER>\\miniconda3\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Users\\<USER>\\miniconda3\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses', 'C:\\Users\\<USER>\\miniconda3\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Users\\<USER>\\miniconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Users\\<USER>\\miniconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'C:\\Users\\<USER>\\miniconda3\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Users\\<USER>\\miniconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Users\\<USER>\\miniconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Users\\<USER>\\miniconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Users\\<USER>\\miniconda3\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Users\\<USER>\\miniconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'C:\\Users\\<USER>\\miniconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Users\\<USER>\\miniconda3\\Lib\\hmac.py', 'PYMODULE'),
  ('http', 'C:\\Users\\<USER>\\miniconda3\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\Users\\<USER>\\miniconda3\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Users\\<USER>\\miniconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'C:\\Users\\<USER>\\miniconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Users\\<USER>\\miniconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Users\\<USER>\\miniconda3\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Users\\<USER>\\miniconda3\\Lib\\netrc.py', 'PYMODULE'),
  ('network',
   'N:\\new project\\windows\\src\\network\\__init__.py',
   'PYMODULE'),
  ('network.discovery',
   'N:\\new project\\windows\\src\\network\\discovery.py',
   'PYMODULE'),
  ('network.sync_client',
   'N:\\new project\\windows\\src\\network\\sync_client.py',
   'PYMODULE'),
  ('network.sync_server',
   'N:\\new project\\windows\\src\\network\\sync_server.py',
   'PYMODULE'),
  ('nturl2path', 'C:\\Users\\<USER>\\miniconda3\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Users\\<USER>\\miniconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Users\\<USER>\\miniconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Users\\<USER>\\miniconda3\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Users\\<USER>\\miniconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('pyperclip',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('queue', 'C:\\Users\\<USER>\\miniconda3\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Users\\<USER>\\miniconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Users\\<USER>\\miniconda3\\Lib\\random.py', 'PYMODULE'),
  ('runpy', 'C:\\Users\\<USER>\\miniconda3\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Users\\<USER>\\miniconda3\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Users\\<USER>\\miniconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'C:\\Users\\<USER>\\miniconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Users\\<USER>\\miniconda3\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\Users\\<USER>\\miniconda3\\Lib\\socket.py', 'PYMODULE'),
  ('ssl', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\Users\\<USER>\\miniconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Users\\<USER>\\miniconda3\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Users\\<USER>\\miniconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Users\\<USER>\\miniconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Users\\<USER>\\miniconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Users\\<USER>\\miniconda3\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'C:\\Users\\<USER>\\miniconda3\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'C:\\Users\\<USER>\\miniconda3\\Lib\\typing.py', 'PYMODULE'),
  ('ui', 'N:\\new project\\windows\\src\\ui\\__init__.py', 'PYMODULE'),
  ('ui.main_window',
   'N:\\new project\\windows\\src\\ui\\main_window.py',
   'PYMODULE'),
  ('urllib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid', 'C:\\Users\\<USER>\\miniconda3\\Lib\\uuid.py', 'PYMODULE'),
  ('websockets',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.__main__',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.auth',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.client',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.connection',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.extensions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.frames',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.headers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.http',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.http11',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.imports',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('websockets.legacy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.async_timeout',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\async_timeout.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.compatibility',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\compatibility.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.protocol',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.server',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.streams',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.sync',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.typing',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.uri',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.version',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('xml', 'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\Users\\<USER>\\miniconda3\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Users\\<USER>\\miniconda3\\Lib\\zipimport.py', 'PYMODULE')])
