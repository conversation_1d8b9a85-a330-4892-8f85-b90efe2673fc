<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/clipsy_background">

    <!-- Send to PC Input Box -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="12dp"
        app:cardBackgroundColor="@color/clipsy_card_dark"
        app:cardCornerRadius="20dp"
        app:cardElevation="4dp"
        app:strokeWidth="1dp"
        app:strokeColor="@color/clipsy_accent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <!-- Keyboard Icon -->
            <TextView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:text="⌨️"
                android:textSize="18sp"
                android:gravity="center"
                android:alpha="0.8" />

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text_message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="Send to PC..."
                android:textColor="@color/clipsy_text_primary"
                android:textColorHint="@color/clipsy_text_secondary"
                android:background="@android:color/transparent"
                android:maxLines="3"
                android:textSize="16sp"
                android:inputType="textMultiLine|textCapSentences" />

            <!-- Send Button with Background -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginStart="12dp"
                app:cardBackgroundColor="@color/clipsy_accent"
                app:cardCornerRadius="22dp"
                app:cardElevation="2dp">

                <ImageButton
                    android:id="@+id/button_send_message"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_send_arrow"
                    android:contentDescription="Send to PC"
                    android:tint="@android:color/white"
                    android:scaleType="center"
                    android:padding="8dp" />

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_history"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="8dp"
            android:clipToPadding="false"
            tools:listitem="@layout/item_clipboard_history" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/layout_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:layout_width="96dp"
                android:layout_height="96dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="16dp"
                android:alpha="0.5"
                android:src="@android:drawable/ic_menu_recent_history"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="8dp"
                android:text="@string/history_empty"
                android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                android:textColor="@color/clipsy_text_primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center"
                android:text="Your clipboard history will appear here\nwhen you copy text"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="@color/clipsy_text_secondary" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
