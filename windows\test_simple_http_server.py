#!/usr/bin/env python3
"""
Simple HTTP server test to check if port 8766 is accessible from Android device.
"""

import socket
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler

class TestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        
        response = f"""
        <html>
        <body>
        <h1>Test Server Running</h1>
        <p>Server is accessible on port 8766</p>
        <p>Client IP: {self.client_address[0]}</p>
        <p>Time: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
        </body>
        </html>
        """
        self.wfile.write(response.encode())
        print(f"HTTP request from {self.client_address[0]}")

    def log_message(self, format, *args):
        # Override to reduce log noise
        pass

def get_local_ip():
    """Get local IP address."""
    try:
        # Connect to a remote address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except:
        return "127.0.0.1"

def main():
    host = get_local_ip()
    port = 8766

    print(f"Starting test HTTP server on {host}:{port}")
    print(f"Test from Android: http://{host}:{port}")
    print("Press Ctrl+C to stop")

    try:
        server = HTTPServer((host, port), TestHandler)
        print(f"Server listening on {host}:{port}")
        server.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
