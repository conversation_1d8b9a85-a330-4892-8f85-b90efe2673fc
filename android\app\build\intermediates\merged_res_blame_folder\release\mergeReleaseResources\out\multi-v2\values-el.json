{"logs": [{"outputFile": "com.clipsy.android.app-mergeReleaseResources-55:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d1724a1e2b468b75777ff8300e82e0e6\\transformed\\material-1.10.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,358,444,529,625,712,814,931,1017,1083,1183,1265,1328,1419,1482,1547,1609,1678,1740,1794,1932,1989,2050,2104,2177,2330,2415,2499,2638,2719,2804,2945,3035,3121,3176,3227,3293,3371,3456,3541,3624,3696,3776,3856,3927,4034,4126,4198,4295,4392,4466,4540,4642,4698,4785,4857,4945,5037,5099,5163,5226,5296,5412,5521,5630,5735,5794,5849", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,87,85,84,95,86,101,116,85,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,83,138,80,84,140,89,85,54,50,65,77,84,84,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90", "endOffsets": "265,353,439,524,620,707,809,926,1012,1078,1178,1260,1323,1414,1477,1542,1604,1673,1735,1789,1927,1984,2045,2099,2172,2325,2410,2494,2633,2714,2799,2940,3030,3116,3171,3222,3288,3366,3451,3536,3619,3691,3771,3851,3922,4029,4121,4193,4290,4387,4461,4535,4637,4693,4780,4852,4940,5032,5094,5158,5221,5291,5407,5516,5625,5730,5789,5844,5935"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3101,3189,3275,3360,3456,4278,4380,4497,4656,4809,4909,4991,5054,5145,5208,5273,5335,5404,5466,5520,5658,5715,5776,5830,5903,6056,6141,6225,6364,6445,6530,6671,6761,6847,6902,6953,7019,7097,7182,7267,7350,7422,7502,7582,7653,7760,7852,7924,8021,8118,8192,8266,8368,8424,8511,8583,8671,8763,8825,8889,8952,9022,9138,9247,9356,9461,9520,9897", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "endColumns": "12,87,85,84,95,86,101,116,85,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,83,138,80,84,140,89,85,54,50,65,77,84,84,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90", "endOffsets": "315,3184,3270,3355,3451,3538,4375,4492,4578,4717,4904,4986,5049,5140,5203,5268,5330,5399,5461,5515,5653,5710,5771,5825,5898,6051,6136,6220,6359,6440,6525,6666,6756,6842,6897,6948,7014,7092,7177,7262,7345,7417,7497,7577,7648,7755,7847,7919,8016,8113,8187,8261,8363,8419,8506,8578,8666,8758,8820,8884,8947,9017,9133,9242,9351,9456,9515,9570,9983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bed6289f91d7b29bf75bbeb68ea27ae0\\transformed\\navigation-ui-2.7.5\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,172", "endColumns": "116,121", "endOffsets": "167,289"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "9575,9692", "endColumns": "116,121", "endOffsets": "9687,9809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0d22882521834a86e542cf7cc54c4b22\\transformed\\core-1.12.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3543,3641,3744,3844,3947,4055,4161,10216", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3636,3739,3839,3942,4050,4156,4273,10312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\983aed2efb51dcef9549ad462abafc61\\transformed\\preference-1.2.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,265,348,490,659,744", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "173,260,343,485,654,739,819"}, "to": {"startLines": "48,50,111,113,116,117,118", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4583,4722,9814,9988,10317,10486,10571", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "4651,4804,9892,10125,10481,10566,10646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c7cbaeefdc37cd24560b8fc7fa3163c1\\transformed\\appcompat-1.6.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,438,549,666,751,857,980,1069,1154,1245,1338,1433,1527,1627,1720,1815,1912,2003,2094,2179,2290,2399,2501,2612,2722,2830,3001,10130", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "433,544,661,746,852,975,1064,1149,1240,1333,1428,1522,1622,1715,1810,1907,1998,2089,2174,2285,2394,2496,2607,2717,2825,2996,3096,10211"}}]}]}