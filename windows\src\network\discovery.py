"""
Device discovery module using UDP broadcast.
Handles automatic discovery of other Clipsy devices on the local network.
"""

import json
import socket
import asyncio
import logging
import netifaces
import threading
import queue
import platform
from typing import Dict, List, Callable
from datetime import datetime, timedelta


class DeviceDiscovery:
    """Handles device discovery using UDP broadcast."""
    
    def __init__(self, config: dict, pairing_manager=None):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.pairing_manager = pairing_manager

        # Network configuration
        self.discovery_port = config.get("discovery_port", 8765)
        self.websocket_port = config.get("websocket_port", 8766)  # For client connections
        self.server_port = config.get("websocket_port", 8766)  # Use same port for consistency
        self.broadcast_interval = config.get("broadcast_interval", 2)  # Reduced from 5 to 2 seconds

        # Discovery state
        self.is_running = False
        self.socket = None
        self.broadcast_task = None
        self.listen_task = None

        # Device tracking
        self.discovered_devices = {}  # ip -> device_info
        self.device_callbacks = []

        # Local device info
        device_id = pairing_manager.device_id if pairing_manager else "unknown"
        self.local_device_info = {
            "device_name": config.get("device_name", "Windows-PC"),
            "device_type": "windows",
            "websocket_port": self.server_port,  # Advertise server port for others to connect to
            "device_id": device_id,  # Add device_id for pairing validation
            "timestamp": datetime.now().isoformat()
        }
    
    def add_device_callback(self, callback: Callable[[str, dict], None]):
        """Add callback for device discovery events."""
        self.device_callbacks.append(callback)

    def update_server_port(self, actual_port: int):
        """Update the advertised server port after server starts."""
        self.server_port = actual_port
        self.local_device_info["websocket_port"] = actual_port
        self.logger.info(f"Updated advertised server port to {actual_port}")
    
    def _notify_device_callbacks(self, event_type: str, device_info: dict):
        """Notify all device callbacks."""
        for callback in self.device_callbacks:
            try:
                callback(event_type, device_info)
            except Exception as e:
                self.logger.error(f"Error in device callback: {e}")
    
    async def start(self):
        """Start device discovery."""
        if self.is_running:
            self.logger.warning("Device discovery already running")
            return

        # Try alternative discovery ports if needed
        ports_to_try = [self.discovery_port, self.discovery_port + 1, self.discovery_port + 2]

        for port_attempt in ports_to_try:
            try:
                # Create UDP socket
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self.socket.bind(('', port_attempt))
                self.socket.setblocking(False)

                # Update port if we had to use an alternative
                if port_attempt != self.discovery_port:
                    self.logger.info(f"Default discovery port {self.discovery_port} was in use, using port {port_attempt}")
                    self.discovery_port = port_attempt

                self.is_running = True

                # Start broadcast and listen tasks
                self.logger.info("Creating broadcast task...")
                self.broadcast_task = asyncio.create_task(self._broadcast_loop())

                self.logger.info("Creating listen task...")
                self.listen_task = asyncio.create_task(self._listen_loop())

                # Start network scanning as fallback (after 3 seconds)
                self.logger.info("Creating scan task...")
                self.scan_task = asyncio.create_task(self._delayed_network_scan())

                self.logger.info(f"Device discovery started on port {self.discovery_port}")
                self.logger.info("Discovery start method returning...")
                return

            except OSError as e:
                if "10048" in str(e) or "Address already in use" in str(e):
                    if self.socket:
                        self.socket.close()
                        self.socket = None
                    continue
                else:
                    self.logger.error(f"Failed to start device discovery on port {port_attempt}: {e}")
                    await self.stop()
                    raise
            except Exception as e:
                self.logger.error(f"Failed to start device discovery on port {port_attempt}: {e}")
                await self.stop()
                raise

        # If we get here, all ports failed
        raise Exception(f"Could not start device discovery on any port from {ports_to_try[0]} to {ports_to_try[-1]}")
    
    async def stop(self):
        """Stop device discovery."""
        self.is_running = False
        
        # Cancel tasks
        if self.broadcast_task:
            self.broadcast_task.cancel()
        if self.listen_task:
            self.listen_task.cancel()
        if hasattr(self, 'scan_task') and self.scan_task:
            self.scan_task.cancel()
        
        # Close socket
        if self.socket:
            self.socket.close()
            self.socket = None
        
        self.logger.info("Device discovery stopped")

    async def _delayed_network_scan(self):
        """Start network scanning after a delay to allow broadcasts to work first."""
        try:
            # Wait 3 seconds for broadcasts to work (reduced from 10)
            await asyncio.sleep(3)

            if self.is_running:
                self.logger.info("Starting network scan as fallback discovery method")
                await self.scan_network_for_devices()

                # Repeat scan every 15 seconds (reduced from 30)
                while self.is_running:
                    await asyncio.sleep(15)
                    if self.is_running:
                        await self.scan_network_for_devices()

        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"Error in delayed network scan: {e}")

    async def _broadcast_loop(self):
        """Broadcast device presence periodically."""
        while self.is_running:
            try:
                await self._send_discovery_broadcast()
                await asyncio.sleep(self.broadcast_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in broadcast loop: {e}")
                await asyncio.sleep(1)
    
    async def _send_discovery_broadcast(self):
        """Send discovery broadcast message."""
        try:
            message = {
                "type": "discovery",
                **self.local_device_info,
                "timestamp": datetime.now().isoformat()
            }
            
            data = json.dumps(message).encode('utf-8')
            
            # Get broadcast addresses
            broadcast_addresses = self._get_broadcast_addresses()

            for broadcast_addr in broadcast_addresses:
                try:
                    self.socket.sendto(data, (broadcast_addr, self.discovery_port))
                    self.logger.debug(f"📡 Sent discovery broadcast to {broadcast_addr}:{self.discovery_port}")
                except Exception as e:
                    self.logger.debug(f"❌ Failed to send broadcast to {broadcast_addr}: {e}")
            
        except Exception as e:
            self.logger.error(f"Failed to send discovery broadcast: {e}")
    
    async def _listen_loop(self):
        """Listen for discovery messages from other devices."""
        import platform

        if platform.system() == "Windows":
            # Windows-compatible UDP listening using threading
            await self._listen_loop_windows()
        else:
            # Unix-compatible async UDP listening
            await self._listen_loop_unix()

    async def _listen_loop_windows(self):
        """Windows-compatible UDP listening using threading."""
        self.logger.info("🔌 Starting Windows UDP listener...")

        # Create a queue for communication between threads
        message_queue = queue.Queue()

        def listen_thread():
            """Thread function for UDP listening on Windows."""
            self.logger.info("🔌 UDP listen thread started")
            while self.is_running:
                try:
                    self.socket.settimeout(1.0)  # 1 second timeout
                    data, addr = self.socket.recvfrom(1024)
                    message_queue.put((data, addr))
                    self.logger.debug(f"🔌 Received UDP message from {addr}")
                except socket.timeout:
                    continue  # Check if still running
                except Exception as e:
                    if self.is_running:  # Only log if we're supposed to be running
                        self.logger.error(f"🔌 Windows UDP listen error: {e}")
                    break
            self.logger.info("🔌 UDP listen thread stopped")

        # Start the listening thread
        thread = threading.Thread(target=listen_thread, daemon=True)
        thread.start()
        self.logger.info("🔌 UDP listen thread launched")

        # Process messages from the queue
        while self.is_running:
            try:
                # Check for messages with timeout
                try:
                    data, addr = message_queue.get(timeout=1.0)
                    await self._handle_discovery_message(data, addr)
                except queue.Empty:
                    continue  # No message, check if still running

            except asyncio.CancelledError:
                self.logger.info("🔌 Windows UDP listener cancelled")
                break
            except Exception as e:
                self.logger.error(f"🔌 Error processing discovery message: {e}")
                await asyncio.sleep(0.1)

        self.logger.info("🔌 Windows UDP listener stopped")

    async def _listen_loop_unix(self):
        """Unix-compatible async UDP listening."""
        while self.is_running:
            try:
                # Use asyncio to make socket non-blocking
                loop = asyncio.get_event_loop()
                data, addr = await loop.sock_recvfrom(self.socket, 1024)

                await self._handle_discovery_message(data, addr)

            except asyncio.CancelledError:
                break
            except socket.error:
                # No data available, continue
                await asyncio.sleep(0.1)
            except Exception as e:
                self.logger.error(f"🔌 Error in Unix listen loop: {e}")
                await asyncio.sleep(1)
    
    async def _handle_discovery_message(self, data: bytes, addr: tuple):
        """Handle received discovery message."""
        try:
            message = json.loads(data.decode('utf-8'))

            if message.get("type") != "discovery":
                return

            ip_address = addr[0]

            # Ignore messages from ourselves and localhost
            if ip_address in self._get_local_ips() or ip_address == '127.0.0.1':
                return
            
            # Update device info
            device_info = {
                "ip": ip_address,
                "device_name": message.get("device_name", "Unknown"),
                "device_type": message.get("device_type", "unknown"),
                "websocket_port": message.get("websocket_port", 8766),
                "device_id": message.get("device_id", f"unknown_{ip_address}"),  # Include device_id
                "last_seen": datetime.now(),
                "timestamp": message.get("timestamp")
            }
            
            # Check if this is a new device or updated info
            is_new_device = ip_address not in self.discovered_devices
            
            self.discovered_devices[ip_address] = device_info
            
            if is_new_device:
                self.logger.info(f"✅ Discovered new device: {device_info['device_name']} ({device_info['device_type']}) at {ip_address}:{device_info['websocket_port']}")
                self._notify_device_callbacks("device_discovered", device_info)
            else:
                self.logger.debug(f"🔄 Updated device: {device_info['device_name']} at {ip_address}")
                self._notify_device_callbacks("device_updated", device_info)
            
        except json.JSONDecodeError:
            pass
        except Exception as e:
            self.logger.error(f"Error handling discovery message: {e}")
    
    def _get_broadcast_addresses(self) -> List[str]:
        """Get list of broadcast addresses for all network interfaces."""
        broadcast_addresses = []
        
        try:
            for interface in netifaces.interfaces():
                try:
                    addrs = netifaces.ifaddresses(interface)
                    if netifaces.AF_INET in addrs:
                        for addr_info in addrs[netifaces.AF_INET]:
                            broadcast = addr_info.get('broadcast')
                            if broadcast:
                                broadcast_addresses.append(broadcast)
                except Exception:
                    continue
        except Exception as e:
            self.logger.error(f"Error getting broadcast addresses: {e}")
        
        # Fallback to common broadcast address
        if not broadcast_addresses:
            broadcast_addresses = ['***************']
        
        return broadcast_addresses
    
    def _get_local_ips(self) -> List[str]:
        """Get list of local IP addresses, prioritizing Wi-Fi and regular networks."""
        wifi_ips = []
        regular_ips = []
        auto_config_ips = []

        try:
            for interface in netifaces.interfaces():
                try:
                    addrs = netifaces.ifaddresses(interface)
                    if netifaces.AF_INET in addrs:
                        for addr_info in addrs[netifaces.AF_INET]:
                            ip = addr_info.get('addr')
                            if ip and ip != '127.0.0.1':
                                # Categorize IPs by type
                                if ip.startswith('192.168.') or ip.startswith('10.') or ip.startswith('172.'):
                                    if 'wi-fi' in interface.lower() or 'wireless' in interface.lower():
                                        wifi_ips.append(ip)
                                    else:
                                        regular_ips.append(ip)
                                elif ip.startswith('169.254.'):
                                    auto_config_ips.append(ip)  # Auto-configuration (APIPA)
                                else:
                                    regular_ips.append(ip)
                except Exception:
                    continue
        except Exception as e:
            self.logger.error(f"Error getting local IPs: {e}")

        # Return prioritized list: Wi-Fi first, then regular, then auto-config
        return wifi_ips + regular_ips + auto_config_ips
    
    def get_discovered_devices(self) -> Dict[str, dict]:
        """Get all discovered devices."""
        # Clean up old devices (not seen for 30 seconds)
        cutoff_time = datetime.now() - timedelta(seconds=30)
        active_devices = {
            ip: info for ip, info in self.discovered_devices.items()
            if info["last_seen"] > cutoff_time
        }
        
        # Update internal list
        removed_devices = set(self.discovered_devices.keys()) - set(active_devices.keys())
        for ip in removed_devices:
            device_info = self.discovered_devices[ip]
            self.logger.info(f"Device timeout: {device_info['device_name']} at {ip}")
            self._notify_device_callbacks("device_lost", device_info)
        
        self.discovered_devices = active_devices
        return active_devices.copy()
    
    def get_local_ip(self):
        """Get the primary local IP address, preferring Wi-Fi networks."""
        local_ips = self._get_local_ips()
        if local_ips:
            selected_ip = local_ips[0]
            self.logger.info(f"Selected local IP: {selected_ip} (from {len(local_ips)} available: {local_ips})")
            return selected_ip
        else:
            self.logger.warning("No local IP addresses found")
            return None
    
    def add_manual_device(self, ip: str, device_name: str = None):
        """Manually add a device by IP address."""
        device_info = {
            "ip": ip,
            "device_name": device_name or f"Manual-{ip}",
            "device_type": "unknown",
            "websocket_port": self.websocket_port,
            "last_seen": datetime.now(),
            "manual": True
        }
        
        self.discovered_devices[ip] = device_info
        self.logger.info(f"Manually added device: {device_name} at {ip}")
        self._notify_device_callbacks("device_discovered", device_info)

    async def scan_network_for_devices(self):
        """Scan the local network for Clipsy devices when broadcasts fail."""
        try:
            local_ip = self.get_local_ip()
            if not local_ip:
                return

            # Get network range (e.g., ***********/24)
            ip_parts = local_ip.split('.')
            if len(ip_parts) != 4:
                return

            network_base = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}"
            self.logger.info(f"Scanning network {network_base}.0/24 for Clipsy devices...")

            # Scan common IP ranges (skip our own IP and localhost)
            scan_tasks = []
            for i in range(1, 255):
                target_ip = f"{network_base}.{i}"
                if target_ip != local_ip and target_ip != '127.0.0.1':
                    scan_tasks.append(self._scan_device_at(target_ip))

            # Run scans concurrently but limit to 20 at a time
            for i in range(0, len(scan_tasks), 20):
                batch = scan_tasks[i:i+20]
                await asyncio.gather(*batch, return_exceptions=True)
                await asyncio.sleep(0.1)  # Small delay between batches

            self.logger.info(f"🔍 Network scan completed. Found {len(self.discovered_devices)} total devices.")

        except Exception as e:
            self.logger.error(f"Error scanning network: {e}")

    async def _scan_device_at(self, ip: str):
        """Check if a specific IP is running a Clipsy device."""
        try:
            # Try to connect to the sync server port with WebSocket handshake
            import websockets

            # Scan on Android server port (8766) since we're looking for Android devices
            uri = f"ws://{ip}:{self.websocket_port}"
            websocket = await asyncio.wait_for(
                websockets.connect(uri, ping_interval=None),
                timeout=3.0
            )

            # First, receive welcome message from Android devices
            welcome = await asyncio.wait_for(websocket.recv(), timeout=2.0)
            welcome_data = json.loads(welcome)

            if welcome_data.get("type") == "welcome":
                # Send a ping message
                ping_message = {
                    "type": "ping",
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send(json.dumps(ping_message))

                # Wait for pong response
                pong = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                await websocket.close()

                # Parse pong response
                pong_data = json.loads(pong)

                if pong_data.get("type") == "pong":
                    # Found a Clipsy device, add it
                    device_info = {
                        "ip": ip,
                        "device_name": welcome_data.get("device_name", f"Clipsy-{ip}"),
                        "device_type": welcome_data.get("device_type", "unknown"),
                        "websocket_port": self.websocket_port,
                        "last_seen": datetime.now(),
                        "scanned": True
                    }

                    self.discovered_devices[ip] = device_info
                    self.logger.info(f"🎯 Found Clipsy device via scan: {device_info['device_name']} ({device_info['device_type']}) at {ip}:{self.websocket_port}")
                    self._notify_device_callbacks("device_discovered", device_info)
                else:
                    await websocket.close()
            else:
                await websocket.close()

        except (asyncio.TimeoutError, ConnectionRefusedError, OSError):
            # Normal - device not running Clipsy or not reachable
            pass
        except Exception as e:
            # Check if this is a pairing-related error (device found but not paired)
            if "4003" in str(e) and "Device not paired" in str(e):
                # Device found but not paired - still add it to discovered devices for pairing
                device_info = {
                    "ip": ip,
                    "device_name": f"Clipsy-{ip}",  # Default name since we can't get real name
                    "device_type": "android",  # Assume Android since it's rejecting due to pairing
                    "websocket_port": self.websocket_port,
                    "last_seen": datetime.now(),
                    "scanned": True,
                    "pairing_required": True  # Flag to indicate pairing is needed
                }

                self.discovered_devices[ip] = device_info
                self.logger.info(f"Found unpaired Clipsy device at {ip} (pairing required)")
                self._notify_device_callbacks("device_discovered", device_info)

