@echo off
REM Clipsy Windows Startup Configuration Script
REM This script configures Clipsy to automatically start with Windows
REM Run as Administrator for system-wide startup or as user for current user only

echo ========================================
echo    Clipsy Windows Startup Setup
echo ========================================
echo.

REM Define Clipsy executable paths
set "CLIPSY_EXE=%~dp0src\dist\Clipsy.exe"
set "CLIPSY_CLEANED_EXE=%~dp0src\dist\ClipsyCleanedCode.exe"
set "LAUNCHER_BAT=%~dp0launch-clipsy.bat"

REM Check which executable exists and prefer the cleaned version
if exist "%CLIPSY_CLEANED_EXE%" (
    set "CLIPSY_APP=%CLIPSY_CLEANED_EXE%"
    set "APP_NAME=ClipsyCleanedCode.exe"
    set "STARTUP_NAME=Clipsy PC (Cleaned)"
) else if exist "%CLIPSY_EXE%" (
    set "CLIPSY_APP=%CLIPSY_EXE%"
    set "APP_NAME=Clipsy.exe"
    set "STARTUP_NAME=Clipsy PC"
) else (
    echo [ERROR] Clipsy executable not found!
    echo.
    echo Please ensure one of these files exists:
    echo - %CLIPSY_CLEANED_EXE%
    echo - %CLIPSY_EXE%
    echo.
    pause
    exit /b 1
)

echo [INFO] Found Clipsy application: %APP_NAME%
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    set "IS_ADMIN=true"
    echo [INFO] Running with Administrator privileges
    echo [INFO] Can configure system-wide or user-specific startup
) else (
    set "IS_ADMIN=false"
    echo [INFO] Running as regular user
    echo [INFO] Will configure user-specific startup only
)
echo.

REM Choose startup method
echo Available startup configuration methods:
echo.
echo 1. Registry Startup (Current User) - Recommended
echo 2. Startup Folder (Current User) - Alternative method
if "%IS_ADMIN%"=="true" (
    echo 3. Registry Startup (All Users) - System-wide [Admin only]
    echo 4. Windows Service - Advanced [Admin only]
)
echo 5. Cancel
echo.

choice /C 12345 /M "Select startup method"
set "METHOD=%errorlevel%"

if "%METHOD%"=="5" (
    echo [INFO] Startup configuration cancelled.
    pause
    exit /b 0
)

if "%METHOD%"=="3" if not "%IS_ADMIN%"=="true" (
    echo [ERROR] Method 3 requires Administrator privileges.
    pause
    exit /b 1
)

if "%METHOD%"=="4" if not "%IS_ADMIN%"=="true" (
    echo [ERROR] Method 4 requires Administrator privileges.
    pause
    exit /b 1
)

echo.

REM Method 1: Registry Startup (Current User)
if "%METHOD%"=="1" (
    echo [STEP 1] Configuring Registry Startup for Current User...
    
    REM Remove existing entries first
    reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "Clipsy" /f >nul 2>&1
    reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" /f >nul 2>&1
    
    REM Add new registry entry
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" /t REG_SZ /d "\"%CLIPSY_APP%\"" /f
    
    if %errorLevel% == 0 (
        echo [SUCCESS] Clipsy added to Windows startup (Current User)
        echo [INFO] Registry key: HKCU\Software\Microsoft\Windows\CurrentVersion\Run
        echo [INFO] Application: %APP_NAME%
    ) else (
        echo [ERROR] Failed to add registry entry
    )
)

REM Method 2: Startup Folder (Current User)
if "%METHOD%"=="2" (
    echo [STEP 1] Configuring Startup Folder for Current User...
    
    REM Get startup folder path
    set "STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"
    
    REM Remove existing shortcuts
    del "%STARTUP_FOLDER%\Clipsy*.lnk" >nul 2>&1
    
    REM Create shortcut using PowerShell
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTUP_FOLDER%\%STARTUP_NAME%.lnk'); $Shortcut.TargetPath = '%CLIPSY_APP%'; $Shortcut.WorkingDirectory = '%~dp0'; $Shortcut.Description = 'Clipsy PC Clipboard Sync'; $Shortcut.Save()"
    
    if exist "%STARTUP_FOLDER%\%STARTUP_NAME%.lnk" (
        echo [SUCCESS] Clipsy shortcut added to Startup folder
        echo [INFO] Location: %STARTUP_FOLDER%
        echo [INFO] Shortcut: %STARTUP_NAME%.lnk
    ) else (
        echo [ERROR] Failed to create startup shortcut
    )
)

REM Method 3: Registry Startup (All Users) - Admin only
if "%METHOD%"=="3" (
    echo [STEP 1] Configuring Registry Startup for All Users...
    
    REM Remove existing entries first
    reg delete "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v "Clipsy" /f >nul 2>&1
    reg delete "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" /f >nul 2>&1
    
    REM Add new registry entry
    reg add "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" /d "\"%CLIPSY_APP%\"" /f
    
    if %errorLevel% == 0 (
        echo [SUCCESS] Clipsy added to Windows startup (All Users)
        echo [INFO] Registry key: HKLM\Software\Microsoft\Windows\CurrentVersion\Run
        echo [INFO] Application: %APP_NAME%
        echo [WARNING] This will start Clipsy for ALL users on this computer
    ) else (
        echo [ERROR] Failed to add registry entry
    )
)

REM Method 4: Windows Service - Admin only
if "%METHOD%"=="4" (
    echo [STEP 1] Configuring Windows Service...
    echo [WARNING] This is an advanced method that creates a Windows service
    echo.
    
    REM Check if service already exists
    sc query "ClipsyService" >nul 2>&1
    if %errorLevel% == 0 (
        echo [INFO] Removing existing Clipsy service...
        sc stop "ClipsyService" >nul 2>&1
        sc delete "ClipsyService" >nul 2>&1
        timeout /t 2 /nobreak >nul
    )
    
    REM Create Windows service
    sc create "ClipsyService" binPath= "\"%CLIPSY_APP%\"" start= auto DisplayName= "Clipsy PC Clipboard Sync Service"
    
    if %errorLevel% == 0 (
        echo [SUCCESS] Clipsy Windows service created
        echo [INFO] Service name: ClipsyService
        echo [INFO] Startup type: Automatic
        
        REM Start the service
        echo [INFO] Starting Clipsy service...
        sc start "ClipsyService"
        
        if %errorLevel% == 0 (
            echo [SUCCESS] Clipsy service started successfully
        ) else (
            echo [WARNING] Service created but failed to start
            echo [INFO] You can start it manually from Services.msc
        )
    ) else (
        echo [ERROR] Failed to create Windows service
    )
)

echo.

REM Verify startup configuration
echo [STEP 2] Verifying startup configuration...
echo.

REM Check registry entries
echo Checking registry entries:
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" >nul 2>&1
if %errorLevel% == 0 (
    echo [FOUND] Current User registry entry
    for /f "tokens=3*" %%a in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" 2^>nul') do echo [INFO] Path: %%a %%b
)

reg query "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" >nul 2>&1
if %errorLevel% == 0 (
    echo [FOUND] All Users registry entry
    for /f "tokens=3*" %%a in ('reg query "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" 2^>nul') do echo [INFO] Path: %%a %%b
)

REM Check startup folder
set "STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"
if exist "%STARTUP_FOLDER%\%STARTUP_NAME%.lnk" (
    echo [FOUND] Startup folder shortcut: %STARTUP_NAME%.lnk
)

REM Check Windows service
sc query "ClipsyService" >nul 2>&1
if %errorLevel% == 0 (
    echo [FOUND] Windows service: ClipsyService
    for /f "tokens=4" %%a in ('sc query "ClipsyService" ^| findstr "STATE"') do echo [INFO] Service state: %%a
)

echo.

REM Test startup (optional)
echo [OPTIONAL] Test startup configuration:
choice /C YN /M "Do you want to test the startup by launching Clipsy now?"
if errorlevel 1 if not errorlevel 2 (
    echo [INFO] Testing startup...
    start "" "%CLIPSY_APP%"
    if %errorLevel% == 0 (
        echo [SUCCESS] Clipsy launched successfully
        echo [INFO] Check system tray for Clipsy icon
    ) else (
        echo [ERROR] Failed to launch Clipsy
    )
)

echo.
echo ========================================
echo    Startup Configuration Complete!
echo ========================================
echo.
echo [SUCCESS] Clipsy has been configured to start with Windows.
echo.
echo Next steps:
echo 1. Restart your computer to test automatic startup
echo 2. Check system tray for Clipsy icon after restart
echo 3. Configure firewall if needed (run setup-firewall.bat)
echo.
echo To remove startup configuration, run: remove-autostart.bat
echo.
pause
