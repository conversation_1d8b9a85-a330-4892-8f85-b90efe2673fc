#!/usr/bin/env python3
"""
Test network scanning approach to verify it can find the Windows Clipsy app.
This simulates what the Android app will do.
"""

import socket
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

def get_local_ip():
    """Get the local IP address."""
    try:
        # Connect to a remote address to determine local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return None

def scan_device_at(ip_address, timeout=2):
    """Scan a specific IP address for Clipsy service on port 8766."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((ip_address, 8766))
        sock.close()
        
        if result == 0:
            return ip_address
        else:
            return None
    except Exception:
        return None

def network_scan():
    """Perform network scan to find Clipsy devices."""
    print("Network Scanning Test")
    print("====================")
    
    # Get local IP
    local_ip = get_local_ip()
    if not local_ip:
        print("❌ Could not determine local IP address")
        return
    
    print(f"Local IP: {local_ip}")
    
    # Extract network prefix
    network_prefix = local_ip.rsplit('.', 1)[0]
    print(f"Scanning network: {network_prefix}.x")
    print()
    
    # Scan network
    found_devices = []
    
    print("Scanning for devices with Clipsy service (port 8766)...")
    
    # Use ThreadPoolExecutor for concurrent scanning
    with ThreadPoolExecutor(max_workers=50) as executor:
        # Submit scan tasks for all IPs in the network
        future_to_ip = {}
        for i in range(1, 255):
            target_ip = f"{network_prefix}.{i}"
            if target_ip != local_ip:  # Skip our own IP
                future = executor.submit(scan_device_at, target_ip, 2)
                future_to_ip[future] = target_ip
        
        # Collect results
        completed = 0
        for future in as_completed(future_to_ip):
            completed += 1
            ip = future_to_ip[future]
            result = future.result()
            
            if result:
                found_devices.append(result)
                print(f"✅ Found Clipsy device at {result}")
            
            # Show progress every 50 scans
            if completed % 50 == 0:
                print(f"   Scanned {completed}/254 addresses...")
    
    print(f"\nScan completed! Checked {completed} addresses.")
    print()
    
    # Results
    if found_devices:
        print("🎉 Discovered Clipsy Devices:")
        for device_ip in found_devices:
            print(f"   • {device_ip}:8766")
        print()
        print("✅ Network scanning approach works!")
        print("   Android app should be able to discover these devices.")
    else:
        print("❌ No Clipsy devices found")
        print("   Make sure Windows Clipsy app is running")
        print("   and both devices are on the same network.")

if __name__ == "__main__":
    network_scan()
