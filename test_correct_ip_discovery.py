#!/usr/bin/env python3
"""
Test if Android devices can now discover the Windows PC at the correct IP (*************)
"""

import socket
from concurrent.futures import ThreadPoolExecutor, as_completed

def scan_device_at(ip_address, timeout=3):
    """Scan a specific IP address for Clipsy service on port 8766."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((ip_address, 8766))
        sock.close()
        
        if result == 0:
            return ip_address
        else:
            return None
    except Exception:
        return None

def test_correct_ip_discovery():
    """Test discovery of Windows PC at correct IP."""
    print("Testing Windows PC Discovery at Correct IP")
    print("==========================================")
    
    # Test the specific IP where Windows PC should be
    target_ip = "*************"
    print(f"Testing Windows PC at {target_ip}...")
    
    result = scan_device_at(target_ip, 5)
    if result:
        print(f"SUCCESS: Found Clipsy at {target_ip}!")
        
        # Test WebSocket connection
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((target_ip, 8766))
            sock.close()
            print("WebSocket connection: OK")
        except Exception as e:
            print(f"WebSocket connection: FAILED - {e}")
    else:
        print(f"FAILED: No Clipsy service at {target_ip}")
        print("Make sure Windows Clipsy app is running")
    
    print()
    print("Scanning 192.168.1.x network to verify...")
    
    found_devices = []
    
    with ThreadPoolExecutor(max_workers=50) as executor:
        # Submit scan tasks for 192.168.1.x range
        future_to_ip = {}
        for i in range(1, 255):
            test_ip = f"192.168.1.{i}"
            future = executor.submit(scan_device_at, test_ip, 2)
            future_to_ip[future] = test_ip
        
        # Collect results
        completed = 0
        for future in as_completed(future_to_ip):
            completed += 1
            ip = future_to_ip[future]
            result = future.result()
            
            if result:
                found_devices.append(result)
                print(f"Found device at {result}")
            
            if completed % 50 == 0:
                print(f"Scanned {completed}/254 addresses...")
    
    print(f"\nScan completed! Found {len(found_devices)} devices on 192.168.1.x")
    
    if found_devices:
        print("All discovered devices:")
        for device in found_devices:
            print(f"  - {device}:8766")
        
        if "*************" in found_devices:
            print("\nSUCCESS: Windows PC found at correct IP!")
            print("Android devices should now be able to connect.")
        else:
            print("\nWindows PC not found at expected IP")
    else:
        print("No devices found on 192.168.1.x network")

if __name__ == "__main__":
    test_correct_ip_discovery()
