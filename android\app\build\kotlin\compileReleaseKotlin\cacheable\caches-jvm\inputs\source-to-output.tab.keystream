Dapp/src/main/java/com/clipsy/android/ui/fragments/DevicesFragment.ktAapp/src/main/java/com/clipsy/android/ui/utils/ButtonStyleUtils.ktHapp/src/main/java/com/clipsy/android/data/repository/DeviceRepository.ktCapp/src/main/java/com/clipsy/android/data/model/ConnectionStatus.kt9app/src/main/java/com/clipsy/android/ClipsyApplication.ktGapp/src/main/java/com/clipsy/android/pairing/SimpleConnectionManager.kt?app/src/main/java/com/clipsy/android/network/WebSocketServer.kt?app/src/main/java/com/clipsy/android/network/WebSocketClient.kt7app/src/main/java/com/clipsy/android/ui/MainActivity.kt@app/src/main/java/com/clipsy/android/data/model/ClipboardItem.ktDapp/src/main/java/com/clipsy/android/service/ClipboardSyncService.ktKapp/src/main/java/com/clipsy/android/data/repository/ClipboardRepository.ktAapp/src/main/java/com/clipsy/android/ui/adapters/DeviceAdapter.kt;app/src/main/java/com/clipsy/android/ui/SettingsActivity.ktOapp/src/main/java/com/clipsy/android/ui/adapters/BluetoothStyleDeviceAdapter.ktCapp/src/main/java/com/clipsy/android/ui/fragments/StatusFragment.kt?app/src/main/java/com/clipsy/android/network/DeviceDiscovery.kt9app/src/main/java/com/clipsy/android/data/model/Device.kt?app/src/main/java/com/clipsy/android/viewmodel/MainViewModel.ktKapp/src/main/java/com/clipsy/android/ui/adapters/ClipboardHistoryAdapter.ktDapp/src/main/java/com/clipsy/android/ui/fragments/HistoryFragment.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   