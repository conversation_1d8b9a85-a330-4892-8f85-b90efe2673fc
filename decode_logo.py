import base64
import io
from PIL import Image

# Read the data URL from the file
with open('logo_original.png', 'r') as f:
    data_url = f.read().strip()

# Extract the base64 data
if data_url.startswith('data:image/png;base64,'):
    base64_data = data_url.split(',')[1]
    
    # Decode the base64 data
    image_data = base64.b64decode(base64_data)
    
    # Create PIL Image from the data
    image = Image.open(io.BytesIO(image_data))
    
    # Save as proper PNG file
    image.save('logo_original_decoded.png', 'PNG')
    print(f"Logo decoded and saved as logo_original_decoded.png")
    print(f"Image size: {image.size}")
else:
    print("File does not contain a valid data URL")
