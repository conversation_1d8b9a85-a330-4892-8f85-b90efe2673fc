#!/bin/bash

# Clipsy Android Build Script
# This script builds the Android application

set -e

echo "=== Clipsy Android Build Script ==="
echo "Building Android application..."

# Change to Android directory
cd "$(dirname "$0")/../android"

# Check if Android SDK is available
if ! command -v adb &> /dev/null; then
    echo "Error: Android SDK not found. Please install Android SDK and add it to PATH."
    exit 1
fi

# Check if Gradle wrapper exists
if [ ! -f "./gradlew" ]; then
    echo "Error: Gradle wrapper not found. Please ensure you're in the Android project directory."
    exit 1
fi

# Make gradlew executable
chmod +x ./gradlew

# Clean previous builds
echo "Cleaning previous builds..."
./gradlew clean

# Build debug APK
echo "Building debug APK..."
./gradlew assembleDebug

# Build release APK (if keystore is configured)
echo "Attempting to build release APK..."
if ./gradlew assembleRelease 2>/dev/null; then
    echo "✓ Release APK built successfully"
    RELEASE_APK="app/build/outputs/apk/release/app-release.apk"
    if [ -f "$RELEASE_APK" ]; then
        echo "Release APK location: $RELEASE_APK"
    fi
else
    echo "Warning: Release build failed (likely missing keystore configuration)"
    echo "Debug APK will be available instead"
fi

# Check if debug APK was created
DEBUG_APK="app/build/outputs/apk/debug/app-debug.apk"
if [ -f "$DEBUG_APK" ]; then
    echo "✓ Debug APK built successfully"
    echo "Debug APK location: $DEBUG_APK"
    
    # Get APK info
    APK_SIZE=$(du -h "$DEBUG_APK" | cut -f1)
    echo "APK size: $APK_SIZE"
else
    echo "Error: Debug APK not found"
    exit 1
fi

# Generate AAB (Android App Bundle) for Play Store
echo "Building Android App Bundle..."
if ./gradlew bundleRelease 2>/dev/null; then
    AAB_FILE="app/build/outputs/bundle/release/app-release.aab"
    if [ -f "$AAB_FILE" ]; then
        echo "✓ Android App Bundle built successfully"
        echo "AAB location: $AAB_FILE"
        AAB_SIZE=$(du -h "$AAB_FILE" | cut -f1)
        echo "AAB size: $AAB_SIZE"
    fi
else
    echo "Warning: AAB build failed (likely missing keystore configuration)"
fi

echo ""
echo "=== Build Summary ==="
echo "Build completed successfully!"
echo ""
echo "Available outputs:"
if [ -f "$DEBUG_APK" ]; then
    echo "- Debug APK: $DEBUG_APK"
fi
if [ -f "$RELEASE_APK" ]; then
    echo "- Release APK: $RELEASE_APK"
fi
if [ -f "$AAB_FILE" ]; then
    echo "- Android App Bundle: $AAB_FILE"
fi

echo ""
echo "To install debug APK on connected device:"
echo "adb install $DEBUG_APK"
