# Clipsy Troubleshooting Guide

## Common Issues and Solutions

### Device Discovery Problems

#### Issue: Devices not appearing in discovery list
**Symptoms**: No devices shown in the devices list after waiting several minutes.

**Solutions**:
1. **Check network connection**:
   - Ensure both devices are connected to the same Wi-Fi network
   - Verify internet connectivity (though not required for Clipsy)
   - Check if network allows device-to-device communication

2. **Check firewall settings**:
   - Windows: Allow Clipsy through Windows Defender Firewall
   - Router: Ensure AP isolation is disabled
   - Corporate networks: May block UDP broadcast traffic

3. **Restart discovery**:
   - Click "Refresh" button in devices list
   - Restart the Clipsy application
   - Wait up to 60 seconds for discovery

4. **Manual connection**:
   - Use "Add Manual" to connect by IP address
   - Find IP addresses in device network settings

#### Issue: Devices appear but connection fails
**Symptoms**: Devices visible in list but "Connect" button fails.

**Solutions**:
1. **Check target device**:
   - Ensure Clipsy is running on target device
   - Verify WebSocket server is started (check logs)
   - Confirm port 8766 is not blocked

2. **Network connectivity**:
   - Test basic connectivity: `ping <target_ip>`
   - Test port connectivity: `telnet <target_ip> 8766`
   - Check for network restrictions

3. **Restart services**:
   - Restart Clipsy on both devices
   - Clear device cache/history if available

### Clipboard Synchronization Issues

#### Issue: Clipboard content not syncing
**Symptoms**: Copy text on one device, but it doesn't appear on connected devices.

**Solutions**:
1. **Check connection status**:
   - Verify devices show as "Connected" in UI
   - Check connection count in status bar
   - Look for connection errors in logs

2. **Verify clipboard settings**:
   - Ensure "Enable clipboard sync" is checked
   - Check if auto-sync is enabled
   - Verify clipboard monitoring is active

3. **Test with simple content**:
   - Try copying plain text (avoid special characters)
   - Test with short content first
   - Avoid copying large amounts of text initially

4. **Check permissions**:
   - Windows: No special permissions needed
   - Android: Ensure notification permissions granted

#### Issue: Clipboard sync is delayed
**Symptoms**: Clipboard content syncs but with significant delay.

**Solutions**:
1. **Adjust monitor interval**:
   - Windows: Reduce `monitor_interval` in config.json
   - Check system performance and resource usage

2. **Network performance**:
   - Check Wi-Fi signal strength
   - Test network latency between devices
   - Consider network congestion

3. **Optimize settings**:
   - Reduce clipboard history limit
   - Disable unnecessary features temporarily

### Android-Specific Issues

#### Issue: Service stops running in background
**Symptoms**: Clipboard sync stops working when app is not in foreground.

**Solutions**:
1. **Battery optimization**:
   - Go to Settings > Battery > Battery Optimization
   - Find Clipsy and select "Don't optimize"
   - Some manufacturers have additional battery settings

2. **Background app restrictions**:
   - Settings > Apps > Clipsy > Battery > Background Activity
   - Enable "Allow background activity"
   - Check manufacturer-specific settings (MIUI, EMUI, etc.)

3. **Auto-start permissions**:
   - Enable auto-start for Clipsy in device settings
   - Check startup manager settings
   - Ensure app is not being killed by task managers

#### Issue: Notification permission denied
**Symptoms**: Android 13+ shows permission denied for notifications.

**Solutions**:
1. **Grant permission manually**:
   - Settings > Apps > Clipsy > Permissions
   - Enable "Notifications"
   - Restart the app

2. **Check notification settings**:
   - Ensure notification channel is enabled
   - Check if notifications are blocked system-wide
   - Verify Do Not Disturb settings

### Windows-Specific Issues

#### Issue: Application won't start
**Symptoms**: Double-clicking main.py or executable shows error.

**Solutions**:
1. **Check Python installation**:
   - Verify Python 3.8+ is installed
   - Check if Python is in system PATH
   - Try running from command prompt for error details

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Check file permissions**:
   - Ensure user has read/write access to application directory
   - Run as administrator if necessary (not recommended)

#### Issue: Firewall blocking connections
**Symptoms**: Connections fail, firewall warnings appear.

**Solutions**:
1. **Configure Windows Firewall**:
   - Control Panel > System and Security > Windows Defender Firewall
   - Click "Allow an app or feature through Windows Defender Firewall"
   - Add Python or Clipsy executable
   - Enable for both Private and Public networks

2. **Third-party firewalls**:
   - Check antivirus firewall settings
   - Add Clipsy to firewall exceptions
   - Temporarily disable to test

### Network and Connectivity Issues

#### Issue: Devices on different subnets
**Symptoms**: Devices can't discover each other despite being on "same" network.

**Solutions**:
1. **Check IP addresses**:
   - Ensure devices are on same subnet (e.g., 192.168.1.x)
   - Some routers create separate networks for 2.4GHz and 5GHz
   - Guest networks may be isolated

2. **Router configuration**:
   - Disable AP isolation if enabled
   - Check VLAN settings
   - Ensure multicast/broadcast is allowed

3. **Use manual connection**:
   - Connect by IP address instead of discovery
   - Test direct connectivity first

#### Issue: Corporate/Enterprise network restrictions
**Symptoms**: Discovery and connections fail on work networks.

**Solutions**:
1. **Check network policies**:
   - Corporate networks often block device-to-device communication
   - UDP broadcast may be disabled
   - Custom ports may be blocked

2. **Alternative approaches**:
   - Use mobile hotspot for testing
   - Request network administrator assistance
   - Consider using different ports if allowed

### Performance Issues

#### Issue: High CPU or memory usage
**Symptoms**: Clipsy uses excessive system resources.

**Solutions**:
1. **Adjust monitoring frequency**:
   - Increase clipboard monitor interval
   - Reduce discovery broadcast frequency
   - Limit clipboard history size

2. **Check for loops**:
   - Look for rapid clipboard changes in logs
   - Disable auto-sync temporarily
   - Check for conflicting clipboard managers

3. **Resource monitoring**:
   - Use Task Manager (Windows) or Activity Monitor
   - Check memory usage patterns
   - Monitor network traffic

### Data and History Issues

#### Issue: Clipboard history not saving
**Symptoms**: History tab/screen shows empty or limited items.

**Solutions**:
1. **Check history settings**:
   - Ensure "Enable clipboard history" is checked
   - Verify history limit is set appropriately
   - Check if history was manually cleared

2. **Storage permissions**:
   - Ensure app has write permissions
   - Check available storage space
   - Verify database file integrity

#### Issue: Large clipboard content causes issues
**Symptoms**: App crashes or becomes unresponsive with large clipboard content.

**Solutions**:
1. **Content size limits**:
   - Check max content size in configuration
   - Avoid copying very large text blocks
   - Consider splitting large content

2. **Memory management**:
   - Monitor memory usage during large transfers
   - Restart app if memory usage is high
   - Adjust history limits

## Diagnostic Tools

### Log Analysis

#### Windows Logs
```bash
# View recent log entries
tail -f clipsy.log

# Search for errors
findstr "ERROR" clipsy.log
findstr "WARN" clipsy.log
```

#### Android Logs
```bash
# Using ADB
adb logcat | grep Clipsy

# In Android Studio
# Use Logcat window with "Clipsy" filter
```

### Network Testing

#### Test UDP Discovery
```bash
# Windows PowerShell
Test-NetConnection -ComputerName <broadcast_ip> -Port 8765 -InformationLevel Detailed

# Linux/Mac
nc -u <target_ip> 8765
```

#### Test WebSocket Connection
```bash
# Using curl (if available)
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" http://<target_ip>:8766/
```

### System Information

#### Network Configuration
```bash
# Windows
ipconfig /all
netstat -an | findstr :8765
netstat -an | findstr :8766

# Android (using terminal app)
ip addr show
netstat -tuln
```

## Getting Help

### Before Reporting Issues
1. **Check logs**: Review application logs for error messages
2. **Test basic connectivity**: Ensure devices can communicate
3. **Try manual connection**: Bypass discovery if possible
4. **Restart everything**: Restart apps, network connections, devices

### Information to Include
When reporting issues, please include:
- Operating systems and versions
- Clipsy version
- Network configuration (router model, network type)
- Error messages from logs
- Steps to reproduce the issue
- Screenshots if applicable

### Support Channels
- GitHub Issues: For bug reports and feature requests
- Documentation: Check README and setup guides
- Community: User forums and discussions

## Advanced Troubleshooting

### Debug Mode
Enable debug logging for more detailed information:

#### Windows
Edit `config.json`:
```json
{
  "debug": true,
  "logging_level": "DEBUG"
}
```

#### Android
Enable developer options and check detailed logs in Android Studio.

### Network Packet Capture
For advanced network troubleshooting:
1. Use Wireshark to capture network traffic
2. Filter by ports 8765 and 8766
3. Analyze UDP broadcast and WebSocket traffic
4. Look for connection attempts and responses

### Factory Reset
If all else fails:
1. **Windows**: Delete config.json and restart
2. **Android**: Clear app data in device settings
3. Reconfigure from scratch
4. Test with minimal configuration first
