#!/usr/bin/env python3
"""
Test WebSocket server with same host resolution as SyncServer
"""

import asyncio
import websockets
import threading
import logging
import time
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from network.discovery import DeviceDiscovery

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HostResolutionTest:
    def __init__(self):
        self.loop = None
        self.loop_thread = None
        self.server = None
        self.connected_clients = set()
        self.initialization_ready = threading.Event()
        self.config = {
            "device_name": "Test-PC-HostRes",
            "websocket_port": 8773,
            "discovery_port": 8774
        }
        self.host = None
        self.port = 8773
    
    def _get_preferred_host(self) -> str:
        """Get preferred host IP (same method as SyncServer)."""
        try:
            logger.info("🔍 Getting preferred host using DeviceDiscovery...")
            
            # Create temporary discovery instance to get local IP (SAME AS SYNCSERVER)
            temp_discovery = DeviceDiscovery(self.config)
            local_ip = temp_discovery.get_local_ip()

            if local_ip and local_ip.startswith(('192.168.', '10.', '172.')):
                logger.info(f"✅ Binding to Wi-Fi interface: {local_ip}")
                return local_ip
            else:
                logger.warning(f"⚠️ No suitable Wi-Fi IP found, using all interfaces")
                return "0.0.0.0"
        except Exception as e:
            logger.error(f"❌ Error getting preferred host: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return "0.0.0.0"
    
    def start_async_loop(self):
        """Start the asyncio event loop in a separate thread."""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        logger.info(f"🔄 Event loop started in thread: {threading.current_thread().name}")
        self.loop.run_forever()
    
    async def _handle_client(self, websocket, path):
        """Handle WebSocket client connection (same as SyncServer)"""
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        client_ip = websocket.remote_address[0]
        logger.info(f"🔗 CLIENT CONNECTION ATTEMPT: {client_address}")
        logger.info(f"🔗 Path: {path}")
        logger.info(f"🔗 Current event loop: {asyncio.current_task()}")
        logger.info(f"🔗 Total connected clients: {len(self.connected_clients) + 1}")

        self.connected_clients.add(websocket)

        try:
            # Send welcome message
            welcome_msg = {
                "type": "welcome",
                "server_info": {
                    "device_name": "Test-PC-HostRes",
                    "device_type": "windows",
                    "timestamp": "2025-07-08T02:30:00.000000"
                }
            }
            
            logger.info(f"📤 Sending welcome message to {client_ip}: {welcome_msg}")
            await websocket.send(str(welcome_msg).replace("'", '"'))
            
            # Echo messages
            async for message in websocket:
                logger.info(f"📥 Received from {client_ip}: {message}")
                try:
                    import json
                    data = json.loads(message)
                    logger.info(f"✅ Valid JSON received: {data}")
                    
                    response = {"type": "echo", "original": data}
                    await websocket.send(json.dumps(response))
                    
                except json.JSONDecodeError:
                    logger.error(f"❌ Invalid JSON received from {client_ip}: {message}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client disconnected: {client_address}")
        except Exception as e:
            logger.error(f"❌ Error handling client: {e}")
        finally:
            self.connected_clients.discard(websocket)
    
    async def start_websocket_server(self):
        """Start WebSocket server with host resolution (same as SyncServer)"""
        logger.info(f"🚀 Starting WebSocket server with host resolution...")
        
        try:
            # Get host using same method as SyncServer
            logger.info("🔍 Resolving host...")
            self.host = self._get_preferred_host()
            logger.info(f"✅ Host resolved to: {self.host}")
            
            # Start server with resolved host
            logger.info(f"🚀 Starting WebSocket server on {self.host}:{self.port}")
            self.server = await websockets.serve(
                self._handle_client,
                self.host,
                self.port,
                ping_interval=20,
                ping_timeout=10
            )
            
            logger.info(f"✅ WebSocket server started on {self.host}:{self.port}")
            logger.info(f"Server object: {self.server}")
            logger.info(f"Server sockets: {self.server.sockets}")
            
            # Signal that initialization is complete
            self.initialization_ready.set()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start WebSocket server: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.initialization_ready.set()  # Signal even on failure
            return False
    
    def run_test(self):
        """Run the host resolution test"""
        logger.info("🚀 Starting host resolution WebSocket test...")
        
        try:
            # Start async event loop in background thread
            self.loop_thread = threading.Thread(target=self.start_async_loop, daemon=True)
            self.loop_thread.start()
            
            # Wait for loop to be ready
            time.sleep(0.5)
            
            # Start WebSocket server
            if self.loop:
                logger.info("🔄 Scheduling WebSocket server start...")
                future = asyncio.run_coroutine_threadsafe(
                    self.start_websocket_server(), self.loop
                )
                
                # Wait for initialization
                logger.info("⏳ Waiting for server initialization...")
                if self.initialization_ready.wait(timeout=15):
                    logger.info("✅ Server initialization completed")
                    
                    # Keep server running for testing
                    logger.info("🔄 Server is running - waiting for connections...")
                    logger.info(f"Test with: python test_minimal_websocket_client.py (port {self.port})")
                    logger.info("Press Ctrl+C to stop")
                    
                    try:
                        while True:
                            time.sleep(1)
                    except KeyboardInterrupt:
                        logger.info("Stopping server...")
                        
                else:
                    logger.error("❌ Server initialization timeout")
                    return False
            else:
                logger.error("❌ Failed to start event loop")
                return False
                
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            return False
        finally:
            # Cleanup
            if self.loop and not self.loop.is_closed():
                self.loop.call_soon_threadsafe(self.loop.stop)
            if self.loop_thread and self.loop_thread.is_alive():
                self.loop_thread.join(timeout=5)
        
        return True

if __name__ == "__main__":
    test = HostResolutionTest()
    success = test.run_test()
    sys.exit(0 if success else 1)
