#!/usr/bin/env python3
"""
Clipsy Windows Test Script
This script runs tests for the Windows application
"""

import sys
import os
import subprocess
import unittest
from pathlib import Path

def run_command(command, cwd=None):
    """Run a command and return the result."""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {command}")
        print(f"Error output: {e.stderr}")
        raise

def check_dependencies():
    """Check if required dependencies are installed."""
    print("Checking Python dependencies...")
    
    required_packages = [
        'websockets',
        'pyperclip',
        'tkinter'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} (missing)")
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    return True

def run_unit_tests():
    """Run unit tests for the Windows application."""
    print("\nRunning unit tests...")
    
    # Change to Windows directory
    windows_dir = Path(__file__).parent.parent / "windows"
    
    # Discover and run tests
    loader = unittest.TestLoader()
    start_dir = windows_dir / "tests"
    
    if not start_dir.exists():
        print("No tests directory found. Creating basic test structure...")
        start_dir.mkdir(exist_ok=True)
        
        # Create a basic test file
        test_file = start_dir / "test_basic.py"
        test_content = '''import unittest
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

class TestBasic(unittest.TestCase):
    """Basic tests to ensure imports work."""
    
    def test_imports(self):
        """Test that all modules can be imported."""
        try:
            from clipboard.monitor import ClipboardMonitor
            from network.discovery import DeviceDiscovery
            from network.sync_server import SyncServer
            from network.sync_client import SyncClient
            from ui.main_window import ClipsyMainWindow
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"Import failed: {e}")

if __name__ == "__main__":
    unittest.main()
'''
        with open(test_file, 'w') as f:
            f.write(test_content)
        print(f"Created basic test file: {test_file}")
    
    # Run tests
    suite = loader.discover(str(start_dir))
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def run_linting():
    """Run code linting."""
    print("\nRunning code linting...")
    
    windows_dir = Path(__file__).parent.parent / "windows" / "src"
    
    try:
        # Try to run flake8
        output = run_command(f"flake8 {windows_dir}", cwd=windows_dir.parent)
        print("✓ Linting passed")
        return True
    except subprocess.CalledProcessError:
        print("✗ Linting issues found")
        return False
    except FileNotFoundError:
        print("Warning: flake8 not found. Install with: pip install flake8")
        return True

def run_type_checking():
    """Run type checking with mypy."""
    print("\nRunning type checking...")
    
    windows_dir = Path(__file__).parent.parent / "windows" / "src"
    
    try:
        output = run_command(f"mypy {windows_dir}", cwd=windows_dir.parent)
        print("✓ Type checking passed")
        return True
    except subprocess.CalledProcessError:
        print("✗ Type checking issues found")
        return False
    except FileNotFoundError:
        print("Warning: mypy not found. Install with: pip install mypy")
        return True

def main():
    """Main test function."""
    print("=== Clipsy Windows Test Script ===")
    print("Starting Windows tests...")
    
    # Check dependencies
    if not check_dependencies():
        print("\nPlease install missing dependencies before running tests.")
        sys.exit(1)
    
    success = True
    
    # Run unit tests
    if not run_unit_tests():
        success = False
    
    # Run linting
    if not run_linting():
        success = False
    
    # Run type checking
    if not run_type_checking():
        success = False
    
    print("\n=== Test Results ===")
    if success:
        print("✓ All tests passed!")
    else:
        print("✗ Some tests failed. Please check the output above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
