// Generated by view binder compiler. Do not edit!
package com.clipsy.android.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.clipsy.android.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDeviceBluetoothStyleBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton buttonInfo;

  @NonNull
  public final ImageView iconDeviceType;

  @NonNull
  public final TextView textConnectionStatus;

  @NonNull
  public final TextView textDeviceName;

  private ItemDeviceBluetoothStyleBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton buttonInfo, @NonNull ImageView iconDeviceType,
      @NonNull TextView textConnectionStatus, @NonNull TextView textDeviceName) {
    this.rootView = rootView;
    this.buttonInfo = buttonInfo;
    this.iconDeviceType = iconDeviceType;
    this.textConnectionStatus = textConnectionStatus;
    this.textDeviceName = textDeviceName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDeviceBluetoothStyleBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDeviceBluetoothStyleBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_device_bluetooth_style, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDeviceBluetoothStyleBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_info;
      ImageButton buttonInfo = ViewBindings.findChildViewById(rootView, id);
      if (buttonInfo == null) {
        break missingId;
      }

      id = R.id.icon_device_type;
      ImageView iconDeviceType = ViewBindings.findChildViewById(rootView, id);
      if (iconDeviceType == null) {
        break missingId;
      }

      id = R.id.text_connection_status;
      TextView textConnectionStatus = ViewBindings.findChildViewById(rootView, id);
      if (textConnectionStatus == null) {
        break missingId;
      }

      id = R.id.text_device_name;
      TextView textDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (textDeviceName == null) {
        break missingId;
      }

      return new ItemDeviceBluetoothStyleBinding((MaterialCardView) rootView, buttonInfo,
          iconDeviceType, textConnectionStatus, textDeviceName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
