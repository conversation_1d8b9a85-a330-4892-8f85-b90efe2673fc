#!/usr/bin/env python3
"""
Simple test to verify Clipsy WebSocket connection
"""

import asyncio
import websockets
import json

async def test_clipsy_websocket(host, port):
    """Test WebSocket connection to verify Clipsy service."""
    try:
        uri = f"ws://{host}:{port}"
        print(f"Connecting to {uri}...")
        
        async with websockets.connect(uri, timeout=5) as websocket:
            print("SUCCESS: WebSocket connection established!")
            
            # Send a ping message to test Clipsy protocol
            ping_message = {
                "type": "ping",
                "timestamp": "2025-07-04T13:20:00Z"
            }
            
            await websocket.send(json.dumps(ping_message))
            print("Sent ping message")
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                response_data = json.loads(response)
                print(f"Received response: {response_data}")
                
                if response_data.get("type") == "pong":
                    print("CONFIRMED: This is a Clipsy service!")
                    return True
                else:
                    print("Not a Clipsy service (unexpected response)")
                    return False
                    
            except asyncio.TimeoutError:
                print("No response received (might not be Clipsy)")
                return False
                
    except Exception as e:
        print(f"Connection failed: {e}")
        return False

async def main():
    """Test the Windows PC specifically."""
    print("Testing Windows PC Clipsy Service")
    print("=================================")
    
    # Test the Windows PC
    host = "*************"
    port = 8766
    
    print(f"Testing {host}:{port}")
    is_clipsy = await test_clipsy_websocket(host, port)
    
    if is_clipsy:
        print(f"\nSUCCESS: Windows PC at {host}:{port} is running Clipsy!")
        print("Android devices should be able to connect to this IP.")
    else:
        print(f"\nFAILED: {host}:{port} is not responding as expected")
        print("Check if Windows Clipsy app is running correctly.")

if __name__ == "__main__":
    asyncio.run(main())
