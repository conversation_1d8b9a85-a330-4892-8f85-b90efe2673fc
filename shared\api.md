# Clipsy API Documentation

## Overview

This document describes the internal APIs and interfaces used by Clipsy components for cross-platform clipboard synchronization.

## Core Components

### 1. Clipboard Monitor
Monitors system clipboard for changes and manages clipboard history.

#### Interface
```
- start(): void
- stop(): void
- getCurrentContent(): string
- setContent(content: string, source: string): void
- getHistory(): ClipboardItem[]
- clearHistory(): void
- setHistoryLimit(limit: int): void
- onClipboardChange(callback: function): void
```

#### ClipboardItem Structure
```json
{
  "content": "string",
  "timestamp": "ISO8601_timestamp",
  "source": "local|remote",
  "device_info": {
    "device_name": "string",
    "device_type": "string",
    "device_id": "string"
  }
}
```

### 2. Device Discovery
Handles automatic discovery of other Clipsy devices on the local network.

#### Interface
```
- start(): void
- stop(): void
- getDiscoveredDevices(): Device[]
- addManualDevice(ip: string, name: string): void
- removeDevice(ip: string): void
- onDeviceDiscovered(callback: function): void
- onDeviceLost(callback: function): void
```

#### Device Structure
```json
{
  "ip": "string",
  "device_name": "string",
  "device_type": "windows|android",
  "websocket_port": 8766,
  "last_seen": "timestamp",
  "manual": false
}
```

### 3. Sync Server
WebSocket server that accepts connections from other devices.

#### Interface
```
- start(): void
- stop(): void
- getConnectedClients(): ClientInfo[]
- broadcastClipboardUpdate(content: string, deviceInfo: object): void
- onClientConnected(callback: function): void
- onClientDisconnected(callback: function): void
- onClipboardReceived(callback: function): void
```

### 4. Sync Client
WebSocket client that connects to other devices' sync servers.

#### Interface
```
- connectToDevice(ip: string, deviceInfo: object): void
- disconnectFromDevice(ip: string): void
- disconnectAll(): void
- sendClipboardUpdate(content: string, deviceInfo: object): void
- requestHistory(ip: string): void
- getConnectedDevices(): ConnectedDevice[]
- onConnected(callback: function): void
- onDisconnected(callback: function): void
- onClipboardReceived(callback: function): void
```

## Configuration API

### Settings Structure
```json
{
  "app": {
    "name": "Clipsy",
    "version": "1.0.0",
    "device_name": "string"
  },
  "network": {
    "discovery_port": 8765,
    "websocket_port": 8766,
    "broadcast_interval": 5,
    "connection_timeout": 10,
    "auto_discovery": true
  },
  "clipboard": {
    "sync_enabled": true,
    "auto_sync": true,
    "history_enabled": true,
    "history_limit": 50,
    "monitor_interval": 0.5
  },
  "ui": {
    "window_width": 600,
    "window_height": 400,
    "minimize_to_tray": true,
    "show_notifications": true
  },
  "startup": {
    "auto_start": false,
    "start_minimized": false
  }
}
```

## Event System

### Event Types

#### Clipboard Events
- `clipboard_changed`: Local clipboard content changed
- `clipboard_received`: Remote clipboard content received
- `clipboard_synced`: Clipboard successfully synced to remote device

#### Device Events
- `device_discovered`: New device found on network
- `device_lost`: Device no longer responding
- `device_connected`: Successfully connected to device
- `device_disconnected`: Disconnected from device
- `device_error`: Connection error with device

#### Service Events
- `service_started`: Background service started
- `service_stopped`: Background service stopped
- `service_error`: Service encountered an error

### Event Data Structures

#### ClipboardEvent
```json
{
  "type": "clipboard_changed|clipboard_received|clipboard_synced",
  "content": "string",
  "timestamp": "ISO8601_timestamp",
  "device_info": {
    "device_name": "string",
    "device_type": "string",
    "device_id": "string"
  }
}
```

#### DeviceEvent
```json
{
  "type": "device_discovered|device_lost|device_connected|device_disconnected",
  "device": {
    "ip": "string",
    "device_name": "string",
    "device_type": "string",
    "websocket_port": 8766
  },
  "timestamp": "ISO8601_timestamp"
}
```

## Platform-Specific APIs

### Windows (Python)

#### Clipboard Access
```python
import pyperclip

# Get clipboard content
content = pyperclip.paste()

# Set clipboard content
pyperclip.copy(content)
```

#### Network Discovery
```python
import socket

# Create UDP socket for broadcasting
sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
```

#### WebSocket Communication
```python
import websockets

# Server
async def handle_client(websocket, path):
    # Handle client connection

# Client
async def connect_to_server(uri):
    async with websockets.connect(uri) as websocket:
        # Handle server communication
```

### Android (Kotlin)

#### Clipboard Access
```kotlin
val clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager

// Get clipboard content
val clipData = clipboardManager.primaryClip
val content = clipData?.getItemAt(0)?.text?.toString()

// Set clipboard content
val clip = ClipData.newPlainText("label", content)
clipboardManager.setPrimaryClip(clip)
```

#### Network Discovery
```kotlin
val socket = DatagramSocket(port)
val packet = DatagramPacket(data, data.size, address, port)
socket.send(packet)
```

#### Background Service
```kotlin
class ClipboardSyncService : Service() {
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        startForeground(NOTIFICATION_ID, createNotification())
        return START_STICKY
    }
}
```

## Error Codes

### Network Errors
- `NET_001`: Connection timeout
- `NET_002`: Connection refused
- `NET_003`: Network unreachable
- `NET_004`: Invalid IP address
- `NET_005`: Port already in use

### Protocol Errors
- `PROTO_001`: Invalid message format
- `PROTO_002`: Unknown message type
- `PROTO_003`: Message too large
- `PROTO_004`: Unsupported protocol version

### Clipboard Errors
- `CLIP_001`: Clipboard access denied
- `CLIP_002`: Clipboard content too large
- `CLIP_003`: Unsupported content type
- `CLIP_004`: Clipboard unavailable

### Service Errors
- `SVC_001`: Service start failed
- `SVC_002`: Permission denied
- `SVC_003`: Service already running
- `SVC_004`: Service crashed

## Testing APIs

### Mock Interfaces
For testing purposes, mock implementations of core interfaces should be provided:

- `MockClipboardMonitor`: Simulates clipboard changes
- `MockDeviceDiscovery`: Simulates device discovery
- `MockSyncServer`: Simulates server connections
- `MockSyncClient`: Simulates client connections

### Test Utilities
- `TestDeviceSimulator`: Creates virtual devices for testing
- `TestNetworkSimulator`: Simulates network conditions
- `TestClipboardGenerator`: Generates test clipboard content
