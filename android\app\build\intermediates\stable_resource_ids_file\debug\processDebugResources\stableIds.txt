com.clipsy.android.debug:styleable/ViewPager2 = 0x7f1300b5
com.clipsy.android.debug:styleable/ViewBackgroundHelper = 0x7f1300b4
com.clipsy.android.debug:styleable/Transition = 0x7f1300b1
com.clipsy.android.debug:styleable/Transform = 0x7f1300b0
com.clipsy.android.debug:styleable/Tooltip = 0x7f1300af
com.clipsy.android.debug:styleable/Toolbar = 0x7f1300ae
com.clipsy.android.debug:styleable/TextInputEditText = 0x7f1300ab
com.clipsy.android.debug:styleable/SwitchMaterial = 0x7f1300a4
com.clipsy.android.debug:styleable/StateListDrawableItem = 0x7f1300a0
com.clipsy.android.debug:styleable/State = 0x7f13009e
com.clipsy.android.debug:styleable/SplitPlaceholderRule = 0x7f13009d
com.clipsy.android.debug:styleable/SplitPairRule = 0x7f13009c
com.clipsy.android.debug:styleable/SplitPairFilter = 0x7f13009b
com.clipsy.android.debug:styleable/Spinner = 0x7f13009a
com.clipsy.android.debug:styleable/Snackbar = 0x7f130098
com.clipsy.android.debug:styleable/Slider = 0x7f130097
com.clipsy.android.debug:styleable/ShapeAppearance = 0x7f130094
com.clipsy.android.debug:styleable/SearchBar = 0x7f130091
com.clipsy.android.debug:styleable/RangeSlider = 0x7f13008c
com.clipsy.android.debug:styleable/RadialViewGroup = 0x7f13008b
com.clipsy.android.debug:styleable/PropertySet = 0x7f13008a
com.clipsy.android.debug:styleable/PreferenceTheme = 0x7f130089
com.clipsy.android.debug:styleable/PreferenceImageView = 0x7f130088
com.clipsy.android.debug:styleable/PreferenceFragmentCompat = 0x7f130086
com.clipsy.android.debug:styleable/NavInclude = 0x7f13007a
com.clipsy.android.debug:styleable/NavHostFragment = 0x7f130079
com.clipsy.android.debug:styleable/NavHost = 0x7f130078
com.clipsy.android.debug:styleable/MotionTelltales = 0x7f130072
com.clipsy.android.debug:styleable/MotionLabel = 0x7f13006f
com.clipsy.android.debug:styleable/MotionHelper = 0x7f13006e
com.clipsy.android.debug:styleable/MenuGroup = 0x7f130068
com.clipsy.android.debug:styleable/MaterialToolbar = 0x7f130067
com.clipsy.android.debug:styleable/MaterialTextView = 0x7f130065
com.clipsy.android.debug:styleable/Preference = 0x7f130084
com.clipsy.android.debug:styleable/MaterialShape = 0x7f130062
com.clipsy.android.debug:styleable/MaterialDivider = 0x7f130060
com.clipsy.android.debug:styleable/MaterialCheckBoxStates = 0x7f13005f
com.clipsy.android.debug:styleable/MaterialButtonToggleGroup = 0x7f13005a
com.clipsy.android.debug:styleable/MaterialAutoCompleteTextView = 0x7f130058
com.clipsy.android.debug:styleable/MaterialAlertDialogTheme = 0x7f130057
com.clipsy.android.debug:styleable/ListPopupWindow = 0x7f130054
com.clipsy.android.debug:styleable/LinearProgressIndicator = 0x7f130053
com.clipsy.android.debug:styleable/LinearLayoutCompat_Layout = 0x7f130052
com.clipsy.android.debug:styleable/LinearLayoutCompat = 0x7f130051
com.clipsy.android.debug:styleable/Layout = 0x7f130050
com.clipsy.android.debug:styleable/KeyTrigger = 0x7f13004f
com.clipsy.android.debug:styleable/Insets = 0x7f130047
com.clipsy.android.debug:styleable/ImageFilterView = 0x7f130046
com.clipsy.android.debug:styleable/GradientColor = 0x7f130044
com.clipsy.android.debug:styleable/ForegroundLinearLayout = 0x7f130040
com.clipsy.android.debug:styleable/FontFamilyFont = 0x7f13003f
com.clipsy.android.debug:styleable/ExtendedFloatingActionButton = 0x7f130039
com.clipsy.android.debug:styleable/DrawerArrowToggle = 0x7f130036
com.clipsy.android.debug:styleable/DialogPreference = 0x7f130035
com.clipsy.android.debug:styleable/DialogFragmentNavigator = 0x7f130034
com.clipsy.android.debug:styleable/CustomAttribute = 0x7f130033
com.clipsy.android.debug:styleable/CoordinatorLayout_Layout = 0x7f130032
com.clipsy.android.debug:styleable/ConstraintOverride = 0x7f13002f
com.clipsy.android.debug:styleable/ConstraintLayout_placeholder = 0x7f13002e
com.clipsy.android.debug:styleable/ConstraintLayout_Layout = 0x7f13002c
com.clipsy.android.debug:styleable/Constraint = 0x7f13002b
com.clipsy.android.debug:styleable/ColorStateListItem = 0x7f130029
com.clipsy.android.debug:styleable/CollapsingToolbarLayout = 0x7f130027
com.clipsy.android.debug:styleable/ClockHandView = 0x7f130026
com.clipsy.android.debug:styleable/CheckedTextView = 0x7f130021
com.clipsy.android.debug:styleable/CheckBoxPreference = 0x7f130020
com.clipsy.android.debug:styleable/Carousel = 0x7f13001f
com.clipsy.android.debug:styleable/BottomNavigationView = 0x7f13001a
com.clipsy.android.debug:styleable/BaseProgressIndicator = 0x7f130018
com.clipsy.android.debug:styleable/BackgroundStyle = 0x7f130016
com.clipsy.android.debug:styleable/AppCompatSeekBar = 0x7f130012
com.clipsy.android.debug:styleable/AppCompatImageView = 0x7f130011
com.clipsy.android.debug:styleable/AppBarLayoutStates = 0x7f13000e
com.clipsy.android.debug:styleable/AppBarLayout = 0x7f13000d
com.clipsy.android.debug:styleable/AnimatedStateListDrawableTransition = 0x7f13000c
com.clipsy.android.debug:styleable/ActivityFilter = 0x7f130006
com.clipsy.android.debug:styleable/ActionMenuView = 0x7f130003
com.clipsy.android.debug:styleable/ActionBarLayout = 0x7f130001
com.clipsy.android.debug:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f120486
com.clipsy.android.debug:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f120485
com.clipsy.android.debug:style/Widget.MaterialComponents.Toolbar = 0x7f120484
com.clipsy.android.debug:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f120483
com.clipsy.android.debug:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f120482
com.clipsy.android.debug:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f120480
com.clipsy.android.debug:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f12047e
com.clipsy.android.debug:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f120475
com.clipsy.android.debug:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f120474
com.clipsy.android.debug:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f12046f
com.clipsy.android.debug:style/Widget.MaterialComponents.TabLayout = 0x7f12046a
com.clipsy.android.debug:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f120463
com.clipsy.android.debug:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f120461
com.clipsy.android.debug:style/Widget.MaterialComponents.NavigationView = 0x7f12045f
com.clipsy.android.debug:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f12045e
com.clipsy.android.debug:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f12045d
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialDivider = 0x7f120459
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f120457
com.clipsy.android.debug:styleable/NavigationBarActiveIndicator = 0x7f13007b
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f120456
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f120454
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f120451
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f120450
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f12044d
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f120446
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f120445
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f120443
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f120442
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar = 0x7f120441
com.clipsy.android.debug:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f12043f
com.clipsy.android.debug:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f12043e
com.clipsy.android.debug:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f12043b
com.clipsy.android.debug:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f120437
com.clipsy.android.debug:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f120436
com.clipsy.android.debug:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f120435
com.clipsy.android.debug:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f120433
com.clipsy.android.debug:style/Widget.MaterialComponents.Chip.Filter = 0x7f120431
com.clipsy.android.debug:style/Widget.MaterialComponents.CheckedTextView = 0x7f12042d
com.clipsy.android.debug:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f120428
com.clipsy.android.debug:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f120427
com.clipsy.android.debug:style/Widget.MaterialComponents.Button = 0x7f120420
com.clipsy.android.debug:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f12041f
com.clipsy.android.debug:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f12041d
com.clipsy.android.debug:style/Widget.MaterialComponents.BottomNavigationView = 0x7f12041b
com.clipsy.android.debug:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f12041a
com.clipsy.android.debug:style/Widget.MaterialComponents.Badge = 0x7f120417
com.clipsy.android.debug:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f12042a
com.clipsy.android.debug:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f12040e
com.clipsy.android.debug:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f12040c
com.clipsy.android.debug:styleable/MaterialTimePicker = 0x7f130066
com.clipsy.android.debug:style/Widget.Material3.Toolbar.Surface = 0x7f120409
com.clipsy.android.debug:style/Widget.Material3.Toolbar = 0x7f120407
com.clipsy.android.debug:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f120406
com.clipsy.android.debug:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f120405
com.clipsy.android.debug:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f120403
com.clipsy.android.debug:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f120402
com.clipsy.android.debug:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f120400
com.clipsy.android.debug:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f1203ff
com.clipsy.android.debug:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f1203fc
com.clipsy.android.debug:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f1203fb
com.clipsy.android.debug:style/Widget.Material3.TabLayout.OnSurface = 0x7f1203f9
com.clipsy.android.debug:style/Widget.Material3.TabLayout = 0x7f1203f8
com.clipsy.android.debug:style/Widget.Material3.Snackbar.FullWidth = 0x7f1203f6
com.clipsy.android.debug:style/Widget.Material3.Snackbar = 0x7f1203f5
com.clipsy.android.debug:style/Widget.Material3.Slider = 0x7f1203f3
com.clipsy.android.debug:style/Widget.Material3.SearchView.Prefix = 0x7f1203ed
com.clipsy.android.debug:style/Widget.Material3.SearchView = 0x7f1203ec
com.clipsy.android.debug:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f1203e9
com.clipsy.android.debug:style/Widget.Material3.PopupMenu.Overflow = 0x7f1203e7
com.clipsy.android.debug:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1203e6
com.clipsy.android.debug:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1203e5
com.clipsy.android.debug:styleable/ActivityChooserView = 0x7f130005
com.clipsy.android.debug:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1203dd
com.clipsy.android.debug:style/Widget.Material3.MaterialTimePicker = 0x7f1203d7
com.clipsy.android.debug:style/Widget.Material3.MaterialDivider = 0x7f1203d5
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1203d4
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1203d0
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1203cf
com.clipsy.android.debug:styleable/NavigationBarView = 0x7f13007c
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.Item = 0x7f1203ce
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1203cb
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1203c9
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1203c8
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1203c7
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1203c6
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1203c5
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1203c0
com.clipsy.android.debug:style/Widget.Material3.LinearProgressIndicator = 0x7f1203bc
com.clipsy.android.debug:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1203b9
com.clipsy.android.debug:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f1203b8
com.clipsy.android.debug:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f1203b7
com.clipsy.android.debug:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f1203b4
com.clipsy.android.debug:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f1203b2
com.clipsy.android.debug:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f1203af
com.clipsy.android.debug:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f1203a8
com.clipsy.android.debug:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f1203a7
com.clipsy.android.debug:style/Widget.Material3.CompoundButton.Switch = 0x7f1203a5
com.clipsy.android.debug:style/Widget.Material3.CollapsingToolbar.Large = 0x7f1203a0
com.clipsy.android.debug:style/Widget.Material3.CollapsingToolbar = 0x7f12039f
com.clipsy.android.debug:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f12039e
com.clipsy.android.debug:style/Widget.Material3.ChipGroup = 0x7f12039a
com.clipsy.android.debug:style/Widget.Material3.Chip.Suggestion = 0x7f120398
com.clipsy.android.debug:style/Widget.Material3.Chip.Filter = 0x7f120392
com.clipsy.android.debug:style/Widget.Material3.Chip.Assist = 0x7f120390
com.clipsy.android.debug:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1203dc
com.clipsy.android.debug:style/Widget.Material3.CardView.Outlined = 0x7f12038e
com.clipsy.android.debug:style/Widget.Material3.CardView.Elevated = 0x7f12038c
com.clipsy.android.debug:style/Widget.Material3.Button.TonalButton = 0x7f120389
com.clipsy.android.debug:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f120388
com.clipsy.android.debug:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f120386
com.clipsy.android.debug:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f120385
com.clipsy.android.debug:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f12037f
com.clipsy.android.debug:style/Widget.Material3.Button.IconButton = 0x7f12037d
com.clipsy.android.debug:style/Widget.Material3.Button.Icon = 0x7f12037c
com.clipsy.android.debug:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f12037b
com.clipsy.android.debug:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f12036e
com.clipsy.android.debug:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f12036d
com.clipsy.android.debug:style/Widget.Material3.ActionMode = 0x7f120369
com.clipsy.android.debug:style/Widget.Material3.ActionBar.Solid = 0x7f120368
com.clipsy.android.debug:style/Widget.Design.TextInputLayout = 0x7f120367
com.clipsy.android.debug:style/Widget.Design.TabLayout = 0x7f120365
com.clipsy.android.debug:style/Widget.Design.Snackbar = 0x7f120364
com.clipsy.android.debug:style/Widget.Design.NavigationView = 0x7f120362
com.clipsy.android.debug:style/Widget.Design.FloatingActionButton = 0x7f120361
com.clipsy.android.debug:style/Widget.Design.CollapsingToolbar = 0x7f120360
com.clipsy.android.debug:style/Widget.Design.BottomSheet.Modal = 0x7f12035f
com.clipsy.android.debug:styleable/SeekBarPreference = 0x7f130093
com.clipsy.android.debug:style/Widget.Design.AppBarLayout = 0x7f12035d
com.clipsy.android.debug:style/Widget.Compat.NotificationActionContainer = 0x7f12035b
com.clipsy.android.debug:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f12035a
com.clipsy.android.debug:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f120358
com.clipsy.android.debug:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f120355
com.clipsy.android.debug:style/Widget.AppCompat.Spinner = 0x7f120353
com.clipsy.android.debug:style/Widget.AppCompat.SeekBar.Discrete = 0x7f120352
com.clipsy.android.debug:style/Widget.AppCompat.SearchView.ActionBar = 0x7f120350
com.clipsy.android.debug:style/Widget.AppCompat.RatingBar.Small = 0x7f12034e
com.clipsy.android.debug:style/Widget.AppCompat.RatingBar.Indicator = 0x7f12034d
com.clipsy.android.debug:style/Widget.AppCompat.RatingBar = 0x7f12034c
com.clipsy.android.debug:style/Widget.AppCompat.PopupMenu = 0x7f120347
com.clipsy.android.debug:style/Widget.AppCompat.ListView.Menu = 0x7f120346
com.clipsy.android.debug:style/Widget.AppCompat.ListView = 0x7f120344
com.clipsy.android.debug:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f120438
com.clipsy.android.debug:style/Widget.AppCompat.Light.SearchView = 0x7f120340
com.clipsy.android.debug:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f12033f
com.clipsy.android.debug:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f120375
com.clipsy.android.debug:style/Widget.AppCompat.Light.PopupMenu = 0x7f12033e
com.clipsy.android.debug:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f12033d
com.clipsy.android.debug:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f12033a
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionButton = 0x7f120335
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f120334
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f120333
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f12032f
com.clipsy.android.debug:style/Widget.AppCompat.DrawerArrowToggle = 0x7f120328
com.clipsy.android.debug:style/Widget.AppCompat.CompoundButton.Switch = 0x7f120327
com.clipsy.android.debug:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f120325
com.clipsy.android.debug:style/Widget.AppCompat.ButtonBar = 0x7f120323
com.clipsy.android.debug:style/Widget.AppCompat.Button.Colored = 0x7f120321
com.clipsy.android.debug:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f12031f
com.clipsy.android.debug:style/Widget.AppCompat.ActionButton.Overflow = 0x7f120319
com.clipsy.android.debug:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f120318
com.clipsy.android.debug:style/Widget.AppCompat.ActionButton = 0x7f120317
com.clipsy.android.debug:style/Widget.AppCompat.ActionBar.TabView = 0x7f120316
com.clipsy.android.debug:styleable/StateSet = 0x7f1300a1
com.clipsy.android.debug:style/Widget.AppCompat.ActionBar.Solid = 0x7f120313
com.clipsy.android.debug:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f12036b
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f120311
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f12030f
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f12030b
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f12030a
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f120309
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f120308
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f120306
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f120304
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f120302
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1202fd
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.Light = 0x7f1202fc
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1202f9
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1202f7
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1202f3
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1202ef
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1202ee
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1202ed
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1202eb
com.clipsy.android.debug:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1202e9
com.clipsy.android.debug:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1202de
com.clipsy.android.debug:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1202db
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f120300
com.clipsy.android.debug:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1202d8
com.clipsy.android.debug:style/Widget.MaterialComponents.TimePicker.Display = 0x7f12047d
com.clipsy.android.debug:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1202d7
com.clipsy.android.debug:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1202cf
com.clipsy.android.debug:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1202cc
com.clipsy.android.debug:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1202ca
com.clipsy.android.debug:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1202c5
com.clipsy.android.debug:style/ThemeOverlay.Material3.Dialog = 0x7f1202c3
com.clipsy.android.debug:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1202c1
com.clipsy.android.debug:style/ThemeOverlay.Material3.Dark = 0x7f1202bf
com.clipsy.android.debug:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f1202bc
com.clipsy.android.debug:style/ThemeOverlay.Material3.Button.TextButton = 0x7f1202ba
com.clipsy.android.debug:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f1202b8
com.clipsy.android.debug:style/ThemeOverlay.Material3.Button.IconButton = 0x7f1202b7
com.clipsy.android.debug:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f1202b3
com.clipsy.android.debug:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f1202af
com.clipsy.android.debug:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f1202ad
com.clipsy.android.debug:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f1202ac
com.clipsy.android.debug:style/ThemeOverlay.Material3 = 0x7f1202aa
com.clipsy.android.debug:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f1202a7
com.clipsy.android.debug:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f1202a5
com.clipsy.android.debug:style/Theme.MaterialComponents.NoActionBar = 0x7f12029e
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f12029c
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f120297
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f120296
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f120295
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f120292
com.clipsy.android.debug:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f12028a
com.clipsy.android.debug:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f120287
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f120284
com.clipsy.android.debug:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1203ac
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f120283
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f120282
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f120280
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f12027f
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f12027e
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f12027a
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f120276
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight = 0x7f120275
com.clipsy.android.debug:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f120272
com.clipsy.android.debug:style/Theme.MaterialComponents = 0x7f120271
com.clipsy.android.debug:style/Theme.Material3.Light.SideSheetDialog = 0x7f120270
com.clipsy.android.debug:style/Theme.Material3.Light.NoActionBar = 0x7f12026f
com.clipsy.android.debug:style/Theme.Material3.Light.DialogWhenLarge = 0x7f12026e
com.clipsy.android.debug:styleable/FloatingActionButton_Behavior_Layout = 0x7f13003c
com.clipsy.android.debug:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f12026d
com.clipsy.android.debug:style/Theme.Material3.DynamicColors.Light = 0x7f120268
com.clipsy.android.debug:style/Theme.Material3.DynamicColors.Dark = 0x7f120266
com.clipsy.android.debug:style/Theme.Material3.DayNight.NoActionBar = 0x7f120264
com.clipsy.android.debug:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f120261
com.clipsy.android.debug:style/Theme.Material3.DayNight.Dialog = 0x7f120260
com.clipsy.android.debug:style/Theme.Material3.Dark.NoActionBar = 0x7f12025c
com.clipsy.android.debug:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f12025a
com.clipsy.android.debug:style/Theme.Material3.Dark.Dialog.Alert = 0x7f120259
com.clipsy.android.debug:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f120257
com.clipsy.android.debug:style/Theme.Design.Light.NoActionBar = 0x7f120254
com.clipsy.android.debug:style/Theme.Design.Light.BottomSheetDialog = 0x7f120253
com.clipsy.android.debug:xml/preferences = 0x7f140003
com.clipsy.android.debug:style/Theme.Design.Light = 0x7f120252
com.clipsy.android.debug:style/Theme.Design.BottomSheetDialog = 0x7f120251
com.clipsy.android.debug:style/Theme.Design = 0x7f120250
com.clipsy.android.debug:style/Theme.Clipsy.Settings = 0x7f12024f
com.clipsy.android.debug:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f12024b
com.clipsy.android.debug:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f12024a
com.clipsy.android.debug:styleable/ActivityNavigator = 0x7f130007
com.clipsy.android.debug:style/Theme.AppCompat.Light.DarkActionBar = 0x7f120247
com.clipsy.android.debug:style/Theme.AppCompat.Light = 0x7f120246
com.clipsy.android.debug:style/Theme.AppCompat.Empty = 0x7f120245
com.clipsy.android.debug:style/Theme.AppCompat.Dialog.MinWidth = 0x7f120243
com.clipsy.android.debug:style/Theme.AppCompat.Dialog.Alert = 0x7f120242
com.clipsy.android.debug:style/Theme.AppCompat.Dialog = 0x7f120241
com.clipsy.android.debug:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f120240
com.clipsy.android.debug:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f12023f
com.clipsy.android.debug:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f12023d
com.clipsy.android.debug:style/Theme.AppCompat.DayNight.Dialog = 0x7f12023c
com.clipsy.android.debug:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f12023b
com.clipsy.android.debug:styleable/ActionMode = 0x7f130004
com.clipsy.android.debug:style/Theme.AppCompat.DayNight = 0x7f12023a
com.clipsy.android.debug:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f120472
com.clipsy.android.debug:style/Theme.AppCompat.CompactMenu = 0x7f120239
com.clipsy.android.debug:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f120237
com.clipsy.android.debug:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f120236
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f120233
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Headline6 = 0x7f12022f
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Headline4 = 0x7f12022d
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Headline3 = 0x7f12022c
com.clipsy.android.debug:styleable/SnackbarLayout = 0x7f130099
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Badge = 0x7f120224
com.clipsy.android.debug:style/TextAppearance.Material3.TitleSmall = 0x7f120223
com.clipsy.android.debug:style/TextAppearance.Material3.SearchView.Prefix = 0x7f120220
com.clipsy.android.debug:style/TextAppearance.Material3.SearchView = 0x7f12021f
com.clipsy.android.debug:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f12021d
com.clipsy.android.debug:style/TextAppearance.Material3.LabelSmall = 0x7f12021c
com.clipsy.android.debug:style/TextAppearance.Material3.HeadlineSmall = 0x7f120219
com.clipsy.android.debug:style/TextAppearance.Material3.HeadlineMedium = 0x7f120218
com.clipsy.android.debug:style/TextAppearance.Material3.ActionBar.Title = 0x7f120210
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f12020e
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f12020d
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f12020b
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f120203
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f120202
com.clipsy.android.debug:style/TextAppearance.Design.Tab = 0x7f1201ff
com.clipsy.android.debug:style/TextAppearance.Design.Hint = 0x7f1201fa
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f120294
com.clipsy.android.debug:style/TextAppearance.Design.HelperText = 0x7f1201f9
com.clipsy.android.debug:style/TextAppearance.Compat.Notification.Title = 0x7f1201f4
com.clipsy.android.debug:style/TextAppearance.Compat.Notification.Info = 0x7f1201f1
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1201ef
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1201ee
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1201ed
com.clipsy.android.debug:styleable/AppCompatTextHelper = 0x7f130013
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1201ea
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.Button = 0x7f1201e6
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1201e3
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1201e2
com.clipsy.android.debug:style/Widget.AppCompat.Button = 0x7f12031d
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1201e1
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1201df
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1201dd
com.clipsy.android.debug:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1201db
com.clipsy.android.debug:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1201d9
com.clipsy.android.debug:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1201d7
com.clipsy.android.debug:style/TextAppearance.AppCompat.Small = 0x7f1201d6
com.clipsy.android.debug:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1201d2
com.clipsy.android.debug:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1201cf
com.clipsy.android.debug:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1201cd
com.clipsy.android.debug:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1201cc
com.clipsy.android.debug:style/TextAppearance.AppCompat.Headline = 0x7f1201c9
com.clipsy.android.debug:style/TextAppearance.AppCompat.Button = 0x7f1201c3
com.clipsy.android.debug:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f120414
com.clipsy.android.debug:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f1201bd
com.clipsy.android.debug:style/TextAppearance.Material3.TitleMedium = 0x7f120222
com.clipsy.android.debug:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f1201bc
com.clipsy.android.debug:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f1201bb
com.clipsy.android.debug:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f1201ba
com.clipsy.android.debug:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f1201b4
com.clipsy.android.debug:style/Widget.Material3.AppBarLayout = 0x7f12036a
com.clipsy.android.debug:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f1201b2
com.clipsy.android.debug:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f1201b1
com.clipsy.android.debug:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f1201b0
com.clipsy.android.debug:style/ShapeAppearanceOverlay.Material3.Button = 0x7f1201ad
com.clipsy.android.debug:style/ShapeAppearanceOverlay.Clipsy.FloatingActionButton = 0x7f1201ac
com.clipsy.android.debug:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f1201a9
com.clipsy.android.debug:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f1201a8
com.clipsy.android.debug:style/ShapeAppearance.Material3.SmallComponent = 0x7f1201a4
com.clipsy.android.debug:style/Widget.Material3.Button.TextButton.Icon = 0x7f120387
com.clipsy.android.debug:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f1201a3
com.clipsy.android.debug:style/ShapeAppearance.Material3.MediumComponent = 0x7f1201a2
com.clipsy.android.debug:style/ShapeAppearance.Material3.Corner.Small = 0x7f1201a0
com.clipsy.android.debug:style/ShapeAppearance.Material3.Corner.None = 0x7f12019f
com.clipsy.android.debug:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f12019b
com.clipsy.android.debug:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f12019a
com.clipsy.android.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f120199
com.clipsy.android.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f120197
com.clipsy.android.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f120196
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f120190
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1201e0
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f12018c
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f12018b
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f120186
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f120184
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f120183
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f120182
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f120181
com.clipsy.android.debug:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f12017f
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f12017c
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f12017b
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f120179
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f120174
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f120172
com.clipsy.android.debug:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f120170
com.clipsy.android.debug:style/PreferenceThemeOverlay.v14.Material = 0x7f12016f
com.clipsy.android.debug:style/PreferenceThemeOverlay.Clipsy = 0x7f12016d
com.clipsy.android.debug:style/PreferenceSummaryTextStyle = 0x7f12016b
com.clipsy.android.debug:style/PreferenceFragment = 0x7f120167
com.clipsy.android.debug:style/PreferenceCategoryTitleTextStyle = 0x7f120166
com.clipsy.android.debug:style/Preference.SwitchPreferenceCompat.Material = 0x7f120165
com.clipsy.android.debug:style/Preference.SwitchPreference.Material = 0x7f120163
com.clipsy.android.debug:style/Preference.SeekBarPreference.Material = 0x7f120161
com.clipsy.android.debug:style/Preference.Information.Material = 0x7f12015c
com.clipsy.android.debug:style/Preference.DropDown.Material = 0x7f12015a
com.clipsy.android.debug:style/Preference.DialogPreference.Material = 0x7f120158
com.clipsy.android.debug:style/Preference.DialogPreference.EditTextPreference = 0x7f120156
com.clipsy.android.debug:style/Preference.DialogPreference = 0x7f120155
com.clipsy.android.debug:style/Preference.Category.Material = 0x7f120152
com.clipsy.android.debug:style/Preference.Category = 0x7f120151
com.clipsy.android.debug:style/Widget.AppCompat.Spinner.Underlined = 0x7f120356
com.clipsy.android.debug:style/Platform.Widget.AppCompat.Spinner = 0x7f12014f
com.clipsy.android.debug:style/Platform.V25.AppCompat.Light = 0x7f12014e
com.clipsy.android.debug:style/Platform.V25.AppCompat = 0x7f12014d
com.clipsy.android.debug:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f120149
com.clipsy.android.debug:style/Platform.ThemeOverlay.AppCompat = 0x7f120148
com.clipsy.android.debug:style/Platform.MaterialComponents.Light = 0x7f120146
com.clipsy.android.debug:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f12039d
com.clipsy.android.debug:style/Platform.MaterialComponents = 0x7f120144
com.clipsy.android.debug:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1203d6
com.clipsy.android.debug:style/Platform.AppCompat = 0x7f120142
com.clipsy.android.debug:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f120141
com.clipsy.android.debug:style/TextAppearance.Design.Suffix = 0x7f1201fe
com.clipsy.android.debug:style/Preference.DialogPreference.EditTextPreference.Material = 0x7f120157
com.clipsy.android.debug:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f12013a
com.clipsy.android.debug:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f120139
com.clipsy.android.debug:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f120137
com.clipsy.android.debug:style/MaterialAlertDialog.Material3.Title.Text = 0x7f120136
com.clipsy.android.debug:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f120131
com.clipsy.android.debug:style/MaterialAlertDialog.Material3.Body.Text = 0x7f120130
com.clipsy.android.debug:style/CardView = 0x7f12012b
com.clipsy.android.debug:style/Button.Clipsy.Success = 0x7f120129
com.clipsy.android.debug:style/Button.Clipsy.Small = 0x7f120128
com.clipsy.android.debug:style/Button.Clipsy.Primary = 0x7f120126
com.clipsy.android.debug:style/Button.Clipsy.Danger = 0x7f120125
com.clipsy.android.debug:style/BasePreferenceThemeOverlay = 0x7f120123
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.TextView = 0x7f120122
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.Snackbar = 0x7f12011f
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.Slider = 0x7f12011e
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f12011b
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f12011a
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f120119
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.Chip = 0x7f120117
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f120115
com.clipsy.android.debug:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f120113
com.clipsy.android.debug:style/Base.Widget.Material3.Snackbar = 0x7f120111
com.clipsy.android.debug:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f120110
com.clipsy.android.debug:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f12010f
com.clipsy.android.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f120195
com.clipsy.android.debug:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f12010d
com.clipsy.android.debug:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f120109
com.clipsy.android.debug:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f120108
com.clipsy.android.debug:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f120107
com.clipsy.android.debug:style/Base.Widget.Material3.Chip = 0x7f120105
com.clipsy.android.debug:style/Base.Widget.Material3.CardView = 0x7f120104
com.clipsy.android.debug:style/Base.Widget.Material3.ActionMode = 0x7f120102
com.clipsy.android.debug:style/Base.Widget.AppCompat.TextView = 0x7f1200fc
com.clipsy.android.debug:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1200fb
com.clipsy.android.debug:style/Base.Widget.AppCompat.Spinner = 0x7f1200fa
com.clipsy.android.debug:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1200f9
com.clipsy.android.debug:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1200f4
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1203cd
com.clipsy.android.debug:style/Base.Widget.AppCompat.PopupWindow = 0x7f1200f0
com.clipsy.android.debug:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1200ea
com.clipsy.android.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1200e6
com.clipsy.android.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1200e4
com.clipsy.android.debug:style/Widget.MaterialComponents.ChipGroup = 0x7f120432
com.clipsy.android.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1200e3
com.clipsy.android.debug:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1200e2
com.clipsy.android.debug:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1200d9
com.clipsy.android.debug:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1200d8
com.clipsy.android.debug:style/Base.Widget.AppCompat.Button.Small = 0x7f1200d6
com.clipsy.android.debug:style/Base.Widget.AppCompat.Button.Colored = 0x7f1200d5
com.clipsy.android.debug:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1200d3
com.clipsy.android.debug:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1200d2
com.clipsy.android.debug:style/Base.Widget.AppCompat.Button = 0x7f1200d1
com.clipsy.android.debug:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1200cf
com.clipsy.android.debug:style/CardView.Light = 0x7f12012d
com.clipsy.android.debug:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1200cd
com.clipsy.android.debug:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1200cc
com.clipsy.android.debug:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1200c9
com.clipsy.android.debug:style/Theme.Material3.Light.Dialog.Alert = 0x7f12026c
com.clipsy.android.debug:style/Base.Widget.AppCompat.ActionBar = 0x7f1200c6
com.clipsy.android.debug:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1200c5
com.clipsy.android.debug:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1200c1
com.clipsy.android.debug:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1200bf
com.clipsy.android.debug:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f12041c
com.clipsy.android.debug:style/TextAppearance.AppCompat.Body2 = 0x7f1201c2
com.clipsy.android.debug:style/Base.V28.Theme.AppCompat.Light = 0x7f1200bd
com.clipsy.android.debug:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1200bb
com.clipsy.android.debug:style/Base.V26.Theme.AppCompat.Light = 0x7f1200ba
com.clipsy.android.debug:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1200b6
com.clipsy.android.debug:style/Base.V23.Theme.AppCompat.Light = 0x7f1200b4
com.clipsy.android.debug:style/Base.V23.Theme.AppCompat = 0x7f1200b3
com.clipsy.android.debug:style/Base.V22.Theme.AppCompat.Light = 0x7f1200b2
com.clipsy.android.debug:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1200b0
com.clipsy.android.debug:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1200af
com.clipsy.android.debug:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1200ad
com.clipsy.android.debug:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1200aa
com.clipsy.android.debug:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1200a8
com.clipsy.android.debug:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1200a2
com.clipsy.android.debug:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f1200a1
com.clipsy.android.debug:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f120326
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Body2 = 0x7f120226
com.clipsy.android.debug:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f12009e
com.clipsy.android.debug:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f12009d
com.clipsy.android.debug:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f12009c
com.clipsy.android.debug:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f12009a
com.clipsy.android.debug:style/Base.V14.Theme.MaterialComponents.Light = 0x7f120099
com.clipsy.android.debug:style/Widget.Material3.Button.IconButton.Outlined = 0x7f120380
com.clipsy.android.debug:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f120096
com.clipsy.android.debug:style/Base.V14.Theme.MaterialComponents = 0x7f120095
com.clipsy.android.debug:style/Base.V14.Theme.Material3.Light = 0x7f120091
com.clipsy.android.debug:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f12008e
com.clipsy.android.debug:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f12008c
com.clipsy.android.debug:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f120089
com.clipsy.android.debug:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f120088
com.clipsy.android.debug:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f120087
com.clipsy.android.debug:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f120086
com.clipsy.android.debug:style/Base.ThemeOverlay.Material3.Dialog = 0x7f120085
com.clipsy.android.debug:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f120081
com.clipsy.android.debug:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f120080
com.clipsy.android.debug:style/Base.ThemeOverlay.AppCompat = 0x7f12007c
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f120079
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f120077
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f120071
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f120070
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f12006b
com.clipsy.android.debug:style/TextAppearance.AppCompat.Body1 = 0x7f1201c1
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Bridge = 0x7f12006a
com.clipsy.android.debug:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f120067
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f120279
com.clipsy.android.debug:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f120062
com.clipsy.android.debug:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f120060
com.clipsy.android.debug:style/Base.Theme.Material3.Dark.Dialog = 0x7f12005f
com.clipsy.android.debug:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f12005e
com.clipsy.android.debug:style/Base.Theme.Material3.Dark = 0x7f12005d
com.clipsy.android.debug:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f13003a
com.clipsy.android.debug:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f12005c
com.clipsy.android.debug:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f12005b
com.clipsy.android.debug:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f12005a
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f120452
com.clipsy.android.debug:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f120057
com.clipsy.android.debug:style/Base.Theme.AppCompat.Light = 0x7f120056
com.clipsy.android.debug:style/Base.Theme.AppCompat.CompactMenu = 0x7f120050
com.clipsy.android.debug:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f12004e
com.clipsy.android.debug:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f12004a
com.clipsy.android.debug:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f120048
com.clipsy.android.debug:styleable/FloatingActionButton = 0x7f13003b
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.Year = 0x7f1203d1
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f120043
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f120042
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f120041
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f12003f
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f12003d
com.clipsy.android.debug:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f120404
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f12003a
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f120039
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f120034
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f12002e
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f12002d
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f12002a
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f120027
com.clipsy.android.debug:style/Base.Widget.AppCompat.ListView = 0x7f1200eb
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f120026
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f12020a
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Large = 0x7f120025
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Display1 = 0x7f12001f
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Caption = 0x7f12001e
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Button = 0x7f12001d
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat = 0x7f12001a
com.clipsy.android.debug:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f120019
com.clipsy.android.debug:style/Base.CardView = 0x7f120014
com.clipsy.android.debug:style/Base.Animation.AppCompat.Tooltip = 0x7f120013
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Body1 = 0x7f120225
com.clipsy.android.debug:style/ShapeAppearance.Material3.Tooltip = 0x7f1201a5
com.clipsy.android.debug:style/Base.Animation.AppCompat.DropDownUp = 0x7f120012
com.clipsy.android.debug:style/Base.Animation.AppCompat.Dialog = 0x7f120011
com.clipsy.android.debug:style/Base.AlertDialog.AppCompat = 0x7f12000f
com.clipsy.android.debug:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f12000e
com.clipsy.android.debug:style/Animation.Material3.SideSheetDialog.Right = 0x7f12000d
com.clipsy.android.debug:style/Animation.Material3.SideSheetDialog.Left = 0x7f12000c
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f12027c
com.clipsy.android.debug:style/Animation.Design.BottomSheetDialog = 0x7f120009
com.clipsy.android.debug:style/Animation.AppCompat.DropDownUp = 0x7f120007
com.clipsy.android.debug:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f120053
com.clipsy.android.debug:style/AlertDialog.Clipsy.Title = 0x7f120005
com.clipsy.android.debug:string/yes = 0x7f110100
com.clipsy.android.debug:string/v7_preference_off = 0x7f1100fe
com.clipsy.android.debug:string/summary_collapsed_preference_list = 0x7f1100fd
com.clipsy.android.debug:string/status_service_running = 0x7f1100fb
com.clipsy.android.debug:string/status_discovering = 0x7f1100fa
com.clipsy.android.debug:string/status_disconnected = 0x7f1100f9
com.clipsy.android.debug:string/status_connected = 0x7f1100f8
com.clipsy.android.debug:string/status_bar_notification_info_overflow = 0x7f1100f7
com.clipsy.android.debug:string/side_sheet_accessibility_pane_title = 0x7f1100f5
com.clipsy.android.debug:string/settings_title = 0x7f1100f3
com.clipsy.android.debug:styleable/MaterialSwitch = 0x7f130063
com.clipsy.android.debug:string/settings_sync_enabled_summary = 0x7f1100f2
com.clipsy.android.debug:string/settings_sync_enabled = 0x7f1100f1
com.clipsy.android.debug:string/settings_network = 0x7f1100ef
com.clipsy.android.debug:string/settings_history_limit_summary = 0x7f1100ee
com.clipsy.android.debug:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f12033c
com.clipsy.android.debug:string/settings_history_limit = 0x7f1100ed
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f120032
com.clipsy.android.debug:string/settings_history_enabled = 0x7f1100eb
com.clipsy.android.debug:styleable/SearchView = 0x7f130092
com.clipsy.android.debug:string/settings_clipboard = 0x7f1100e8
com.clipsy.android.debug:string/settings_auto_start_summary = 0x7f1100e7
com.clipsy.android.debug:string/settings_auto_start = 0x7f1100e6
com.clipsy.android.debug:string/settings_auto_discovery_summary = 0x7f1100e5
com.clipsy.android.debug:style/TextAppearance.Compat.Notification.Line2 = 0x7f1201f2
com.clipsy.android.debug:string/settings_auto_discovery = 0x7f1100e4
com.clipsy.android.debug:string/searchbar_scrolling_view_behavior = 0x7f1100e1
com.clipsy.android.debug:string/search_menu_title = 0x7f1100e0
com.clipsy.android.debug:string/pref_sync_interval_title = 0x7f1100dc
com.clipsy.android.debug:string/pref_sync_interval_summary = 0x7f1100db
com.clipsy.android.debug:string/pref_history_limit_title = 0x7f1100da
com.clipsy.android.debug:string/pref_discovery_port_title = 0x7f1100d8
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionBar = 0x7f12032c
com.clipsy.android.debug:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1200dd
com.clipsy.android.debug:string/pref_discovery_port_summary = 0x7f1100d7
com.clipsy.android.debug:string/pref_category_sync = 0x7f1100d6
com.clipsy.android.debug:string/pref_category_network = 0x7f1100d5
com.clipsy.android.debug:string/pref_category_history = 0x7f1100d4
com.clipsy.android.debug:string/pref_category_about = 0x7f1100d3
com.clipsy.android.debug:string/pref_auto_sync_title = 0x7f1100d2
com.clipsy.android.debug:string/pref_app_version_title = 0x7f1100ce
com.clipsy.android.debug:string/path_password_eye_mask_strike_through = 0x7f1100cb
com.clipsy.android.debug:string/path_password_eye = 0x7f1100ca
com.clipsy.android.debug:string/password_toggle_content_description = 0x7f1100c9
com.clipsy.android.debug:string/searchview_clear_text_content_description = 0x7f1100e2
com.clipsy.android.debug:string/ok = 0x7f1100c8
com.clipsy.android.debug:string/nav_devices = 0x7f1100c0
com.clipsy.android.debug:string/mtrl_switch_track_path = 0x7f1100bb
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f120444
com.clipsy.android.debug:string/mtrl_switch_thumb_path_unchecked = 0x7f1100b9
com.clipsy.android.debug:string/mtrl_switch_thumb_path_pressed = 0x7f1100b8
com.clipsy.android.debug:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f120066
com.clipsy.android.debug:string/mtrl_switch_thumb_path_checked = 0x7f1100b5
com.clipsy.android.debug:string/mtrl_switch_thumb_group_name = 0x7f1100b4
com.clipsy.android.debug:string/mtrl_picker_toggle_to_year_selection = 0x7f1100b3
com.clipsy.android.debug:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f120098
com.clipsy.android.debug:string/mtrl_picker_toggle_to_text_input_mode = 0x7f1100b2
com.clipsy.android.debug:string/mtrl_picker_toggle_to_day_selection = 0x7f1100b1
com.clipsy.android.debug:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f1100b0
com.clipsy.android.debug:string/mtrl_picker_today_description = 0x7f1100af
com.clipsy.android.debug:string/mtrl_picker_text_input_month_abbr = 0x7f1100ad
com.clipsy.android.debug:string/mtrl_picker_text_input_day_abbr = 0x7f1100ac
com.clipsy.android.debug:string/mtrl_picker_text_input_date_range_start_hint = 0x7f1100ab
com.clipsy.android.debug:styleable/MotionEffect = 0x7f13006d
com.clipsy.android.debug:string/mtrl_picker_text_input_date_range_end_hint = 0x7f1100aa
com.clipsy.android.debug:string/mtrl_picker_start_date_description = 0x7f1100a8
com.clipsy.android.debug:string/mtrl_picker_save = 0x7f1100a7
com.clipsy.android.debug:string/mtrl_picker_range_header_only_start_selected = 0x7f1100a3
com.clipsy.android.debug:string/mtrl_picker_out_of_range = 0x7f1100a1
com.clipsy.android.debug:string/mtrl_picker_navigate_to_year_description = 0x7f1100a0
com.clipsy.android.debug:string/mtrl_picker_navigate_to_current_year_description = 0x7f11009f
com.clipsy.android.debug:string/mtrl_picker_invalid_format_use = 0x7f11009d
com.clipsy.android.debug:string/mtrl_picker_invalid_format_example = 0x7f11009c
com.clipsy.android.debug:string/mtrl_picker_end_date_description = 0x7f11009a
com.clipsy.android.debug:string/mtrl_picker_date_header_title = 0x7f110097
com.clipsy.android.debug:string/mtrl_picker_date_header_selected = 0x7f110096
com.clipsy.android.debug:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1202d5
com.clipsy.android.debug:string/mtrl_picker_announce_current_selection = 0x7f110092
com.clipsy.android.debug:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f120423
com.clipsy.android.debug:style/Base.Theme.AppCompat.Light.Dialog = 0x7f120058
com.clipsy.android.debug:string/mtrl_picker_announce_current_range_selection = 0x7f110091
com.clipsy.android.debug:style/Widget.Material3.BottomSheet = 0x7f120376
com.clipsy.android.debug:string/mtrl_exceed_max_badge_number_content_description = 0x7f11008d
com.clipsy.android.debug:string/mtrl_checkbox_state_description_unchecked = 0x7f11008b
com.clipsy.android.debug:string/mtrl_checkbox_button_path_unchecked = 0x7f110088
com.clipsy.android.debug:string/mtrl_checkbox_button_path_name = 0x7f110087
com.clipsy.android.debug:string/mtrl_checkbox_button_path_group_name = 0x7f110086
com.clipsy.android.debug:string/mtrl_checkbox_button_path_checked = 0x7f110085
com.clipsy.android.debug:string/mtrl_checkbox_button_icon_path_group_name = 0x7f110082
com.clipsy.android.debug:string/material_timepicker_text_input_mode_description = 0x7f11007f
com.clipsy.android.debug:string/material_timepicker_select_time = 0x7f11007e
com.clipsy.android.debug:string/material_timepicker_pm = 0x7f11007d
com.clipsy.android.debug:string/material_timepicker_minute = 0x7f11007c
com.clipsy.android.debug:string/material_timepicker_hour = 0x7f11007b
com.clipsy.android.debug:string/material_timepicker_clock_mode_description = 0x7f11007a
com.clipsy.android.debug:string/material_timepicker_am = 0x7f110079
com.clipsy.android.debug:string/material_slider_range_start = 0x7f110077
com.clipsy.android.debug:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f1202a3
com.clipsy.android.debug:string/material_motion_easing_standard = 0x7f110075
com.clipsy.android.debug:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f120415
com.clipsy.android.debug:string/material_motion_easing_emphasized = 0x7f110073
com.clipsy.android.debug:string/material_motion_easing_decelerated = 0x7f110072
com.clipsy.android.debug:string/material_minute_suffix = 0x7f110070
com.clipsy.android.debug:string/material_hour_selection = 0x7f11006d
com.clipsy.android.debug:string/m3_sys_motion_easing_standard = 0x7f110067
com.clipsy.android.debug:string/m3_sys_motion_easing_legacy_accelerate = 0x7f110064
com.clipsy.android.debug:string/m3_sys_motion_easing_emphasized_path_data = 0x7f110062
com.clipsy.android.debug:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f110061
com.clipsy.android.debug:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f110060
com.clipsy.android.debug:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1203d9
com.clipsy.android.debug:string/m3_sys_motion_easing_emphasized = 0x7f11005f
com.clipsy.android.debug:string/m3_ref_typeface_plain_medium = 0x7f11005d
com.clipsy.android.debug:style/Widget.Material3.Button.UnelevatedButton = 0x7f12038b
com.clipsy.android.debug:string/m3_ref_typeface_brand_regular = 0x7f11005c
com.clipsy.android.debug:string/m3_ref_typeface_brand_medium = 0x7f11005b
com.clipsy.android.debug:style/ShapeAppearance.MaterialComponents.Badge = 0x7f1201a7
com.clipsy.android.debug:string/m3_exceed_max_badge_text_suffix = 0x7f11005a
com.clipsy.android.debug:string/icon_content_description = 0x7f110058
com.clipsy.android.debug:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1202c8
com.clipsy.android.debug:string/history_item_copy = 0x7f110055
com.clipsy.android.debug:string/history_empty = 0x7f110054
com.clipsy.android.debug:string/hide_bottom_view_on_scroll_behavior = 0x7f110051
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f12006f
com.clipsy.android.debug:string/fab_transformation_scrim_behavior = 0x7f11004f
com.clipsy.android.debug:string/error_permission_denied = 0x7f11004c
com.clipsy.android.debug:string/error_network_unavailable = 0x7f11004b
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1201e5
com.clipsy.android.debug:string/error_icon_content_description = 0x7f110049
com.clipsy.android.debug:string/error_connection_failed = 0x7f110048
com.clipsy.android.debug:string/error_a11y_label = 0x7f110047
com.clipsy.android.debug:string/devices_title = 0x7f110046
com.clipsy.android.debug:style/CardView.Dark = 0x7f12012c
com.clipsy.android.debug:string/devices_empty = 0x7f110045
com.clipsy.android.debug:string/device_disconnect = 0x7f110041
com.clipsy.android.debug:string/device_connected = 0x7f110040
com.clipsy.android.debug:string/device_connect = 0x7f11003f
com.clipsy.android.debug:string/device_add_manual = 0x7f11003e
com.clipsy.android.debug:string/dest_title = 0x7f11003d
com.clipsy.android.debug:string/delete = 0x7f11003c
com.clipsy.android.debug:string/copy = 0x7f11003b
com.clipsy.android.debug:string/clear_text_end_icon_content_description = 0x7f11003a
com.clipsy.android.debug:string/character_counter_overflowed_content_description = 0x7f110038
com.clipsy.android.debug:style/Theme.AppCompat.Light.NoActionBar = 0x7f12024c
com.clipsy.android.debug:string/cancel = 0x7f110036
com.clipsy.android.debug:string/call_notification_incoming_text = 0x7f110033
com.clipsy.android.debug:string/call_notification_hang_up_action = 0x7f110032
com.clipsy.android.debug:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f12046b
com.clipsy.android.debug:string/call_notification_answer_video_action = 0x7f110030
com.clipsy.android.debug:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1202e4
com.clipsy.android.debug:string/bottomsheet_drag_handle_content_description = 0x7f11002e
com.clipsy.android.debug:string/bottomsheet_drag_handle_clicked = 0x7f11002d
com.clipsy.android.debug:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f120412
com.clipsy.android.debug:string/bottomsheet_action_expand = 0x7f11002b
com.clipsy.android.debug:string/bottomsheet_action_collapse = 0x7f11002a
com.clipsy.android.debug:style/TextAppearance.Material3.DisplayMedium = 0x7f120215
com.clipsy.android.debug:string/bottom_sheet_behavior = 0x7f110029
com.clipsy.android.debug:string/appbar_scrolling_view_behavior = 0x7f110028
com.clipsy.android.debug:string/app_version = 0x7f110027
com.clipsy.android.debug:string/androidx_startup = 0x7f110025
com.clipsy.android.debug:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f120329
com.clipsy.android.debug:string/action_remove = 0x7f110021
com.clipsy.android.debug:string/action_refresh = 0x7f110020
com.clipsy.android.debug:string/action_pair = 0x7f11001f
com.clipsy.android.debug:string/action_delete = 0x7f11001e
com.clipsy.android.debug:string/action_clear_history = 0x7f11001b
com.clipsy.android.debug:styleable/ListPreference = 0x7f130055
com.clipsy.android.debug:string/abc_shareactionprovider_share_with_application = 0x7f110019
com.clipsy.android.debug:string/abc_shareactionprovider_share_with = 0x7f110018
com.clipsy.android.debug:string/abc_searchview_description_search = 0x7f110015
com.clipsy.android.debug:string/abc_prepend_shortcut_label = 0x7f110011
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar = 0x7f1203be
com.clipsy.android.debug:string/abc_menu_sym_shortcut_label = 0x7f110010
com.clipsy.android.debug:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1202d4
com.clipsy.android.debug:style/Theme.AppCompat.NoActionBar = 0x7f12024d
com.clipsy.android.debug:style/Base.V24.Theme.Material3.Light = 0x7f1200b7
com.clipsy.android.debug:string/abc_menu_meta_shortcut_label = 0x7f11000d
com.clipsy.android.debug:string/abc_menu_function_shortcut_label = 0x7f11000c
com.clipsy.android.debug:string/abc_menu_alt_shortcut_label = 0x7f110008
com.clipsy.android.debug:string/abc_capital_off = 0x7f110006
com.clipsy.android.debug:string/abc_activity_chooser_view_see_all = 0x7f110004
com.clipsy.android.debug:string/abc_action_mode_done = 0x7f110003
com.clipsy.android.debug:string/abc_action_menu_overflow_description = 0x7f110002
com.clipsy.android.debug:string/abc_action_bar_up_description = 0x7f110001
com.clipsy.android.debug:menu/menu_history = 0x7f0e0002
com.clipsy.android.debug:menu/main_menu = 0x7f0e0001
com.clipsy.android.debug:menu/bottom_navigation_menu = 0x7f0e0000
com.clipsy.android.debug:style/Base.Widget.AppCompat.PopupMenu = 0x7f1200ee
com.clipsy.android.debug:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0d0174
com.clipsy.android.debug:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0d0173
com.clipsy.android.debug:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0d0172
com.clipsy.android.debug:macro/m3_comp_top_app_bar_small_container_color = 0x7f0d0170
com.clipsy.android.debug:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0d016f
com.clipsy.android.debug:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0d016e
com.clipsy.android.debug:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0d016d
com.clipsy.android.debug:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0d016c
com.clipsy.android.debug:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1202ce
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0d0169
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0d0166
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0d0165
com.clipsy.android.debug:style/TextAppearance.AppCompat.Display1 = 0x7f1201c5
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0d0164
com.clipsy.android.debug:style/Base.Widget.AppCompat.ListMenuView = 0x7f1200e9
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0d0163
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0d0161
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0d0160
com.clipsy.android.debug:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0d015e
com.clipsy.android.debug:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f120410
com.clipsy.android.debug:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0d015b
com.clipsy.android.debug:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0d015a
com.clipsy.android.debug:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0d0157
com.clipsy.android.debug:style/Widget.Material3.SideSheet.Detached = 0x7f1203f0
com.clipsy.android.debug:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0d0154
com.clipsy.android.debug:macro/m3_comp_time_picker_headline_type = 0x7f0d0152
com.clipsy.android.debug:macro/m3_comp_time_picker_headline_color = 0x7f0d0151
com.clipsy.android.debug:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0d014c
com.clipsy.android.debug:style/Theme.Clipsy = 0x7f12024e
com.clipsy.android.debug:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0d014b
com.clipsy.android.debug:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0d014a
com.clipsy.android.debug:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0d0144
com.clipsy.android.debug:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0d0143
com.clipsy.android.debug:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0d013f
com.clipsy.android.debug:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0d013d
com.clipsy.android.debug:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0d013c
com.clipsy.android.debug:style/TextAppearance.Design.Counter.Overflow = 0x7f1201f7
com.clipsy.android.debug:macro/m3_comp_switch_unselected_icon_color = 0x7f0d013b
com.clipsy.android.debug:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0d0139
com.clipsy.android.debug:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0d0138
com.clipsy.android.debug:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0d0136
com.clipsy.android.debug:style/Widget.MaterialComponents.PopupMenu = 0x7f120460
com.clipsy.android.debug:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0d0134
com.clipsy.android.debug:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0d0133
com.clipsy.android.debug:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0d0132
com.clipsy.android.debug:macro/m3_comp_switch_selected_track_color = 0x7f0d012f
com.clipsy.android.debug:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0d012d
com.clipsy.android.debug:styleable/MaterialCheckBox = 0x7f13005e
com.clipsy.android.debug:style/PreferenceThemeOverlay.v14 = 0x7f12016e
com.clipsy.android.debug:string/mtrl_picker_day_of_week_column_header = 0x7f110099
com.clipsy.android.debug:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0d012c
com.clipsy.android.debug:macro/m3_comp_switch_selected_icon_color = 0x7f0d012a
com.clipsy.android.debug:macro/m3_comp_switch_selected_hover_track_color = 0x7f0d0129
com.clipsy.android.debug:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0d0128
com.clipsy.android.debug:macro/m3_comp_switch_selected_focus_track_color = 0x7f0d0124
com.clipsy.android.debug:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0d0122
com.clipsy.android.debug:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0d0120
com.clipsy.android.debug:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0d011f
com.clipsy.android.debug:style/Widget.AppCompat.ActionBar = 0x7f120312
com.clipsy.android.debug:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0d011e
com.clipsy.android.debug:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0d011c
com.clipsy.android.debug:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0d0119
com.clipsy.android.debug:macro/m3_comp_snackbar_supporting_text_color = 0x7f0d0116
com.clipsy.android.debug:macro/m3_comp_snackbar_container_color = 0x7f0d0114
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f12029a
com.clipsy.android.debug:macro/m3_comp_slider_handle_color = 0x7f0d0110
com.clipsy.android.debug:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0d010f
com.clipsy.android.debug:macro/m3_comp_slider_disabled_handle_color = 0x7f0d010e
com.clipsy.android.debug:macro/m3_comp_sheet_side_docked_container_color = 0x7f0d010a
com.clipsy.android.debug:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0d0109
com.clipsy.android.debug:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0d0108
com.clipsy.android.debug:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0d0107
com.clipsy.android.debug:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0d0104
com.clipsy.android.debug:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0d0103
com.clipsy.android.debug:style/Widget.AppCompat.EditText = 0x7f12032a
com.clipsy.android.debug:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0d0102
com.clipsy.android.debug:string/pref_websocket_port_title = 0x7f1100de
com.clipsy.android.debug:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0d0101
com.clipsy.android.debug:macro/m3_comp_search_view_divider_color = 0x7f0d00f4
com.clipsy.android.debug:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0d00f1
com.clipsy.android.debug:macro/m3_comp_search_bar_supporting_text_type = 0x7f0d00f0
com.clipsy.android.debug:macro/m3_comp_search_bar_supporting_text_color = 0x7f0d00ef
com.clipsy.android.debug:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0d00ee
com.clipsy.android.debug:style/TextAppearance.Material3.DisplayLarge = 0x7f120214
com.clipsy.android.debug:macro/m3_comp_search_bar_input_text_color = 0x7f0d00ea
com.clipsy.android.debug:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0d00e4
com.clipsy.android.debug:styleable/ScrimInsetsFrameLayout = 0x7f13008f
com.clipsy.android.debug:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0d00de
com.clipsy.android.debug:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0d00dd
com.clipsy.android.debug:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0d00d8
com.clipsy.android.debug:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0d00d6
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0d00d5
com.clipsy.android.debug:string/mtrl_checkbox_button_icon_path_name = 0x7f110084
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0d00d2
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0d00d0
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0d00ce
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0d00cc
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0d00ca
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0d00c9
com.clipsy.android.debug:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0d00c8
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0d00c6
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_outline_color = 0x7f0d00c5
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0d00c4
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0d00c3
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0d00c1
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0d00bc
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Chip = 0x7f120229
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0d00ba
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0d00b8
com.clipsy.android.debug:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1202da
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0d00b7
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0d00b6
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_container_shape = 0x7f0d00b3
com.clipsy.android.debug:styleable/ClockFaceView = 0x7f130025
com.clipsy.android.debug:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1203de
com.clipsy.android.debug:macro/m3_comp_outlined_card_outline_color = 0x7f0d00b0
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f12032d
com.clipsy.android.debug:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0d00af
com.clipsy.android.debug:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0d00ae
com.clipsy.android.debug:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0d00ad
com.clipsy.android.debug:macro/m3_comp_outlined_card_container_shape = 0x7f0d00ab
com.clipsy.android.debug:macro/m3_comp_outlined_card_container_color = 0x7f0d00aa
com.clipsy.android.debug:macro/m3_comp_outlined_button_outline_color = 0x7f0d00a8
com.clipsy.android.debug:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0d00a6
com.clipsy.android.debug:style/ShapeAppearance.Material3.Corner.Full = 0x7f12019c
com.clipsy.android.debug:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0d00a4
com.clipsy.android.debug:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0d00a3
com.clipsy.android.debug:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0d00a0
com.clipsy.android.debug:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0d009f
com.clipsy.android.debug:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0d009e
com.clipsy.android.debug:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0d009c
com.clipsy.android.debug:macro/m3_comp_navigation_rail_container_color = 0x7f0d009b
com.clipsy.android.debug:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0d0098
com.clipsy.android.debug:style/Base.Widget.Design.TabLayout = 0x7f120100
com.clipsy.android.debug:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0d0097
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0d0094
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0d0091
com.clipsy.android.debug:style/TextAppearance.AppCompat.Inverse = 0x7f1201ca
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0d008f
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0d008d
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0d008c
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0d008a
com.clipsy.android.debug:styleable/ShapeableImageView = 0x7f130095
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0d0089
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_headline_type = 0x7f0d0088
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0d0084
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0d0083
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Dialog = 0x7f12006c
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0d0081
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0d007f
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0d007d
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0d007c
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0d007a
com.clipsy.android.debug:macro/m3_comp_navigation_bar_label_text_type = 0x7f0d0079
com.clipsy.android.debug:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0d0078
com.clipsy.android.debug:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0d0074
com.clipsy.android.debug:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0d0072
com.clipsy.android.debug:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0d0073
com.clipsy.android.debug:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0d0071
com.clipsy.android.debug:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0d006f
com.clipsy.android.debug:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0d006b
com.clipsy.android.debug:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0d0068
com.clipsy.android.debug:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0d0062
com.clipsy.android.debug:macro/m3_comp_linear_progress_indicator_track_color = 0x7f0d005f
com.clipsy.android.debug:macro/m3_comp_input_chip_label_text_type = 0x7f0d005d
com.clipsy.android.debug:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1200ca
com.clipsy.android.debug:macro/m3_comp_icon_button_selected_icon_color = 0x7f0d005a
com.clipsy.android.debug:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0d0057
com.clipsy.android.debug:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0d0056
com.clipsy.android.debug:style/Widget.AppCompat.Button.Borderless = 0x7f12031e
com.clipsy.android.debug:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0d0054
com.clipsy.android.debug:macro/m3_comp_filled_text_field_input_text_type = 0x7f0d0051
com.clipsy.android.debug:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0d0123
com.clipsy.android.debug:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0d004f
com.clipsy.android.debug:macro/m3_comp_filled_text_field_container_shape = 0x7f0d004d
com.clipsy.android.debug:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0d004b
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f12027b
com.clipsy.android.debug:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0d004a
com.clipsy.android.debug:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f1201ae
com.clipsy.android.debug:macro/m3_comp_filled_icon_button_container_color = 0x7f0d0049
com.clipsy.android.debug:macro/m3_comp_filled_card_container_color = 0x7f0d0047
com.clipsy.android.debug:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0d0121
com.clipsy.android.debug:macro/m3_comp_filled_button_label_text_color = 0x7f0d0045
com.clipsy.android.debug:macro/m3_comp_filled_button_container_color = 0x7f0d0044
com.clipsy.android.debug:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0d0043
com.clipsy.android.debug:macro/m3_comp_filled_autocomplete_menu_list_item_selected_container_color = 0x7f0d0042
com.clipsy.android.debug:macro/m3_comp_fab_tertiary_icon_color = 0x7f0d0041
com.clipsy.android.debug:styleable/ConstraintLayout_ReactiveGuide = 0x7f13002d
com.clipsy.android.debug:macro/m3_comp_fab_secondary_icon_color = 0x7f0d003d
com.clipsy.android.debug:macro/m3_comp_search_view_docked_container_shape = 0x7f0d00f5
com.clipsy.android.debug:macro/m3_comp_fab_primary_icon_color = 0x7f0d0039
com.clipsy.android.debug:macro/m3_comp_fab_primary_container_shape = 0x7f0d0038
com.clipsy.android.debug:style/Platform.MaterialComponents.Light.Dialog = 0x7f120147
com.clipsy.android.debug:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0d0036
com.clipsy.android.debug:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0d0035
com.clipsy.android.debug:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0d0034
com.clipsy.android.debug:macro/m3_comp_extended_fab_surface_container_color = 0x7f0d0033
com.clipsy.android.debug:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0d0031
com.clipsy.android.debug:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f1201b9
com.clipsy.android.debug:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0d0030
com.clipsy.android.debug:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0d002f
com.clipsy.android.debug:macro/m3_comp_extended_fab_primary_container_color = 0x7f0d002d
com.clipsy.android.debug:macro/m3_comp_divider_color = 0x7f0d0029
com.clipsy.android.debug:macro/m3_comp_dialog_supporting_text_color = 0x7f0d0027
com.clipsy.android.debug:macro/m3_comp_dialog_container_shape = 0x7f0d0024
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0d0093
com.clipsy.android.debug:macro/m3_comp_dialog_container_color = 0x7f0d0023
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0d0022
com.clipsy.android.debug:string/settings_startup = 0x7f1100f0
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0d0021
com.clipsy.android.debug:styleable/MotionScene = 0x7f130071
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0d001f
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0d001e
com.clipsy.android.debug:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0d0066
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0d001d
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0d001c
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0d001a
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0d0019
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0d0017
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0d0016
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0d0015
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0d0011
com.clipsy.android.debug:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0d000a
com.clipsy.android.debug:string/app_name = 0x7f110026
com.clipsy.android.debug:macro/m3_comp_checkbox_selected_container_color = 0x7f0d0006
com.clipsy.android.debug:macro/m3_comp_bottom_app_bar_container_color = 0x7f0d0005
com.clipsy.android.debug:macro/m3_comp_badge_large_label_text_color = 0x7f0d0003
com.clipsy.android.debug:macro/m3_comp_badge_color = 0x7f0d0002
com.clipsy.android.debug:macro/m3_comp_assist_chip_label_text_type = 0x7f0d0001
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f120310
com.clipsy.android.debug:layout/select_dialog_singlechoice_material = 0x7f0c0085
com.clipsy.android.debug:layout/select_dialog_multichoice_material = 0x7f0c0084
com.clipsy.android.debug:layout/preference_widget_switch_compat = 0x7f0c0082
com.clipsy.android.debug:layout/preference_widget_seekbar = 0x7f0c007f
com.clipsy.android.debug:layout/preference_widget_checkbox = 0x7f0c007e
com.clipsy.android.debug:layout/preference_list_fragment = 0x7f0c007b
com.clipsy.android.debug:layout/preference_information_material = 0x7f0c007a
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents = 0x7f1202ea
com.clipsy.android.debug:layout/preference_dropdown_material = 0x7f0c0078
com.clipsy.android.debug:layout/notification_template_part_time = 0x7f0c0072
com.clipsy.android.debug:layout/notification_template_part_chronometer = 0x7f0c0071
com.clipsy.android.debug:style/Widget.MaterialComponents.TimePicker = 0x7f12047a
com.clipsy.android.debug:layout/notification_template_custom_big = 0x7f0c006f
com.clipsy.android.debug:style/TextAppearance.AppCompat.Large = 0x7f1201cb
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0d0082
com.clipsy.android.debug:layout/notification_action_tombstone = 0x7f0c006e
com.clipsy.android.debug:layout/notification_action = 0x7f0c006d
com.clipsy.android.debug:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1201d0
com.clipsy.android.debug:layout/mtrl_search_view = 0x7f0c006c
com.clipsy.android.debug:layout/mtrl_search_bar = 0x7f0c006b
com.clipsy.android.debug:layout/mtrl_picker_text_input_date = 0x7f0c0069
com.clipsy.android.debug:style/Theme.Material3.Dark.Dialog = 0x7f120258
com.clipsy.android.debug:style/Preference.CheckBoxPreference.Material = 0x7f120154
com.clipsy.android.debug:layout/mtrl_picker_header_title_text = 0x7f0c0067
com.clipsy.android.debug:layout/mtrl_picker_header_selection_text = 0x7f0c0066
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f120336
com.clipsy.android.debug:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f120263
com.clipsy.android.debug:layout/mtrl_picker_header_dialog = 0x7f0c0064
com.clipsy.android.debug:style/MaterialAlertDialog.MaterialComponents = 0x7f120138
com.clipsy.android.debug:layout/mtrl_picker_actions = 0x7f0c0061
com.clipsy.android.debug:layout/mtrl_navigation_rail_item = 0x7f0c0060
com.clipsy.android.debug:styleable/KeyFramesAcceleration = 0x7f13004b
com.clipsy.android.debug:styleable/ButtonBarLayout = 0x7f13001c
com.clipsy.android.debug:string/abc_search_hint = 0x7f110012
com.clipsy.android.debug:layout/mtrl_layout_snackbar = 0x7f0c005e
com.clipsy.android.debug:layout/mtrl_calendar_year = 0x7f0c005d
com.clipsy.android.debug:layout/mtrl_calendar_month_navigation = 0x7f0c005a
com.clipsy.android.debug:layout/mtrl_calendar_month_labeled = 0x7f0c0059
com.clipsy.android.debug:layout/mtrl_calendar_month = 0x7f0c0058
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f120036
com.clipsy.android.debug:layout/mtrl_calendar_horizontal = 0x7f0c0057
com.clipsy.android.debug:layout/mtrl_calendar_days_of_week = 0x7f0c0056
com.clipsy.android.debug:layout/mtrl_calendar_day_of_week = 0x7f0c0055
com.clipsy.android.debug:string/abc_toolbar_collapse_description = 0x7f11001a
com.clipsy.android.debug:layout/mtrl_calendar_day = 0x7f0c0054
com.clipsy.android.debug:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0c0052
com.clipsy.android.debug:layout/mtrl_alert_select_dialog_multichoice = 0x7f0c0051
com.clipsy.android.debug:layout/mtrl_alert_select_dialog_item = 0x7f0c0050
com.clipsy.android.debug:layout/mtrl_alert_dialog = 0x7f0c004d
com.clipsy.android.debug:layout/material_timepicker_textinput_display = 0x7f0c004c
com.clipsy.android.debug:layout/material_timepicker_dialog = 0x7f0c004b
com.clipsy.android.debug:layout/material_time_input = 0x7f0c0049
com.clipsy.android.debug:layout/material_time_chip = 0x7f0c0048
com.clipsy.android.debug:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f12009f
com.clipsy.android.debug:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f120092
com.clipsy.android.debug:layout/material_textinput_timepicker = 0x7f0c0047
com.clipsy.android.debug:layout/material_clockface_view = 0x7f0c0045
com.clipsy.android.debug:layout/material_clock_period_toggle = 0x7f0c0042
com.clipsy.android.debug:layout/m3_side_sheet_dialog = 0x7f0c003e
com.clipsy.android.debug:layout/m3_auto_complete_simple_item = 0x7f0c003d
com.clipsy.android.debug:styleable/CircularProgressIndicator = 0x7f130024
com.clipsy.android.debug:style/Widget.Support.CoordinatorLayout = 0x7f120489
com.clipsy.android.debug:layout/m3_alert_dialog_title = 0x7f0c003c
com.clipsy.android.debug:layout/m3_alert_dialog = 0x7f0c003a
com.clipsy.android.debug:layout/item_device_bluetooth_style = 0x7f0c0039
com.clipsy.android.debug:layout/ime_secondary_split_test_activity = 0x7f0c0036
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f12007a
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_caret_color = 0x7f0d00b2
com.clipsy.android.debug:layout/ime_base_split_test_activity = 0x7f0c0035
com.clipsy.android.debug:layout/fragment_status = 0x7f0c0033
com.clipsy.android.debug:layout/fragment_history = 0x7f0c0032
com.clipsy.android.debug:layout/fragment_devices = 0x7f0c0031
com.clipsy.android.debug:layout/dialog_pairing = 0x7f0c002f
com.clipsy.android.debug:styleable/NavAction = 0x7f130074
com.clipsy.android.debug:layout/dialog_add_manual_device = 0x7f0c002e
com.clipsy.android.debug:layout/design_text_input_start_icon = 0x7f0c002d
com.clipsy.android.debug:string/abc_action_bar_home_description = 0x7f110000
com.clipsy.android.debug:layout/design_navigation_menu = 0x7f0c002a
com.clipsy.android.debug:layout/design_navigation_item_subheader = 0x7f0c0029
com.clipsy.android.debug:layout/design_navigation_item_separator = 0x7f0c0028
com.clipsy.android.debug:layout/design_navigation_item = 0x7f0c0026
com.clipsy.android.debug:layout/design_menu_item_action_area = 0x7f0c0025
com.clipsy.android.debug:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0d0061
com.clipsy.android.debug:layout/design_layout_tab_icon = 0x7f0c0023
com.clipsy.android.debug:string/mtrl_switch_thumb_path_name = 0x7f1100b7
com.clipsy.android.debug:layout/design_layout_snackbar_include = 0x7f0c0022
com.clipsy.android.debug:layout/design_bottom_navigation_item = 0x7f0c001f
com.clipsy.android.debug:layout/custom_dialog = 0x7f0c001e
com.clipsy.android.debug:layout/activity_settings = 0x7f0c001d
com.clipsy.android.debug:layout/mtrl_picker_text_input_date_range = 0x7f0c006a
com.clipsy.android.debug:layout/activity_main = 0x7f0c001c
com.clipsy.android.debug:styleable/Badge = 0x7f130017
com.clipsy.android.debug:layout/abc_tooltip = 0x7f0c001b
com.clipsy.android.debug:layout/abc_search_view = 0x7f0c0019
com.clipsy.android.debug:styleable/EditTextPreference = 0x7f130038
com.clipsy.android.debug:layout/abc_screen_simple = 0x7f0c0015
com.clipsy.android.debug:layout/abc_popup_menu_item_layout = 0x7f0c0013
com.clipsy.android.debug:layout/abc_list_menu_item_radio = 0x7f0c0011
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0d0012
com.clipsy.android.debug:layout/abc_list_menu_item_layout = 0x7f0c0010
com.clipsy.android.debug:layout/abc_list_menu_item_icon = 0x7f0c000f
com.clipsy.android.debug:layout/abc_dialog_title_material = 0x7f0c000c
com.clipsy.android.debug:layout/abc_alert_dialog_button_bar_material = 0x7f0c0008
com.clipsy.android.debug:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f1202c2
com.clipsy.android.debug:layout/abc_action_bar_up_container = 0x7f0c0001
com.clipsy.android.debug:interpolator/mtrl_linear_out_slow_in = 0x7f0b0011
com.clipsy.android.debug:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0b000d
com.clipsy.android.debug:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0b000c
com.clipsy.android.debug:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0b0009
com.clipsy.android.debug:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0b0008
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0d0085
com.clipsy.android.debug:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0b0005
com.clipsy.android.debug:styleable/NavArgument = 0x7f130075
com.clipsy.android.debug:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0b0004
com.clipsy.android.debug:style/Widget.AppCompat.ActionBar.TabText = 0x7f120315
com.clipsy.android.debug:string/v7_preference_on = 0x7f1100ff
com.clipsy.android.debug:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0b0000
com.clipsy.android.debug:integer/preferences_header_pane_weight = 0x7f0a0044
com.clipsy.android.debug:integer/mtrl_view_invisible = 0x7f0a0041
com.clipsy.android.debug:integer/mtrl_switch_track_viewport_height = 0x7f0a003d
com.clipsy.android.debug:integer/mtrl_switch_thumb_viewport_size = 0x7f0a003c
com.clipsy.android.debug:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f0a003b
com.clipsy.android.debug:layout/abc_expanded_menu_layout = 0x7f0c000d
com.clipsy.android.debug:integer/mtrl_switch_thumb_pressed_duration = 0x7f0a003a
com.clipsy.android.debug:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f0a0039
com.clipsy.android.debug:integer/mtrl_switch_thumb_motion_duration = 0x7f0a0037
com.clipsy.android.debug:integer/mtrl_chip_anim_duration = 0x7f0a0036
com.clipsy.android.debug:integer/mtrl_calendar_year_selector_span = 0x7f0a0033
com.clipsy.android.debug:integer/mtrl_calendar_header_orientation = 0x7f0a0031
com.clipsy.android.debug:integer/mtrl_badge_max_character_count = 0x7f0a002e
com.clipsy.android.debug:integer/material_motion_duration_short_2 = 0x7f0a002c
com.clipsy.android.debug:integer/material_motion_duration_short_1 = 0x7f0a002b
com.clipsy.android.debug:integer/material_motion_duration_medium_1 = 0x7f0a0029
com.clipsy.android.debug:layout/preference_category_material = 0x7f0c0075
com.clipsy.android.debug:integer/m3_sys_shape_corner_large_corner_family = 0x7f0a0024
com.clipsy.android.debug:style/Preference.SwitchPreferenceCompat = 0x7f120164
com.clipsy.android.debug:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f0a0022
com.clipsy.android.debug:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0d0130
com.clipsy.android.debug:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f0a0021
com.clipsy.android.debug:integer/m3_sys_motion_duration_short4 = 0x7f0a001f
com.clipsy.android.debug:string/pref_auto_cleanup_title = 0x7f1100d0
com.clipsy.android.debug:integer/m3_sys_motion_duration_medium4 = 0x7f0a001b
com.clipsy.android.debug:integer/m3_sys_motion_duration_medium1 = 0x7f0a0018
com.clipsy.android.debug:style/Widget.Material3.Button = 0x7f120379
com.clipsy.android.debug:integer/m3_sys_motion_duration_long4 = 0x7f0a0017
com.clipsy.android.debug:integer/m3_sys_motion_duration_long3 = 0x7f0a0016
com.clipsy.android.debug:integer/m3_sys_motion_duration_long1 = 0x7f0a0014
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f120191
com.clipsy.android.debug:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0d00f9
com.clipsy.android.debug:integer/m3_card_anim_duration_ms = 0x7f0a000e
com.clipsy.android.debug:integer/m3_btn_anim_duration_ms = 0x7f0a000c
com.clipsy.android.debug:style/PreferenceFragmentList.Material = 0x7f12016a
com.clipsy.android.debug:integer/m3_badge_max_number = 0x7f0a000a
com.clipsy.android.debug:integer/design_tab_indicator_anim_duration_ms = 0x7f0a0008
com.clipsy.android.debug:integer/config_tooltipAnimTime = 0x7f0a0006
com.clipsy.android.debug:integer/abc_config_activityDefaultDur = 0x7f0a0000
com.clipsy.android.debug:id/x_right = 0x7f09023d
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f120339
com.clipsy.android.debug:id/x_left = 0x7f09023c
com.clipsy.android.debug:id/wrap_content_constrained = 0x7f09023b
com.clipsy.android.debug:id/wrap_content = 0x7f09023a
com.clipsy.android.debug:id/withinBounds = 0x7f090238
com.clipsy.android.debug:id/withText = 0x7f090236
com.clipsy.android.debug:id/visible_removing_fragment_view_tag = 0x7f090234
com.clipsy.android.debug:id/view_tree_view_model_store_owner = 0x7f090232
com.clipsy.android.debug:integer/m3_sys_shape_corner_small_corner_family = 0x7f0a0026
com.clipsy.android.debug:id/view_tree_saved_state_registry_owner = 0x7f090231
com.clipsy.android.debug:id/view_offset_helper = 0x7f09022d
com.clipsy.android.debug:id/useLogo = 0x7f09022b
com.clipsy.android.debug:id/up = 0x7f09022a
com.clipsy.android.debug:id/unlabeled = 0x7f090229
com.clipsy.android.debug:id/uniform = 0x7f090228
com.clipsy.android.debug:id/tv_pairing_title = 0x7f090226
com.clipsy.android.debug:id/tv_pairing_code = 0x7f090224
com.clipsy.android.debug:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f12033b
com.clipsy.android.debug:id/triangle = 0x7f090223
com.clipsy.android.debug:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f120426
com.clipsy.android.debug:id/transition_transform = 0x7f090222
com.clipsy.android.debug:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f120288
com.clipsy.android.debug:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f12025b
com.clipsy.android.debug:id/transition_scene_layoutid_cache = 0x7f090221
com.clipsy.android.debug:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1202be
com.clipsy.android.debug:id/transition_position = 0x7f090220
com.clipsy.android.debug:macro/m3_comp_filter_chip_label_text_type = 0x7f0d0059
com.clipsy.android.debug:id/transition_layout_save = 0x7f09021f
com.clipsy.android.debug:id/transitionToStart = 0x7f09021d
com.clipsy.android.debug:id/touch_outside = 0x7f09021b
com.clipsy.android.debug:id/topPanel = 0x7f09021a
com.clipsy.android.debug:id/title_template = 0x7f090216
com.clipsy.android.debug:id/titleDividerNoCustom = 0x7f090215
com.clipsy.android.debug:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f120084
com.clipsy.android.debug:id/time = 0x7f090213
com.clipsy.android.debug:id/textinput_suffix_text = 0x7f090212
com.clipsy.android.debug:id/textinput_placeholder = 0x7f090210
com.clipsy.android.debug:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f12029f
com.clipsy.android.debug:id/textinput_helper_text = 0x7f09020f
com.clipsy.android.debug:id/text_source = 0x7f09020b
com.clipsy.android.debug:macro/m3_comp_switch_unselected_handle_color = 0x7f0d0135
com.clipsy.android.debug:id/text_service_status = 0x7f09020a
com.clipsy.android.debug:string/pref_history_limit_summary = 0x7f1100d9
com.clipsy.android.debug:id/text_last_seen = 0x7f090208
com.clipsy.android.debug:id/text_input_error_icon = 0x7f090206
com.clipsy.android.debug:id/text_input_end_icon = 0x7f090205
com.clipsy.android.debug:id/text_discovered_devices = 0x7f090203
com.clipsy.android.debug:id/text_device_ip = 0x7f090200
com.clipsy.android.debug:id/text_content = 0x7f0901fc
com.clipsy.android.debug:id/text_connection_status = 0x7f0901fb
com.clipsy.android.debug:id/text_connected_devices = 0x7f0901fa
com.clipsy.android.debug:id/textTop = 0x7f0901f9
com.clipsy.android.debug:id/textSpacerNoTitle = 0x7f0901f7
com.clipsy.android.debug:string/add = 0x7f110024
com.clipsy.android.debug:id/textSpacerNoButtons = 0x7f0901f6
com.clipsy.android.debug:id/text = 0x7f0901f3
com.clipsy.android.debug:id/tag_unhandled_key_listeners = 0x7f0901f1
com.clipsy.android.debug:id/tag_transition_group = 0x7f0901ef
com.clipsy.android.debug:id/tag_state_description = 0x7f0901ee
com.clipsy.android.debug:id/tag_screen_reader_focusable = 0x7f0901ed
com.clipsy.android.debug:id/tag_on_receive_content_mime_types = 0x7f0901ec
com.clipsy.android.debug:styleable/Chip = 0x7f130022
com.clipsy.android.debug:id/tag_on_apply_window_listener = 0x7f0901ea
com.clipsy.android.debug:id/switchWidget = 0x7f0901e3
com.clipsy.android.debug:id/supportScrollUp = 0x7f0901e1
com.clipsy.android.debug:id/text_current_device_name = 0x7f0901fd
com.clipsy.android.debug:id/submit_area = 0x7f0901e0
com.clipsy.android.debug:id/submenuarrow = 0x7f0901df
com.clipsy.android.debug:id/stretch = 0x7f0901de
com.clipsy.android.debug:id/staticPostLayout = 0x7f0901dc
com.clipsy.android.debug:id/staticLayout = 0x7f0901db
com.clipsy.android.debug:id/startVertical = 0x7f0901da
com.clipsy.android.debug:id/start = 0x7f0901d7
com.clipsy.android.debug:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f1201b5
com.clipsy.android.debug:id/spread_inside = 0x7f0901d0
com.clipsy.android.debug:id/spinner = 0x7f0901cc
com.clipsy.android.debug:id/spacer = 0x7f0901ca
com.clipsy.android.debug:id/snap = 0x7f0901c7
com.clipsy.android.debug:id/snackbar_action = 0x7f0901c5
com.clipsy.android.debug:styleable/TextEffects = 0x7f1300aa
com.clipsy.android.debug:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f1203b5
com.clipsy.android.debug:id/sliding_pane_detail_container = 0x7f0901c3
com.clipsy.android.debug:id/slide = 0x7f0901c2
com.clipsy.android.debug:id/skipped = 0x7f0901c1
com.clipsy.android.debug:style/Platform.V21.AppCompat.Light = 0x7f12014c
com.clipsy.android.debug:id/showTitle = 0x7f0901be
com.clipsy.android.debug:id/showCustom = 0x7f0901bc
com.clipsy.android.debug:id/shortcut = 0x7f0901bb
com.clipsy.android.debug:id/sharedValueSet = 0x7f0901b9
com.clipsy.android.debug:style/TextAppearance.AppCompat.Subhead = 0x7f1201d8
com.clipsy.android.debug:id/settings_container = 0x7f0901b8
com.clipsy.android.debug:style/ThemeOverlay.Material3.Chip = 0x7f1202bd
com.clipsy.android.debug:id/selection_type = 0x7f0901b7
com.clipsy.android.debug:id/seekbar_value = 0x7f0901b4
com.clipsy.android.debug:id/seekbar = 0x7f0901b3
com.clipsy.android.debug:id/search_voice_btn = 0x7f0901b2
com.clipsy.android.debug:id/search_src_text = 0x7f0901b1
com.clipsy.android.debug:id/search_plate = 0x7f0901b0
com.clipsy.android.debug:id/search_mag_icon = 0x7f0901af
com.clipsy.android.debug:id/search_go_btn = 0x7f0901ae
com.clipsy.android.debug:style/Widget.Material3.Badge.AdjustToBounds = 0x7f120370
com.clipsy.android.debug:id/search_edit_frame = 0x7f0901ad
com.clipsy.android.debug:macro/m3_comp_linear_progress_indicator_active_indicator_color = 0x7f0d005e
com.clipsy.android.debug:id/search_button = 0x7f0901ab
com.clipsy.android.debug:id/search_badge = 0x7f0901a9
com.clipsy.android.debug:id/scrollView = 0x7f0901a7
com.clipsy.android.debug:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f1203a9
com.clipsy.android.debug:layout/abc_list_menu_item_checkbox = 0x7f0c000e
com.clipsy.android.debug:id/scrollIndicatorDown = 0x7f0901a5
com.clipsy.android.debug:id/scroll = 0x7f0901a4
com.clipsy.android.debug:id/save_overlay_view = 0x7f0901a0
com.clipsy.android.debug:id/save_non_transition_alpha = 0x7f09019f
com.clipsy.android.debug:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0d00da
com.clipsy.android.debug:id/rounded = 0x7f09019c
com.clipsy.android.debug:id/right_side = 0x7f09019b
com.clipsy.android.debug:id/right_icon = 0x7f09019a
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f120044
com.clipsy.android.debug:interpolator/mtrl_linear = 0x7f0b0010
com.clipsy.android.debug:id/right = 0x7f090198
com.clipsy.android.debug:id/reverseSawtooth = 0x7f090197
com.clipsy.android.debug:id/report_drawn = 0x7f090196
com.clipsy.android.debug:id/recycler_view_history = 0x7f090195
com.clipsy.android.debug:integer/material_motion_path = 0x7f0a002d
com.clipsy.android.debug:id/rectangles = 0x7f090191
com.clipsy.android.debug:id/ratio = 0x7f090190
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_container_color = 0x7f0d000e
com.clipsy.android.debug:id/progress_discovery = 0x7f09018c
com.clipsy.android.debug:id/progress_circular = 0x7f09018b
com.clipsy.android.debug:id/pressed = 0x7f09018a
com.clipsy.android.debug:id/preferences_detail = 0x7f090187
com.clipsy.android.debug:id/postLayout = 0x7f090186
com.clipsy.android.debug:id/position = 0x7f090185
com.clipsy.android.debug:style/Base.TextAppearance.Material3.Search = 0x7f120047
com.clipsy.android.debug:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0d004e
com.clipsy.android.debug:id/pathRelative = 0x7f090181
com.clipsy.android.debug:id/path = 0x7f090180
com.clipsy.android.debug:id/parent = 0x7f09017b
com.clipsy.android.debug:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f120422
com.clipsy.android.debug:id/packed = 0x7f090179
com.clipsy.android.debug:id/outward = 0x7f090177
com.clipsy.android.debug:id/outline = 0x7f090176
com.clipsy.android.debug:id/open_search_view_toolbar_container = 0x7f090175
com.clipsy.android.debug:id/open_search_view_status_bar_spacer = 0x7f090173
com.clipsy.android.debug:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0d010b
com.clipsy.android.debug:id/open_search_view_search_prefix = 0x7f090172
com.clipsy.android.debug:id/open_search_view_header_container = 0x7f09016f
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1201e7
com.clipsy.android.debug:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1201ce
com.clipsy.android.debug:id/open_search_view_edit_text = 0x7f09016e
com.clipsy.android.debug:id/open_search_view_dummy_toolbar = 0x7f09016d
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1203d3
com.clipsy.android.debug:id/open_search_view_background = 0x7f090169
com.clipsy.android.debug:id/open_search_bar_text_view = 0x7f090168
com.clipsy.android.debug:style/Widget.Material3.CheckedTextView = 0x7f12038f
com.clipsy.android.debug:id/onInterceptTouchReturnSwipe = 0x7f090167
com.clipsy.android.debug:id/off = 0x7f090165
com.clipsy.android.debug:id/notification_main_column = 0x7f090163
com.clipsy.android.debug:id/normal = 0x7f090160
com.clipsy.android.debug:id/none = 0x7f09015f
com.clipsy.android.debug:layout/design_layout_snackbar = 0x7f0c0021
com.clipsy.android.debug:id/noState = 0x7f09015e
com.clipsy.android.debug:id/neverCompleteToStart = 0x7f09015c
com.clipsy.android.debug:id/navigation_header_container = 0x7f090159
com.clipsy.android.debug:styleable/ConstraintSet = 0x7f130030
com.clipsy.android.debug:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f110083
com.clipsy.android.debug:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0d0063
com.clipsy.android.debug:id/navigation_bar_item_small_label_view = 0x7f090158
com.clipsy.android.debug:id/navigation_bar_item_large_label_view = 0x7f090157
com.clipsy.android.debug:id/navigation_bar_item_labels_group = 0x7f090156
com.clipsy.android.debug:id/navigation_bar_item_icon_view = 0x7f090155
com.clipsy.android.debug:id/navigation_bar_item_icon_container = 0x7f090154
com.clipsy.android.debug:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0d012b
com.clipsy.android.debug:id/navigation_bar_item_active_indicator_view = 0x7f090153
com.clipsy.android.debug:id/nav_status = 0x7f090152
com.clipsy.android.debug:id/nav_host_fragment_container = 0x7f090151
com.clipsy.android.debug:id/nav_history = 0x7f090150
com.clipsy.android.debug:id/nav_devices = 0x7f09014f
com.clipsy.android.debug:id/multiply = 0x7f09014d
com.clipsy.android.debug:id/mtrl_view_tag_bottom_padding = 0x7f09014c
com.clipsy.android.debug:id/mtrl_picker_text_input_range_end = 0x7f090149
com.clipsy.android.debug:id/mtrl_picker_header_toggle = 0x7f090147
com.clipsy.android.debug:id/mtrl_picker_header_selection_text = 0x7f090145
com.clipsy.android.debug:id/mtrl_picker_fullscreen = 0x7f090143
com.clipsy.android.debug:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f120054
com.clipsy.android.debug:id/mtrl_motion_snapshot_view = 0x7f090142
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f12006d
com.clipsy.android.debug:id/mtrl_child_content_container = 0x7f090140
com.clipsy.android.debug:id/mtrl_calendar_year_selector_frame = 0x7f09013e
com.clipsy.android.debug:id/mtrl_calendar_text_input_frame = 0x7f09013d
com.clipsy.android.debug:style/Widget.MaterialComponents.Button.Icon = 0x7f120421
com.clipsy.android.debug:id/mtrl_calendar_selection_frame = 0x7f09013c
com.clipsy.android.debug:id/mtrl_calendar_main_pane = 0x7f09013a
com.clipsy.android.debug:id/mtrl_calendar_frame = 0x7f090139
com.clipsy.android.debug:id/mtrl_calendar_days_of_week = 0x7f090138
com.clipsy.android.debug:style/Widget.Material3.TabLayout.Secondary = 0x7f1203fa
com.clipsy.android.debug:id/mtrl_anchor_parent = 0x7f090136
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1202f4
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0d0020
com.clipsy.android.debug:id/motion_base = 0x7f090135
com.clipsy.android.debug:id/month_title = 0x7f090134
com.clipsy.android.debug:id/month_navigation_previous = 0x7f090133
com.clipsy.android.debug:id/sharedValueUnset = 0x7f0901ba
com.clipsy.android.debug:id/month_navigation_next = 0x7f090132
com.clipsy.android.debug:string/abc_menu_ctrl_shortcut_label = 0x7f110009
com.clipsy.android.debug:id/month_navigation_fragment_toggle = 0x7f090131
com.clipsy.android.debug:id/month_navigation_bar = 0x7f090130
com.clipsy.android.debug:macro/m3_comp_suggestion_chip_container_shape = 0x7f0d0118
com.clipsy.android.debug:id/month_grid = 0x7f09012f
com.clipsy.android.debug:id/mini = 0x7f09012e
com.clipsy.android.debug:id/middle = 0x7f09012d
com.clipsy.android.debug:id/view_tree_lifecycle_owner = 0x7f09022f
com.clipsy.android.debug:id/message = 0x7f09012c
com.clipsy.android.debug:id/material_timepicker_ok_button = 0x7f090128
com.clipsy.android.debug:style/Animation.AppCompat.Dialog = 0x7f120006
com.clipsy.android.debug:id/material_timepicker_mode_button = 0x7f090127
com.clipsy.android.debug:id/material_timepicker_container = 0x7f090126
com.clipsy.android.debug:id/material_textinput_timepicker = 0x7f090124
com.clipsy.android.debug:id/material_minute_tv = 0x7f090123
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0d0014
com.clipsy.android.debug:id/material_label = 0x7f090121
com.clipsy.android.debug:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f12045c
com.clipsy.android.debug:integer/m3_sys_motion_duration_medium3 = 0x7f0a001a
com.clipsy.android.debug:id/material_hour_tv = 0x7f090120
com.clipsy.android.debug:id/material_hour_text_input = 0x7f09011f
com.clipsy.android.debug:id/material_clock_period_toggle = 0x7f09011e
com.clipsy.android.debug:id/material_clock_period_am_button = 0x7f09011c
com.clipsy.android.debug:id/material_clock_level = 0x7f09011b
com.clipsy.android.debug:id/west = 0x7f090235
com.clipsy.android.debug:id/material_clock_hand = 0x7f09011a
com.clipsy.android.debug:id/material_clock_face = 0x7f090119
com.clipsy.android.debug:id/material_clock_display_and_toggle = 0x7f090118
com.clipsy.android.debug:id/material_clock_display = 0x7f090117
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.Day = 0x7f1203bf
com.clipsy.android.debug:id/match_parent = 0x7f090116
com.clipsy.android.debug:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f1203b1
com.clipsy.android.debug:id/view_transition = 0x7f09022e
com.clipsy.android.debug:id/match_constraint = 0x7f090115
com.clipsy.android.debug:id/masked = 0x7f090114
com.clipsy.android.debug:id/marquee = 0x7f090113
com.clipsy.android.debug:id/m3_side_sheet = 0x7f090112
com.clipsy.android.debug:id/ltr = 0x7f090111
com.clipsy.android.debug:style/Widget.Material3.BottomAppBar = 0x7f120371
com.clipsy.android.debug:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f12008a
com.clipsy.android.debug:id/listMode = 0x7f09010e
com.clipsy.android.debug:id/line3 = 0x7f09010c
com.clipsy.android.debug:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0d00d7
com.clipsy.android.debug:id/line1 = 0x7f09010b
com.clipsy.android.debug:id/leftToRight = 0x7f090109
com.clipsy.android.debug:id/labeled = 0x7f090105
com.clipsy.android.debug:id/jumpToEnd = 0x7f090103
com.clipsy.android.debug:styleable/ScrollingViewBehavior_Layout = 0x7f130090
com.clipsy.android.debug:style/PreferenceFragmentList = 0x7f120169
com.clipsy.android.debug:id/invisible = 0x7f0900ff
com.clipsy.android.debug:id/indeterminate = 0x7f0900fc
com.clipsy.android.debug:id/included = 0x7f0900fb
com.clipsy.android.debug:id/immediateStop = 0x7f0900fa
com.clipsy.android.debug:id/image = 0x7f0900f9
com.clipsy.android.debug:id/ignoreRequest = 0x7f0900f8
com.clipsy.android.debug:id/ifRoom = 0x7f0900f6
com.clipsy.android.debug:id/icon_group = 0x7f0900f5
com.clipsy.android.debug:id/icon_frame = 0x7f0900f4
com.clipsy.android.debug:id/icon_device_type = 0x7f0900f3
com.clipsy.android.debug:id/icon_content_type = 0x7f0900f2
com.clipsy.android.debug:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1200c7
com.clipsy.android.debug:id/honorRequest = 0x7f0900ef
com.clipsy.android.debug:id/hideable = 0x7f0900ec
com.clipsy.android.debug:id/hide_ime_id = 0x7f0900eb
com.clipsy.android.debug:id/header_title = 0x7f0900ea
com.clipsy.android.debug:id/header_connected_devices = 0x7f0900e9
com.clipsy.android.debug:id/header_available_devices = 0x7f0900e8
com.clipsy.android.debug:id/graph_wrap = 0x7f0900e4
com.clipsy.android.debug:id/graph = 0x7f0900e3
com.clipsy.android.debug:id/gone = 0x7f0900e2
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0d0092
com.clipsy.android.debug:id/ghost_view_holder = 0x7f0900e1
com.clipsy.android.debug:id/ghost_view = 0x7f0900e0
com.clipsy.android.debug:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1200ec
com.clipsy.android.debug:id/fullscreen_header = 0x7f0900df
com.clipsy.android.debug:id/frost = 0x7f0900de
com.clipsy.android.debug:id/forever = 0x7f0900db
com.clipsy.android.debug:string/abc_searchview_description_submit = 0x7f110016
com.clipsy.android.debug:id/fill_vertical = 0x7f0900d1
com.clipsy.android.debug:id/fill_horizontal = 0x7f0900d0
com.clipsy.android.debug:style/TextAppearance.Material3.HeadlineLarge = 0x7f120217
com.clipsy.android.debug:id/fade = 0x7f0900ce
com.clipsy.android.debug:id/fab_manual_sync = 0x7f0900cd
com.clipsy.android.debug:id/fab_add_device = 0x7f0900cc
com.clipsy.android.debug:macro/m3_comp_radio_button_selected_icon_color = 0x7f0d00dc
com.clipsy.android.debug:id/exitUntilCollapsed = 0x7f0900c9
com.clipsy.android.debug:id/enterAlwaysCollapsed = 0x7f0900c8
com.clipsy.android.debug:id/elastic = 0x7f0900c3
com.clipsy.android.debug:id/edit_text_name = 0x7f0900c2
com.clipsy.android.debug:id/edit_text_message = 0x7f0900c1
com.clipsy.android.debug:id/edit_text_ip = 0x7f0900c0
com.clipsy.android.debug:id/tag_window_insets_animation_callback = 0x7f0901f2
com.clipsy.android.debug:id/edit_query = 0x7f0900be
com.clipsy.android.debug:id/edge = 0x7f0900bd
com.clipsy.android.debug:style/ThemeOverlay.AppCompat = 0x7f1202a0
com.clipsy.android.debug:id/east = 0x7f0900bc
com.clipsy.android.debug:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f12007d
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0d015f
com.clipsy.android.debug:id/easeOut = 0x7f0900bb
com.clipsy.android.debug:id/easeIn = 0x7f0900b9
com.clipsy.android.debug:id/dragLeft = 0x7f0900b4
com.clipsy.android.debug:id/dragDown = 0x7f0900b2
com.clipsy.android.debug:id/text2 = 0x7f0901f4
com.clipsy.android.debug:id/screen = 0x7f0901a3
com.clipsy.android.debug:id/dragClockwise = 0x7f0900b1
com.clipsy.android.debug:id/disablePostScroll = 0x7f0900ad
com.clipsy.android.debug:macro/m3_comp_dialog_headline_type = 0x7f0d0026
com.clipsy.android.debug:id/disableIntraAutoTransition = 0x7f0900ac
com.clipsy.android.debug:id/disableHome = 0x7f0900ab
com.clipsy.android.debug:id/dialog_button = 0x7f0900a8
com.clipsy.android.debug:id/design_navigation_view = 0x7f0900a7
com.clipsy.android.debug:string/notification_service_text = 0x7f1100c6
com.clipsy.android.debug:id/design_menu_item_text = 0x7f0900a6
com.clipsy.android.debug:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1202d2
com.clipsy.android.debug:id/design_menu_item_action_area = 0x7f0900a4
com.clipsy.android.debug:style/Platform.AppCompat.Light = 0x7f120143
com.clipsy.android.debug:id/design_bottom_sheet = 0x7f0900a3
com.clipsy.android.debug:id/dependency_ordering = 0x7f0900a2
com.clipsy.android.debug:id/deltaRelative = 0x7f0900a1
com.clipsy.android.debug:id/north = 0x7f090161
com.clipsy.android.debug:id/material_value_index = 0x7f09012a
com.clipsy.android.debug:id/default_activity_button = 0x7f0900a0
com.clipsy.android.debug:id/decor_content_parent = 0x7f09009f
com.clipsy.android.debug:integer/status_bar_notification_info_maxnum = 0x7f0a0046
com.clipsy.android.debug:id/decelerateAndComplete = 0x7f09009e
com.clipsy.android.debug:id/date_picker_actions = 0x7f09009c
com.clipsy.android.debug:id/cut = 0x7f09009b
com.clipsy.android.debug:id/customPanel = 0x7f09009a
com.clipsy.android.debug:id/custom = 0x7f090099
com.clipsy.android.debug:id/currentState = 0x7f090098
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f120337
com.clipsy.android.debug:id/cradle = 0x7f090097
com.clipsy.android.debug:id/counterclockwise = 0x7f090096
com.clipsy.android.debug:style/Base.Widget.AppCompat.ActionButton = 0x7f1200cb
com.clipsy.android.debug:id/cos = 0x7f090095
com.clipsy.android.debug:macro/m3_comp_search_bar_leading_icon_color = 0x7f0d00ec
com.clipsy.android.debug:id/coordinator = 0x7f090094
com.clipsy.android.debug:id/continuousVelocity = 0x7f090093
com.clipsy.android.debug:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0d0077
com.clipsy.android.debug:id/contiguous = 0x7f090092
com.clipsy.android.debug:id/contentPanel = 0x7f090091
com.clipsy.android.debug:id/container = 0x7f09008f
com.clipsy.android.debug:styleable/PreferenceGroup = 0x7f130087
com.clipsy.android.debug:id/dragStart = 0x7f0900b6
com.clipsy.android.debug:id/constraint = 0x7f09008e
com.clipsy.android.debug:id/confirm_button = 0x7f09008d
com.clipsy.android.debug:id/compress = 0x7f09008c
com.clipsy.android.debug:id/collapseActionView = 0x7f09008b
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f120305
com.clipsy.android.debug:id/closest = 0x7f09008a
com.clipsy.android.debug:string/call_notification_answer_action = 0x7f11002f
com.clipsy.android.debug:id/clip_vertical = 0x7f090088
com.clipsy.android.debug:id/clear_text = 0x7f090086
com.clipsy.android.debug:id/circle_center = 0x7f090085
com.clipsy.android.debug:id/checkbox = 0x7f090082
com.clipsy.android.debug:styleable/NavigationRailView = 0x7f13007d
com.clipsy.android.debug:id/open_search_view_scrim = 0x7f090171
com.clipsy.android.debug:id/chain2 = 0x7f090080
com.clipsy.android.debug:styleable/OnSwipe = 0x7f130081
com.clipsy.android.debug:styleable/MaterialCalendar = 0x7f13005b
com.clipsy.android.debug:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0d0105
com.clipsy.android.debug:id/chain = 0x7f09007f
com.clipsy.android.debug:id/center_vertical = 0x7f09007e
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1202f2
com.clipsy.android.debug:id/center_horizontal = 0x7f09007d
com.clipsy.android.debug:id/toolbar = 0x7f090218
com.clipsy.android.debug:id/centerInside = 0x7f09007c
com.clipsy.android.debug:string/nav_status = 0x7f1100c2
com.clipsy.android.debug:id/card_service_status = 0x7f090078
com.clipsy.android.debug:id/card_no_available_devices = 0x7f090077
com.clipsy.android.debug:id/card_device_name = 0x7f090075
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f12032e
com.clipsy.android.debug:id/cancel_button = 0x7f090074
com.clipsy.android.debug:id/callMeasure = 0x7f090073
com.clipsy.android.debug:layout/design_bottom_sheet_dialog = 0x7f0c0020
com.clipsy.android.debug:id/button_send_message = 0x7f090070
com.clipsy.android.debug:style/Widget.Material3.BottomAppBar.Legacy = 0x7f120373
com.clipsy.android.debug:id/button_remove = 0x7f09006f
com.clipsy.android.debug:id/button_refresh_devices = 0x7f09006e
com.clipsy.android.debug:id/button_refresh = 0x7f09006d
com.clipsy.android.debug:id/button_pair = 0x7f09006c
com.clipsy.android.debug:id/button_info = 0x7f09006b
com.clipsy.android.debug:id/buttonPanel = 0x7f090068
com.clipsy.android.debug:id/on = 0x7f090166
com.clipsy.android.debug:id/bounceStart = 0x7f090066
com.clipsy.android.debug:id/mtrl_picker_text_input_range_start = 0x7f09014a
com.clipsy.android.debug:id/bounceBoth = 0x7f090064
com.clipsy.android.debug:styleable/Capability = 0x7f13001d
com.clipsy.android.debug:id/bounce = 0x7f090063
com.clipsy.android.debug:id/bestChoice = 0x7f09005f
com.clipsy.android.debug:style/Preference.CheckBoxPreference = 0x7f120153
com.clipsy.android.debug:id/beginning = 0x7f09005e
com.clipsy.android.debug:id/beginOnFirstDraw = 0x7f09005d
com.clipsy.android.debug:id/baseline = 0x7f09005c
com.clipsy.android.debug:id/barrier = 0x7f09005b
com.clipsy.android.debug:id/autoCompleteToStart = 0x7f09005a
com.clipsy.android.debug:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f120468
com.clipsy.android.debug:style/ShapeAppearance.MaterialComponents = 0x7f1201a6
com.clipsy.android.debug:id/async = 0x7f090056
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1202fa
com.clipsy.android.debug:id/asConfigured = 0x7f090055
com.clipsy.android.debug:style/Widget.Material3.Toolbar.OnSurface = 0x7f120408
com.clipsy.android.debug:style/Widget.AppCompat.ListPopupWindow = 0x7f120343
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f120330
com.clipsy.android.debug:string/expand_button_title = 0x7f11004d
com.clipsy.android.debug:id/arc = 0x7f090054
com.clipsy.android.debug:id/animateToEnd = 0x7f090050
com.clipsy.android.debug:id/androidx_window_activity_scope = 0x7f09004f
com.clipsy.android.debug:id/always = 0x7f09004e
com.clipsy.android.debug:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f12025f
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0d00c7
com.clipsy.android.debug:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0d0065
com.clipsy.android.debug:id/all = 0x7f09004c
com.clipsy.android.debug:id/aligned = 0x7f09004b
com.clipsy.android.debug:id/add = 0x7f090049
com.clipsy.android.debug:id/activity_chooser_view_content = 0x7f090048
com.clipsy.android.debug:id/actions = 0x7f090047
com.clipsy.android.debug:style/Widget.Material3.NavigationView = 0x7f1203e3
com.clipsy.android.debug:id/action_text = 0x7f090046
com.clipsy.android.debug:id/action_mode_close_button = 0x7f090043
com.clipsy.android.debug:id/action_mode_bar = 0x7f090041
com.clipsy.android.debug:style/Widget.Material3.NavigationRailView = 0x7f1203e0
com.clipsy.android.debug:id/action_menu_presenter = 0x7f090040
com.clipsy.android.debug:id/action_menu_divider = 0x7f09003f
com.clipsy.android.debug:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0d0155
com.clipsy.android.debug:id/action_divider = 0x7f09003d
com.clipsy.android.debug:string/history_clear_confirm = 0x7f110053
com.clipsy.android.debug:id/action_context_bar = 0x7f09003c
com.clipsy.android.debug:id/action_container = 0x7f09003b
com.clipsy.android.debug:id/action_clear_history = 0x7f09003a
com.clipsy.android.debug:id/action_bar_title = 0x7f090039
com.clipsy.android.debug:id/action_bar_subtitle = 0x7f090038
com.clipsy.android.debug:id/action_bar_spinner = 0x7f090037
com.clipsy.android.debug:id/action_bar_root = 0x7f090036
com.clipsy.android.debug:id/action_bar_container = 0x7f090035
com.clipsy.android.debug:id/action_bar_activity_content = 0x7f090034
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f12030e
com.clipsy.android.debug:id/action_bar = 0x7f090033
com.clipsy.android.debug:id/actionUp = 0x7f090032
com.clipsy.android.debug:id/actionDownUp = 0x7f090031
com.clipsy.android.debug:id/accessibility_custom_action_9 = 0x7f09002f
com.clipsy.android.debug:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f12020f
com.clipsy.android.debug:id/accessibility_custom_action_31 = 0x7f090029
com.clipsy.android.debug:id/accessibility_custom_action_30 = 0x7f090028
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Body1 = 0x7f12001b
com.clipsy.android.debug:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0d00ed
com.clipsy.android.debug:id/accessibility_custom_action_3 = 0x7f090027
com.clipsy.android.debug:id/accessibility_custom_action_24 = 0x7f090021
com.clipsy.android.debug:id/accessibility_custom_action_22 = 0x7f09001f
com.clipsy.android.debug:id/accessibility_custom_action_20 = 0x7f09001d
com.clipsy.android.debug:id/accessibility_custom_action_2 = 0x7f09001c
com.clipsy.android.debug:id/accessibility_custom_action_18 = 0x7f09001a
com.clipsy.android.debug:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f120348
com.clipsy.android.debug:id/accessibility_custom_action_17 = 0x7f090019
com.clipsy.android.debug:id/accessibility_custom_action_16 = 0x7f090018
com.clipsy.android.debug:id/accessibility_custom_action_15 = 0x7f090017
com.clipsy.android.debug:id/accessibility_custom_action_13 = 0x7f090015
com.clipsy.android.debug:id/accessibility_custom_action_11 = 0x7f090013
com.clipsy.android.debug:id/accessibility_custom_action_1 = 0x7f090011
com.clipsy.android.debug:id/mtrl_card_checked_layer_id = 0x7f09013f
com.clipsy.android.debug:id/accessibility_custom_action_0 = 0x7f090010
com.clipsy.android.debug:id/accelerate = 0x7f09000e
com.clipsy.android.debug:style/Base.Widget.AppCompat.EditText = 0x7f1200df
com.clipsy.android.debug:id/TOP_END = 0x7f09000c
com.clipsy.android.debug:id/SYM = 0x7f09000b
com.clipsy.android.debug:id/SHOW_PROGRESS = 0x7f09000a
com.clipsy.android.debug:style/Widget.Material3.Chip.Input.Icon = 0x7f120396
com.clipsy.android.debug:id/ignore = 0x7f0900f7
com.clipsy.android.debug:id/SHOW_PATH = 0x7f090009
com.clipsy.android.debug:id/SHIFT = 0x7f090007
com.clipsy.android.debug:id/FUNCTION = 0x7f090004
com.clipsy.android.debug:id/BOTTOM_START = 0x7f090002
com.clipsy.android.debug:id/BOTTOM_END = 0x7f090001
com.clipsy.android.debug:drawable/tooltip_frame_light = 0x7f0800ff
com.clipsy.android.debug:drawable/tooltip_frame_dark = 0x7f0800fe
com.clipsy.android.debug:drawable/test_level_drawable = 0x7f0800fd
com.clipsy.android.debug:style/Widget.AppCompat.PopupWindow = 0x7f120349
com.clipsy.android.debug:style/Widget.AppCompat.ActionBar.TabBar = 0x7f120314
com.clipsy.android.debug:drawable/preference_list_divider_material = 0x7f0800fc
com.clipsy.android.debug:style/Widget.Design.BottomNavigationView = 0x7f12035e
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1202f6
com.clipsy.android.debug:id/transitionToEnd = 0x7f09021c
com.clipsy.android.debug:drawable/notification_icon_background = 0x7f0800f6
com.clipsy.android.debug:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f12004c
com.clipsy.android.debug:drawable/notification_bg_normal = 0x7f0800f4
com.clipsy.android.debug:drawable/notification_bg_low_pressed = 0x7f0800f3
com.clipsy.android.debug:drawable/notification_bg = 0x7f0800f0
com.clipsy.android.debug:styleable/Navigator = 0x7f13007f
com.clipsy.android.debug:styleable/AppCompatTextView = 0x7f130014
com.clipsy.android.debug:drawable/notification_action_background = 0x7f0800ef
com.clipsy.android.debug:drawable/navigation_empty_icon = 0x7f0800ee
com.clipsy.android.debug:id/content = 0x7f090090
com.clipsy.android.debug:drawable/mtrl_tabs_default_indicator = 0x7f0800ed
com.clipsy.android.debug:drawable/mtrl_switch_track_decoration = 0x7f0800ec
com.clipsy.android.debug:style/TextAppearance.Design.Snackbar.Message = 0x7f1201fd
com.clipsy.android.debug:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0800e9
com.clipsy.android.debug:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0800e7
com.clipsy.android.debug:drawable/mtrl_switch_thumb_pressed_checked = 0x7f0800e6
com.clipsy.android.debug:drawable/mtrl_switch_thumb = 0x7f0800e1
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0d00b9
com.clipsy.android.debug:id/accessibility_custom_action_8 = 0x7f09002e
com.clipsy.android.debug:drawable/mtrl_navigation_bar_item_background = 0x7f0800de
com.clipsy.android.debug:drawable/mtrl_ic_checkbox_unchecked = 0x7f0800db
com.clipsy.android.debug:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f1203ad
com.clipsy.android.debug:drawable/mtrl_ic_checkbox_checked = 0x7f0800da
com.clipsy.android.debug:style/TextAppearance.AppCompat.Display3 = 0x7f1201c7
com.clipsy.android.debug:drawable/mtrl_dropdown_arrow = 0x7f0800d5
com.clipsy.android.debug:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0800d2
com.clipsy.android.debug:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0800cf
com.clipsy.android.debug:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f0800ce
com.clipsy.android.debug:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f0800cb
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f120303
com.clipsy.android.debug:drawable/mtrl_bottomsheet_drag_handle = 0x7f0800c9
com.clipsy.android.debug:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1202d9
com.clipsy.android.debug:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0800c8
com.clipsy.android.debug:macro/m3_comp_fab_primary_large_container_shape = 0x7f0d003a
com.clipsy.android.debug:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f0800c5
com.clipsy.android.debug:id/mtrl_calendar_day_selector_frame = 0x7f090137
com.clipsy.android.debug:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0800c4
com.clipsy.android.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1200e5
com.clipsy.android.debug:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0800c3
com.clipsy.android.debug:integer/bottom_sheet_slide_duration = 0x7f0a0003
com.clipsy.android.debug:drawable/material_ic_edit_black_24dp = 0x7f0800c2
com.clipsy.android.debug:drawable/material_ic_clear_black_24dp = 0x7f0800c1
com.clipsy.android.debug:drawable/material_ic_calendar_black_24dp = 0x7f0800c0
com.clipsy.android.debug:drawable/material_cursor_drawable = 0x7f0800bf
com.clipsy.android.debug:drawable/m3_tabs_transparent_background = 0x7f0800be
com.clipsy.android.debug:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f120341
com.clipsy.android.debug:drawable/m3_tabs_rounded_line_indicator = 0x7f0800bd
com.clipsy.android.debug:drawable/m3_tabs_line_indicator = 0x7f0800bc
com.clipsy.android.debug:id/SHOW_ALL = 0x7f090008
com.clipsy.android.debug:drawable/m3_tabs_background = 0x7f0800bb
com.clipsy.android.debug:id/filled = 0x7f0900d2
com.clipsy.android.debug:drawable/m3_selection_control_ripple = 0x7f0800ba
com.clipsy.android.debug:style/Widget.Material3.DrawerLayout = 0x7f1203a6
com.clipsy.android.debug:drawable/m3_radiobutton_ripple = 0x7f0800b9
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f12029b
com.clipsy.android.debug:integer/m3_sys_motion_duration_extra_long3 = 0x7f0a0012
com.clipsy.android.debug:drawable/m3_bottom_sheet_drag_handle = 0x7f0800b6
com.clipsy.android.debug:style/ThemeOverlay.AppCompat.ActionBar = 0x7f1202a1
com.clipsy.android.debug:drawable/m3_avd_show_password = 0x7f0800b5
com.clipsy.android.debug:drawable/m3_appbar_background = 0x7f0800b3
com.clipsy.android.debug:layout/preference_dialog_edittext = 0x7f0c0076
com.clipsy.android.debug:id/autoComplete = 0x7f090058
com.clipsy.android.debug:drawable/ic_sync_to_pc = 0x7f0800b2
com.clipsy.android.debug:drawable/ic_sync = 0x7f0800b1
com.clipsy.android.debug:drawable/ic_settings = 0x7f0800af
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f120298
com.clipsy.android.debug:drawable/ic_search_black_24 = 0x7f0800ad
com.clipsy.android.debug:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0d00e3
com.clipsy.android.debug:id/spring = 0x7f0901d1
com.clipsy.android.debug:drawable/ic_scan_animation = 0x7f0800ab
com.clipsy.android.debug:drawable/ic_refresh = 0x7f0800aa
com.clipsy.android.debug:drawable/ic_notification = 0x7f0800a8
com.clipsy.android.debug:string/mtrl_picker_date_header_unselected = 0x7f110098
com.clipsy.android.debug:macro/m3_comp_checkbox_selected_icon_color = 0x7f0d000b
com.clipsy.android.debug:id/title = 0x7f090214
com.clipsy.android.debug:drawable/ic_mtrl_chip_checked_circle = 0x7f0800a6
com.clipsy.android.debug:style/Base.DialogWindowTitle.AppCompat = 0x7f120015
com.clipsy.android.debug:drawable/ic_m3_chip_close = 0x7f0800a3
com.clipsy.android.debug:drawable/ic_m3_chip_check = 0x7f0800a1
com.clipsy.android.debug:layout/mtrl_picker_dialog = 0x7f0c0062
com.clipsy.android.debug:drawable/ic_history_vector = 0x7f08009e
com.clipsy.android.debug:attr/onPositiveCross = 0x7f040384
com.clipsy.android.debug:drawable/ic_computer = 0x7f08009c
com.clipsy.android.debug:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f120090
com.clipsy.android.debug:attr/actionBarTabTextStyle = 0x7f04000b
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant60 = 0x7f060133
com.clipsy.android.debug:drawable/ic_clock_black_24dp = 0x7f08009b
com.clipsy.android.debug:dimen/design_navigation_icon_size = 0x7f070083
com.clipsy.android.debug:color/m3_ref_palette_secondary80 = 0x7f06014f
com.clipsy.android.debug:drawable/ic_clipsy_settings = 0x7f080099
com.clipsy.android.debug:drawable/ic_clear_black_24 = 0x7f080095
com.clipsy.android.debug:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0701ae
com.clipsy.android.debug:drawable/ic_call_decline_low = 0x7f080094
com.clipsy.android.debug:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1200f2
com.clipsy.android.debug:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1200a4
com.clipsy.android.debug:drawable/ic_call_decline = 0x7f080093
com.clipsy.android.debug:style/Theme.MaterialComponents.Bridge = 0x7f120273
com.clipsy.android.debug:drawable/ic_call_answer_video_low = 0x7f080092
com.clipsy.android.debug:drawable/dialog_background = 0x7f08008b
com.clipsy.android.debug:drawable/design_ic_visibility = 0x7f080087
com.clipsy.android.debug:drawable/code_background = 0x7f080085
com.clipsy.android.debug:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
com.clipsy.android.debug:drawable/circle_indicator = 0x7f080084
com.clipsy.android.debug:style/Button.Clipsy.Tertiary = 0x7f12012a
com.clipsy.android.debug:drawable/circle_background = 0x7f080083
com.clipsy.android.debug:interpolator/m3_sys_motion_easing_emphasized = 0x7f0b0007
com.clipsy.android.debug:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f070258
com.clipsy.android.debug:drawable/button_secondary = 0x7f080082
com.clipsy.android.debug:drawable/btn_radio_on_mtrl = 0x7f080080
com.clipsy.android.debug:attr/expandedTitleTextColor = 0x7f0401df
com.clipsy.android.debug:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f08007d
com.clipsy.android.debug:attr/menu = 0x7f040332
com.clipsy.android.debug:drawable/btn_checkbox_unchecked_mtrl = 0x7f08007c
com.clipsy.android.debug:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f080073
com.clipsy.android.debug:style/Theme.MaterialComponents.Dialog = 0x7f120285
com.clipsy.android.debug:drawable/abc_textfield_default_mtrl_alpha = 0x7f080072
com.clipsy.android.debug:attr/paddingRightSystemWindowInsets = 0x7f040390
com.clipsy.android.debug:drawable/abc_tab_indicator_material = 0x7f08006b
com.clipsy.android.debug:id/scrollIndicatorUp = 0x7f0901a6
com.clipsy.android.debug:drawable/abc_star_black_48dp = 0x7f080067
com.clipsy.android.debug:style/Base.V22.Theme.AppCompat = 0x7f1200b1
com.clipsy.android.debug:drawable/abc_spinner_textfield_background_material = 0x7f080066
com.clipsy.android.debug:drawable/abc_seekbar_tick_mark_material = 0x7f080063
com.clipsy.android.debug:attr/extraMultilineHeightEnabled = 0x7f0401e7
com.clipsy.android.debug:drawable/abc_scrubber_track_mtrl_alpha = 0x7f080061
com.clipsy.android.debug:drawable/abc_ratingbar_material = 0x7f08005b
com.clipsy.android.debug:dimen/m3_btn_dialog_btn_spacing = 0x7f0700d8
com.clipsy.android.debug:drawable/abc_ratingbar_indicator_material = 0x7f08005a
com.clipsy.android.debug:attr/percentY = 0x7f0403a2
com.clipsy.android.debug:color/m3_highlighted_text = 0x7f0600a9
com.clipsy.android.debug:drawable/abc_list_selector_holo_light = 0x7f080057
com.clipsy.android.debug:drawable/abc_list_selector_background_transition_holo_dark = 0x7f080052
com.clipsy.android.debug:drawable/abc_list_pressed_holo_dark = 0x7f080050
com.clipsy.android.debug:style/TextAppearance.AppCompat.Display2 = 0x7f1201c6
com.clipsy.android.debug:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1200c8
com.clipsy.android.debug:drawable/abc_list_focused_holo = 0x7f08004e
com.clipsy.android.debug:layout/item_clipboard_history = 0x7f0c0037
com.clipsy.android.debug:drawable/abc_spinner_mtrl_am_alpha = 0x7f080065
com.clipsy.android.debug:string/material_motion_easing_accelerated = 0x7f110071
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f080019
com.clipsy.android.debug:drawable/abc_list_divider_mtrl_alpha = 0x7f08004d
com.clipsy.android.debug:drawable/abc_item_background_holo_light = 0x7f08004b
com.clipsy.android.debug:styleable/Fragment = 0x7f130041
com.clipsy.android.debug:id/fitXY = 0x7f0900d7
com.clipsy.android.debug:color/clipsy_green_bright = 0x7f060044
com.clipsy.android.debug:drawable/abc_item_background_holo_dark = 0x7f08004a
com.clipsy.android.debug:drawable/abc_ic_search_api_material = 0x7f080048
com.clipsy.android.debug:string/abc_activitychooserview_choose_application = 0x7f110005
com.clipsy.android.debug:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f080047
com.clipsy.android.debug:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f080046
com.clipsy.android.debug:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f080042
com.clipsy.android.debug:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f080040
com.clipsy.android.debug:attr/SharedValue = 0x7f040000
com.clipsy.android.debug:drawable/abc_ic_ab_back_material = 0x7f08003d
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1201eb
com.clipsy.android.debug:color/material_personalized_color_on_surface = 0x7f060286
com.clipsy.android.debug:drawable/abc_edit_text_material = 0x7f08003c
com.clipsy.android.debug:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f1201be
com.clipsy.android.debug:string/notification_service_title = 0x7f1100c7
com.clipsy.android.debug:attr/sliderStyle = 0x7f04041b
com.clipsy.android.debug:attr/closeIcon = 0x7f0400e9
com.clipsy.android.debug:drawable/abc_control_background_material = 0x7f08003a
com.clipsy.android.debug:drawable/abc_cab_background_internal_bg = 0x7f080037
com.clipsy.android.debug:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f080036
com.clipsy.android.debug:attr/queryHint = 0x7f0403cc
com.clipsy.android.debug:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f080035
com.clipsy.android.debug:style/Base.V14.Theme.Material3.Dark = 0x7f12008d
com.clipsy.android.debug:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f080034
com.clipsy.android.debug:layout/material_clockface_textview = 0x7f0c0044
com.clipsy.android.debug:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f080033
com.clipsy.android.debug:attr/checkedTextViewStyle = 0x7f0400c6
com.clipsy.android.debug:attr/tintNavigationIcon = 0x7f0404dd
com.clipsy.android.debug:drawable/abc_btn_default_mtrl_shape = 0x7f080030
com.clipsy.android.debug:style/TextAppearance.AppCompat.Display4 = 0x7f1201c8
com.clipsy.android.debug:attr/srcCompat = 0x7f04042d
com.clipsy.android.debug:drawable/abc_btn_colored_material = 0x7f08002f
com.clipsy.android.debug:drawable/abc_btn_check_material_anim = 0x7f08002c
com.clipsy.android.debug:drawable/abc_btn_borderless_material = 0x7f08002a
com.clipsy.android.debug:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f080027
com.clipsy.android.debug:attr/recyclerViewStyle = 0x7f0403d7
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f08001d
com.clipsy.android.debug:layout/abc_action_menu_layout = 0x7f0c0003
com.clipsy.android.debug:dimen/m3_snackbar_margin = 0x7f0701f4
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f08001c
com.clipsy.android.debug:id/preferences_header = 0x7f090188
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f08001b
com.clipsy.android.debug:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0b0001
com.clipsy.android.debug:attr/moveWhenScrollAtTop = 0x7f040370
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f08001a
com.clipsy.android.debug:style/Theme.AppCompat.Light.Dialog = 0x7f120248
com.clipsy.android.debug:dimen/preferences_header_width = 0x7f070325
com.clipsy.android.debug:color/material_dynamic_neutral50 = 0x7f060227
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f080016
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f080015
com.clipsy.android.debug:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0d0070
com.clipsy.android.debug:color/m3_ref_palette_error20 = 0x7f06010a
com.clipsy.android.debug:drawable/$m3_avd_show_password__2 = 0x7f08000b
com.clipsy.android.debug:drawable/$m3_avd_show_password__0 = 0x7f080009
com.clipsy.android.debug:attr/materialButtonToggleGroupStyle = 0x7f040300
com.clipsy.android.debug:drawable/$avd_show_password__0 = 0x7f080003
com.clipsy.android.debug:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b
com.clipsy.android.debug:dimen/tooltip_y_offset_non_touch = 0x7f07032f
com.clipsy.android.debug:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f070220
com.clipsy.android.debug:dimen/tooltip_vertical_padding = 0x7f07032e
com.clipsy.android.debug:macro/m3_comp_search_bar_container_surface_tint_layer_color = 0x7f0d00e7
com.clipsy.android.debug:layout/abc_activity_chooser_view_list_item = 0x7f0c0007
com.clipsy.android.debug:dimen/tooltip_horizontal_padding = 0x7f07032a
com.clipsy.android.debug:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f12007f
com.clipsy.android.debug:attr/subheaderColor = 0x7f040447
com.clipsy.android.debug:dimen/text_size_button_small = 0x7f070328
com.clipsy.android.debug:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f120180
com.clipsy.android.debug:id/icon = 0x7f0900f1
com.clipsy.android.debug:dimen/preference_seekbar_value_minWidth = 0x7f070323
com.clipsy.android.debug:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1200c3
com.clipsy.android.debug:dimen/notification_top_pad = 0x7f07031d
com.clipsy.android.debug:dimen/abc_button_inset_horizontal_material = 0x7f070012
com.clipsy.android.debug:dimen/notification_media_narrow_margin = 0x7f070317
com.clipsy.android.debug:dimen/notification_main_column_padding_top = 0x7f070316
com.clipsy.android.debug:dimen/notification_large_icon_width = 0x7f070315
com.clipsy.android.debug:style/Widget.MaterialComponents.FloatingActionButton = 0x7f12043d
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1202fb
com.clipsy.android.debug:dimen/notification_big_circle_margin = 0x7f070312
com.clipsy.android.debug:dimen/notification_action_text_size = 0x7f070311
com.clipsy.android.debug:id/NO_DEBUG = 0x7f090006
com.clipsy.android.debug:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f07030f
com.clipsy.android.debug:style/Widget.Material3.Badge = 0x7f12036f
com.clipsy.android.debug:color/m3_navigation_item_icon_tint = 0x7f0600b0
com.clipsy.android.debug:dimen/mtrl_tooltip_padding = 0x7f07030e
com.clipsy.android.debug:dimen/mtrl_tooltip_cornerSize = 0x7f07030b
com.clipsy.android.debug:string/preference_copied = 0x7f1100df
com.clipsy.android.debug:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0702bd
com.clipsy.android.debug:dimen/mtrl_toolbar_default_height = 0x7f070309
com.clipsy.android.debug:dimen/mtrl_textinput_counter_margin_start = 0x7f070305
com.clipsy.android.debug:integer/preferences_detail_pane_weight = 0x7f0a0043
com.clipsy.android.debug:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f070302
com.clipsy.android.debug:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f070300
com.clipsy.android.debug:dimen/mtrl_switch_track_width = 0x7f0702ff
com.clipsy.android.debug:attr/indicatorInset = 0x7f04025d
com.clipsy.android.debug:dimen/mtrl_switch_track_height = 0x7f0702fe
com.clipsy.android.debug:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f07023e
com.clipsy.android.debug:dimen/mtrl_switch_thumb_size = 0x7f0702fd
com.clipsy.android.debug:dimen/mtrl_switch_thumb_icon_size = 0x7f0702fc
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1202ec
com.clipsy.android.debug:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1202e3
com.clipsy.android.debug:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f120093
com.clipsy.android.debug:dimen/mtrl_switch_thumb_elevation = 0x7f0702fb
com.clipsy.android.debug:dimen/mtrl_switch_text_padding = 0x7f0702fa
com.clipsy.android.debug:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0d00e9
com.clipsy.android.debug:drawable/mtrl_switch_track = 0x7f0800eb
com.clipsy.android.debug:dimen/notification_content_margin_start = 0x7f070313
com.clipsy.android.debug:id/left = 0x7f090108
com.clipsy.android.debug:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f0702f8
com.clipsy.android.debug:attr/itemTextAppearanceActiveBoldEnabled = 0x7f040281
com.clipsy.android.debug:dimen/mtrl_slider_widget_height = 0x7f0702f3
com.clipsy.android.debug:color/m3_sys_color_dark_primary = 0x7f06017c
com.clipsy.android.debug:dimen/mtrl_slider_track_height = 0x7f0702f1
com.clipsy.android.debug:dimen/mtrl_slider_tick_radius = 0x7f0702f0
com.clipsy.android.debug:style/Widget.Material3.Button.OutlinedButton = 0x7f120381
com.clipsy.android.debug:layout/mtrl_auto_complete_simple_item = 0x7f0c0053
com.clipsy.android.debug:dimen/mtrl_slider_thumb_elevation = 0x7f0702ee
com.clipsy.android.debug:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0d0100
com.clipsy.android.debug:dimen/mtrl_shape_corner_size_medium_component = 0x7f0702e8
com.clipsy.android.debug:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f12028c
com.clipsy.android.debug:dimen/mtrl_shape_corner_size_large_component = 0x7f0702e7
com.clipsy.android.debug:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0d00fd
com.clipsy.android.debug:attr/actionModeSelectAllDrawable = 0x7f04001c
com.clipsy.android.debug:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0702e5
com.clipsy.android.debug:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0702e4
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1202f0
com.clipsy.android.debug:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f070112
com.clipsy.android.debug:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0702e3
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f120171
com.clipsy.android.debug:layout/design_navigation_item_header = 0x7f0c0027
com.clipsy.android.debug:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0702b6
com.clipsy.android.debug:dimen/mtrl_progress_circular_size_small = 0x7f0702e1
com.clipsy.android.debug:dimen/mtrl_progress_circular_size_medium = 0x7f0702e0
com.clipsy.android.debug:color/m3_textfield_indicator_text_color = 0x7f06020b
com.clipsy.android.debug:dimen/mtrl_progress_circular_size_extra_small = 0x7f0702df
com.clipsy.android.debug:dimen/mtrl_progress_circular_size = 0x7f0702de
com.clipsy.android.debug:dimen/mtrl_progress_circular_radius = 0x7f0702dd
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0d00c0
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary95 = 0x7f060105
com.clipsy.android.debug:dimen/mtrl_progress_circular_inset_medium = 0x7f0702db
com.clipsy.android.debug:color/material_on_primary_emphasis_high_type = 0x7f060271
com.clipsy.android.debug:dimen/mtrl_navigation_rail_icon_margin = 0x7f0702d4
com.clipsy.android.debug:string/mtrl_timepicker_confirm = 0x7f1100bd
com.clipsy.android.debug:drawable/m3_avd_hide_password = 0x7f0800b4
com.clipsy.android.debug:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0702cf
com.clipsy.android.debug:dimen/mtrl_navigation_item_icon_size = 0x7f0702cd
com.clipsy.android.debug:style/Widget.AppCompat.ActivityChooserView = 0x7f12031b
com.clipsy.android.debug:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0702cb
com.clipsy.android.debug:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0702c6
com.clipsy.android.debug:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0702c1
com.clipsy.android.debug:style/Widget.Material3.SearchView.Toolbar = 0x7f1203ee
com.clipsy.android.debug:id/mtrl_calendar_months = 0x7f09013b
com.clipsy.android.debug:attr/actionModeTheme = 0x7f040020
com.clipsy.android.debug:dimen/mtrl_high_ripple_focused_alpha = 0x7f0702c0
com.clipsy.android.debug:attr/tabMinWidth = 0x7f04046f
com.clipsy.android.debug:dimen/mtrl_high_ripple_default_alpha = 0x7f0702bf
com.clipsy.android.debug:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1201d4
com.clipsy.android.debug:dimen/m3_comp_divider_thickness = 0x7f070114
com.clipsy.android.debug:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0702b9
com.clipsy.android.debug:dimen/mtrl_extended_fab_start_padding = 0x7f0702b5
com.clipsy.android.debug:attr/itemIconTint = 0x7f04026e
com.clipsy.android.debug:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f080045
com.clipsy.android.debug:color/m3_button_background_color_selector = 0x7f060081
com.clipsy.android.debug:dimen/mtrl_extended_fab_min_width = 0x7f0702b4
com.clipsy.android.debug:id/ALT = 0x7f090000
com.clipsy.android.debug:dimen/mtrl_extended_fab_min_height = 0x7f0702b3
com.clipsy.android.debug:attr/animationMode = 0x7f04003d
com.clipsy.android.debug:attr/layout_goneMarginBaseline = 0x7f0402cb
com.clipsy.android.debug:dimen/mtrl_extended_fab_icon_size = 0x7f0702b1
com.clipsy.android.debug:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0d015d
com.clipsy.android.debug:attr/titleTextColor = 0x7f0404ea
com.clipsy.android.debug:dimen/mtrl_extended_fab_end_padding = 0x7f0702af
com.clipsy.android.debug:drawable/ic_clipsy_devices = 0x7f080096
com.clipsy.android.debug:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0702a8
com.clipsy.android.debug:dimen/mtrl_card_dragged_z = 0x7f0702a3
com.clipsy.android.debug:dimen/mtrl_card_corner_radius = 0x7f0702a2
com.clipsy.android.debug:id/src_over = 0x7f0901d5
com.clipsy.android.debug:dimen/mtrl_calendar_year_width = 0x7f07029f
com.clipsy.android.debug:drawable/mtrl_switch_thumb_checked = 0x7f0800e2
com.clipsy.android.debug:dimen/mtrl_calendar_year_height = 0x7f07029c
com.clipsy.android.debug:drawable/ic_arrow_down_24dp = 0x7f08008d
com.clipsy.android.debug:styleable/FontFamily = 0x7f13003e
com.clipsy.android.debug:dimen/mtrl_calendar_year_corner = 0x7f07029b
com.clipsy.android.debug:dimen/mtrl_calendar_title_baseline_to_top = 0x7f070299
com.clipsy.android.debug:dimen/mtrl_calendar_text_input_padding_top = 0x7f070298
com.clipsy.android.debug:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f070297
com.clipsy.android.debug:drawable/design_fab_background = 0x7f080086
com.clipsy.android.debug:attr/colorControlNormal = 0x7f040103
com.clipsy.android.debug:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f070296
com.clipsy.android.debug:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f070295
com.clipsy.android.debug:attr/startIconMinSize = 0x7f040434
com.clipsy.android.debug:dimen/mtrl_calendar_navigation_top_padding = 0x7f070292
com.clipsy.android.debug:dimen/mtrl_calendar_navigation_height = 0x7f070291
com.clipsy.android.debug:dimen/mtrl_calendar_month_horizontal_padding = 0x7f07028e
com.clipsy.android.debug:color/m3_navigation_rail_ripple_color_selector = 0x7f0600b5
com.clipsy.android.debug:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f07028d
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f120232
com.clipsy.android.debug:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f07028a
com.clipsy.android.debug:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1200ab
com.clipsy.android.debug:string/path_password_eye_mask_visible = 0x7f1100cc
com.clipsy.android.debug:dimen/mtrl_calendar_header_height = 0x7f070286
com.clipsy.android.debug:dimen/mtrl_calendar_header_content_padding = 0x7f070283
com.clipsy.android.debug:dimen/mtrl_calendar_dialog_background_inset = 0x7f070282
com.clipsy.android.debug:dimen/mtrl_calendar_day_width = 0x7f070280
com.clipsy.android.debug:dimen/mtrl_calendar_day_height = 0x7f07027c
com.clipsy.android.debug:dimen/mtrl_calendar_day_corner = 0x7f07027b
com.clipsy.android.debug:dimen/mtrl_calendar_content_padding = 0x7f07027a
com.clipsy.android.debug:color/m3_ref_palette_primary20 = 0x7f06013c
com.clipsy.android.debug:dimen/mtrl_calendar_action_padding = 0x7f070278
com.clipsy.android.debug:integer/m3_sys_shape_corner_full_corner_family = 0x7f0a0023
com.clipsy.android.debug:color/m3_ref_palette_secondary40 = 0x7f06014b
com.clipsy.android.debug:dimen/mtrl_btn_z = 0x7f070275
com.clipsy.android.debug:color/material_on_surface_emphasis_high_type = 0x7f060274
com.clipsy.android.debug:dimen/mtrl_btn_text_size = 0x7f070274
com.clipsy.android.debug:dimen/mtrl_btn_text_btn_padding_left = 0x7f070272
com.clipsy.android.debug:layout/abc_cascading_menu_item_layout = 0x7f0c000b
com.clipsy.android.debug:dimen/mtrl_btn_stroke_size = 0x7f070270
com.clipsy.android.debug:dimen/m3_chip_checked_hovered_translation_z = 0x7f0700ff
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary99 = 0x7f060106
com.clipsy.android.debug:dimen/mtrl_btn_pressed_z = 0x7f07026e
com.clipsy.android.debug:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0702e2
com.clipsy.android.debug:style/TextAppearance.Material3.DisplaySmall = 0x7f120216
com.clipsy.android.debug:dimen/mtrl_btn_padding_left = 0x7f07026b
com.clipsy.android.debug:styleable/TabLayout = 0x7f1300a8
com.clipsy.android.debug:dimen/mtrl_btn_letter_spacing = 0x7f070268
com.clipsy.android.debug:integer/cancel_button_image_alpha = 0x7f0a0004
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral87 = 0x7f0600cb
com.clipsy.android.debug:dimen/mtrl_btn_inset = 0x7f070267
com.clipsy.android.debug:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0401e5
com.clipsy.android.debug:dimen/mtrl_btn_icon_btn_padding_left = 0x7f070265
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Display4 = 0x7f120022
com.clipsy.android.debug:dimen/mtrl_btn_hovered_z = 0x7f070264
com.clipsy.android.debug:dimen/mtrl_btn_elevation = 0x7f070262
com.clipsy.android.debug:style/Base.V7.Widget.AppCompat.EditText = 0x7f1200c4
com.clipsy.android.debug:attr/boxBackgroundColor = 0x7f040087
com.clipsy.android.debug:attr/marginHorizontal = 0x7f0402f4
com.clipsy.android.debug:dimen/mtrl_btn_disabled_z = 0x7f070261
com.clipsy.android.debug:attr/circularflow_defaultRadius = 0x7f0400df
com.clipsy.android.debug:dimen/mtrl_btn_disabled_elevation = 0x7f070260
com.clipsy.android.debug:color/design_default_color_secondary = 0x7f060064
com.clipsy.android.debug:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f07025b
com.clipsy.android.debug:interpolator/mtrl_fast_out_linear_in = 0x7f0b000e
com.clipsy.android.debug:dimen/mtrl_badge_with_text_size = 0x7f070257
com.clipsy.android.debug:dimen/m3_comp_fab_primary_icon_size = 0x7f070127
com.clipsy.android.debug:dimen/mtrl_extended_fab_elevation = 0x7f0702ae
com.clipsy.android.debug:dimen/mtrl_badge_text_size = 0x7f070254
com.clipsy.android.debug:styleable/CompoundButton = 0x7f13002a
com.clipsy.android.debug:attr/closeItemLayout = 0x7f0400f0
com.clipsy.android.debug:dimen/mtrl_alert_dialog_background_inset_top = 0x7f07024e
com.clipsy.android.debug:dimen/mtrl_alert_dialog_background_inset_start = 0x7f07024d
com.clipsy.android.debug:dimen/m3_datepicker_elevation = 0x7f0701b3
com.clipsy.android.debug:dimen/mtrl_alert_dialog_background_inset_end = 0x7f07024c
com.clipsy.android.debug:style/TextAppearance.AppCompat.Title = 0x7f1201da
com.clipsy.android.debug:attr/snackbarTextViewStyle = 0x7f04041e
com.clipsy.android.debug:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f07024b
com.clipsy.android.debug:dimen/material_timepicker_dialog_buttons_margin_top = 0x7f07024a
com.clipsy.android.debug:dimen/material_textinput_max_width = 0x7f070246
com.clipsy.android.debug:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0702da
com.clipsy.android.debug:drawable/abc_ratingbar_small_material = 0x7f08005c
com.clipsy.android.debug:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f07023d
com.clipsy.android.debug:dimen/mtrl_navigation_rail_compact_width = 0x7f0702d1
com.clipsy.android.debug:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f080021
com.clipsy.android.debug:dimen/material_clock_size = 0x7f070233
com.clipsy.android.debug:dimen/material_clock_period_toggle_width = 0x7f070232
com.clipsy.android.debug:attr/textAppearanceHeadline3 = 0x7f040490
com.clipsy.android.debug:dimen/material_clock_number_text_size = 0x7f07022e
com.clipsy.android.debug:dimen/material_clock_display_padding = 0x7f070228
com.clipsy.android.debug:style/ThemeOverlay.Material3.Button = 0x7f1202b5
com.clipsy.android.debug:color/m3_sys_color_light_surface_dim = 0x7f0601f1
com.clipsy.android.debug:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f07028b
com.clipsy.android.debug:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f070171
com.clipsy.android.debug:dimen/material_bottom_sheet_max_width = 0x7f070226
com.clipsy.android.debug:dimen/tooltip_precise_anchor_extra_offset = 0x7f07032c
com.clipsy.android.debug:id/autoCompleteToEnd = 0x7f090059
com.clipsy.android.debug:attr/layout_editor_absoluteX = 0x7f0402c9
com.clipsy.android.debug:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f070222
com.clipsy.android.debug:string/mtrl_picker_a11y_prev_month = 0x7f110090
com.clipsy.android.debug:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f070221
com.clipsy.android.debug:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f07021f
com.clipsy.android.debug:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f07021b
com.clipsy.android.debug:dimen/mtrl_calendar_year_horizontal_padding = 0x7f07029d
com.clipsy.android.debug:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f070218
com.clipsy.android.debug:style/Widget.Material3.Button.TextButton = 0x7f120383
com.clipsy.android.debug:color/mtrl_switch_thumb_icon_tint = 0x7f0602e0
com.clipsy.android.debug:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f070217
com.clipsy.android.debug:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f070216
com.clipsy.android.debug:dimen/m3_appbar_scrim_height_trigger = 0x7f0700b2
com.clipsy.android.debug:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f070215
com.clipsy.android.debug:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f070214
com.clipsy.android.debug:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f070212
com.clipsy.android.debug:style/Base.V28.Theme.AppCompat = 0x7f1200bc
com.clipsy.android.debug:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f070211
com.clipsy.android.debug:color/m3_slider_inactive_track_color = 0x7f060165
com.clipsy.android.debug:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f070210
com.clipsy.android.debug:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1200da
com.clipsy.android.debug:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f07020d
com.clipsy.android.debug:id/text_device_info = 0x7f0901ff
com.clipsy.android.debug:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f07020c
com.clipsy.android.debug:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f07020b
com.clipsy.android.debug:dimen/mtrl_progress_circular_inset_small = 0x7f0702dc
com.clipsy.android.debug:color/material_dynamic_neutral10 = 0x7f060222
com.clipsy.android.debug:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f07020a
com.clipsy.android.debug:color/abc_decor_view_status_guard = 0x7f060005
com.clipsy.android.debug:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f070206
com.clipsy.android.debug:dimen/m3_simple_item_color_selected_alpha = 0x7f0701ee
com.clipsy.android.debug:attr/imageRotate = 0x7f040256
com.clipsy.android.debug:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f070204
com.clipsy.android.debug:anim/abc_slide_in_bottom = 0x7f010006
com.clipsy.android.debug:dimen/preference_seekbar_padding_vertical = 0x7f070322
com.clipsy.android.debug:string/searchview_navigation_content_description = 0x7f1100e3
com.clipsy.android.debug:dimen/compat_control_corner_material = 0x7f070066
com.clipsy.android.debug:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f070201
com.clipsy.android.debug:macro/m3_comp_switch_unselected_track_color = 0x7f0d0141
com.clipsy.android.debug:attr/checkedIconSize = 0x7f0400c2
com.clipsy.android.debug:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0701fc
com.clipsy.android.debug:dimen/mtrl_btn_padding_right = 0x7f07026c
com.clipsy.android.debug:dimen/m3_sys_elevation_level5 = 0x7f0701fa
com.clipsy.android.debug:dimen/m3_sys_elevation_level3 = 0x7f0701f8
com.clipsy.android.debug:dimen/m3_sys_elevation_level2 = 0x7f0701f7
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary30 = 0x7f0600e4
com.clipsy.android.debug:dimen/m3_snackbar_action_text_color_alpha = 0x7f0701f3
com.clipsy.android.debug:dimen/m3_small_fab_size = 0x7f0701f2
com.clipsy.android.debug:dimen/m3_small_fab_max_image_size = 0x7f0701f1
com.clipsy.android.debug:id/info = 0x7f0900fe
com.clipsy.android.debug:drawable/ic_info = 0x7f08009f
com.clipsy.android.debug:id/accessibility_custom_action_29 = 0x7f090026
com.clipsy.android.debug:dimen/m3_slider_inactive_track_height = 0x7f0701ef
com.clipsy.android.debug:dimen/m3_side_sheet_modal_elevation = 0x7f0701ea
com.clipsy.android.debug:dimen/m3_searchview_height = 0x7f0701e8
com.clipsy.android.debug:dimen/m3_searchbar_text_size = 0x7f0701e5
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Tooltip = 0x7f120234
com.clipsy.android.debug:dimen/m3_searchbar_padding_start = 0x7f0701e3
com.clipsy.android.debug:dimen/m3_searchbar_outlined_stroke_width = 0x7f0701e2
com.clipsy.android.debug:dimen/m3_searchbar_elevation = 0x7f0701de
com.clipsy.android.debug:dimen/m3_ripple_default_alpha = 0x7f0701d9
com.clipsy.android.debug:drawable/$m3_avd_show_password__1 = 0x7f08000a
com.clipsy.android.debug:dimen/mtrl_calendar_day_horizontal_padding = 0x7f07027d
com.clipsy.android.debug:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0701d6
com.clipsy.android.debug:id/item_touch_helper_previous_elevation = 0x7f090102
com.clipsy.android.debug:dimen/m3_navigation_rail_item_min_height = 0x7f0701d4
com.clipsy.android.debug:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0701cd
com.clipsy.android.debug:drawable/notification_bg_low = 0x7f0800f1
com.clipsy.android.debug:dimen/m3_navigation_item_shape_inset_top = 0x7f0701ca
com.clipsy.android.debug:color/m3_switch_thumb_tint = 0x7f060167
com.clipsy.android.debug:dimen/m3_navigation_item_shape_inset_end = 0x7f0701c8
com.clipsy.android.debug:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0701c7
com.clipsy.android.debug:dimen/m3_timepicker_window_elevation = 0x7f070224
com.clipsy.android.debug:dimen/m3_navigation_item_horizontal_padding = 0x7f0701c5
com.clipsy.android.debug:dimen/m3_menu_elevation = 0x7f0701c2
com.clipsy.android.debug:dimen/m3_large_fab_max_image_size = 0x7f0701bf
com.clipsy.android.debug:id/tv_pairing_instruction = 0x7f090225
com.clipsy.android.debug:dimen/m3_fab_translation_z_pressed = 0x7f0701be
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f120209
com.clipsy.android.debug:macro/m3_comp_dialog_headline_color = 0x7f0d0025
com.clipsy.android.debug:dimen/m3_fab_translation_z_hovered_focused = 0x7f0701bd
com.clipsy.android.debug:id/tag_unhandled_key_event_manager = 0x7f0901f0
com.clipsy.android.debug:dimen/m3_extended_fab_top_padding = 0x7f0701ba
com.clipsy.android.debug:color/secondary_text_default_material_light = 0x7f060302
com.clipsy.android.debug:dimen/m3_extended_fab_min_height = 0x7f0701b8
com.clipsy.android.debug:attr/motionDurationLong1 = 0x7f040348
com.clipsy.android.debug:drawable/abc_ic_menu_overflow_material = 0x7f080044
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f120046
com.clipsy.android.debug:dimen/m3_extended_fab_bottom_padding = 0x7f0701b5
com.clipsy.android.debug:dimen/m3_divider_heavy_thickness = 0x7f0701b4
com.clipsy.android.debug:color/m3_ref_palette_neutral17 = 0x7f060118
com.clipsy.android.debug:dimen/mtrl_snackbar_background_corner_radius = 0x7f0702f5
com.clipsy.android.debug:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f120235
com.clipsy.android.debug:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0701b2
com.clipsy.android.debug:layout/mtrl_picker_header_fullscreen = 0x7f0c0065
com.clipsy.android.debug:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0701b1
com.clipsy.android.debug:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0d00fb
com.clipsy.android.debug:integer/m3_card_anim_delay_ms = 0x7f0a000d
com.clipsy.android.debug:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0701b0
com.clipsy.android.debug:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0701ad
com.clipsy.android.debug:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0701ac
com.clipsy.android.debug:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0d00db
com.clipsy.android.debug:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0701c3
com.clipsy.android.debug:attr/state_dragged = 0x7f04043b
com.clipsy.android.debug:dimen/button_stroke_width_small = 0x7f07005d
com.clipsy.android.debug:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0701aa
com.clipsy.android.debug:style/MaterialAlertDialog.Material3 = 0x7f12012e
com.clipsy.android.debug:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0701a8
com.clipsy.android.debug:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0701a7
com.clipsy.android.debug:dimen/mtrl_textinput_box_stroke_width_default = 0x7f070303
com.clipsy.android.debug:style/Widget.AppCompat.Button.Small = 0x7f120322
com.clipsy.android.debug:dimen/m3_badge_horizontal_offset = 0x7f0700bf
com.clipsy.android.debug:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f0701a5
com.clipsy.android.debug:attr/values = 0x7f040517
com.clipsy.android.debug:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f0701a4
com.clipsy.android.debug:integer/m3_sys_motion_duration_medium2 = 0x7f0a0019
com.clipsy.android.debug:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f0701a3
com.clipsy.android.debug:attr/hintAnimationEnabled = 0x7f04023e
com.clipsy.android.debug:color/m3_sys_color_dark_primary_container = 0x7f06017d
com.clipsy.android.debug:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f0701a2
com.clipsy.android.debug:style/Preference.PreferenceScreen = 0x7f12015e
com.clipsy.android.debug:animator/mtrl_chip_state_list_anim = 0x7f020018
com.clipsy.android.debug:attr/floatingActionButtonSmallPrimaryStyle = 0x7f040200
com.clipsy.android.debug:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f0701a0
com.clipsy.android.debug:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f12013c
com.clipsy.android.debug:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f07019f
com.clipsy.android.debug:dimen/m3_comp_switch_track_width = 0x7f07019e
com.clipsy.android.debug:integer/mtrl_view_visible = 0x7f0a0042
com.clipsy.android.debug:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f07019a
com.clipsy.android.debug:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f070194
com.clipsy.android.debug:bool/abc_config_actionMenuItemAllCaps = 0x7f050001
com.clipsy.android.debug:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f070192
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1201e9
com.clipsy.android.debug:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f070191
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f12003e
com.clipsy.android.debug:attr/titleCollapseMode = 0x7f0404e0
com.clipsy.android.debug:dimen/m3_comp_suggestion_chip_container_height = 0x7f070190
com.clipsy.android.debug:id/transition_current_scene = 0x7f09021e
com.clipsy.android.debug:dimen/m3_comp_snackbar_container_elevation = 0x7f07018f
com.clipsy.android.debug:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f07018d
com.clipsy.android.debug:layout/preference_dropdown = 0x7f0c0077
com.clipsy.android.debug:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f07018c
com.clipsy.android.debug:style/Widget.MaterialComponents.ShapeableImageView = 0x7f120465
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f12018e
com.clipsy.android.debug:dimen/design_bottom_navigation_active_item_min_width = 0x7f07006c
com.clipsy.android.debug:drawable/abc_list_pressed_holo_light = 0x7f080051
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f120189
com.clipsy.android.debug:dimen/m3_comp_sheet_side_docked_container_width = 0x7f070188
com.clipsy.android.debug:color/m3_ref_palette_neutral10 = 0x7f060115
com.clipsy.android.debug:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f070187
com.clipsy.android.debug:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f070184
com.clipsy.android.debug:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f070183
com.clipsy.android.debug:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f070180
com.clipsy.android.debug:dimen/m3_comp_search_view_container_elevation = 0x7f07017d
com.clipsy.android.debug:dimen/m3_comp_search_bar_container_elevation = 0x7f070179
com.clipsy.android.debug:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f070174
com.clipsy.android.debug:macro/m3_sys_color_light_surface_tint = 0x7f0d0176
com.clipsy.android.debug:attr/layout_constraintLeft_creator = 0x7f0402b4
com.clipsy.android.debug:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f070170
com.clipsy.android.debug:attr/spanCount = 0x7f04041f
com.clipsy.android.debug:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f07016e
com.clipsy.android.debug:drawable/mtrl_ic_cancel = 0x7f0800d8
com.clipsy.android.debug:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f07016b
com.clipsy.android.debug:attr/splitMinWidth = 0x7f040425
com.clipsy.android.debug:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f070169
com.clipsy.android.debug:styleable/MaterialButton = 0x7f130059
com.clipsy.android.debug:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f070196
com.clipsy.android.debug:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f070168
com.clipsy.android.debug:attr/activityChooserViewStyle = 0x7f040029
com.clipsy.android.debug:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f070164
com.clipsy.android.debug:color/abc_decor_view_status_guard_light = 0x7f060006
com.clipsy.android.debug:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f070162
com.clipsy.android.debug:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f120419
com.clipsy.android.debug:dimen/m3_comp_outlined_card_icon_size = 0x7f070160
com.clipsy.android.debug:attr/contentScrim = 0x7f040151
com.clipsy.android.debug:color/m3_checkbox_button_icon_tint = 0x7f06008b
com.clipsy.android.debug:dimen/m3_navigation_item_shape_inset_start = 0x7f0701c9
com.clipsy.android.debug:dimen/abc_panel_menu_list_width = 0x7f070034
com.clipsy.android.debug:dimen/m3_comp_outlined_card_container_elevation = 0x7f07015e
com.clipsy.android.debug:color/material_dynamic_tertiary10 = 0x7f060256
com.clipsy.android.debug:dimen/m3_comp_navigation_rail_icon_size = 0x7f070159
com.clipsy.android.debug:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f070157
com.clipsy.android.debug:dimen/m3_comp_navigation_rail_container_width = 0x7f070156
com.clipsy.android.debug:string/mtrl_checkbox_state_description_checked = 0x7f110089
com.clipsy.android.debug:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f070154
com.clipsy.android.debug:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f070151
com.clipsy.android.debug:style/Animation.Material3.BottomSheetDialog = 0x7f12000a
com.clipsy.android.debug:dimen/material_clock_display_height = 0x7f070227
com.clipsy.android.debug:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f070150
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1201ec
com.clipsy.android.debug:dimen/m3_comp_navigation_drawer_icon_size = 0x7f07014f
com.clipsy.android.debug:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f07014d
com.clipsy.android.debug:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f0a0038
com.clipsy.android.debug:dimen/m3_comp_navigation_bar_icon_size = 0x7f07014a
com.clipsy.android.debug:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0d0142
com.clipsy.android.debug:id/blocking = 0x7f090060
com.clipsy.android.debug:attr/quantizeMotionSteps = 0x7f0403ca
com.clipsy.android.debug:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f070148
com.clipsy.android.debug:style/Base.V7.Theme.AppCompat = 0x7f1200be
com.clipsy.android.debug:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f07021d
com.clipsy.android.debug:dimen/m3_comp_navigation_bar_container_elevation = 0x7f070146
com.clipsy.android.debug:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f070144
com.clipsy.android.debug:attr/actionBarDivider = 0x7f040003
com.clipsy.android.debug:dimen/m3_comp_linear_progress_indicator_active_indicator_height = 0x7f070142
com.clipsy.android.debug:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f1203b0
com.clipsy.android.debug:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f070140
com.clipsy.android.debug:dimen/hint_pressed_alpha_material_dark = 0x7f0700a4
com.clipsy.android.debug:dimen/m3_comp_input_chip_container_height = 0x7f07013e
com.clipsy.android.debug:dimen/m3_comp_input_chip_container_elevation = 0x7f07013d
com.clipsy.android.debug:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f07013b
com.clipsy.android.debug:attr/collapsingToolbarLayoutStyle = 0x7f0400fb
com.clipsy.android.debug:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f070139
com.clipsy.android.debug:dimen/m3_comp_filter_chip_container_height = 0x7f070138
com.clipsy.android.debug:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f12013e
com.clipsy.android.debug:id/text_input_start_icon = 0x7f090207
com.clipsy.android.debug:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f070132
com.clipsy.android.debug:attr/initialActivityCount = 0x7f04025f
com.clipsy.android.debug:dimen/m3_comp_fab_primary_small_icon_size = 0x7f07012d
com.clipsy.android.debug:dimen/m3_comp_fab_primary_small_container_height = 0x7f07012c
com.clipsy.android.debug:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f070149
com.clipsy.android.debug:style/Widget.AppCompat.AutoCompleteTextView = 0x7f12031c
com.clipsy.android.debug:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0d00df
com.clipsy.android.debug:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f07012a
com.clipsy.android.debug:dimen/m3_comp_fab_primary_container_height = 0x7f070123
com.clipsy.android.debug:attr/cornerRadius = 0x7f04015b
com.clipsy.android.debug:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f07011d
com.clipsy.android.debug:drawable/$avd_hide_password__0 = 0x7f080000
com.clipsy.android.debug:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f07011c
com.clipsy.android.debug:attr/bottomNavigationStyle = 0x7f040083
com.clipsy.android.debug:attr/popupTheme = 0x7f0403b3
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral24 = 0x7f0600c2
com.clipsy.android.debug:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f07011b
com.clipsy.android.debug:id/anticipate = 0x7f090053
com.clipsy.android.debug:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0700be
com.clipsy.android.debug:color/material_dynamic_secondary40 = 0x7f06024d
com.clipsy.android.debug:dimen/button_min_height_small = 0x7f070056
com.clipsy.android.debug:dimen/m3_comp_extended_fab_primary_container_height = 0x7f07011a
com.clipsy.android.debug:dimen/m3_comp_elevated_card_container_elevation = 0x7f070117
com.clipsy.android.debug:color/design_error = 0x7f060067
com.clipsy.android.debug:drawable/abc_text_select_handle_left_mtrl = 0x7f08006e
com.clipsy.android.debug:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f070116
com.clipsy.android.debug:id/clockwise = 0x7f090089
com.clipsy.android.debug:attr/motionEasingLinearInterpolator = 0x7f04035b
com.clipsy.android.debug:dimen/m3_comp_elevated_button_container_elevation = 0x7f070115
com.clipsy.android.debug:layout/mtrl_layout_snackbar_include = 0x7f0c005f
com.clipsy.android.debug:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f070111
com.clipsy.android.debug:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f07010f
com.clipsy.android.debug:dimen/m3_comp_outlined_text_field_outline_width = 0x7f070167
com.clipsy.android.debug:dimen/m3_comp_badge_size = 0x7f07010c
com.clipsy.android.debug:attr/layout_constraintGuide_percent = 0x7f0402ab
com.clipsy.android.debug:dimen/m3_comp_badge_large_size = 0x7f07010b
com.clipsy.android.debug:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f070256
com.clipsy.android.debug:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f07010a
com.clipsy.android.debug:attr/minHideDelay = 0x7f040339
com.clipsy.android.debug:dimen/hint_alpha_material_dark = 0x7f0700a2
com.clipsy.android.debug:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f070109
com.clipsy.android.debug:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1201d5
com.clipsy.android.debug:attr/motionDebug = 0x7f040343
com.clipsy.android.debug:dimen/m3_comp_assist_chip_container_height = 0x7f070106
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f120073
com.clipsy.android.debug:dimen/m3_chip_dragged_translation_z = 0x7f070102
com.clipsy.android.debug:layout/abc_popup_menu_header_item_layout = 0x7f0c0012
com.clipsy.android.debug:dimen/m3_chip_disabled_translation_z = 0x7f070101
com.clipsy.android.debug:dimen/mtrl_slider_halo_radius = 0x7f0702ea
com.clipsy.android.debug:dimen/m3_carousel_small_item_size_max = 0x7f0700fd
com.clipsy.android.debug:dimen/abc_action_bar_default_height_material = 0x7f070002
com.clipsy.android.debug:dimen/m3_carousel_small_item_default_corner_size = 0x7f0700fc
com.clipsy.android.debug:drawable/abc_list_selector_holo_dark = 0x7f080056
com.clipsy.android.debug:dimen/material_clock_face_margin_top = 0x7f07022a
com.clipsy.android.debug:dimen/m3_carousel_debug_keyline_width = 0x7f0700f9
com.clipsy.android.debug:attr/seekBarIncrement = 0x7f0403ef
com.clipsy.android.debug:dimen/m3_card_elevated_dragged_z = 0x7f0700f3
com.clipsy.android.debug:dimen/m3_card_elevated_disabled_z = 0x7f0700f2
com.clipsy.android.debug:dimen/m3_card_dragged_z = 0x7f0700f1
com.clipsy.android.debug:string/no = 0x7f1100c3
com.clipsy.android.debug:string/device_manual_ip_hint = 0x7f110043
com.clipsy.android.debug:dimen/m3_card_disabled_z = 0x7f0700f0
com.clipsy.android.debug:id/text_timestamp = 0x7f09020c
com.clipsy.android.debug:dimen/m3_btn_text_btn_padding_right = 0x7f0700ed
com.clipsy.android.debug:dimen/m3_btn_padding_top = 0x7f0700e8
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f120187
com.clipsy.android.debug:dimen/m3_btn_padding_right = 0x7f0700e7
com.clipsy.android.debug:dimen/material_emphasis_medium = 0x7f07023a
com.clipsy.android.debug:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f07015a
com.clipsy.android.debug:dimen/m3_btn_padding_left = 0x7f0700e6
com.clipsy.android.debug:style/ThemeOverlay.Material3.Search = 0x7f1202df
com.clipsy.android.debug:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f070208
com.clipsy.android.debug:dimen/m3_btn_padding_bottom = 0x7f0700e5
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1202fe
com.clipsy.android.debug:style/TextAppearance.Design.Prefix = 0x7f1201fc
com.clipsy.android.debug:attr/titleTextAppearance = 0x7f0404e9
com.clipsy.android.debug:dimen/m3_btn_inset = 0x7f0700e3
com.clipsy.android.debug:color/design_dark_default_color_on_surface = 0x7f060053
com.clipsy.android.debug:dimen/m3_btn_icon_only_icon_padding = 0x7f0700e1
com.clipsy.android.debug:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f070141
com.clipsy.android.debug:dimen/design_tab_text_size = 0x7f070097
com.clipsy.android.debug:dimen/m3_chip_icon_size = 0x7f070105
com.clipsy.android.debug:dimen/m3_btn_icon_btn_padding_left = 0x7f0700dd
com.clipsy.android.debug:style/ThemeOverlay.Material3.ActionBar = 0x7f1202ab
com.clipsy.android.debug:dimen/m3_btn_elevation = 0x7f0700dc
com.clipsy.android.debug:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1200d4
com.clipsy.android.debug:attr/layout_constraintHeight_max = 0x7f0402ae
com.clipsy.android.debug:dimen/m3_btn_elevated_btn_elevation = 0x7f0700db
com.clipsy.android.debug:color/mtrl_tabs_ripple_color = 0x7f0602e8
com.clipsy.android.debug:dimen/m3_btn_disabled_elevation = 0x7f0700d9
com.clipsy.android.debug:attr/minSeparation = 0x7f04033a
com.clipsy.android.debug:dimen/m3_btn_dialog_btn_min_width = 0x7f0700d7
com.clipsy.android.debug:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f120476
com.clipsy.android.debug:dimen/m3_bottomappbar_height = 0x7f0700d5
com.clipsy.android.debug:dimen/m3_bottom_sheet_modal_elevation = 0x7f0700d0
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f120116
com.clipsy.android.debug:dimen/m3_comp_search_bar_avatar_size = 0x7f070178
com.clipsy.android.debug:dimen/m3_bottom_sheet_elevation = 0x7f0700cf
com.clipsy.android.debug:macro/m3_comp_search_view_container_surface_tint_layer_color = 0x7f0d00f3
com.clipsy.android.debug:attr/simpleItems = 0x7f040415
com.clipsy.android.debug:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0700ce
com.clipsy.android.debug:id/accessibility_custom_action_21 = 0x7f09001e
com.clipsy.android.debug:anim/m3_bottom_sheet_slide_out = 0x7f010022
com.clipsy.android.debug:dimen/m3_extended_fab_icon_padding = 0x7f0701b7
com.clipsy.android.debug:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f12010b
com.clipsy.android.debug:attr/colorPrimaryContainer = 0x7f04011e
com.clipsy.android.debug:dimen/m3_bottom_nav_item_padding_top = 0x7f0700cc
com.clipsy.android.debug:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0700ca
com.clipsy.android.debug:style/Widget.MaterialComponents.Chip.Entry = 0x7f120430
com.clipsy.android.debug:id/dropdown_menu = 0x7f0900b8
com.clipsy.android.debug:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0700c9
com.clipsy.android.debug:style/Animation.Material3.SideSheetDialog = 0x7f12000b
com.clipsy.android.debug:attr/cornerSizeBottomLeft = 0x7f04015d
com.clipsy.android.debug:dimen/m3_badge_with_text_size = 0x7f0700c5
com.clipsy.android.debug:dimen/m3_badge_vertical_offset = 0x7f0700c2
com.clipsy.android.debug:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0700bd
com.clipsy.android.debug:dimen/material_time_picker_minimum_screen_width = 0x7f070249
com.clipsy.android.debug:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0700bc
com.clipsy.android.debug:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0700bb
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_tertiary = 0x7f0601c5
com.clipsy.android.debug:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0700b9
com.clipsy.android.debug:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f070182
com.clipsy.android.debug:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0700b8
com.clipsy.android.debug:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f0701a1
com.clipsy.android.debug:attr/dividerPadding = 0x7f040197
com.clipsy.android.debug:dimen/m3_appbar_size_compact = 0x7f0700b5
com.clipsy.android.debug:dimen/m3_alert_dialog_icon_margin = 0x7f0700ad
com.clipsy.android.debug:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f1203a1
com.clipsy.android.debug:dimen/m3_alert_dialog_elevation = 0x7f0700ac
com.clipsy.android.debug:layout/notification_template_icon_group = 0x7f0c0070
com.clipsy.android.debug:dimen/m3_alert_dialog_action_bottom_padding = 0x7f0700a9
com.clipsy.android.debug:attr/layout_constraintHorizontal_bias = 0x7f0402b1
com.clipsy.android.debug:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f070158
com.clipsy.android.debug:dimen/item_touch_helper_swipe_escape_velocity = 0x7f0700a8
com.clipsy.android.debug:dimen/mtrl_extended_fab_top_padding = 0x7f0702b7
com.clipsy.android.debug:attr/layout_constraintBaseline_toBottomOf = 0x7f04029e
com.clipsy.android.debug:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f0700a6
com.clipsy.android.debug:dimen/hint_alpha_material_light = 0x7f0700a3
com.clipsy.android.debug:style/Base.Widget.Material3.FloatingActionButton = 0x7f12010c
com.clipsy.android.debug:attr/region_heightLessThan = 0x7f0403d8
com.clipsy.android.debug:dimen/highlight_alpha_material_dark = 0x7f0700a0
com.clipsy.android.debug:xml/backup_rules = 0x7f140000
com.clipsy.android.debug:string/abc_menu_space_shortcut_label = 0x7f11000f
com.clipsy.android.debug:attr/snackbarButtonStyle = 0x7f04041c
com.clipsy.android.debug:dimen/highlight_alpha_material_colored = 0x7f07009f
com.clipsy.android.debug:string/bottomsheet_action_expand_halfway = 0x7f11002c
com.clipsy.android.debug:attr/actionModeStyle = 0x7f04001f
com.clipsy.android.debug:dimen/notification_large_icon_height = 0x7f070314
com.clipsy.android.debug:dimen/design_tab_text_size_2line = 0x7f070098
com.clipsy.android.debug:style/TextAppearance.Material3.TitleLarge = 0x7f120221
com.clipsy.android.debug:dimen/mtrl_navigation_rail_text_size = 0x7f0702d8
com.clipsy.android.debug:color/material_dynamic_neutral_variant95 = 0x7f060239
com.clipsy.android.debug:dimen/design_tab_scrollable_min_width = 0x7f070096
com.clipsy.android.debug:id/endToStart = 0x7f0900c6
com.clipsy.android.debug:dimen/design_snackbar_text_size = 0x7f070094
com.clipsy.android.debug:dimen/design_snackbar_padding_vertical_2lines = 0x7f070093
com.clipsy.android.debug:dimen/design_snackbar_min_width = 0x7f070090
com.clipsy.android.debug:id/scale = 0x7f0901a2
com.clipsy.android.debug:dimen/design_snackbar_extra_spacing_horizontal = 0x7f07008e
com.clipsy.android.debug:dimen/abc_text_size_medium_material = 0x7f070049
com.clipsy.android.debug:dimen/design_navigation_padding_bottom = 0x7f070088
com.clipsy.android.debug:dimen/design_navigation_max_width = 0x7f070087
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1203ca
com.clipsy.android.debug:color/mtrl_navigation_item_text_color = 0x7f0602d9
com.clipsy.android.debug:dimen/design_navigation_item_horizontal_padding = 0x7f070084
com.clipsy.android.debug:styleable/AppCompatTheme = 0x7f130015
com.clipsy.android.debug:layout/item_device = 0x7f0c0038
com.clipsy.android.debug:dimen/preference_dropdown_padding_start = 0x7f07031f
com.clipsy.android.debug:dimen/design_navigation_elevation = 0x7f070081
com.clipsy.android.debug:style/AlertDialog.Clipsy.Body = 0x7f120003
com.clipsy.android.debug:dimen/design_fab_size_normal = 0x7f07007e
com.clipsy.android.debug:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0d009a
com.clipsy.android.debug:dimen/material_clock_hand_center_dot_radius = 0x7f07022b
com.clipsy.android.debug:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0d00a5
com.clipsy.android.debug:dimen/design_fab_image_size = 0x7f07007c
com.clipsy.android.debug:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f12043c
com.clipsy.android.debug:drawable/abc_seekbar_thumb_material = 0x7f080062
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral100 = 0x7f0600bd
com.clipsy.android.debug:dimen/design_fab_border_width = 0x7f07007a
com.clipsy.android.debug:dimen/material_clock_period_toggle_vertical_gap = 0x7f070231
com.clipsy.android.debug:dimen/design_bottom_sheet_modal_elevation = 0x7f070078
com.clipsy.android.debug:dimen/design_bottom_navigation_item_min_width = 0x7f070072
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1201e8
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f120035
com.clipsy.android.debug:layout/material_clock_display = 0x7f0c0040
com.clipsy.android.debug:attr/arrowHeadLength = 0x7f040042
com.clipsy.android.debug:attr/materialCalendarYearNavigationButton = 0x7f04030f
com.clipsy.android.debug:dimen/design_bottom_navigation_item_max_width = 0x7f070071
com.clipsy.android.debug:attr/textOutlineColor = 0x7f0404bb
com.clipsy.android.debug:dimen/design_bottom_navigation_elevation = 0x7f07006e
com.clipsy.android.debug:dimen/m3_comp_bottom_app_bar_container_height = 0x7f07010e
com.clipsy.android.debug:dimen/design_bottom_navigation_active_text_size = 0x7f07006d
com.clipsy.android.debug:dimen/design_bottom_navigation_active_item_max_width = 0x7f07006b
com.clipsy.android.debug:dimen/design_appbar_elevation = 0x7f07006a
com.clipsy.android.debug:style/Widget.Material3.FloatingActionButton.Primary = 0x7f1203b3
com.clipsy.android.debug:dimen/compat_button_padding_vertical_material = 0x7f070065
com.clipsy.android.debug:dimen/compat_button_inset_horizontal_material = 0x7f070062
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral98 = 0x7f0600d1
com.clipsy.android.debug:dimen/clock_face_margin_start = 0x7f070061
com.clipsy.android.debug:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0d0156
com.clipsy.android.debug:dimen/mtrl_tooltip_minHeight = 0x7f07030c
com.clipsy.android.debug:dimen/button_padding_vertical_small = 0x7f07005b
com.clipsy.android.debug:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f12028b
com.clipsy.android.debug:string/character_counter_pattern = 0x7f110039
com.clipsy.android.debug:dimen/button_padding_horizontal_small = 0x7f070059
com.clipsy.android.debug:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f1201b8
com.clipsy.android.debug:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f070134
com.clipsy.android.debug:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1203d8
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f120188
com.clipsy.android.debug:color/abc_tint_btn_checkable = 0x7f060013
com.clipsy.android.debug:dimen/button_padding_horizontal = 0x7f070058
com.clipsy.android.debug:dimen/button_min_width = 0x7f070057
com.clipsy.android.debug:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0d00d9
com.clipsy.android.debug:dimen/m3_badge_with_text_offset = 0x7f0700c4
com.clipsy.android.debug:dimen/m3_chip_elevated_elevation = 0x7f070103
com.clipsy.android.debug:dimen/button_min_height = 0x7f070055
com.clipsy.android.debug:dimen/appcompat_dialog_background_inset = 0x7f070051
com.clipsy.android.debug:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0800d0
com.clipsy.android.debug:dimen/abc_text_size_title_material_toolbar = 0x7f070050
com.clipsy.android.debug:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f070133
com.clipsy.android.debug:dimen/abc_text_size_subtitle_material_toolbar = 0x7f07004e
com.clipsy.android.debug:drawable/abc_star_half_black_48dp = 0x7f080068
com.clipsy.android.debug:style/Widget.AppCompat.ListView.DropDown = 0x7f120345
com.clipsy.android.debug:color/design_box_stroke_color = 0x7f06004c
com.clipsy.android.debug:dimen/abc_text_size_small_material = 0x7f07004c
com.clipsy.android.debug:dimen/abc_text_size_menu_material = 0x7f07004b
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f120040
com.clipsy.android.debug:attr/actionModeCloseContentDescription = 0x7f040015
com.clipsy.android.debug:dimen/abc_text_size_menu_header_material = 0x7f07004a
com.clipsy.android.debug:id/open_search_view_root = 0x7f090170
com.clipsy.android.debug:drawable/notification_bg_normal_pressed = 0x7f0800f5
com.clipsy.android.debug:dimen/abc_dialog_corner_radius_material = 0x7f07001b
com.clipsy.android.debug:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f070307
com.clipsy.android.debug:attr/placeholderActivityName = 0x7f0403a6
com.clipsy.android.debug:dimen/design_snackbar_action_inline_max_width = 0x7f07008a
com.clipsy.android.debug:dimen/abc_text_size_large_material = 0x7f070048
com.clipsy.android.debug:dimen/abc_text_size_headline_material = 0x7f070047
com.clipsy.android.debug:dimen/abc_text_size_body_2_material = 0x7f070040
com.clipsy.android.debug:color/m3_timepicker_button_background_color = 0x7f06020f
com.clipsy.android.debug:dimen/abc_text_size_body_1_material = 0x7f07003f
com.clipsy.android.debug:dimen/abc_star_small = 0x7f07003d
com.clipsy.android.debug:color/m3_card_stroke_color = 0x7f06008a
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_surface = 0x7f06019e
com.clipsy.android.debug:drawable/$avd_show_password__1 = 0x7f080004
com.clipsy.android.debug:id/recycler_view_available_devices = 0x7f090193
com.clipsy.android.debug:attr/textStartPadding = 0x7f0404bf
com.clipsy.android.debug:dimen/m3_badge_with_text_horizontal_offset = 0x7f0700c3
com.clipsy.android.debug:dimen/abc_star_medium = 0x7f07003c
com.clipsy.android.debug:color/material_timepicker_clockface = 0x7f0602b4
com.clipsy.android.debug:dimen/abc_select_dialog_padding_start_material = 0x7f07003a
com.clipsy.android.debug:id/accessibility_action_clickable_span = 0x7f09000f
com.clipsy.android.debug:attr/splitMinSmallestWidth = 0x7f040424
com.clipsy.android.debug:dimen/abc_search_view_preferred_width = 0x7f070037
com.clipsy.android.debug:style/TextAppearance.Design.Counter = 0x7f1201f6
com.clipsy.android.debug:dimen/material_cursor_width = 0x7f070235
com.clipsy.android.debug:dimen/abc_search_view_preferred_height = 0x7f070036
com.clipsy.android.debug:attr/onTouchUp = 0x7f040387
com.clipsy.android.debug:dimen/abc_list_item_padding_horizontal_material = 0x7f070033
com.clipsy.android.debug:macro/m3_comp_elevated_card_container_shape = 0x7f0d002c
com.clipsy.android.debug:attr/summary = 0x7f040455
com.clipsy.android.debug:dimen/abc_edit_text_inset_top_material = 0x7f07002e
com.clipsy.android.debug:dimen/abc_dropdownitem_text_padding_right = 0x7f07002b
com.clipsy.android.debug:string/mtrl_picker_range_header_unselected = 0x7f1100a6
com.clipsy.android.debug:dimen/abc_dropdownitem_text_padding_left = 0x7f07002a
com.clipsy.android.debug:dimen/abc_disabled_alpha_material_light = 0x7f070028
com.clipsy.android.debug:id/snapMargins = 0x7f0901c8
com.clipsy.android.debug:attr/shapeAppearance = 0x7f0403f8
com.clipsy.android.debug:dimen/abc_disabled_alpha_material_dark = 0x7f070027
com.clipsy.android.debug:dimen/abc_dialog_title_divider_material = 0x7f070026
com.clipsy.android.debug:dimen/abc_dialog_padding_top_material = 0x7f070025
com.clipsy.android.debug:string/mtrl_picker_range_header_selected = 0x7f1100a4
com.clipsy.android.debug:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f07015f
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0d0162
com.clipsy.android.debug:attr/textAppearanceSmallPopupMenu = 0x7f0404a3
com.clipsy.android.debug:dimen/abc_dialog_min_width_major = 0x7f070022
com.clipsy.android.debug:style/Widget.Material3.Chip.Filter.Elevated = 0x7f120393
com.clipsy.android.debug:drawable/abc_textfield_search_material = 0x7f080075
com.clipsy.android.debug:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f070020
com.clipsy.android.debug:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1202c9
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0d00b5
com.clipsy.android.debug:id/square = 0x7f0901d2
com.clipsy.android.debug:dimen/abc_dialog_fixed_height_minor = 0x7f07001d
com.clipsy.android.debug:attr/layout_constraintHeight_percent = 0x7f0402b0
com.clipsy.android.debug:dimen/abc_dialog_fixed_height_major = 0x7f07001c
com.clipsy.android.debug:dimen/abc_control_padding_material = 0x7f07001a
com.clipsy.android.debug:attr/showPaths = 0x7f04040b
com.clipsy.android.debug:dimen/m3_btn_icon_only_default_padding = 0x7f0700df
com.clipsy.android.debug:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1202e7
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0600d4
com.clipsy.android.debug:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f07025a
com.clipsy.android.debug:dimen/abc_control_inset_material = 0x7f070019
com.clipsy.android.debug:dimen/abc_control_corner_material = 0x7f070018
com.clipsy.android.debug:macro/m3_comp_outlined_autocomplete_menu_list_item_selected_container_color = 0x7f0d00a2
com.clipsy.android.debug:dimen/abc_action_button_min_width_overflow_material = 0x7f07000f
com.clipsy.android.debug:styleable/MotionLayout = 0x7f130070
com.clipsy.android.debug:dimen/design_tab_max_width = 0x7f070095
com.clipsy.android.debug:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0d006a
com.clipsy.android.debug:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f070185
com.clipsy.android.debug:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f07000c
com.clipsy.android.debug:attr/itemRippleColor = 0x7f040274
com.clipsy.android.debug:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0700eb
com.clipsy.android.debug:dimen/abc_action_bar_overflow_padding_start_material = 0x7f070008
com.clipsy.android.debug:style/ThemeOverlay.Material3.NavigationView = 0x7f1202dd
com.clipsy.android.debug:style/Theme.Material3.DynamicColors.DayNight = 0x7f120267
com.clipsy.android.debug:dimen/abc_action_bar_overflow_padding_end_material = 0x7f070007
com.clipsy.android.debug:dimen/abc_action_bar_elevation_material = 0x7f070005
com.clipsy.android.debug:dimen/abc_action_bar_default_padding_end_material = 0x7f070003
com.clipsy.android.debug:color/clipsy_primary = 0x7f060045
com.clipsy.android.debug:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f070186
com.clipsy.android.debug:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f070120
com.clipsy.android.debug:dimen/abc_action_bar_content_inset_with_nav = 0x7f070001
com.clipsy.android.debug:color/white = 0x7f060318
com.clipsy.android.debug:integer/mtrl_view_gone = 0x7f0a0040
com.clipsy.android.debug:color/highlighted_text_material_dark = 0x7f06007b
com.clipsy.android.debug:attr/collapseIcon = 0x7f0400f2
com.clipsy.android.debug:attr/fastScrollEnabled = 0x7f0401f1
com.clipsy.android.debug:attr/motionDurationMedium4 = 0x7f04034f
com.clipsy.android.debug:color/text_secondary = 0x7f060315
com.clipsy.android.debug:string/mtrl_picker_a11y_next_month = 0x7f11008f
com.clipsy.android.debug:dimen/abc_dialog_fixed_width_major = 0x7f07001e
com.clipsy.android.debug:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f070304
com.clipsy.android.debug:color/text_primary = 0x7f060314
com.clipsy.android.debug:layout/abc_search_dropdown_item_icons_2line = 0x7f0c0018
com.clipsy.android.debug:color/switch_track_color = 0x7f060311
com.clipsy.android.debug:style/Base.V26.Theme.AppCompat = 0x7f1200b9
com.clipsy.android.debug:color/switch_thumb_normal_material_light = 0x7f060310
com.clipsy.android.debug:id/tag_accessibility_clickable_spans = 0x7f0901e7
com.clipsy.android.debug:dimen/design_bottom_navigation_margin = 0x7f070074
com.clipsy.android.debug:styleable/OnClick = 0x7f130080
com.clipsy.android.debug:attr/iconStartPadding = 0x7f04024d
com.clipsy.android.debug:color/switch_thumb_material_dark = 0x7f06030d
com.clipsy.android.debug:attr/rotationCenterId = 0x7f0403e0
com.clipsy.android.debug:color/status_disconnected = 0x7f060309
com.clipsy.android.debug:color/status_connected = 0x7f060307
com.clipsy.android.debug:dimen/abc_text_size_subhead_material = 0x7f07004d
com.clipsy.android.debug:color/source_local = 0x7f060305
com.clipsy.android.debug:dimen/abc_dropdownitem_icon_width = 0x7f070029
com.clipsy.android.debug:attr/panelMenuListTheme = 0x7f040396
com.clipsy.android.debug:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0701fd
com.clipsy.android.debug:attr/layout_scrollFlags = 0x7f0402d7
com.clipsy.android.debug:color/secondary_text_disabled_material_light = 0x7f060304
com.clipsy.android.debug:attr/actionModeWebSearchDrawable = 0x7f040021
com.clipsy.android.debug:color/secondary_text_default_material_dark = 0x7f060301
com.clipsy.android.debug:macro/m3_comp_search_view_header_input_text_type = 0x7f0d00f7
com.clipsy.android.debug:dimen/notification_right_side_padding_top = 0x7f070319
com.clipsy.android.debug:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f070255
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f120076
com.clipsy.android.debug:color/ripple_material_light = 0x7f0602ff
com.clipsy.android.debug:drawable/ic_search = 0x7f0800ac
com.clipsy.android.debug:dimen/m3_comp_switch_track_height = 0x7f07019d
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f120291
com.clipsy.android.debug:color/ripple_material_dark = 0x7f0602fe
com.clipsy.android.debug:color/purple_500 = 0x7f0602fc
com.clipsy.android.debug:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f070284
com.clipsy.android.debug:dimen/m3_btn_text_btn_padding_left = 0x7f0700ec
com.clipsy.android.debug:color/purple_200 = 0x7f0602fb
com.clipsy.android.debug:color/primary_text_disabled_material_dark = 0x7f0602f9
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_outline = 0x7f060198
com.clipsy.android.debug:color/primary_material_light = 0x7f0602f6
com.clipsy.android.debug:color/primary_dark_material_light = 0x7f0602f4
com.clipsy.android.debug:color/mtrl_textinput_hovered_box_stroke_color = 0x7f0602ee
com.clipsy.android.debug:color/teal_200 = 0x7f060312
com.clipsy.android.debug:color/mtrl_textinput_focused_box_stroke_color = 0x7f0602ed
com.clipsy.android.debug:color/mtrl_textinput_disabled_color = 0x7f0602eb
com.clipsy.android.debug:style/Base.Widget.Material3.CollapsingToolbar = 0x7f120106
com.clipsy.android.debug:color/mtrl_text_btn_text_color_selector = 0x7f0602e9
com.clipsy.android.debug:color/mtrl_tabs_icon_color_selector = 0x7f0602e5
com.clipsy.android.debug:dimen/abc_dialog_fixed_width_minor = 0x7f07001f
com.clipsy.android.debug:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f120416
com.clipsy.android.debug:color/mtrl_tabs_colored_ripple_color = 0x7f0602e4
com.clipsy.android.debug:color/mtrl_outlined_stroke_color = 0x7f0602dd
com.clipsy.android.debug:dimen/cardview_default_radius = 0x7f070060
com.clipsy.android.debug:attr/motionDurationExtraLong2 = 0x7f040345
com.clipsy.android.debug:color/mtrl_outlined_icon_tint = 0x7f0602dc
com.clipsy.android.debug:id/accessibility_custom_action_6 = 0x7f09002c
com.clipsy.android.debug:attr/colorControlActivated = 0x7f040101
com.clipsy.android.debug:attr/tabIndicatorFullWidth = 0x7f04046a
com.clipsy.android.debug:color/mtrl_on_surface_ripple_color = 0x7f0602db
com.clipsy.android.debug:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0602da
com.clipsy.android.debug:dimen/m3_comp_fab_primary_large_icon_size = 0x7f070129
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0600fe
com.clipsy.android.debug:dimen/abc_text_size_display_3_material = 0x7f070045
com.clipsy.android.debug:color/mtrl_navigation_bar_ripple_color = 0x7f0602d6
com.clipsy.android.debug:dimen/material_cursor_inset = 0x7f070234
com.clipsy.android.debug:attr/iconGravity = 0x7f040249
com.clipsy.android.debug:color/mtrl_navigation_bar_item_tint = 0x7f0602d5
com.clipsy.android.debug:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f08003e
com.clipsy.android.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f120194
com.clipsy.android.debug:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0602d4
com.clipsy.android.debug:style/Widget.Material3.Chip.Input = 0x7f120394
com.clipsy.android.debug:attr/keyboardIcon = 0x7f040287
com.clipsy.android.debug:color/mtrl_navigation_bar_colored_item_tint = 0x7f0602d3
com.clipsy.android.debug:attr/layout_goneMarginStart = 0x7f0402d0
com.clipsy.android.debug:color/mtrl_indicator_text_color = 0x7f0602d2
com.clipsy.android.debug:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f070189
com.clipsy.android.debug:dimen/design_snackbar_elevation = 0x7f07008d
com.clipsy.android.debug:color/mtrl_filled_stroke_color = 0x7f0602d1
com.clipsy.android.debug:id/checked = 0x7f090083
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f0601a5
com.clipsy.android.debug:color/mtrl_filled_icon_tint = 0x7f0602d0
com.clipsy.android.debug:color/mtrl_fab_bg_color_selector = 0x7f0602cc
com.clipsy.android.debug:color/mtrl_navigation_item_background_color = 0x7f0602d7
com.clipsy.android.debug:color/mtrl_error = 0x7f0602cb
com.clipsy.android.debug:styleable/KeyFramesVelocity = 0x7f13004c
com.clipsy.android.debug:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0d00fe
com.clipsy.android.debug:dimen/m3_card_hovered_z = 0x7f0700f7
com.clipsy.android.debug:color/mtrl_choice_chip_text_color = 0x7f0602ca
com.clipsy.android.debug:color/m3_sys_color_light_on_primary = 0x7f0601dc
com.clipsy.android.debug:drawable/abc_switch_track_mtrl_alpha = 0x7f08006a
com.clipsy.android.debug:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f120462
com.clipsy.android.debug:attr/SharedValueId = 0x7f040001
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f08000c
com.clipsy.android.debug:attr/contentPaddingLeft = 0x7f04014d
com.clipsy.android.debug:color/mtrl_choice_chip_ripple_color = 0x7f0602c9
com.clipsy.android.debug:attr/dialogIcon = 0x7f040189
com.clipsy.android.debug:color/mtrl_choice_chip_background_color = 0x7f0602c8
com.clipsy.android.debug:attr/materialIconButtonOutlinedStyle = 0x7f04031b
com.clipsy.android.debug:attr/motionEffect_alpha = 0x7f040360
com.clipsy.android.debug:color/m3_ref_palette_tertiary0 = 0x7f060153
com.clipsy.android.debug:color/mtrl_chip_text_color = 0x7f0602c7
com.clipsy.android.debug:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f1203b6
com.clipsy.android.debug:color/mtrl_chip_surface_color = 0x7f0602c6
com.clipsy.android.debug:id/accessibility_custom_action_12 = 0x7f090014
com.clipsy.android.debug:color/mtrl_card_view_foreground = 0x7f0602c2
com.clipsy.android.debug:color/purple_700 = 0x7f0602fd
com.clipsy.android.debug:color/mtrl_calendar_selected_range = 0x7f0602c1
com.clipsy.android.debug:color/mtrl_btn_text_color_disabled = 0x7f0602bd
com.clipsy.android.debug:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f120439
com.clipsy.android.debug:color/mtrl_btn_stroke_color_selector = 0x7f0602ba
com.clipsy.android.debug:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1200ed
com.clipsy.android.debug:attr/dropDownBackgroundTint = 0x7f0401aa
com.clipsy.android.debug:color/mtrl_btn_ripple_color = 0x7f0602b9
com.clipsy.android.debug:color/medium_contrast_text = 0x7f0602b7
com.clipsy.android.debug:id/sawtooth = 0x7f0901a1
com.clipsy.android.debug:attr/chipCornerRadius = 0x7f0400c8
com.clipsy.android.debug:attr/windowFixedWidthMajor = 0x7f04052e
com.clipsy.android.debug:attr/useMaterialThemeColors = 0x7f040515
com.clipsy.android.debug:color/material_timepicker_modebutton_tint = 0x7f0602b5
com.clipsy.android.debug:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0d0137
com.clipsy.android.debug:id/accessibility_custom_action_19 = 0x7f09001b
com.clipsy.android.debug:attr/animateCircleAngleTo = 0x7f040039
com.clipsy.android.debug:dimen/mtrl_tooltip_arrowSize = 0x7f07030a
com.clipsy.android.debug:color/material_timepicker_clock_text_color = 0x7f0602b3
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f12030c
com.clipsy.android.debug:style/Base.Widget.AppCompat.ImageButton = 0x7f1200e0
com.clipsy.android.debug:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f07012e
com.clipsy.android.debug:color/material_slider_inactive_track_color = 0x7f0602af
com.clipsy.android.debug:color/material_slider_inactive_tick_marks_color = 0x7f0602ae
com.clipsy.android.debug:dimen/mtrl_progress_track_thickness = 0x7f0702e6
com.clipsy.android.debug:color/material_slider_active_track_color = 0x7f0602ac
com.clipsy.android.debug:dimen/m3_carousel_small_item_size_min = 0x7f0700fe
com.clipsy.android.debug:string/pref_auto_cleanup_summary = 0x7f1100cf
com.clipsy.android.debug:attr/cardViewStyle = 0x7f0400aa
com.clipsy.android.debug:color/material_slider_active_tick_marks_color = 0x7f0602ab
com.clipsy.android.debug:attr/textAppearanceBodyMedium = 0x7f040487
com.clipsy.android.debug:color/material_personalized_hint_foreground_inverse = 0x7f0602a8
com.clipsy.android.debug:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0602a5
com.clipsy.android.debug:dimen/mtrl_chip_text_size = 0x7f0702a7
com.clipsy.android.debug:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f0602a4
com.clipsy.android.debug:color/m3_ref_palette_primary80 = 0x7f060142
com.clipsy.android.debug:color/material_personalized_color_text_primary_inverse = 0x7f0602a3
com.clipsy.android.debug:id/centerCrop = 0x7f09007b
com.clipsy.android.debug:color/material_personalized_color_tertiary_container = 0x7f0602a1
com.clipsy.android.debug:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f070165
com.clipsy.android.debug:color/material_personalized_color_surface_inverse = 0x7f06029e
com.clipsy.android.debug:color/material_personalized_color_surface_dim = 0x7f06029d
com.clipsy.android.debug:color/material_personalized_color_surface_container_highest = 0x7f06029a
com.clipsy.android.debug:color/material_personalized_color_surface_container_high = 0x7f060299
com.clipsy.android.debug:color/material_personalized_color_surface_container = 0x7f060298
com.clipsy.android.debug:attr/materialCalendarHeaderSelection = 0x7f040308
com.clipsy.android.debug:color/material_personalized_color_surface_bright = 0x7f060297
com.clipsy.android.debug:attr/headerLayout = 0x7f040233
com.clipsy.android.debug:color/material_personalized_color_secondary_text_inverse = 0x7f060295
com.clipsy.android.debug:color/material_personalized_color_secondary_text = 0x7f060294
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1203c4
com.clipsy.android.debug:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f0700a7
com.clipsy.android.debug:color/material_personalized_color_primary_container = 0x7f06028e
com.clipsy.android.debug:dimen/m3_card_elevated_elevation = 0x7f0700f4
com.clipsy.android.debug:color/material_personalized_color_primary = 0x7f06028d
com.clipsy.android.debug:color/material_personalized_color_outline_variant = 0x7f06028c
com.clipsy.android.debug:style/ThemeOverlay.AppCompat.Dialog = 0x7f1202a6
com.clipsy.android.debug:id/accessibility_custom_action_27 = 0x7f090024
com.clipsy.android.debug:color/material_personalized_color_outline = 0x7f06028b
com.clipsy.android.debug:string/mtrl_switch_track_decoration_path = 0x7f1100ba
com.clipsy.android.debug:color/material_personalized_color_on_tertiary_container = 0x7f06028a
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary0 = 0x7f0600ed
com.clipsy.android.debug:dimen/mtrl_slider_label_radius = 0x7f0702ec
com.clipsy.android.debug:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f120401
com.clipsy.android.debug:color/material_personalized_color_on_surface_inverse = 0x7f060287
com.clipsy.android.debug:color/material_personalized_color_on_secondary = 0x7f060284
com.clipsy.android.debug:color/m3_sys_color_tertiary_fixed_dim = 0x7f060200
com.clipsy.android.debug:color/material_personalized_color_on_error = 0x7f060280
com.clipsy.android.debug:attr/contentPaddingEnd = 0x7f04014c
com.clipsy.android.debug:color/material_personalized_color_on_background = 0x7f06027f
com.clipsy.android.debug:color/material_personalized_color_error_container = 0x7f06027e
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f12011d
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f120118
com.clipsy.android.debug:color/material_personalized_color_error = 0x7f06027d
com.clipsy.android.debug:color/material_personalized_color_control_activated = 0x7f06027a
com.clipsy.android.debug:attr/drawableRightCompat = 0x7f0401a1
com.clipsy.android.debug:color/material_personalized__highlighted_text = 0x7f060277
com.clipsy.android.debug:color/mtrl_switch_track_decoration_tint = 0x7f0602e2
com.clipsy.android.debug:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0702c9
com.clipsy.android.debug:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f090230
com.clipsy.android.debug:attr/enableEdgeToEdge = 0x7f0401ba
com.clipsy.android.debug:color/material_on_surface_stroke = 0x7f060276
com.clipsy.android.debug:color/material_on_surface_emphasis_medium = 0x7f060275
com.clipsy.android.debug:color/material_on_surface_disabled = 0x7f060273
com.clipsy.android.debug:color/material_on_primary_emphasis_medium = 0x7f060272
com.clipsy.android.debug:style/Theme.MaterialComponents.Light = 0x7f12028e
com.clipsy.android.debug:dimen/design_snackbar_background_corner_radius = 0x7f07008c
com.clipsy.android.debug:color/material_on_background_emphasis_high_type = 0x7f06026e
com.clipsy.android.debug:color/material_harmonized_color_on_error_container = 0x7f06026c
com.clipsy.android.debug:attr/forceApplySystemWindowInsetTop = 0x7f040227
com.clipsy.android.debug:dimen/abc_text_size_caption_material = 0x7f070042
com.clipsy.android.debug:color/material_harmonized_color_error_container = 0x7f06026a
com.clipsy.android.debug:color/material_grey_850 = 0x7f060267
com.clipsy.android.debug:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f1201b6
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f12017e
com.clipsy.android.debug:color/material_grey_300 = 0x7f060263
com.clipsy.android.debug:color/mtrl_chip_background_color = 0x7f0602c4
com.clipsy.android.debug:color/material_dynamic_tertiary99 = 0x7f060261
com.clipsy.android.debug:color/material_dynamic_tertiary60 = 0x7f06025c
com.clipsy.android.debug:id/bottom_navigation = 0x7f090062
com.clipsy.android.debug:attr/logoDescription = 0x7f0402f2
com.clipsy.android.debug:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f0702f6
com.clipsy.android.debug:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0d0096
com.clipsy.android.debug:color/material_dynamic_tertiary0 = 0x7f060255
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Headline5 = 0x7f12022e
com.clipsy.android.debug:color/material_dynamic_secondary99 = 0x7f060254
com.clipsy.android.debug:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f120262
com.clipsy.android.debug:dimen/m3_appbar_size_medium = 0x7f0700b7
com.clipsy.android.debug:color/material_dynamic_secondary95 = 0x7f060253
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f120332
com.clipsy.android.debug:color/material_dynamic_secondary90 = 0x7f060252
com.clipsy.android.debug:color/material_dynamic_secondary70 = 0x7f060250
com.clipsy.android.debug:color/material_dynamic_secondary50 = 0x7f06024e
com.clipsy.android.debug:color/material_dynamic_secondary10 = 0x7f060249
com.clipsy.android.debug:color/material_dynamic_secondary0 = 0x7f060248
com.clipsy.android.debug:attr/checkedChip = 0x7f0400bd
com.clipsy.android.debug:color/material_dynamic_primary95 = 0x7f060246
com.clipsy.android.debug:color/material_dynamic_primary90 = 0x7f060245
com.clipsy.android.debug:string/abc_capital_on = 0x7f110007
com.clipsy.android.debug:dimen/m3_appbar_size_large = 0x7f0700b6
com.clipsy.android.debug:color/material_dynamic_primary80 = 0x7f060244
com.clipsy.android.debug:color/material_dynamic_primary70 = 0x7f060243
com.clipsy.android.debug:color/material_dynamic_primary50 = 0x7f060241
com.clipsy.android.debug:layout/m3_alert_dialog_actions = 0x7f0c003b
com.clipsy.android.debug:attr/textAppearanceHeadline5 = 0x7f040492
com.clipsy.android.debug:color/material_dynamic_primary40 = 0x7f060240
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f120121
com.clipsy.android.debug:attr/errorTextAppearance = 0x7f0401d2
com.clipsy.android.debug:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f070175
com.clipsy.android.debug:color/material_dynamic_primary20 = 0x7f06023e
com.clipsy.android.debug:color/material_dynamic_primary100 = 0x7f06023d
com.clipsy.android.debug:style/Widget.Material3.SearchBar = 0x7f1203ea
com.clipsy.android.debug:color/material_dynamic_primary0 = 0x7f06023b
com.clipsy.android.debug:color/material_dynamic_neutral_variant99 = 0x7f06023a
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f12028f
com.clipsy.android.debug:color/material_on_primary_disabled = 0x7f060270
com.clipsy.android.debug:color/material_dynamic_neutral_variant70 = 0x7f060236
com.clipsy.android.debug:dimen/abc_edit_text_inset_horizontal_material = 0x7f07002d
com.clipsy.android.debug:color/material_dynamic_neutral_variant60 = 0x7f060235
com.clipsy.android.debug:attr/materialButtonStyle = 0x7f0402ff
com.clipsy.android.debug:dimen/mtrl_fab_elevation = 0x7f0702bb
com.clipsy.android.debug:color/material_dynamic_neutral_variant0 = 0x7f06022e
com.clipsy.android.debug:color/material_dynamic_neutral_variant50 = 0x7f060234
com.clipsy.android.debug:attr/colorPrimaryDark = 0x7f04011f
com.clipsy.android.debug:color/material_dynamic_neutral99 = 0x7f06022d
com.clipsy.android.debug:macro/m3_comp_slider_inactive_track_color = 0x7f0d0111
com.clipsy.android.debug:color/status_connecting = 0x7f060308
com.clipsy.android.debug:color/material_dynamic_neutral80 = 0x7f06022a
com.clipsy.android.debug:color/material_dynamic_neutral60 = 0x7f060228
com.clipsy.android.debug:style/Widget.Material3.SideSheet = 0x7f1203ef
com.clipsy.android.debug:drawable/ic_arrow_right = 0x7f08008e
com.clipsy.android.debug:attr/dialogTheme = 0x7f04018e
com.clipsy.android.debug:drawable/abc_popup_background_mtrl_mult = 0x7f080059
com.clipsy.android.debug:styleable/AlertDialog = 0x7f130009
com.clipsy.android.debug:attr/defaultDuration = 0x7f04017c
com.clipsy.android.debug:color/material_dynamic_neutral20 = 0x7f060224
com.clipsy.android.debug:dimen/m3_badge_with_text_vertical_offset = 0x7f0700c6
com.clipsy.android.debug:color/material_deep_teal_500 = 0x7f06021f
com.clipsy.android.debug:id/src_in = 0x7f0901d4
com.clipsy.android.debug:drawable/notification_oversize_large_icon_bg = 0x7f0800f7
com.clipsy.android.debug:attr/listChoiceIndicatorSingleAnimated = 0x7f0402e3
com.clipsy.android.debug:drawable/abc_text_select_handle_right_mtrl = 0x7f080070
com.clipsy.android.debug:attr/helperTextTextColor = 0x7f040238
com.clipsy.android.debug:dimen/mtrl_calendar_header_height_fullscreen = 0x7f070287
com.clipsy.android.debug:id/dragUp = 0x7f0900b7
com.clipsy.android.debug:attr/itemShapeInsetTop = 0x7f04027b
com.clipsy.android.debug:color/material_deep_teal_200 = 0x7f06021e
com.clipsy.android.debug:color/material_blue_grey_950 = 0x7f06021c
com.clipsy.android.debug:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f080026
com.clipsy.android.debug:color/m3_tonal_button_ripple_color_selector = 0x7f060219
com.clipsy.android.debug:style/Widget.Compat.NotificationActionText = 0x7f12035c
com.clipsy.android.debug:color/m3_timepicker_time_input_stroke_color = 0x7f060218
com.clipsy.android.debug:style/Theme.AppCompat.DialogWhenLarge = 0x7f120244
com.clipsy.android.debug:color/primary_text_default_material_dark = 0x7f0602f7
com.clipsy.android.debug:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f060216
com.clipsy.android.debug:color/m3_timepicker_display_background_color = 0x7f060213
com.clipsy.android.debug:color/m3_timepicker_clock_text_color = 0x7f060212
com.clipsy.android.debug:attr/colorOnSurface = 0x7f040114
com.clipsy.android.debug:color/m3_timepicker_button_text_color = 0x7f060211
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary60 = 0x7f060101
com.clipsy.android.debug:color/m3_timepicker_button_ripple_color = 0x7f060210
com.clipsy.android.debug:id/groups = 0x7f0900e7
com.clipsy.android.debug:color/m3_textfield_stroke_color = 0x7f06020e
com.clipsy.android.debug:color/m3_textfield_input_text_color = 0x7f06020c
com.clipsy.android.debug:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0d0069
com.clipsy.android.debug:drawable/ic_clipsy_refresh = 0x7f080098
com.clipsy.android.debug:anim/mtrl_card_lowers_interpolator = 0x7f01002b
com.clipsy.android.debug:color/m3_textfield_filled_background_color = 0x7f06020a
com.clipsy.android.debug:id/parentPanel = 0x7f09017c
com.clipsy.android.debug:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f070137
com.clipsy.android.debug:color/m3_tabs_text_color_secondary = 0x7f060206
com.clipsy.android.debug:id/search_close_btn = 0x7f0901ac
com.clipsy.android.debug:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f0800e4
com.clipsy.android.debug:color/m3_tabs_text_color = 0x7f060205
com.clipsy.android.debug:dimen/mtrl_calendar_day_vertical_padding = 0x7f07027f
com.clipsy.android.debug:color/m3_tabs_icon_color_secondary = 0x7f060202
com.clipsy.android.debug:color/m3_sys_color_primary_fixed = 0x7f0601fb
com.clipsy.android.debug:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f0601fa
com.clipsy.android.debug:macro/m3_comp_elevated_button_container_color = 0x7f0d002a
com.clipsy.android.debug:color/m3_sys_color_on_tertiary_fixed = 0x7f0601f9
com.clipsy.android.debug:color/m3_button_ripple_color = 0x7f060084
com.clipsy.android.debug:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0702d7
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Menu = 0x7f12002b
com.clipsy.android.debug:attr/buttonCompat = 0x7f040098
com.clipsy.android.debug:color/material_personalized_color_secondary_container = 0x7f060293
com.clipsy.android.debug:color/m3_sys_color_on_secondary_fixed_variant = 0x7f0601f8
com.clipsy.android.debug:attr/colorOnSurfaceInverse = 0x7f040115
com.clipsy.android.debug:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f070205
com.clipsy.android.debug:attr/tabPadding = 0x7f040471
com.clipsy.android.debug:dimen/m3_extended_fab_end_padding = 0x7f0701b6
com.clipsy.android.debug:color/mtrl_fab_ripple_color = 0x7f0602ce
com.clipsy.android.debug:color/m3_sys_color_on_secondary_fixed = 0x7f0601f7
com.clipsy.android.debug:string/m3_sys_motion_easing_legacy_decelerate = 0x7f110065
com.clipsy.android.debug:drawable/btn_radio_off_mtrl = 0x7f08007e
com.clipsy.android.debug:attr/triggerSlack = 0x7f04050e
com.clipsy.android.debug:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0700ba
com.clipsy.android.debug:attr/badgeWidePadding = 0x7f040065
com.clipsy.android.debug:attr/shrinkMotionSpec = 0x7f04040f
com.clipsy.android.debug:color/m3_sys_color_on_primary_fixed_variant = 0x7f0601f6
com.clipsy.android.debug:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f070163
com.clipsy.android.debug:color/m3_sys_color_on_primary_fixed = 0x7f0601f5
com.clipsy.android.debug:attr/textOutlineThickness = 0x7f0404bc
com.clipsy.android.debug:color/m3_sys_color_light_tertiary = 0x7f0601f3
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f12018d
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0600df
com.clipsy.android.debug:color/m3_sys_color_light_surface_container_low = 0x7f0601ef
com.clipsy.android.debug:color/m3_sys_color_light_surface_container = 0x7f0601ec
com.clipsy.android.debug:drawable/ic_clipsy_history = 0x7f080097
com.clipsy.android.debug:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1200ac
com.clipsy.android.debug:color/m3_sys_color_light_surface_bright = 0x7f0601eb
com.clipsy.android.debug:drawable/$m3_avd_hide_password__0 = 0x7f080006
com.clipsy.android.debug:color/material_dynamic_primary10 = 0x7f06023c
com.clipsy.android.debug:color/m3_sys_color_light_outline = 0x7f0601e4
com.clipsy.android.debug:attr/layout = 0x7f040291
com.clipsy.android.debug:color/material_timepicker_button_background = 0x7f0602b1
com.clipsy.android.debug:color/m3_sys_color_light_on_tertiary_container = 0x7f0601e3
com.clipsy.android.debug:color/m3_sys_color_light_on_tertiary = 0x7f0601e2
com.clipsy.android.debug:color/m3_sys_color_light_on_surface_variant = 0x7f0601e1
com.clipsy.android.debug:color/m3_sys_color_light_on_secondary = 0x7f0601de
com.clipsy.android.debug:style/TextAppearance.AppCompat = 0x7f1201c0
com.clipsy.android.debug:color/m3_sys_color_light_on_primary_container = 0x7f0601dd
com.clipsy.android.debug:color/m3_sys_color_light_on_error_container = 0x7f0601db
com.clipsy.android.debug:attr/overlapAnchor = 0x7f04038a
com.clipsy.android.debug:drawable/abc_list_longpressed_holo = 0x7f08004f
com.clipsy.android.debug:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f1203ab
com.clipsy.android.debug:id/showHome = 0x7f0901bd
com.clipsy.android.debug:id/preferences_sliding_pane_layout = 0x7f090189
com.clipsy.android.debug:dimen/mtrl_low_ripple_default_alpha = 0x7f0702c3
com.clipsy.android.debug:color/m3_sys_color_light_on_error = 0x7f0601da
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.Bridge = 0x7f120290
com.clipsy.android.debug:id/startToEnd = 0x7f0901d9
com.clipsy.android.debug:color/m3_sys_color_dark_outline_variant = 0x7f06017b
com.clipsy.android.debug:dimen/abc_edit_text_inset_bottom_material = 0x7f07002c
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f120448
com.clipsy.android.debug:color/m3_sys_color_light_on_background = 0x7f0601d9
com.clipsy.android.debug:bool/mtrl_btn_textappearance_all_caps = 0x7f050006
com.clipsy.android.debug:color/m3_sys_color_light_inverse_surface = 0x7f0601d8
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0601c4
com.clipsy.android.debug:color/m3_sys_color_light_inverse_primary = 0x7f0601d7
com.clipsy.android.debug:style/Base.Widget.AppCompat.SeekBar = 0x7f1200f8
com.clipsy.android.debug:color/m3_ref_palette_neutral22 = 0x7f06011a
com.clipsy.android.debug:color/mtrl_btn_transparent_bg_color = 0x7f0602bf
com.clipsy.android.debug:color/design_default_color_primary_dark = 0x7f060062
com.clipsy.android.debug:color/m3_sys_color_light_error_container = 0x7f0601d5
com.clipsy.android.debug:attr/secondaryActivityName = 0x7f0403ee
com.clipsy.android.debug:color/m3_sys_color_light_error = 0x7f0601d4
com.clipsy.android.debug:style/Widget.Material3.BottomNavigationView = 0x7f120374
com.clipsy.android.debug:id/text_local_ip = 0x7f090209
com.clipsy.android.debug:color/m3_sys_color_light_background = 0x7f0601d3
com.clipsy.android.debug:color/m3_slider_active_track_color = 0x7f060163
com.clipsy.android.debug:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0601d2
com.clipsy.android.debug:styleable/MaterialCalendarItem = 0x7f13005c
com.clipsy.android.debug:style/Widget.Material3.CircularProgressIndicator = 0x7f12039b
com.clipsy.android.debug:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1200c2
com.clipsy.android.debug:color/m3_ref_palette_secondary0 = 0x7f060146
com.clipsy.android.debug:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0601d1
com.clipsy.android.debug:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0601d0
com.clipsy.android.debug:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1200de
com.clipsy.android.debug:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0601ce
com.clipsy.android.debug:color/material_dynamic_tertiary50 = 0x7f06025b
com.clipsy.android.debug:style/Base.Widget.AppCompat.Toolbar = 0x7f1200fe
com.clipsy.android.debug:attr/materialCalendarHeaderConfirmButton = 0x7f040305
com.clipsy.android.debug:color/m3_sys_color_dynamic_primary_fixed = 0x7f0601cd
com.clipsy.android.debug:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0601cc
com.clipsy.android.debug:string/nav_app_bar_navigate_up_description = 0x7f1100be
com.clipsy.android.debug:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0601c9
com.clipsy.android.debug:dimen/mtrl_slider_label_square_side = 0x7f0702ed
com.clipsy.android.debug:style/Platform.V21.AppCompat = 0x7f12014b
com.clipsy.android.debug:dimen/abc_button_padding_vertical_material = 0x7f070015
com.clipsy.android.debug:color/m3_sys_color_light_on_secondary_container = 0x7f0601df
com.clipsy.android.debug:dimen/m3_toolbar_text_size_title = 0x7f070225
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0601c1
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f120045
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0601bf
com.clipsy.android.debug:color/m3_icon_button_icon_color_selector = 0x7f0600ab
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_surface_container = 0x7f0601be
com.clipsy.android.debug:style/ShapeAppearance.Material3.Corner.Large = 0x7f12019d
com.clipsy.android.debug:drawable/ic_arrow_back_black_24 = 0x7f08008c
com.clipsy.android.debug:dimen/m3_navigation_item_icon_padding = 0x7f0701c6
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_secondary = 0x7f0601ba
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_primary_container = 0x7f0601b9
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_primary = 0x7f0601b8
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0601b7
com.clipsy.android.debug:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0d0153
com.clipsy.android.debug:id/button_delete = 0x7f09006a
com.clipsy.android.debug:attr/percentHeight = 0x7f04039f
com.clipsy.android.debug:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f070202
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_outline = 0x7f0601b6
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0601b5
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0601b0
com.clipsy.android.debug:color/abc_background_cache_hint_selector_material_dark = 0x7f060000
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0601af
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_on_primary = 0x7f0601ae
com.clipsy.android.debug:attr/constraints = 0x7f040141
com.clipsy.android.debug:dimen/m3_comp_switch_disabled_track_opacity = 0x7f070197
com.clipsy.android.debug:style/Widget.Material3.SideSheet.Modal = 0x7f1203f1
com.clipsy.android.debug:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f12009b
com.clipsy.android.debug:dimen/m3_comp_outlined_button_outline_width = 0x7f07015d
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_on_background = 0x7f0601ad
com.clipsy.android.debug:id/end = 0x7f0900c5
com.clipsy.android.debug:attr/behavior_hideable = 0x7f040076
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0601ac
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0601ab
com.clipsy.android.debug:style/Widget.AppCompat.ListMenuView = 0x7f120342
com.clipsy.android.debug:dimen/highlight_alpha_material_light = 0x7f0700a1
com.clipsy.android.debug:style/MaterialAlertDialog.Material3.Animation = 0x7f12012f
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_tertiary = 0x7f0601a7
com.clipsy.android.debug:style/Widget.MaterialComponents.TextView = 0x7f120479
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f0601a6
com.clipsy.android.debug:color/primary_material_dark = 0x7f0602f5
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f0601a4
com.clipsy.android.debug:style/TextAppearance.AppCompat.Tooltip = 0x7f1201dc
com.clipsy.android.debug:dimen/fastscroll_margin = 0x7f07009d
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f0601a3
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_surface_container = 0x7f0601a0
com.clipsy.android.debug:color/material_personalized_color_surface_container_lowest = 0x7f06029c
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1202f1
com.clipsy.android.debug:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f120016
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f06019d
com.clipsy.android.debug:color/m3_sys_color_light_on_surface = 0x7f0601e0
com.clipsy.android.debug:style/Widget.Design.TextInputEditText = 0x7f120366
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f060199
com.clipsy.android.debug:attr/checkMarkTint = 0x7f0400b9
com.clipsy.android.debug:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f070181
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f060197
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_on_surface = 0x7f060194
com.clipsy.android.debug:color/mtrl_popupmenu_overlay_color = 0x7f0602de
com.clipsy.android.debug:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f0800d3
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0601c0
com.clipsy.android.debug:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f120487
com.clipsy.android.debug:color/bright_foreground_inverse_material_dark = 0x7f060028
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f060192
com.clipsy.android.debug:string/abc_searchview_description_clear = 0x7f110013
com.clipsy.android.debug:color/material_dynamic_neutral_variant30 = 0x7f060232
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_on_primary = 0x7f060190
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f06018d
com.clipsy.android.debug:string/abc_menu_shift_shortcut_label = 0x7f11000e
com.clipsy.android.debug:id/fragment_container = 0x7f0900dc
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_background = 0x7f06018b
com.clipsy.android.debug:color/m3_sys_color_dark_tertiary_container = 0x7f06018a
com.clipsy.android.debug:attr/panelBackground = 0x7f040395
com.clipsy.android.debug:color/m3_sys_color_dark_surface_variant = 0x7f060188
com.clipsy.android.debug:color/m3_ref_palette_error99 = 0x7f060113
com.clipsy.android.debug:color/m3_sys_color_dark_surface_container_lowest = 0x7f060186
com.clipsy.android.debug:style/Base.Widget.Material3.TabLayout = 0x7f120112
com.clipsy.android.debug:color/m3_sys_color_dark_surface_container_low = 0x7f060185
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Headline2 = 0x7f12022b
com.clipsy.android.debug:color/primary_text_disabled_material_light = 0x7f0602fa
com.clipsy.android.debug:color/m3_sys_color_dark_surface_container_highest = 0x7f060184
com.clipsy.android.debug:integer/mtrl_switch_track_viewport_width = 0x7f0a003e
com.clipsy.android.debug:attr/layoutDescription = 0x7f040292
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral90 = 0x7f0600cc
com.clipsy.android.debug:color/m3_ref_palette_neutral20 = 0x7f060119
com.clipsy.android.debug:color/m3_sys_color_dark_surface_container_high = 0x7f060183
com.clipsy.android.debug:style/Base.V24.Theme.Material3.Dark = 0x7f1200b5
com.clipsy.android.debug:color/m3_sys_color_dark_surface_bright = 0x7f060181
com.clipsy.android.debug:color/m3_sys_color_dark_surface = 0x7f060180
com.clipsy.android.debug:drawable/ic_mtrl_chip_checked_black = 0x7f0800a5
com.clipsy.android.debug:color/material_on_background_emphasis_medium = 0x7f06026f
com.clipsy.android.debug:drawable/avd_show_password = 0x7f080078
com.clipsy.android.debug:color/tooltip_background_dark = 0x7f060316
com.clipsy.android.debug:color/m3_sys_color_dark_secondary_container = 0x7f06017f
com.clipsy.android.debug:color/button_material_light = 0x7f060031
com.clipsy.android.debug:dimen/text_size_button = 0x7f070327
com.clipsy.android.debug:color/m3_sys_color_dark_secondary = 0x7f06017e
com.clipsy.android.debug:color/androidx_core_secondary_text_default_material_light = 0x7f06001c
com.clipsy.android.debug:dimen/tooltip_corner_radius = 0x7f070329
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f12020c
com.clipsy.android.debug:attr/foregroundInsidePadding = 0x7f040229
com.clipsy.android.debug:dimen/material_emphasis_disabled = 0x7f070237
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f12044c
com.clipsy.android.debug:color/m3_sys_color_dark_background = 0x7f060169
com.clipsy.android.debug:color/m3_sys_color_dark_on_tertiary_container = 0x7f060179
com.clipsy.android.debug:color/m3_sys_color_dark_on_tertiary = 0x7f060178
com.clipsy.android.debug:color/m3_sys_color_dark_on_surface_variant = 0x7f060177
com.clipsy.android.debug:color/m3_sys_color_dark_on_surface = 0x7f060176
com.clipsy.android.debug:color/m3_sys_color_dark_on_secondary = 0x7f060174
com.clipsy.android.debug:attr/buttonTint = 0x7f0400a1
com.clipsy.android.debug:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f070193
com.clipsy.android.debug:color/m3_sys_color_dark_on_error_container = 0x7f060171
com.clipsy.android.debug:dimen/mtrl_calendar_year_vertical_padding = 0x7f07029e
com.clipsy.android.debug:id/button_connect = 0x7f090069
com.clipsy.android.debug:color/m3_sys_color_dark_on_error = 0x7f060170
com.clipsy.android.debug:color/m3_sys_color_dark_inverse_surface = 0x7f06016e
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f120307
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0d0018
com.clipsy.android.debug:color/m3_sys_color_dark_inverse_on_surface = 0x7f06016c
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0d00c2
com.clipsy.android.debug:id/center = 0x7f09007a
com.clipsy.android.debug:dimen/design_bottom_sheet_peek_height_min = 0x7f070079
com.clipsy.android.debug:color/m3_switch_track_tint = 0x7f060168
com.clipsy.android.debug:color/m3_slider_thumb_color = 0x7f060166
com.clipsy.android.debug:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f07020e
com.clipsy.android.debug:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0701dd
com.clipsy.android.debug:color/m3_slider_halo_color = 0x7f060164
com.clipsy.android.debug:attr/seekBarPreferenceStyle = 0x7f0403f0
com.clipsy.android.debug:color/m3_ref_palette_tertiary99 = 0x7f06015f
com.clipsy.android.debug:color/m3_ref_palette_tertiary90 = 0x7f06015d
com.clipsy.android.debug:color/m3_ref_palette_tertiary80 = 0x7f06015c
com.clipsy.android.debug:id/percent = 0x7f090183
com.clipsy.android.debug:color/material_grey_50 = 0x7f060264
com.clipsy.android.debug:color/m3_ref_palette_white = 0x7f060160
com.clipsy.android.debug:color/m3_ref_palette_tertiary70 = 0x7f06015b
com.clipsy.android.debug:macro/m3_comp_snackbar_supporting_text_type = 0x7f0d0117
com.clipsy.android.debug:color/m3_ref_palette_secondary60 = 0x7f06014d
com.clipsy.android.debug:color/m3_ref_palette_tertiary20 = 0x7f060156
com.clipsy.android.debug:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1202cb
com.clipsy.android.debug:id/action_image = 0x7f09003e
com.clipsy.android.debug:attr/motionEasingStandardAccelerateInterpolator = 0x7f04035d
com.clipsy.android.debug:dimen/notification_top_pad_large_text = 0x7f07031e
com.clipsy.android.debug:color/m3_ref_palette_tertiary100 = 0x7f060155
com.clipsy.android.debug:color/m3_sys_color_primary_fixed_dim = 0x7f0601fc
com.clipsy.android.debug:dimen/mtrl_tooltip_minWidth = 0x7f07030d
com.clipsy.android.debug:dimen/disabled_alpha_material_dark = 0x7f07009a
com.clipsy.android.debug:drawable/$avd_hide_password__1 = 0x7f080001
com.clipsy.android.debug:style/Widget.Material3.Tooltip = 0x7f12040a
com.clipsy.android.debug:dimen/design_textinput_caption_translate_y = 0x7f070099
com.clipsy.android.debug:color/m3_ref_palette_secondary95 = 0x7f060151
com.clipsy.android.debug:color/m3_ref_palette_secondary70 = 0x7f06014e
com.clipsy.android.debug:style/ThemeOverlay.Material3.Light = 0x7f1202d3
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Caption = 0x7f120228
com.clipsy.android.debug:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1200f5
com.clipsy.android.debug:style/Base.Theme.MaterialComponents = 0x7f120069
com.clipsy.android.debug:id/overshoot = 0x7f090178
com.clipsy.android.debug:id/group_divider = 0x7f0900e5
com.clipsy.android.debug:attr/toggleCheckedStateOnClick = 0x7f0404ed
com.clipsy.android.debug:color/m3_ref_palette_secondary50 = 0x7f06014c
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f12017d
com.clipsy.android.debug:string/path_password_strike_through = 0x7f1100cd
com.clipsy.android.debug:attr/checkedButton = 0x7f0400bc
com.clipsy.android.debug:dimen/mtrl_navigation_rail_icon_size = 0x7f0702d5
com.clipsy.android.debug:color/m3_ref_palette_secondary20 = 0x7f060149
com.clipsy.android.debug:attr/tooltipForegroundColor = 0x7f0404f2
com.clipsy.android.debug:color/material_personalized_color_on_primary = 0x7f060282
com.clipsy.android.debug:color/m3_ref_palette_secondary100 = 0x7f060148
com.clipsy.android.debug:attr/circleRadius = 0x7f0400db
com.clipsy.android.debug:color/m3_ref_palette_secondary10 = 0x7f060147
com.clipsy.android.debug:color/m3_ref_palette_primary99 = 0x7f060145
com.clipsy.android.debug:attr/methodName = 0x7f040335
com.clipsy.android.debug:drawable/design_password_eye = 0x7f080089
com.clipsy.android.debug:color/m3_ref_palette_primary95 = 0x7f060144
com.clipsy.android.debug:style/TextAppearance.AppCompat.Menu = 0x7f1201d3
com.clipsy.android.debug:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0702ce
com.clipsy.android.debug:color/m3_ref_palette_primary90 = 0x7f060143
com.clipsy.android.debug:dimen/mtrl_card_checked_icon_size = 0x7f0702a1
com.clipsy.android.debug:integer/show_password_duration = 0x7f0a0045
com.clipsy.android.debug:color/m3_ref_palette_primary100 = 0x7f06013b
com.clipsy.android.debug:color/m3_ref_palette_primary10 = 0x7f06013a
com.clipsy.android.debug:dimen/material_helper_text_default_padding_top = 0x7f070241
com.clipsy.android.debug:color/m3_ref_palette_primary0 = 0x7f060139
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant99 = 0x7f060138
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant95 = 0x7f060137
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant70 = 0x7f060134
com.clipsy.android.debug:layout/abc_action_mode_close_item_material = 0x7f0c0005
com.clipsy.android.debug:color/abc_background_cache_hint_selector_material_light = 0x7f060001
com.clipsy.android.debug:dimen/button_elevation = 0x7f070054
com.clipsy.android.debug:dimen/abc_alert_dialog_button_dimen = 0x7f070011
com.clipsy.android.debug:color/m3_ref_palette_tertiary30 = 0x7f060157
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0601bb
com.clipsy.android.debug:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1202b0
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant40 = 0x7f060131
com.clipsy.android.debug:attr/telltales_velocityMode = 0x7f040482
com.clipsy.android.debug:color/clipsy_background = 0x7f060040
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant30 = 0x7f060130
com.clipsy.android.debug:attr/endIconContentDescription = 0x7f0401bd
com.clipsy.android.debug:dimen/m3_ripple_hovered_alpha = 0x7f0701db
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant10 = 0x7f06012d
com.clipsy.android.debug:color/m3_ref_palette_neutral99 = 0x7f06012b
com.clipsy.android.debug:dimen/tooltip_margin = 0x7f07032b
com.clipsy.android.debug:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f12013f
com.clipsy.android.debug:color/m3_ref_palette_neutral98 = 0x7f06012a
com.clipsy.android.debug:color/m3_ref_palette_neutral96 = 0x7f060129
com.clipsy.android.debug:integer/material_motion_duration_medium_2 = 0x7f0a002a
com.clipsy.android.debug:color/mtrl_textinput_default_box_stroke_color = 0x7f0602ea
com.clipsy.android.debug:color/m3_ref_palette_neutral90 = 0x7f060125
com.clipsy.android.debug:color/m3_ref_palette_neutral87 = 0x7f060124
com.clipsy.android.debug:color/m3_ref_palette_neutral70 = 0x7f060122
com.clipsy.android.debug:styleable/Variant = 0x7f1300b2
com.clipsy.android.debug:string/mtrl_picker_text_input_date_hint = 0x7f1100a9
com.clipsy.android.debug:color/m3_ref_palette_neutral60 = 0x7f060121
com.clipsy.android.debug:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f120055
com.clipsy.android.debug:color/m3_ref_palette_neutral6 = 0x7f060120
com.clipsy.android.debug:color/m3_ref_palette_neutral50 = 0x7f06011f
com.clipsy.android.debug:color/m3_ref_palette_neutral4 = 0x7f06011d
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f120458
com.clipsy.android.debug:attr/cardForegroundColor = 0x7f0400a6
com.clipsy.android.debug:color/m3_ref_palette_neutral30 = 0x7f06011c
com.clipsy.android.debug:dimen/m3_badge_offset = 0x7f0700c0
com.clipsy.android.debug:string/item_view_role_description = 0x7f110059
com.clipsy.android.debug:dimen/design_navigation_item_icon_padding = 0x7f070085
com.clipsy.android.debug:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f080023
com.clipsy.android.debug:color/m3_ref_palette_neutral24 = 0x7f06011b
com.clipsy.android.debug:dimen/mtrl_calendar_header_selection_line_height = 0x7f070288
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary30 = 0x7f0600f1
com.clipsy.android.debug:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0701fe
com.clipsy.android.debug:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0800cd
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f08001e
com.clipsy.android.debug:color/m3_ref_palette_neutral0 = 0x7f060114
com.clipsy.android.debug:dimen/cardview_compat_inset_shadow = 0x7f07005e
com.clipsy.android.debug:color/m3_ref_palette_error95 = 0x7f060112
com.clipsy.android.debug:style/Widget.Material3.CardView.Filled = 0x7f12038d
com.clipsy.android.debug:color/m3_ref_palette_error90 = 0x7f060111
com.clipsy.android.debug:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f12010a
com.clipsy.android.debug:dimen/m3_ripple_pressed_alpha = 0x7f0701dc
com.clipsy.android.debug:color/m3_ref_palette_error80 = 0x7f060110
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0d0167
com.clipsy.android.debug:id/fitToContents = 0x7f0900d6
com.clipsy.android.debug:dimen/m3_card_elevation = 0x7f0700f6
com.clipsy.android.debug:color/m3_ref_palette_error60 = 0x7f06010e
com.clipsy.android.debug:id/accessibility_custom_action_5 = 0x7f09002b
com.clipsy.android.debug:color/m3_ref_palette_error50 = 0x7f06010d
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f0601a2
com.clipsy.android.debug:attr/lineHeight = 0x7f0402de
com.clipsy.android.debug:color/m3_ref_palette_error30 = 0x7f06010b
com.clipsy.android.debug:color/m3_text_button_ripple_color_selector = 0x7f060209
com.clipsy.android.debug:color/m3_ref_palette_error100 = 0x7f060109
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant20 = 0x7f06012f
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary90 = 0x7f060104
com.clipsy.android.debug:attr/cursorColor = 0x7f040169
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary80 = 0x7f060103
com.clipsy.android.debug:layout/preference_widget_switch = 0x7f0c0081
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary70 = 0x7f060102
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary40 = 0x7f0600ff
com.clipsy.android.debug:styleable/SwipeRefreshLayout = 0x7f1300a2
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0600fd
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0600fc
com.clipsy.android.debug:dimen/m3_ripple_focused_alpha = 0x7f0701da
com.clipsy.android.debug:color/material_slider_thumb_color = 0x7f0602b0
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0600fb
com.clipsy.android.debug:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f12040d
com.clipsy.android.debug:dimen/m3_chip_hovered_translation_z = 0x7f070104
com.clipsy.android.debug:dimen/design_snackbar_action_text_color_alpha = 0x7f07008b
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary99 = 0x7f0600f9
com.clipsy.android.debug:id/textinput_error = 0x7f09020e
com.clipsy.android.debug:drawable/abc_ic_go_search_api_material = 0x7f080041
com.clipsy.android.debug:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f07016a
com.clipsy.android.debug:color/high_contrast_background = 0x7f060079
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary80 = 0x7f0600f6
com.clipsy.android.debug:attr/materialIconButtonFilledStyle = 0x7f040319
com.clipsy.android.debug:dimen/m3_searchbar_margin_vertical = 0x7f0701e1
com.clipsy.android.debug:dimen/mtrl_calendar_action_height = 0x7f070277
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary60 = 0x7f0600f4
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary40 = 0x7f0600f2
com.clipsy.android.debug:styleable/BottomSheetBehavior_Layout = 0x7f13001b
com.clipsy.android.debug:attr/tabPaddingEnd = 0x7f040473
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary20 = 0x7f0600f0
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary100 = 0x7f0600ef
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f12003b
com.clipsy.android.debug:attr/mock_showDiagonals = 0x7f040341
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary95 = 0x7f0600eb
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f120440
com.clipsy.android.debug:style/Widget.Material3.NavigationRailView.Badge = 0x7f1203e2
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary80 = 0x7f0600e9
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary60 = 0x7f0600e7
com.clipsy.android.debug:dimen/m3_comp_navigation_drawer_container_width = 0x7f07014c
com.clipsy.android.debug:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0d00e2
com.clipsy.android.debug:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0d0099
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary50 = 0x7f0600e6
com.clipsy.android.debug:macro/m3_comp_slider_active_track_color = 0x7f0d010c
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary40 = 0x7f0600e5
com.clipsy.android.debug:attr/expandedTitleMarginTop = 0x7f0401dd
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f060196
com.clipsy.android.debug:attr/mimeType = 0x7f040336
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary20 = 0x7f0600e3
com.clipsy.android.debug:integer/hide_password_duration = 0x7f0a0009
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary100 = 0x7f0600e2
com.clipsy.android.debug:color/material_personalized_color_background = 0x7f060279
com.clipsy.android.debug:id/startHorizontal = 0x7f0901d8
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary10 = 0x7f0600e1
com.clipsy.android.debug:color/m3_sys_color_dark_on_background = 0x7f06016f
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0600de
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0600dc
com.clipsy.android.debug:dimen/abc_seekbar_track_background_height_material = 0x7f070038
com.clipsy.android.debug:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f120399
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0600db
com.clipsy.android.debug:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
com.clipsy.android.debug:attr/dragScale = 0x7f04019b
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0600d8
com.clipsy.android.debug:dimen/fastscroll_minimum_range = 0x7f07009e
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0600d7
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0600d6
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral96 = 0x7f0600d0
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f12018f
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral80 = 0x7f0600ca
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral60 = 0x7f0600c8
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral6 = 0x7f0600c7
com.clipsy.android.debug:style/Theme.AppCompat = 0x7f120238
com.clipsy.android.debug:attr/textBackgroundZoom = 0x7f0404ad
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral50 = 0x7f0600c6
com.clipsy.android.debug:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f07013c
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral4 = 0x7f0600c4
com.clipsy.android.debug:integer/mtrl_card_anim_duration_ms = 0x7f0a0035
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral30 = 0x7f0600c3
com.clipsy.android.debug:style/Widget.MaterialComponents.Snackbar = 0x7f120467
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral22 = 0x7f0600c1
com.clipsy.android.debug:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f120017
com.clipsy.android.debug:attr/nestedScrollable = 0x7f04037c
com.clipsy.android.debug:color/design_dark_default_color_primary_variant = 0x7f060056
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral20 = 0x7f0600c0
com.clipsy.android.debug:id/action_settings = 0x7f090045
com.clipsy.android.debug:attr/checkedState = 0x7f0400c5
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral12 = 0x7f0600be
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral10 = 0x7f0600bc
com.clipsy.android.debug:color/m3_ref_palette_black = 0x7f0600ba
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0d00cd
com.clipsy.android.debug:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0d006c
com.clipsy.android.debug:attr/defaultState = 0x7f040181
com.clipsy.android.debug:color/mtrl_chip_close_icon_tint = 0x7f0602c5
com.clipsy.android.debug:color/m3_radiobutton_button_tint = 0x7f0600b8
com.clipsy.android.debug:dimen/m3_comp_filled_card_icon_size = 0x7f070135
com.clipsy.android.debug:color/abc_tint_edittext = 0x7f060015
com.clipsy.android.debug:color/m3_navigation_item_text_color = 0x7f0600b2
com.clipsy.android.debug:id/sin = 0x7f0901bf
com.clipsy.android.debug:color/m3_navigation_item_ripple_color = 0x7f0600b1
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Inverse = 0x7f120024
com.clipsy.android.debug:color/m3_navigation_item_background_color = 0x7f0600af
com.clipsy.android.debug:id/parentRelative = 0x7f09017d
com.clipsy.android.debug:animator/nav_default_pop_exit_anim = 0x7f020025
com.clipsy.android.debug:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f0600ac
com.clipsy.android.debug:color/m3_hint_foreground = 0x7f0600aa
com.clipsy.android.debug:styleable/ActionMenuItemView = 0x7f130002
com.clipsy.android.debug:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0d011b
com.clipsy.android.debug:color/m3_efab_ripple_color_selector = 0x7f0600a3
com.clipsy.android.debug:attr/curveFit = 0x7f04016b
com.clipsy.android.debug:attr/listPreferredItemHeightSmall = 0x7f0402eb
com.clipsy.android.debug:dimen/design_navigation_item_vertical_padding = 0x7f070086
com.clipsy.android.debug:attr/fontVariationSettings = 0x7f040225
com.clipsy.android.debug:color/m3_dynamic_highlighted_text = 0x7f0600a0
com.clipsy.android.debug:dimen/mtrl_textinput_end_icon_margin_start = 0x7f070306
com.clipsy.android.debug:dimen/mtrl_card_spacing = 0x7f0702a5
com.clipsy.android.debug:dimen/m3_comp_filled_card_container_elevation = 0x7f070131
com.clipsy.android.debug:attr/hintTextAppearance = 0x7f040240
com.clipsy.android.debug:color/m3_dynamic_default_color_primary_text = 0x7f06009e
com.clipsy.android.debug:color/m3_dynamic_dark_primary_text_disable_only = 0x7f06009d
com.clipsy.android.debug:attr/flow_lastVerticalBias = 0x7f040212
com.clipsy.android.debug:attr/popupMenuStyle = 0x7f0403b2
com.clipsy.android.debug:attr/layout_constraintWidth = 0x7f0402c3
com.clipsy.android.debug:attr/motionDurationShort4 = 0x7f040353
com.clipsy.android.debug:color/m3_dynamic_dark_hint_foreground = 0x7f06009c
com.clipsy.android.debug:color/m3_dynamic_dark_highlighted_text = 0x7f06009b
com.clipsy.android.debug:style/Base.AlertDialog.AppCompat.Light = 0x7f120010
com.clipsy.android.debug:attr/fontProviderFetchStrategy = 0x7f04021f
com.clipsy.android.debug:attr/shouldRemoveExpandedCorners = 0x7f040405
com.clipsy.android.debug:color/m3_default_color_primary_text = 0x7f060097
com.clipsy.android.debug:layout/material_clock_period_toggle_land = 0x7f0c0043
com.clipsy.android.debug:attr/expandActivityOverflowButtonDrawable = 0x7f0401d5
com.clipsy.android.debug:id/rightToLeft = 0x7f090199
com.clipsy.android.debug:color/m3_dark_hint_foreground = 0x7f060095
com.clipsy.android.debug:macro/m3_comp_search_bar_input_text_type = 0x7f0d00eb
com.clipsy.android.debug:color/m3_dark_default_color_secondary_text = 0x7f060093
com.clipsy.android.debug:string/action_unpair = 0x7f110023
com.clipsy.android.debug:attr/layout_wrapBehaviorInParent = 0x7f0402d9
com.clipsy.android.debug:color/m3_chip_text_color = 0x7f060091
com.clipsy.android.debug:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0d00e8
com.clipsy.android.debug:id/fragment_container_view_tag = 0x7f0900dd
com.clipsy.android.debug:color/m3_card_ripple_color = 0x7f060089
com.clipsy.android.debug:color/m3_calendar_item_disabled_text = 0x7f060086
com.clipsy.android.debug:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f070125
com.clipsy.android.debug:color/m3_card_foreground_color = 0x7f060088
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Title = 0x7f120033
com.clipsy.android.debug:color/m3_tabs_ripple_color_secondary = 0x7f060204
com.clipsy.android.debug:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0d0171
com.clipsy.android.debug:color/m3_button_ripple_color_selector = 0x7f060085
com.clipsy.android.debug:dimen/mtrl_textinput_box_corner_radius_small = 0x7f070301
com.clipsy.android.debug:color/switch_thumb_material_light = 0x7f06030e
com.clipsy.android.debug:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f12013b
com.clipsy.android.debug:attr/fabAlignmentModeEndMargin = 0x7f0401e9
com.clipsy.android.debug:color/abc_hint_foreground_material_dark = 0x7f060007
com.clipsy.android.debug:color/m3_button_outline_color_selector = 0x7f060083
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1203c1
com.clipsy.android.debug:dimen/abc_floating_window_z = 0x7f07002f
com.clipsy.android.debug:string/side_sheet_behavior = 0x7f1100f6
com.clipsy.android.debug:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0d0149
com.clipsy.android.debug:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0d011d
com.clipsy.android.debug:attr/imagePanY = 0x7f040255
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0600da
com.clipsy.android.debug:color/m3_assist_chip_stroke_color = 0x7f06007f
com.clipsy.android.debug:color/foreground_material_dark = 0x7f060077
com.clipsy.android.debug:attr/layout_constraintCircleRadius = 0x7f0402a5
com.clipsy.android.debug:color/material_divider_color = 0x7f060220
com.clipsy.android.debug:style/BottomNavActiveIndicator = 0x7f120124
com.clipsy.android.debug:attr/dividerInsetStart = 0x7f040196
com.clipsy.android.debug:color/error_color_material_light = 0x7f060076
com.clipsy.android.debug:attr/drawableLeftCompat = 0x7f0401a0
com.clipsy.android.debug:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f1201bf
com.clipsy.android.debug:color/error_color_material_dark = 0x7f060075
com.clipsy.android.debug:attr/layout_insetEdge = 0x7f0402d2
com.clipsy.android.debug:color/design_snackbar_background_color = 0x7f060070
com.clipsy.android.debug:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f07021a
com.clipsy.android.debug:color/design_fab_stroke_end_inner_color = 0x7f06006b
com.clipsy.android.debug:drawable/ic_m3_chip_checked_circle = 0x7f0800a2
com.clipsy.android.debug:id/fitCenter = 0x7f0900d3
com.clipsy.android.debug:color/design_fab_shadow_start_color = 0x7f06006a
com.clipsy.android.debug:color/design_fab_shadow_mid_color = 0x7f060069
com.clipsy.android.debug:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0702b2
com.clipsy.android.debug:attr/shapeAppearanceLargeComponent = 0x7f0403fe
com.clipsy.android.debug:styleable/GradientColorItem = 0x7f130045
com.clipsy.android.debug:color/design_default_color_primary_variant = 0x7f060063
com.clipsy.android.debug:drawable/abc_seekbar_track_material = 0x7f080064
com.clipsy.android.debug:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f08005f
com.clipsy.android.debug:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f07021e
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f060195
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f080012
com.clipsy.android.debug:attr/carousel_touchUp_dampeningFactor = 0x7f0400b3
com.clipsy.android.debug:color/design_default_color_on_surface = 0x7f060060
com.clipsy.android.debug:color/code_background = 0x7f06004a
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary70 = 0x7f0600e8
com.clipsy.android.debug:attr/colorSurfaceContainer = 0x7f04012c
com.clipsy.android.debug:attr/contentInsetEnd = 0x7f040144
com.clipsy.android.debug:attr/textAppearanceLineHeightEnabled = 0x7f04049b
com.clipsy.android.debug:color/design_default_color_on_error = 0x7f06005d
com.clipsy.android.debug:attr/logoAdjustViewBounds = 0x7f0402f1
com.clipsy.android.debug:attr/layout_constraintWidth_min = 0x7f0402c6
com.clipsy.android.debug:attr/drawableTintMode = 0x7f0401a5
com.clipsy.android.debug:color/design_default_color_background = 0x7f06005a
com.clipsy.android.debug:id/bounceEnd = 0x7f090065
com.clipsy.android.debug:drawable/abc_btn_radio_material = 0x7f080031
com.clipsy.android.debug:attr/customIntegerValue = 0x7f040171
com.clipsy.android.debug:attr/transitionFlags = 0x7f040509
com.clipsy.android.debug:color/design_dark_default_color_surface = 0x7f060059
com.clipsy.android.debug:drawable/abc_cab_background_top_mtrl_alpha = 0x7f080039
com.clipsy.android.debug:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0d006e
com.clipsy.android.debug:color/design_dark_default_color_primary_dark = 0x7f060055
com.clipsy.android.debug:style/Preference.SwitchPreference = 0x7f120162
com.clipsy.android.debug:layout/mtrl_calendar_months = 0x7f0c005b
com.clipsy.android.debug:color/design_dark_default_color_on_background = 0x7f06004f
com.clipsy.android.debug:attr/cursorErrorColor = 0x7f04016a
com.clipsy.android.debug:color/design_bottom_navigation_shadow_color = 0x7f06004b
com.clipsy.android.debug:layout/preference_material = 0x7f0c007c
com.clipsy.android.debug:color/clipsy_primary_dark = 0x7f060046
com.clipsy.android.debug:color/clipsy_error = 0x7f060043
com.clipsy.android.debug:id/mtrl_picker_header_title_and_selection = 0x7f090146
com.clipsy.android.debug:attr/thumbIconSize = 0x7f0404c9
com.clipsy.android.debug:color/m3_button_foreground_color_selector = 0x7f060082
com.clipsy.android.debug:attr/exitAnim = 0x7f0401d4
com.clipsy.android.debug:color/clipsy_card_dark = 0x7f060042
com.clipsy.android.debug:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f07013a
com.clipsy.android.debug:color/m3_tabs_icon_color = 0x7f060201
com.clipsy.android.debug:style/Base.Widget.AppCompat.SearchView = 0x7f1200f6
com.clipsy.android.debug:attr/dragThreshold = 0x7f04019c
com.clipsy.android.debug:color/clipsy_card_background = 0x7f060041
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0d008e
com.clipsy.android.debug:color/m3_sys_color_light_surface_container_highest = 0x7f0601ee
com.clipsy.android.debug:color/m3_dynamic_hint_foreground = 0x7f0600a1
com.clipsy.android.debug:color/cardview_shadow_end_color = 0x7f06003d
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral94 = 0x7f0600ce
com.clipsy.android.debug:color/cardview_light_background = 0x7f06003c
com.clipsy.android.debug:color/cardview_dark_background = 0x7f06003b
com.clipsy.android.debug:color/m3_ref_palette_primary30 = 0x7f06013d
com.clipsy.android.debug:attr/itemHorizontalPadding = 0x7f04026a
com.clipsy.android.debug:color/m3_fab_efab_background_color_selector = 0x7f0600a5
com.clipsy.android.debug:color/call_notification_decline_color = 0x7f06003a
com.clipsy.android.debug:color/call_notification_answer_color = 0x7f060039
com.clipsy.android.debug:attr/crossfade = 0x7f040167
com.clipsy.android.debug:color/cardview_shadow_start_color = 0x7f06003e
com.clipsy.android.debug:color/button_text_secondary_selector = 0x7f060038
com.clipsy.android.debug:attr/hideOnScroll = 0x7f04023d
com.clipsy.android.debug:attr/thumbIcon = 0x7f0404c8
com.clipsy.android.debug:color/button_text_secondary = 0x7f060037
com.clipsy.android.debug:macro/m3_comp_badge_large_label_text_type = 0x7f0d0004
com.clipsy.android.debug:color/button_background_secondary = 0x7f06002f
com.clipsy.android.debug:id/action_mode_bar_stub = 0x7f090042
com.clipsy.android.debug:anim/nav_default_exit_anim = 0x7f01002d
com.clipsy.android.debug:attr/arrowShaftLength = 0x7f040043
com.clipsy.android.debug:dimen/mtrl_calendar_month_vertical_padding = 0x7f07028f
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f080017
com.clipsy.android.debug:dimen/abc_action_button_min_width_material = 0x7f07000e
com.clipsy.android.debug:style/Base.Theme.Material3.Light.Dialog = 0x7f120065
com.clipsy.android.debug:id/pin = 0x7f090184
com.clipsy.android.debug:color/bottom_nav_color_selector = 0x7f060025
com.clipsy.android.debug:color/border_color = 0x7f060024
com.clipsy.android.debug:attr/closeIconEndPadding = 0x7f0400eb
com.clipsy.android.debug:color/bluetooth_blue = 0x7f060023
com.clipsy.android.debug:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f07018a
com.clipsy.android.debug:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1200a0
com.clipsy.android.debug:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0700b0
com.clipsy.android.debug:style/Widget.Material3.Button.ElevatedButton = 0x7f12037a
com.clipsy.android.debug:macro/m3_comp_dialog_supporting_text_type = 0x7f0d0028
com.clipsy.android.debug:color/background_floating_material_dark = 0x7f06001e
com.clipsy.android.debug:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0701fb
com.clipsy.android.debug:attr/constraint_referenced_tags = 0x7f040140
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.Dialog = 0x7f120293
com.clipsy.android.debug:id/flip = 0x7f0900d9
com.clipsy.android.debug:color/high_contrast_text = 0x7f06007a
com.clipsy.android.debug:drawable/m3_password_eye = 0x7f0800b7
com.clipsy.android.debug:attr/colorOutlineVariant = 0x7f04011c
com.clipsy.android.debug:attr/windowMinWidthMajor = 0x7f040530
com.clipsy.android.debug:color/background_color = 0x7f06001d
com.clipsy.android.debug:color/mtrl_tabs_legacy_text_color_selector = 0x7f0602e7
com.clipsy.android.debug:color/material_personalized_color_primary_inverse = 0x7f06028f
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0d00bb
com.clipsy.android.debug:attr/floatingActionButtonSmallSecondaryStyle = 0x7f040201
com.clipsy.android.debug:id/fitStart = 0x7f0900d5
com.clipsy.android.debug:color/accent_material_light = 0x7f06001a
com.clipsy.android.debug:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f07014b
com.clipsy.android.debug:color/accent_material_dark = 0x7f060019
com.clipsy.android.debug:color/material_grey_800 = 0x7f060266
com.clipsy.android.debug:color/design_fab_stroke_top_inner_color = 0x7f06006d
com.clipsy.android.debug:attr/autoShowKeyboard = 0x7f040048
com.clipsy.android.debug:layout/preference_widget_seekbar_material = 0x7f0c0080
com.clipsy.android.debug:attr/switchMinWidth = 0x7f040459
com.clipsy.android.debug:color/abc_search_url_text_pressed = 0x7f06000f
com.clipsy.android.debug:color/abc_primary_text_material_dark = 0x7f06000b
com.clipsy.android.debug:string/notification_clipboard_synced = 0x7f1100c5
com.clipsy.android.debug:integer/material_motion_duration_long_1 = 0x7f0a0027
com.clipsy.android.debug:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f0404b7
com.clipsy.android.debug:color/material_timepicker_button_stroke = 0x7f0602b2
com.clipsy.android.debug:id/card_network_info = 0x7f090076
com.clipsy.android.debug:color/abc_primary_text_disable_only_material_light = 0x7f06000a
com.clipsy.android.debug:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f1202b4
com.clipsy.android.debug:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f0600ad
com.clipsy.android.debug:styleable/NavDeepLink = 0x7f130076
com.clipsy.android.debug:styleable/FlowLayout = 0x7f13003d
com.clipsy.android.debug:string/m3_sys_motion_easing_linear = 0x7f110066
com.clipsy.android.debug:color/abc_primary_text_disable_only_material_dark = 0x7f060009
com.clipsy.android.debug:styleable/MultiSelectListPreference = 0x7f130073
com.clipsy.android.debug:style/Base.ThemeOverlay.AppCompat.Light = 0x7f120082
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary50 = 0x7f0600f3
com.clipsy.android.debug:attr/colorTertiaryFixedDim = 0x7f040138
com.clipsy.android.debug:color/abc_primary_text_material_light = 0x7f06000c
com.clipsy.android.debug:color/design_dark_default_color_on_error = 0x7f060050
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant50 = 0x7f060132
com.clipsy.android.debug:color/abc_btn_colored_borderless_text_material = 0x7f060002
com.clipsy.android.debug:attr/textInputFilledStyle = 0x7f0404b4
com.clipsy.android.debug:color/m3_checkbox_button_tint = 0x7f06008c
com.clipsy.android.debug:dimen/mtrl_snackbar_margin = 0x7f0702f7
com.clipsy.android.debug:attr/chipStandaloneStyle = 0x7f0400d5
com.clipsy.android.debug:color/material_harmonized_color_error = 0x7f060269
com.clipsy.android.debug:attr/ratingBarStyleIndicator = 0x7f0403d1
com.clipsy.android.debug:attr/materialIconButtonStyle = 0x7f04031c
com.clipsy.android.debug:id/TOP_START = 0x7f09000d
com.clipsy.android.debug:attr/yearStyle = 0x7f040534
com.clipsy.android.debug:dimen/m3_navigation_rail_elevation = 0x7f0701cf
com.clipsy.android.debug:attr/yearSelectedStyle = 0x7f040533
com.clipsy.android.debug:attr/maxImageSize = 0x7f04032c
com.clipsy.android.debug:id/tabMode = 0x7f0901e5
com.clipsy.android.debug:attr/onStateTransition = 0x7f040386
com.clipsy.android.debug:color/material_personalized_color_surface_container_low = 0x7f06029b
com.clipsy.android.debug:dimen/abc_action_bar_default_padding_start_material = 0x7f070004
com.clipsy.android.debug:attr/windowMinWidthMinor = 0x7f040531
com.clipsy.android.debug:attr/windowActionModeOverlay = 0x7f04052b
com.clipsy.android.debug:dimen/design_navigation_icon_padding = 0x7f070082
com.clipsy.android.debug:attr/navGraph = 0x7f040372
com.clipsy.android.debug:string/material_slider_value = 0x7f110078
com.clipsy.android.debug:attr/colorTertiaryContainer = 0x7f040136
com.clipsy.android.debug:attr/windowActionBarOverlay = 0x7f04052a
com.clipsy.android.debug:attr/controlBackground = 0x7f040153
com.clipsy.android.debug:color/m3_sys_color_secondary_fixed_dim = 0x7f0601fe
com.clipsy.android.debug:color/m3_sys_color_light_secondary_container = 0x7f0601e9
com.clipsy.android.debug:id/select_dialog_listview = 0x7f0901b5
com.clipsy.android.debug:color/material_dynamic_neutral_variant90 = 0x7f060238
com.clipsy.android.debug:attr/waveShape = 0x7f040526
com.clipsy.android.debug:attr/chipStrokeColor = 0x7f0400d7
com.clipsy.android.debug:dimen/m3_alert_dialog_icon_size = 0x7f0700ae
com.clipsy.android.debug:attr/wavePhase = 0x7f040525
com.clipsy.android.debug:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f07016d
com.clipsy.android.debug:attr/viewTransitionOnPositiveCross = 0x7f04051e
com.clipsy.android.debug:attr/viewTransitionOnCross = 0x7f04051c
com.clipsy.android.debug:string/settings_history_enabled_summary = 0x7f1100ec
com.clipsy.android.debug:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f0800d1
com.clipsy.android.debug:drawable/abc_switch_thumb_material = 0x7f080069
com.clipsy.android.debug:interpolator/mtrl_fast_out_slow_in = 0x7f0b000f
com.clipsy.android.debug:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f07024f
com.clipsy.android.debug:color/mtrl_switch_track_tint = 0x7f0602e3
com.clipsy.android.debug:id/accessibility_custom_action_26 = 0x7f090023
com.clipsy.android.debug:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f070177
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_on_background = 0x7f06018f
com.clipsy.android.debug:attr/viewTransitionMode = 0x7f04051b
com.clipsy.android.debug:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f120140
com.clipsy.android.debug:attr/largeFontVerticalOffsetAdjustment = 0x7f04028d
com.clipsy.android.debug:id/with_icon = 0x7f090237
com.clipsy.android.debug:attr/viewInflaterClass = 0x7f04051a
com.clipsy.android.debug:attr/indicatorColor = 0x7f04025a
com.clipsy.android.debug:dimen/m3_alert_dialog_action_top_padding = 0x7f0700aa
com.clipsy.android.debug:attr/floatingActionButtonPrimaryStyle = 0x7f0401fe
com.clipsy.android.debug:attr/entries = 0x7f0401c8
com.clipsy.android.debug:attr/progressBarStyle = 0x7f0403c7
com.clipsy.android.debug:attr/verticalOffsetWithText = 0x7f040519
com.clipsy.android.debug:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1203fe
com.clipsy.android.debug:color/m3_dynamic_default_color_secondary_text = 0x7f06009f
com.clipsy.android.debug:attr/verticalOffset = 0x7f040518
com.clipsy.android.debug:attr/ifTagSet = 0x7f040252
com.clipsy.android.debug:attr/tabTextColor = 0x7f04047c
com.clipsy.android.debug:styleable/NavigationView = 0x7f13007e
com.clipsy.android.debug:attr/useSimpleSummaryProvider = 0x7f040516
com.clipsy.android.debug:styleable/MockView = 0x7f13006b
com.clipsy.android.debug:attr/splitLayoutDirection = 0x7f040423
com.clipsy.android.debug:dimen/mtrl_calendar_header_divider_thickness = 0x7f070285
com.clipsy.android.debug:attr/useCompatPadding = 0x7f040513
com.clipsy.android.debug:style/Widget.AppCompat.Spinner.DropDown = 0x7f120354
com.clipsy.android.debug:macro/m3_comp_circular_progress_indicator_active_indicator_color = 0x7f0d000d
com.clipsy.android.debug:drawable/abc_list_divider_material = 0x7f08004c
com.clipsy.android.debug:id/accessibility_custom_action_25 = 0x7f090022
com.clipsy.android.debug:dimen/mtrl_card_checked_icon_margin = 0x7f0702a0
com.clipsy.android.debug:attr/materialDividerHeavyStyle = 0x7f040317
com.clipsy.android.debug:attr/colorPrimaryFixedDim = 0x7f040121
com.clipsy.android.debug:attr/uri = 0x7f040512
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f080014
com.clipsy.android.debug:attr/updatesContinuously = 0x7f040511
com.clipsy.android.debug:style/TextAppearance.Compat.Notification.Time = 0x7f1201f3
com.clipsy.android.debug:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08005e
com.clipsy.android.debug:attr/colorOnTertiaryFixedVariant = 0x7f04011a
com.clipsy.android.debug:drawable/notification_bg_low_normal = 0x7f0800f2
com.clipsy.android.debug:attr/upDuration = 0x7f040510
com.clipsy.android.debug:attr/ttcIndex = 0x7f04050f
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0d007e
com.clipsy.android.debug:color/teal_700 = 0x7f060313
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f12018a
com.clipsy.android.debug:attr/colorSurfaceDim = 0x7f040131
com.clipsy.android.debug:id/wrap = 0x7f090239
com.clipsy.android.debug:attr/windowActionBar = 0x7f040529
com.clipsy.android.debug:attr/transitionEasing = 0x7f040508
com.clipsy.android.debug:color/material_personalized__highlighted_text_inverse = 0x7f060278
com.clipsy.android.debug:attr/cardUseCompatPadding = 0x7f0400a9
com.clipsy.android.debug:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0601cb
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0601c6
com.clipsy.android.debug:attr/startIconScaleType = 0x7f040435
com.clipsy.android.debug:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.clipsy.android.debug:attr/trackTintMode = 0x7f040505
com.clipsy.android.debug:attr/trackDecorationTintMode = 0x7f040501
com.clipsy.android.debug:drawable/$m3_avd_hide_password__1 = 0x7f080007
com.clipsy.android.debug:id/text_device_type = 0x7f090202
com.clipsy.android.debug:attr/actionModeFindDrawable = 0x7f040019
com.clipsy.android.debug:attr/trackDecorationTint = 0x7f040500
com.clipsy.android.debug:id/south = 0x7f0901c9
com.clipsy.android.debug:dimen/m3_comp_navigation_rail_container_elevation = 0x7f070155
com.clipsy.android.debug:color/material_dynamic_neutral70 = 0x7f060229
com.clipsy.android.debug:attr/trackCornerRadius = 0x7f0404fe
com.clipsy.android.debug:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f070136
com.clipsy.android.debug:attr/flow_horizontalAlign = 0x7f04020c
com.clipsy.android.debug:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f120469
com.clipsy.android.debug:attr/trackColorActive = 0x7f0404fc
com.clipsy.android.debug:dimen/material_textinput_min_width = 0x7f070247
com.clipsy.android.debug:attr/trackColor = 0x7f0404fb
com.clipsy.android.debug:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f070130
com.clipsy.android.debug:color/material_personalized_color_primary_text = 0x7f060290
com.clipsy.android.debug:attr/tickColorInactive = 0x7f0404d4
com.clipsy.android.debug:attr/track = 0x7f0404fa
com.clipsy.android.debug:color/switch_thumb_color = 0x7f06030a
com.clipsy.android.debug:attr/touchAnchorId = 0x7f0404f7
com.clipsy.android.debug:styleable/RecycleListView = 0x7f13008d
com.clipsy.android.debug:attr/chipIconEnabled = 0x7f0400cc
com.clipsy.android.debug:color/foreground_material_light = 0x7f060078
com.clipsy.android.debug:attr/carousel_emptyViewsBehavior = 0x7f0400ac
com.clipsy.android.debug:attr/fastScrollVerticalThumbDrawable = 0x7f0401f4
com.clipsy.android.debug:attr/preferenceTheme = 0x7f0403bf
com.clipsy.android.debug:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0402f9
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f08000f
com.clipsy.android.debug:attr/tooltipText = 0x7f0404f5
com.clipsy.android.debug:attr/subheaderInsetEnd = 0x7f040448
com.clipsy.android.debug:attr/tooltipStyle = 0x7f0404f4
com.clipsy.android.debug:mipmap/ic_launcher_foreground = 0x7f0f0001
com.clipsy.android.debug:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f080043
com.clipsy.android.debug:string/action_settings = 0x7f110022
com.clipsy.android.debug:dimen/button_corner_radius_small = 0x7f070053
com.clipsy.android.debug:color/material_dynamic_neutral_variant20 = 0x7f060231
com.clipsy.android.debug:integer/config_navAnimTime = 0x7f0a0005
com.clipsy.android.debug:color/m3_text_button_foreground_color_selector = 0x7f060208
com.clipsy.android.debug:attr/cornerFamilyTopRight = 0x7f04015a
com.clipsy.android.debug:style/Widget.AppCompat.ProgressBar = 0x7f12034a
com.clipsy.android.debug:color/design_dark_default_color_secondary = 0x7f060057
com.clipsy.android.debug:anim/nav_default_enter_anim = 0x7f01002c
com.clipsy.android.debug:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f070242
com.clipsy.android.debug:attr/textBackgroundPanX = 0x7f0404aa
com.clipsy.android.debug:color/design_default_color_surface = 0x7f060066
com.clipsy.android.debug:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0700cb
com.clipsy.android.debug:dimen/m3_timepicker_display_stroke_width = 0x7f070223
com.clipsy.android.debug:attr/trackThickness = 0x7f040503
com.clipsy.android.debug:dimen/abc_action_bar_stacked_max_height = 0x7f070009
com.clipsy.android.debug:string/settings_device_name = 0x7f1100e9
com.clipsy.android.debug:string/mtrl_picker_confirm = 0x7f110095
com.clipsy.android.debug:attr/toolbarSurfaceStyle = 0x7f0404f1
com.clipsy.android.debug:attr/titleTextStyle = 0x7f0404ec
com.clipsy.android.debug:styleable/MaterialTextAppearance = 0x7f130064
com.clipsy.android.debug:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f120265
com.clipsy.android.debug:attr/behavior_halfExpandedRatio = 0x7f040075
com.clipsy.android.debug:dimen/abc_progress_bar_height_material = 0x7f070035
com.clipsy.android.debug:color/switch_thumb_disabled_material_light = 0x7f06030c
com.clipsy.android.debug:layout/material_chip_input_combo = 0x7f0c003f
com.clipsy.android.debug:attr/motionEasingStandardInterpolator = 0x7f04035f
com.clipsy.android.debug:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0d005b
com.clipsy.android.debug:attr/titleTextEllipsize = 0x7f0404eb
com.clipsy.android.debug:string/exposed_dropdown_menu_content_description = 0x7f11004e
com.clipsy.android.debug:attr/materialAlertDialogBodyTextStyle = 0x7f0402f8
com.clipsy.android.debug:styleable/KeyFrame = 0x7f13004a
com.clipsy.android.debug:dimen/m3_fab_border_width = 0x7f0701bb
com.clipsy.android.debug:attr/titleMargins = 0x7f0404e7
com.clipsy.android.debug:attr/motionPath = 0x7f040369
com.clipsy.android.debug:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0d0140
com.clipsy.android.debug:attr/titleMarginBottom = 0x7f0404e3
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Subhead = 0x7f120031
com.clipsy.android.debug:attr/keylines = 0x7f040288
com.clipsy.android.debug:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f070126
com.clipsy.android.debug:dimen/mtrl_calendar_bottom_padding = 0x7f070279
com.clipsy.android.debug:attr/titleMargin = 0x7f0404e2
com.clipsy.android.debug:dimen/design_bottom_navigation_text_size = 0x7f070076
com.clipsy.android.debug:attr/isPreferenceVisible = 0x7f040266
com.clipsy.android.debug:id/fixed = 0x7f0900d8
com.clipsy.android.debug:anim/abc_slide_in_top = 0x7f010007
com.clipsy.android.debug:attr/simpleItemLayout = 0x7f040412
com.clipsy.android.debug:attr/titleEnabled = 0x7f0404e1
com.clipsy.android.debug:style/Theme.Design.NoActionBar = 0x7f120255
com.clipsy.android.debug:dimen/m3_alert_dialog_corner_size = 0x7f0700ab
com.clipsy.android.debug:attr/thumbIconTint = 0x7f0404ca
com.clipsy.android.debug:attr/titleCentered = 0x7f0404df
com.clipsy.android.debug:attr/itemPaddingTop = 0x7f040273
com.clipsy.android.debug:attr/motionEasingStandard = 0x7f04035c
com.clipsy.android.debug:attr/preferenceScreenStyle = 0x7f0403bd
com.clipsy.android.debug:attr/tickVisible = 0x7f0404da
com.clipsy.android.debug:attr/layout_constraintStart_toStartOf = 0x7f0402bb
com.clipsy.android.debug:attr/tickRadiusActive = 0x7f0404d8
com.clipsy.android.debug:id/list_item = 0x7f09010f
com.clipsy.android.debug:drawable/mtrl_ic_check_mark = 0x7f0800d9
com.clipsy.android.debug:attr/expandedTitleGravity = 0x7f0401d8
com.clipsy.android.debug:dimen/m3_searchview_elevation = 0x7f0701e7
com.clipsy.android.debug:attr/tickMarkTintMode = 0x7f0404d7
com.clipsy.android.debug:style/Base.Theme.AppCompat.Dialog = 0x7f120051
com.clipsy.android.debug:attr/floatingActionButtonSmallStyle = 0x7f040202
com.clipsy.android.debug:attr/textureHeight = 0x7f0404c2
com.clipsy.android.debug:color/material_dynamic_primary30 = 0x7f06023f
com.clipsy.android.debug:attr/tickColorActive = 0x7f0404d3
com.clipsy.android.debug:attr/dropDownListViewStyle = 0x7f0401ab
com.clipsy.android.debug:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0d0127
com.clipsy.android.debug:attr/tickColor = 0x7f0404d2
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Headline = 0x7f120023
com.clipsy.android.debug:attr/thumbTint = 0x7f0404d0
com.clipsy.android.debug:attr/behavior_autoHide = 0x7f040070
com.clipsy.android.debug:id/action_refresh = 0x7f090044
com.clipsy.android.debug:id/accessibility_custom_action_4 = 0x7f09002a
com.clipsy.android.debug:color/design_default_color_error = 0x7f06005b
com.clipsy.android.debug:style/ThemeOverlay.AppCompat.Dark = 0x7f1202a2
com.clipsy.android.debug:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f080024
com.clipsy.android.debug:dimen/design_bottom_navigation_label_padding = 0x7f070073
com.clipsy.android.debug:color/m3_popupmenu_overlay_color = 0x7f0600b6
com.clipsy.android.debug:attr/shapeAppearanceCornerExtraSmall = 0x7f0403fa
com.clipsy.android.debug:attr/thumbTextPadding = 0x7f0404cf
com.clipsy.android.debug:attr/contentInsetStartWithNavigation = 0x7f040149
com.clipsy.android.debug:attr/thumbStrokeWidth = 0x7f0404ce
com.clipsy.android.debug:color/m3_ref_palette_tertiary40 = 0x7f060158
com.clipsy.android.debug:attr/thumbIconTintMode = 0x7f0404cb
com.clipsy.android.debug:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0701d5
com.clipsy.android.debug:attr/titleMarginEnd = 0x7f0404e4
com.clipsy.android.debug:id/progress_scanning = 0x7f09018e
com.clipsy.android.debug:attr/tooltipFrameBackground = 0x7f0404f3
com.clipsy.android.debug:dimen/mtrl_btn_text_btn_icon_padding = 0x7f070271
com.clipsy.android.debug:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0702c2
com.clipsy.android.debug:attr/onNegativeCross = 0x7f040383
com.clipsy.android.debug:attr/textureWidth = 0x7f0404c3
com.clipsy.android.debug:attr/stackFromEnd = 0x7f04042e
com.clipsy.android.debug:color/source_remote = 0x7f060306
com.clipsy.android.debug:attr/topInsetScrimEnabled = 0x7f0404f6
com.clipsy.android.debug:attr/customStringValue = 0x7f040175
com.clipsy.android.debug:dimen/mtrl_extended_fab_bottom_padding = 0x7f0702ab
com.clipsy.android.debug:attr/itemShapeFillColor = 0x7f040277
com.clipsy.android.debug:attr/textInputFilledExposedDropdownMenuStyle = 0x7f0404b3
com.clipsy.android.debug:attr/springDamping = 0x7f040429
com.clipsy.android.debug:attr/touchRegionId = 0x7f0404f9
com.clipsy.android.debug:dimen/m3_comp_menu_container_elevation = 0x7f070143
com.clipsy.android.debug:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1200e1
com.clipsy.android.debug:dimen/m3_btn_translation_z_hovered = 0x7f0700ef
com.clipsy.android.debug:style/Base.Widget.Material3.BottomNavigationView = 0x7f120103
com.clipsy.android.debug:color/material_dynamic_neutral100 = 0x7f060223
com.clipsy.android.debug:attr/textInputFilledDenseStyle = 0x7f0404b2
com.clipsy.android.debug:attr/textColorSearchUrl = 0x7f0404af
com.clipsy.android.debug:id/src_atop = 0x7f0901d3
com.clipsy.android.debug:attr/textColorAlertDialogListItem = 0x7f0404ae
com.clipsy.android.debug:attr/roundPercent = 0x7f0403e2
com.clipsy.android.debug:attr/textBackgroundPanY = 0x7f0404ab
com.clipsy.android.debug:attr/textAppearanceTitleLarge = 0x7f0404a6
com.clipsy.android.debug:attr/textAppearanceSubtitle2 = 0x7f0404a5
com.clipsy.android.debug:id/accessibility_custom_action_28 = 0x7f090025
com.clipsy.android.debug:attr/textAppearanceSubtitle1 = 0x7f0404a4
com.clipsy.android.debug:integer/m3_sys_motion_duration_extra_long4 = 0x7f0a0013
com.clipsy.android.debug:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f080081
com.clipsy.android.debug:attr/labelBehavior = 0x7f04028a
com.clipsy.android.debug:attr/textAppearanceSearchResultSubtitle = 0x7f0404a1
com.clipsy.android.debug:attr/textAppearancePopupMenuHeader = 0x7f0404a0
com.clipsy.android.debug:attr/textAppearanceOverline = 0x7f04049f
com.clipsy.android.debug:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f08007b
com.clipsy.android.debug:color/m3_ref_palette_primary70 = 0x7f060141
com.clipsy.android.debug:styleable/RecyclerView = 0x7f13008e
com.clipsy.android.debug:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1203e1
com.clipsy.android.debug:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1200ae
com.clipsy.android.debug:macro/m3_comp_slider_label_container_color = 0x7f0d0112
com.clipsy.android.debug:attr/boxStrokeWidthFocused = 0x7f040091
com.clipsy.android.debug:attr/textAppearanceListItemSmall = 0x7f04049e
com.clipsy.android.debug:attr/textAppearanceLargePopupMenu = 0x7f04049a
com.clipsy.android.debug:color/m3_calendar_item_stroke_color = 0x7f060087
com.clipsy.android.debug:attr/textAppearanceLabelLarge = 0x7f040497
com.clipsy.android.debug:style/TextAppearance.Material3.BodySmall = 0x7f120213
com.clipsy.android.debug:attr/colorOnSecondaryFixedVariant = 0x7f040113
com.clipsy.android.debug:attr/placeholderTextAppearance = 0x7f0403a8
com.clipsy.android.debug:attr/textAppearanceHeadlineLarge = 0x7f040494
com.clipsy.android.debug:id/open_search_view_divider = 0x7f09016c
com.clipsy.android.debug:attr/cornerFamilyTopLeft = 0x7f040159
com.clipsy.android.debug:color/clipsy_surface = 0x7f060047
com.clipsy.android.debug:dimen/mtrl_navigation_rail_default_width = 0x7f0702d2
com.clipsy.android.debug:styleable/include = 0x7f1300b8
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f120331
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0600d9
com.clipsy.android.debug:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0701e4
com.clipsy.android.debug:attr/layout_constraintBottom_creator = 0x7f0402a0
com.clipsy.android.debug:attr/colorSurfaceInverse = 0x7f040132
com.clipsy.android.debug:attr/textAppearanceHeadline2 = 0x7f04048f
com.clipsy.android.debug:attr/checkedIconVisible = 0x7f0400c4
com.clipsy.android.debug:attr/popupMenuBackground = 0x7f0403b1
com.clipsy.android.debug:color/m3_textfield_label_color = 0x7f06020d
com.clipsy.android.debug:attr/passwordToggleEnabled = 0x7f04039a
com.clipsy.android.debug:attr/textAppearanceDisplaySmall = 0x7f04048d
com.clipsy.android.debug:dimen/design_fab_translation_z_pressed = 0x7f070080
com.clipsy.android.debug:attr/subheaderTextAppearance = 0x7f04044a
com.clipsy.android.debug:attr/textAppearanceDisplayMedium = 0x7f04048c
com.clipsy.android.debug:attr/animateMenuItems = 0x7f04003a
com.clipsy.android.debug:attr/toolbarId = 0x7f0404ee
com.clipsy.android.debug:attr/textAppearanceButton = 0x7f040489
com.clipsy.android.debug:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0702ba
com.clipsy.android.debug:color/material_personalized_color_control_highlight = 0x7f06027b
com.clipsy.android.debug:attr/textAppearanceBodyLarge = 0x7f040486
com.clipsy.android.debug:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0d0007
com.clipsy.android.debug:color/abc_hint_foreground_material_light = 0x7f060008
com.clipsy.android.debug:style/AlertDialog.AppCompat = 0x7f120000
com.clipsy.android.debug:color/tooltip_background_light = 0x7f060317
com.clipsy.android.debug:color/design_dark_default_color_error = 0x7f06004e
com.clipsy.android.debug:style/Theme.Material3.Light = 0x7f120269
com.clipsy.android.debug:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0800c7
com.clipsy.android.debug:attr/passwordToggleContentDescription = 0x7f040398
com.clipsy.android.debug:attr/telltales_tailScale = 0x7f040481
com.clipsy.android.debug:dimen/sliding_pane_detail_pane_width = 0x7f070326
com.clipsy.android.debug:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1200db
com.clipsy.android.debug:color/mtrl_calendar_item_stroke_color = 0x7f0602c0
com.clipsy.android.debug:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f12004d
com.clipsy.android.debug:attr/selectorSize = 0x7f0403f6
com.clipsy.android.debug:color/m3_default_color_secondary_text = 0x7f060098
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0600d5
com.clipsy.android.debug:attr/layout_constraintVertical_chainStyle = 0x7f0402c1
com.clipsy.android.debug:attr/targetPackage = 0x7f04047f
com.clipsy.android.debug:color/design_dark_default_color_on_secondary = 0x7f060052
com.clipsy.android.debug:dimen/m3_extended_fab_start_padding = 0x7f0701b9
com.clipsy.android.debug:color/mtrl_textinput_filled_box_default_background_color = 0x7f0602ec
com.clipsy.android.debug:id/textinput_counter = 0x7f09020d
com.clipsy.android.debug:attr/targetId = 0x7f04047e
com.clipsy.android.debug:string/material_hour_suffix = 0x7f11006e
com.clipsy.android.debug:dimen/m3_carousel_extra_small_item_size = 0x7f0700fa
com.clipsy.android.debug:attr/tabUnboundedRipple = 0x7f04047d
com.clipsy.android.debug:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0d011a
com.clipsy.android.debug:id/locale = 0x7f090110
com.clipsy.android.debug:attr/listDividerAlertDialog = 0x7f0402e4
com.clipsy.android.debug:attr/tabTextAppearance = 0x7f04047b
com.clipsy.android.debug:dimen/material_input_text_to_prefix_suffix_padding = 0x7f070244
com.clipsy.android.debug:attr/clearTop = 0x7f0400e2
com.clipsy.android.debug:attr/tabStyle = 0x7f04047a
com.clipsy.android.debug:attr/scrimAnimationDuration = 0x7f0403e6
com.clipsy.android.debug:attr/viewTransitionOnNegativeCross = 0x7f04051d
com.clipsy.android.debug:styleable/ActionBar = 0x7f130000
com.clipsy.android.debug:attr/cardBackgroundColor = 0x7f0400a3
com.clipsy.android.debug:attr/tabSelectedTextColor = 0x7f040479
com.clipsy.android.debug:id/peekHeight = 0x7f090182
com.clipsy.android.debug:attr/itemMinHeight = 0x7f040270
com.clipsy.android.debug:attr/tabSecondaryStyle = 0x7f040477
com.clipsy.android.debug:id/expanded_menu = 0x7f0900cb
com.clipsy.android.debug:attr/tabRippleColor = 0x7f040476
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f12007b
com.clipsy.android.debug:attr/switchPadding = 0x7f04045a
com.clipsy.android.debug:attr/tabMaxWidth = 0x7f04046e
com.clipsy.android.debug:style/Preference.SeekBarPreference = 0x7f120160
com.clipsy.android.debug:attr/tabIndicatorGravity = 0x7f04046b
com.clipsy.android.debug:attr/motion_triggerOnCollision = 0x7f04036f
com.clipsy.android.debug:color/button_material_dark = 0x7f060030
com.clipsy.android.debug:color/button_background_primary = 0x7f06002d
com.clipsy.android.debug:attr/tabContentStart = 0x7f040462
com.clipsy.android.debug:attr/positiveButtonText = 0x7f0403b5
com.clipsy.android.debug:attr/switchPreferenceStyle = 0x7f04045c
com.clipsy.android.debug:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0700b1
com.clipsy.android.debug:integer/m3_sys_motion_path = 0x7f0a0020
com.clipsy.android.debug:attr/switchPreferenceCompatStyle = 0x7f04045b
com.clipsy.android.debug:attr/suggestionRowLayout = 0x7f040454
com.clipsy.android.debug:id/text_discovery_status = 0x7f090204
com.clipsy.android.debug:color/button_text_disabled = 0x7f060034
com.clipsy.android.debug:style/Theme.MaterialComponents.Dialog.Alert = 0x7f120286
com.clipsy.android.debug:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0700b3
com.clipsy.android.debug:drawable/ic_call_answer_low = 0x7f080090
com.clipsy.android.debug:color/m3_ref_palette_secondary99 = 0x7f060152
com.clipsy.android.debug:attr/thumbStrokeColor = 0x7f0404cd
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Light = 0x7f120072
com.clipsy.android.debug:layout/abc_alert_dialog_title_material = 0x7f0c000a
com.clipsy.android.debug:attr/suffixTextColor = 0x7f040453
com.clipsy.android.debug:attr/subtitleTextStyle = 0x7f040450
com.clipsy.android.debug:mipmap/ic_launcher = 0x7f0f0000
com.clipsy.android.debug:animator/fragment_open_enter = 0x7f020007
com.clipsy.android.debug:attr/subtitleTextColor = 0x7f04044f
com.clipsy.android.debug:plurals/mtrl_badge_content_description = 0x7f100000
com.clipsy.android.debug:attr/submitBackground = 0x7f04044b
com.clipsy.android.debug:attr/defaultNavHost = 0x7f04017e
com.clipsy.android.debug:attr/emojiCompatEnabled = 0x7f0401b8
com.clipsy.android.debug:attr/dividerHorizontal = 0x7f040194
com.clipsy.android.debug:attr/subtitleTextAppearance = 0x7f04044e
com.clipsy.android.debug:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0700b4
com.clipsy.android.debug:drawable/m3_popupmenu_background_overlay = 0x7f0800b8
com.clipsy.android.debug:attr/errorShown = 0x7f0401d1
com.clipsy.android.debug:attr/subtitle = 0x7f04044c
com.clipsy.android.debug:dimen/m3_comp_fab_primary_large_container_height = 0x7f070128
com.clipsy.android.debug:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f120083
com.clipsy.android.debug:animator/m3_card_state_list_anim = 0x7f02000d
com.clipsy.android.debug:attr/colorButtonNormal = 0x7f0400ff
com.clipsy.android.debug:styleable/MenuView = 0x7f13006a
com.clipsy.android.debug:attr/chipEndPadding = 0x7f0400c9
com.clipsy.android.debug:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0701ff
com.clipsy.android.debug:attr/activityName = 0x7f04002a
com.clipsy.android.debug:color/m3_bottom_sheet_drag_handle_color = 0x7f060080
com.clipsy.android.debug:dimen/m3_sys_elevation_level4 = 0x7f0701f9
com.clipsy.android.debug:attr/colorSurface = 0x7f04012a
com.clipsy.android.debug:string/mtrl_switch_thumb_path_morphing = 0x7f1100b6
com.clipsy.android.debug:drawable/mtrl_ic_arrow_drop_down = 0x7f0800d6
com.clipsy.android.debug:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f070119
com.clipsy.android.debug:color/m3_sys_color_light_primary_container = 0x7f0601e7
com.clipsy.android.debug:id/open_search_view_toolbar = 0x7f090174
com.clipsy.android.debug:color/m3_ref_palette_tertiary10 = 0x7f060154
com.clipsy.android.debug:attr/subMenuArrow = 0x7f040446
com.clipsy.android.debug:attr/statusBarScrim = 0x7f040443
com.clipsy.android.debug:attr/statusBarBackground = 0x7f040441
com.clipsy.android.debug:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f12042b
com.clipsy.android.debug:attr/state_liftable = 0x7f04043e
com.clipsy.android.debug:attr/fastScrollVerticalTrackDrawable = 0x7f0401f5
com.clipsy.android.debug:attr/cornerSizeTopRight = 0x7f040160
com.clipsy.android.debug:attr/state_with_icon = 0x7f040440
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f120178
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0601c3
com.clipsy.android.debug:attr/maxHeight = 0x7f04032b
com.clipsy.android.debug:attr/state_collapsible = 0x7f04043a
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f120175
com.clipsy.android.debug:array/history_limit_values = 0x7f030001
com.clipsy.android.debug:attr/backgroundColor = 0x7f040051
com.clipsy.android.debug:id/rtl = 0x7f09019e
com.clipsy.android.debug:color/design_icon_tint = 0x7f06006f
com.clipsy.android.debug:attr/helperTextTextAppearance = 0x7f040237
com.clipsy.android.debug:attr/state_collapsed = 0x7f040439
com.clipsy.android.debug:string/abc_menu_enter_shortcut_label = 0x7f11000b
com.clipsy.android.debug:attr/itemShapeAppearanceOverlay = 0x7f040276
com.clipsy.android.debug:dimen/m3_sys_elevation_level0 = 0x7f0701f5
com.clipsy.android.debug:id/btn_cancel_pairing = 0x7f090067
com.clipsy.android.debug:attr/state_above_anchor = 0x7f040438
com.clipsy.android.debug:attr/contentPadding = 0x7f04014a
com.clipsy.android.debug:attr/itemIconSize = 0x7f04026d
com.clipsy.android.debug:attr/motionPathRotate = 0x7f04036a
com.clipsy.android.debug:attr/startIconTintMode = 0x7f040437
com.clipsy.android.debug:color/design_fab_stroke_end_outer_color = 0x7f06006c
com.clipsy.android.debug:attr/iconEndPadding = 0x7f040248
com.clipsy.android.debug:color/abc_secondary_text_material_light = 0x7f060012
com.clipsy.android.debug:color/material_dynamic_primary60 = 0x7f060242
com.clipsy.android.debug:id/antiClockwise = 0x7f090052
com.clipsy.android.debug:attr/triggerId = 0x7f04050c
com.clipsy.android.debug:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f07021c
com.clipsy.android.debug:attr/actionBarSize = 0x7f040006
com.clipsy.android.debug:style/TextAppearance.Material3.BodyLarge = 0x7f120211
com.clipsy.android.debug:animator/m3_extended_fab_state_list_animator = 0x7f020014
com.clipsy.android.debug:attr/showText = 0x7f04040d
com.clipsy.android.debug:styleable/CardView = 0x7f13001e
com.clipsy.android.debug:attr/thumbTintMode = 0x7f0404d1
com.clipsy.android.debug:color/material_dynamic_tertiary30 = 0x7f060259
com.clipsy.android.debug:attr/startIconContentDescription = 0x7f040432
com.clipsy.android.debug:integer/material_motion_duration_long_2 = 0x7f0a0028
com.clipsy.android.debug:dimen/fastscroll_default_thickness = 0x7f07009c
com.clipsy.android.debug:layout/expand_button = 0x7f0c0030
com.clipsy.android.debug:attr/dependency = 0x7f040185
com.clipsy.android.debug:attr/alertDialogCenterButtons = 0x7f04002e
com.clipsy.android.debug:color/m3_ref_palette_error70 = 0x7f06010f
com.clipsy.android.debug:attr/startIconCheckable = 0x7f040431
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f12029d
com.clipsy.android.debug:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
com.clipsy.android.debug:attr/fragment = 0x7f04022a
com.clipsy.android.debug:color/abc_tint_seek_thumb = 0x7f060016
com.clipsy.android.debug:color/material_blue_grey_800 = 0x7f06021a
com.clipsy.android.debug:color/m3_ref_palette_neutral92 = 0x7f060126
com.clipsy.android.debug:attr/springStiffness = 0x7f04042b
com.clipsy.android.debug:styleable/TextAppearance = 0x7f1300a9
com.clipsy.android.debug:attr/springMass = 0x7f04042a
com.clipsy.android.debug:attr/subtitleCentered = 0x7f04044d
com.clipsy.android.debug:drawable/design_ic_visibility_off = 0x7f080088
com.clipsy.android.debug:attr/tabPaddingTop = 0x7f040475
com.clipsy.android.debug:attr/trackDecoration = 0x7f0404ff
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0d00d1
com.clipsy.android.debug:dimen/mtrl_textinput_start_icon_margin_end = 0x7f070308
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1203cc
com.clipsy.android.debug:attr/springBoundary = 0x7f040428
com.clipsy.android.debug:color/m3_ref_palette_primary40 = 0x7f06013e
com.clipsy.android.debug:attr/textAppearanceListItemSecondary = 0x7f04049d
com.clipsy.android.debug:color/design_dark_default_color_secondary_variant = 0x7f060058
com.clipsy.android.debug:attr/spinnerStyle = 0x7f040422
com.clipsy.android.debug:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1203bb
com.clipsy.android.debug:string/status_service_stopped = 0x7f1100fc
com.clipsy.android.debug:attr/showAnimationBehavior = 0x7f040406
com.clipsy.android.debug:attr/liftOnScrollTargetViewId = 0x7f0402dc
com.clipsy.android.debug:attr/materialCardViewStyle = 0x7f040313
com.clipsy.android.debug:attr/itemActiveIndicatorStyle = 0x7f040267
com.clipsy.android.debug:attr/sizePercent = 0x7f04041a
com.clipsy.android.debug:attr/sideSheetDialogTheme = 0x7f040410
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1203d2
com.clipsy.android.debug:id/inward = 0x7f090100
com.clipsy.android.debug:attr/tabIconTint = 0x7f040464
com.clipsy.android.debug:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f12007e
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary90 = 0x7f0600ea
com.clipsy.android.debug:attr/colorOnError = 0x7f040109
com.clipsy.android.debug:attr/alwaysExpand = 0x7f040038
com.clipsy.android.debug:attr/transformPivotTarget = 0x7f040506
com.clipsy.android.debug:layout/preference_recyclerview = 0x7f0c007d
com.clipsy.android.debug:attr/bottomSheetStyle = 0x7f040086
com.clipsy.android.debug:attr/checkedIconMargin = 0x7f0400c1
com.clipsy.android.debug:attr/showSeekBarValue = 0x7f04040c
com.clipsy.android.debug:id/matrix = 0x7f09012b
com.clipsy.android.debug:attr/backgroundInsetStart = 0x7f040054
com.clipsy.android.debug:attr/shortcutMatchRequired = 0x7f040403
com.clipsy.android.debug:id/special_effects_controller_view_tag = 0x7f0901cb
com.clipsy.android.debug:attr/shapeAppearanceOverlay = 0x7f040400
com.clipsy.android.debug:attr/restoreState = 0x7f0403dd
com.clipsy.android.debug:anim/m3_side_sheet_enter_from_right = 0x7f010026
com.clipsy.android.debug:attr/shapeAppearanceCornerSmall = 0x7f0403fd
com.clipsy.android.debug:attr/shapeAppearanceCornerLarge = 0x7f0403fb
com.clipsy.android.debug:attr/shapeAppearanceCornerExtraLarge = 0x7f0403f9
com.clipsy.android.debug:attr/selectableItemBackground = 0x7f0403f3
com.clipsy.android.debug:attr/seekBarStyle = 0x7f0403f1
com.clipsy.android.debug:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f120471
com.clipsy.android.debug:style/ThemeOverlay.Design.TextInputEditText = 0x7f1202a9
com.clipsy.android.debug:color/abc_secondary_text_material_dark = 0x7f060011
com.clipsy.android.debug:attr/layout_goneMarginTop = 0x7f0402d1
com.clipsy.android.debug:attr/popUpTo = 0x7f0403ae
com.clipsy.android.debug:attr/listPopupWindowStyle = 0x7f0402e8
com.clipsy.android.debug:attr/secondaryActivityAction = 0x7f0403ed
com.clipsy.android.debug:color/button_background_disabled = 0x7f06002c
com.clipsy.android.debug:bool/enable_system_foreground_service_default = 0x7f050004
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f12044b
com.clipsy.android.debug:attr/removeEmbeddedFabElevation = 0x7f0403dc
com.clipsy.android.debug:attr/searchViewStyle = 0x7f0403ec
com.clipsy.android.debug:id/textEnd = 0x7f0901f5
com.clipsy.android.debug:attr/layout_constraintStart_toEndOf = 0x7f0402ba
com.clipsy.android.debug:animator/fragment_fade_enter = 0x7f020005
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_container_color = 0x7f0d0086
com.clipsy.android.debug:color/design_fab_stroke_top_outer_color = 0x7f06006e
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0d00bd
com.clipsy.android.debug:color/bright_foreground_inverse_material_light = 0x7f060029
com.clipsy.android.debug:attr/barrierAllowsGoneWidgets = 0x7f04006d
com.clipsy.android.debug:attr/badgeRadius = 0x7f04005d
com.clipsy.android.debug:drawable/abc_cab_background_top_material = 0x7f080038
com.clipsy.android.debug:styleable/SideSheetBehavior_Layout = 0x7f130096
com.clipsy.android.debug:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0d00b1
com.clipsy.android.debug:attr/searchPrefixText = 0x7f0403eb
com.clipsy.android.debug:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f120135
com.clipsy.android.debug:dimen/abc_alert_dialog_button_bar_height = 0x7f070010
com.clipsy.android.debug:attr/scrimBackground = 0x7f0403e7
com.clipsy.android.debug:attr/indeterminateAnimationType = 0x7f040258
com.clipsy.android.debug:style/Widget.Material3.Slider.Label = 0x7f1203f4
com.clipsy.android.debug:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.clipsy.android.debug:attr/scaleFromTextSize = 0x7f0403e5
com.clipsy.android.debug:style/Base.Widget.AppCompat.ActionMode = 0x7f1200ce
com.clipsy.android.debug:macro/m3_comp_fab_primary_small_container_shape = 0x7f0d003b
com.clipsy.android.debug:attr/actionProviderClass = 0x7f040024
com.clipsy.android.debug:attr/saturation = 0x7f0403e4
com.clipsy.android.debug:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0d0126
com.clipsy.android.debug:attr/route = 0x7f0403e3
com.clipsy.android.debug:attr/onShow = 0x7f040385
com.clipsy.android.debug:style/TextAppearance.Material3.SearchBar = 0x7f12021e
com.clipsy.android.debug:attr/defaultValue = 0x7f040182
com.clipsy.android.debug:attr/shapeCornerFamily = 0x7f040402
com.clipsy.android.debug:attr/liftOnScrollColor = 0x7f0402db
com.clipsy.android.debug:color/material_on_background_disabled = 0x7f06026d
com.clipsy.android.debug:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f12039c
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant0 = 0x7f06012c
com.clipsy.android.debug:attr/round = 0x7f0403e1
com.clipsy.android.debug:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f070251
com.clipsy.android.debug:attr/barLength = 0x7f04006c
com.clipsy.android.debug:attr/reverseLayout = 0x7f0403de
com.clipsy.android.debug:integer/mtrl_btn_anim_duration_ms = 0x7f0a0030
com.clipsy.android.debug:attr/tickMarkTint = 0x7f0404d6
com.clipsy.android.debug:attr/minTouchTargetSize = 0x7f04033b
com.clipsy.android.debug:attr/region_widthLessThan = 0x7f0403da
com.clipsy.android.debug:drawable/notification_template_icon_bg = 0x7f0800f8
com.clipsy.android.debug:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f07023f
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary90 = 0x7f0600f7
com.clipsy.android.debug:style/Theme.Material3.Dark = 0x7f120256
com.clipsy.android.debug:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f080074
com.clipsy.android.debug:attr/region_heightMoreThan = 0x7f0403d9
com.clipsy.android.debug:id/fitEnd = 0x7f0900d4
com.clipsy.android.debug:attr/motionEasingEmphasizedInterpolator = 0x7f040359
com.clipsy.android.debug:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f07013f
com.clipsy.android.debug:attr/thumbColor = 0x7f0404c6
com.clipsy.android.debug:attr/reactiveGuide_valueId = 0x7f0403d6
com.clipsy.android.debug:styleable/MaterialAlertDialog = 0x7f130056
com.clipsy.android.debug:attr/bottomSheetDragHandleStyle = 0x7f040085
com.clipsy.android.debug:attr/reactiveGuide_applyToConstraintSet = 0x7f0403d5
com.clipsy.android.debug:attr/collapsingToolbarLayoutMediumStyle = 0x7f0400fa
com.clipsy.android.debug:attr/materialButtonOutlinedStyle = 0x7f0402fe
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral70 = 0x7f0600c9
com.clipsy.android.debug:attr/reactiveGuide_applyToAllConstraintSets = 0x7f0403d4
com.clipsy.android.debug:styleable/AnimatedStateListDrawableItem = 0x7f13000b
com.clipsy.android.debug:attr/switchStyle = 0x7f04045d
com.clipsy.android.debug:dimen/abc_text_size_title_material = 0x7f07004f
com.clipsy.android.debug:attr/reactiveGuide_animateChange = 0x7f0403d3
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0d0010
com.clipsy.android.debug:id/tag_accessibility_actions = 0x7f0901e6
com.clipsy.android.debug:dimen/mtrl_badge_size = 0x7f070252
com.clipsy.android.debug:string/history_item_delete = 0x7f110056
com.clipsy.android.debug:attr/ratingBarStyleSmall = 0x7f0403d2
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral99 = 0x7f0600d2
com.clipsy.android.debug:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f07016c
com.clipsy.android.debug:attr/textBackgroundRotate = 0x7f0404ac
com.clipsy.android.debug:attr/radioButtonStyle = 0x7f0403ce
com.clipsy.android.debug:color/mtrl_fab_icon_text_color_selector = 0x7f0602cd
com.clipsy.android.debug:attr/closeIconStartPadding = 0x7f0400ed
com.clipsy.android.debug:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1200a3
com.clipsy.android.debug:string/history_title = 0x7f110057
com.clipsy.android.debug:attr/queryBackground = 0x7f0403cb
com.clipsy.android.debug:dimen/material_clock_period_toggle_height = 0x7f07022f
com.clipsy.android.debug:dimen/mtrl_btn_corner_radius = 0x7f07025e
com.clipsy.android.debug:style/ThemeOverlay.Material3.TabLayout = 0x7f1202e2
com.clipsy.android.debug:attr/badgeHeight = 0x7f04005c
com.clipsy.android.debug:attr/quantizeMotionPhase = 0x7f0403c9
com.clipsy.android.debug:integer/app_bar_elevation_anim_duration = 0x7f0a0002
com.clipsy.android.debug:drawable/abc_btn_check_material = 0x7f08002b
com.clipsy.android.debug:attr/layout_constraintWidth_default = 0x7f0402c4
com.clipsy.android.debug:color/material_dynamic_tertiary40 = 0x7f06025a
com.clipsy.android.debug:attr/materialAlertDialogTitleTextStyle = 0x7f0402fd
com.clipsy.android.debug:attr/selectionRequired = 0x7f0403f5
com.clipsy.android.debug:attr/quantizeMotionInterpolator = 0x7f0403c8
com.clipsy.android.debug:dimen/m3_comp_navigation_bar_container_height = 0x7f070147
com.clipsy.android.debug:attr/animateRelativeTo = 0x7f04003c
com.clipsy.android.debug:id/textStart = 0x7f0901f8
com.clipsy.android.debug:attr/haloColor = 0x7f040231
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Button = 0x7f120227
com.clipsy.android.debug:attr/alertDialogStyle = 0x7f04002f
com.clipsy.android.debug:attr/yearTodayStyle = 0x7f040535
com.clipsy.android.debug:attr/titlePositionInterpolator = 0x7f0404e8
com.clipsy.android.debug:style/Widget.MaterialComponents.Chip.Action = 0x7f12042e
com.clipsy.android.debug:attr/progressBarPadding = 0x7f0403c6
com.clipsy.android.debug:attr/materialSearchViewToolbarStyle = 0x7f040321
com.clipsy.android.debug:attr/destination = 0x7f040187
com.clipsy.android.debug:color/material_grey_100 = 0x7f060262
com.clipsy.android.debug:attr/primaryActivityName = 0x7f0403c5
com.clipsy.android.debug:attr/pressedTranslationZ = 0x7f0403c4
com.clipsy.android.debug:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f07020f
com.clipsy.android.debug:style/Platform.MaterialComponents.Dialog = 0x7f120145
com.clipsy.android.debug:id/stop = 0x7f0901dd
com.clipsy.android.debug:attr/prefixTextAppearance = 0x7f0403c1
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f08000d
com.clipsy.android.debug:attr/prefixText = 0x7f0403c0
com.clipsy.android.debug:drawable/mtrl_switch_thumb_checked_pressed = 0x7f0800e3
com.clipsy.android.debug:dimen/material_clock_hand_padding = 0x7f07022c
com.clipsy.android.debug:macro/m3_comp_slider_disabled_active_track_color = 0x7f0d010d
com.clipsy.android.debug:attr/clickAction = 0x7f0400e4
com.clipsy.android.debug:attr/preferenceStyle = 0x7f0403be
com.clipsy.android.debug:attr/preferenceInformationStyle = 0x7f0403bc
com.clipsy.android.debug:attr/preferenceFragmentListStyle = 0x7f0403ba
com.clipsy.android.debug:layout/mtrl_picker_header_toggle = 0x7f0c0068
com.clipsy.android.debug:attr/preferenceFragmentCompatStyle = 0x7f0403b9
com.clipsy.android.debug:attr/motionDurationMedium1 = 0x7f04034c
com.clipsy.android.debug:attr/textAppearanceLabelMedium = 0x7f040498
com.clipsy.android.debug:attr/preferenceCategoryTitleTextColor = 0x7f0403b8
com.clipsy.android.debug:drawable/mtrl_checkbox_button = 0x7f0800ca
com.clipsy.android.debug:color/m3_timepicker_secondary_text_button_text_color = 0x7f060217
com.clipsy.android.debug:attr/popUpToSaveState = 0x7f0403b0
com.clipsy.android.debug:attr/cornerFamilyBottomLeft = 0x7f040157
com.clipsy.android.debug:attr/actionBarSplitStyle = 0x7f040007
com.clipsy.android.debug:string/m3_sys_motion_easing_standard_decelerate = 0x7f110069
com.clipsy.android.debug:id/material_minute_text_input = 0x7f090122
com.clipsy.android.debug:color/switch_thumb_normal_material_dark = 0x7f06030f
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f120301
com.clipsy.android.debug:id/disjoint = 0x7f0900af
com.clipsy.android.debug:attr/fabCustomSize = 0x7f0401ef
com.clipsy.android.debug:attr/polarRelativeTo = 0x7f0403ab
com.clipsy.android.debug:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0d00e5
com.clipsy.android.debug:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0700ea
com.clipsy.android.debug:attr/chipIconTint = 0x7f0400ce
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0600fa
com.clipsy.android.debug:attr/borderRoundPercent = 0x7f04007e
com.clipsy.android.debug:attr/subheaderInsetStart = 0x7f040449
com.clipsy.android.debug:style/Base.Widget.AppCompat.ButtonBar = 0x7f1200d7
com.clipsy.android.debug:anim/m3_side_sheet_enter_from_left = 0x7f010025
com.clipsy.android.debug:attr/placeholder_emptyVisibility = 0x7f0403aa
com.clipsy.android.debug:attr/placeholderTextColor = 0x7f0403a9
com.clipsy.android.debug:attr/placeholderText = 0x7f0403a7
com.clipsy.android.debug:dimen/notification_subtext_size = 0x7f07031c
com.clipsy.android.debug:dimen/design_fab_size_mini = 0x7f07007d
com.clipsy.android.debug:layout/abc_screen_simple_overlay_action_mode = 0x7f0c0016
com.clipsy.android.debug:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f07015b
com.clipsy.android.debug:attr/persistent = 0x7f0403a4
com.clipsy.android.debug:color/button_stroke_primary = 0x7f060032
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f120120
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f080011
com.clipsy.android.debug:attr/perpendicularPath_percent = 0x7f0403a3
com.clipsy.android.debug:attr/materialCardViewElevatedStyle = 0x7f040310
com.clipsy.android.debug:attr/behavior_skipCollapsed = 0x7f04007b
com.clipsy.android.debug:attr/percentWidth = 0x7f0403a0
com.clipsy.android.debug:attr/singleLineTitle = 0x7f040418
com.clipsy.android.debug:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f120429
com.clipsy.android.debug:integer/m3_sys_motion_duration_long2 = 0x7f0a0015
com.clipsy.android.debug:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f070207
com.clipsy.android.debug:string/mtrl_chip_close_icon_content_description = 0x7f11008c
com.clipsy.android.debug:attr/contrast = 0x7f040152
com.clipsy.android.debug:attr/path_percent = 0x7f04039e
com.clipsy.android.debug:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0d0052
com.clipsy.android.debug:attr/pathMotionArc = 0x7f04039d
com.clipsy.android.debug:attr/passwordToggleTint = 0x7f04039b
com.clipsy.android.debug:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0700d3
com.clipsy.android.debug:attr/tickRadiusInactive = 0x7f0404d9
com.clipsy.android.debug:attr/passwordToggleDrawable = 0x7f040399
com.clipsy.android.debug:attr/motionEffect_end = 0x7f040361
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0d00d4
com.clipsy.android.debug:attr/textLocale = 0x7f0404ba
com.clipsy.android.debug:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f1201af
com.clipsy.android.debug:attr/panelMenuListWidth = 0x7f040397
com.clipsy.android.debug:drawable/abc_text_select_handle_middle_mtrl = 0x7f08006f
com.clipsy.android.debug:attr/suffixText = 0x7f040451
com.clipsy.android.debug:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f12011c
com.clipsy.android.debug:attr/buttonPanelSideLayout = 0x7f04009e
com.clipsy.android.debug:macro/m3_comp_fab_surface_icon_color = 0x7f0d003f
com.clipsy.android.debug:attr/layout_optimizationLevel = 0x7f0402d5
com.clipsy.android.debug:attr/colorTertiaryFixed = 0x7f040137
com.clipsy.android.debug:attr/strokeColor = 0x7f040444
com.clipsy.android.debug:color/mtrl_scrim_color = 0x7f0602df
com.clipsy.android.debug:attr/paddingLeftSystemWindowInsets = 0x7f04038f
com.clipsy.android.debug:attr/textBackground = 0x7f0404a9
com.clipsy.android.debug:attr/paddingEnd = 0x7f04038e
com.clipsy.android.debug:attr/iconSpaceReserved = 0x7f04024c
com.clipsy.android.debug:attr/motionEffect_move = 0x7f040362
com.clipsy.android.debug:styleable/ViewStubCompat = 0x7f1300b6
com.clipsy.android.debug:attr/shapeAppearanceMediumComponent = 0x7f0403ff
com.clipsy.android.debug:style/Theme.Material3.Light.BottomSheetDialog = 0x7f12026a
com.clipsy.android.debug:attr/dividerThickness = 0x7f040198
com.clipsy.android.debug:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f120411
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f120074
com.clipsy.android.debug:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f08002d
com.clipsy.android.debug:layout/mtrl_picker_fullscreen = 0x7f0c0063
com.clipsy.android.debug:color/button_background_primary_selector = 0x7f06002e
com.clipsy.android.debug:dimen/m3_slider_thumb_elevation = 0x7f0701f0
com.clipsy.android.debug:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f12047f
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1203c2
com.clipsy.android.debug:attr/windowFixedHeightMajor = 0x7f04052c
com.clipsy.android.debug:attr/colorSurfaceVariant = 0x7f040133
com.clipsy.android.debug:color/m3_ref_palette_tertiary50 = 0x7f060159
com.clipsy.android.debug:attr/orderingFromXml = 0x7f040389
com.clipsy.android.debug:id/textinput_prefix_text = 0x7f090211
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f0601a8
com.clipsy.android.debug:macro/m3_comp_text_button_label_text_color = 0x7f0d0145
com.clipsy.android.debug:attr/circularflow_angles = 0x7f0400dd
com.clipsy.android.debug:macro/m3_comp_switch_selected_handle_color = 0x7f0d0125
com.clipsy.android.debug:attr/popupWindowStyle = 0x7f0403b4
com.clipsy.android.debug:attr/onHide = 0x7f040382
com.clipsy.android.debug:string/material_slider_range_end = 0x7f110076
com.clipsy.android.debug:attr/sideSheetModalStyle = 0x7f040411
com.clipsy.android.debug:attr/onCross = 0x7f040381
com.clipsy.android.debug:drawable/notification_tile_bg = 0x7f0800fa
com.clipsy.android.debug:attr/tabIndicator = 0x7f040466
com.clipsy.android.debug:color/m3_sys_color_dark_surface_dim = 0x7f060187
com.clipsy.android.debug:attr/offsetAlignmentMode = 0x7f040380
com.clipsy.android.debug:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f0a003f
com.clipsy.android.debug:dimen/mtrl_navigation_elevation = 0x7f0702ca
com.clipsy.android.debug:attr/numericModifiers = 0x7f04037f
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f12006e
com.clipsy.android.debug:attr/number = 0x7f04037e
com.clipsy.android.debug:attr/drawableBottomCompat = 0x7f04019e
com.clipsy.android.debug:attr/nullable = 0x7f04037d
com.clipsy.android.debug:dimen/m3_fab_corner_size = 0x7f0701bc
com.clipsy.android.debug:dimen/mtrl_low_ripple_focused_alpha = 0x7f0702c4
com.clipsy.android.debug:color/m3_sys_color_light_inverse_on_surface = 0x7f0601d6
com.clipsy.android.debug:color/abc_tint_switch_track = 0x7f060018
com.clipsy.android.debug:anim/abc_slide_out_bottom = 0x7f010008
com.clipsy.android.debug:id/legacy = 0x7f09010a
com.clipsy.android.debug:attr/layout_constraintGuide_begin = 0x7f0402a9
com.clipsy.android.debug:attr/navigationIconTint = 0x7f040375
com.clipsy.android.debug:attr/multiChoiceItemLayout = 0x7f040371
com.clipsy.android.debug:style/Widget.AppCompat.TextView = 0x7f120357
com.clipsy.android.debug:layout/abc_action_menu_item_layout = 0x7f0c0002
com.clipsy.android.debug:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f07010d
com.clipsy.android.debug:color/mtrl_card_view_ripple = 0x7f0602c3
com.clipsy.android.debug:attr/motion_postLayoutCollision = 0x7f04036e
com.clipsy.android.debug:color/material_personalized_color_on_tertiary = 0x7f060289
com.clipsy.android.debug:attr/searchIcon = 0x7f0403ea
com.clipsy.android.debug:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f1203a3
com.clipsy.android.debug:layout/support_simple_spinner_dropdown_item = 0x7f0c0086
com.clipsy.android.debug:attr/mock_diagonalsColor = 0x7f04033d
com.clipsy.android.debug:attr/motionStagger = 0x7f04036c
com.clipsy.android.debug:attr/hintTextColor = 0x7f040241
com.clipsy.android.debug:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f07019b
com.clipsy.android.debug:anim/linear_indeterminate_line1_head_interpolator = 0x7f01001d
com.clipsy.android.debug:color/mtrl_tabs_icon_color_selector_colored = 0x7f0602e6
com.clipsy.android.debug:attr/chipStartPadding = 0x7f0400d6
com.clipsy.android.debug:attr/trackHeight = 0x7f040502
com.clipsy.android.debug:styleable/CoordinatorLayout = 0x7f130031
com.clipsy.android.debug:id/sliding_pane_layout = 0x7f0901c4
com.clipsy.android.debug:attr/motionEffect_viewTransition = 0x7f040367
com.clipsy.android.debug:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f070107
com.clipsy.android.debug:attr/shapeAppearanceCornerMedium = 0x7f0403fc
com.clipsy.android.debug:attr/motionEffect_translationX = 0x7f040365
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0d016a
com.clipsy.android.debug:attr/motionEffect_start = 0x7f040363
com.clipsy.android.debug:id/META = 0x7f090005
com.clipsy.android.debug:attr/textAppearanceBody2 = 0x7f040485
com.clipsy.android.debug:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0d0106
com.clipsy.android.debug:attr/textAllCaps = 0x7f040483
com.clipsy.android.debug:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f07012b
com.clipsy.android.debug:dimen/cardview_default_elevation = 0x7f07005f
com.clipsy.android.debug:id/actionDown = 0x7f090030
com.clipsy.android.debug:attr/fabCradleRoundedCornerRadius = 0x7f0401ed
com.clipsy.android.debug:attr/isMaterial3Theme = 0x7f040264
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f080010
com.clipsy.android.debug:color/m3_elevated_chip_background_color = 0x7f0600a4
com.clipsy.android.debug:dimen/abc_dialog_list_padding_top_no_title = 0x7f070021
com.clipsy.android.debug:attr/data = 0x7f040176
com.clipsy.android.debug:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f070259
com.clipsy.android.debug:attr/motionEasingLinear = 0x7f04035a
com.clipsy.android.debug:style/Preference.Material = 0x7f12015d
com.clipsy.android.debug:id/visible = 0x7f090233
com.clipsy.android.debug:attr/titleMarginStart = 0x7f0404e5
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral95 = 0x7f0600cf
com.clipsy.android.debug:layout/mtrl_calendar_vertical = 0x7f0c005c
com.clipsy.android.debug:attr/flow_padding = 0x7f040215
com.clipsy.android.debug:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f070108
com.clipsy.android.debug:attr/triggerReceiver = 0x7f04050d
com.clipsy.android.debug:color/design_dark_default_color_background = 0x7f06004d
com.clipsy.android.debug:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f040357
com.clipsy.android.debug:id/linear = 0x7f09010d
com.clipsy.android.debug:attr/boxCornerRadiusBottomEnd = 0x7f04008a
com.clipsy.android.debug:attr/buttonBarPositiveButtonStyle = 0x7f040096
com.clipsy.android.debug:attr/motionEasingDecelerated = 0x7f040355
com.clipsy.android.debug:integer/design_snackbar_text_max_lines = 0x7f0a0007
com.clipsy.android.debug:drawable/abc_list_selector_disabled_holo_dark = 0x7f080054
com.clipsy.android.debug:styleable/SwitchCompat = 0x7f1300a3
com.clipsy.android.debug:attr/colorOnTertiaryContainer = 0x7f040118
com.clipsy.android.debug:attr/motionEasingAccelerated = 0x7f040354
com.clipsy.android.debug:drawable/ic_mtrl_chip_close_circle = 0x7f0800a7
com.clipsy.android.debug:attr/fontWeight = 0x7f040226
com.clipsy.android.debug:style/Widget.MaterialComponents.Button.TextButton = 0x7f120424
com.clipsy.android.debug:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1203db
com.clipsy.android.debug:attr/textInputOutlinedStyle = 0x7f0404b8
com.clipsy.android.debug:attr/drawerLayoutStyle = 0x7f0401a9
com.clipsy.android.debug:attr/motionDurationShort3 = 0x7f040352
com.clipsy.android.debug:drawable/avd_hide_password = 0x7f080077
com.clipsy.android.debug:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f070172
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f120278
com.clipsy.android.debug:style/Base.TextAppearance.MaterialComponents.Button = 0x7f120049
com.clipsy.android.debug:attr/layout_dodgeInsetEdges = 0x7f0402c8
com.clipsy.android.debug:style/Base.Widget.AppCompat.ProgressBar = 0x7f1200f1
com.clipsy.android.debug:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f070166
com.clipsy.android.debug:dimen/m3_btn_translation_z_base = 0x7f0700ee
com.clipsy.android.debug:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.clipsy.android.debug:attr/springStopThreshold = 0x7f04042c
com.clipsy.android.debug:attr/motionDurationLong4 = 0x7f04034b
com.clipsy.android.debug:color/abc_tint_spinner = 0x7f060017
com.clipsy.android.debug:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f07011e
com.clipsy.android.debug:attr/counterTextAppearance = 0x7f040165
com.clipsy.android.debug:dimen/tooltip_precise_anchor_threshold = 0x7f07032d
com.clipsy.android.debug:attr/motionDurationExtraLong4 = 0x7f040347
com.clipsy.android.debug:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f08002e
com.clipsy.android.debug:attr/motionDurationExtraLong3 = 0x7f040346
com.clipsy.android.debug:attr/alertDialogTheme = 0x7f040030
com.clipsy.android.debug:color/material_dynamic_primary99 = 0x7f060247
com.clipsy.android.debug:dimen/abc_cascading_menus_min_smallest_width = 0x7f070016
com.clipsy.android.debug:attr/motionDurationExtraLong1 = 0x7f040344
com.clipsy.android.debug:dimen/mtrl_navigation_rail_elevation = 0x7f0702d3
com.clipsy.android.debug:attr/customColorDrawableValue = 0x7f04016d
com.clipsy.android.debug:attr/mock_showLabel = 0x7f040342
com.clipsy.android.debug:attr/paddingBottomSystemWindowInsets = 0x7f04038d
com.clipsy.android.debug:attr/mock_labelColor = 0x7f040340
com.clipsy.android.debug:color/material_dynamic_neutral0 = 0x7f060221
com.clipsy.android.debug:id/tag_accessibility_pane_title = 0x7f0901e9
com.clipsy.android.debug:attr/startDestination = 0x7f040430
com.clipsy.android.debug:attr/itemShapeInsetEnd = 0x7f040279
com.clipsy.android.debug:attr/materialCalendarHeaderToggleButton = 0x7f04030a
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f120204
com.clipsy.android.debug:id/vertical_only = 0x7f09022c
com.clipsy.android.debug:attr/mock_labelBackgroundColor = 0x7f04033f
com.clipsy.android.debug:dimen/mtrl_calendar_days_of_week_height = 0x7f070281
com.clipsy.android.debug:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1203ae
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0601c2
com.clipsy.android.debug:dimen/m3_searchview_divider_size = 0x7f0701e6
com.clipsy.android.debug:color/m3_sys_color_dark_surface_container = 0x7f060182
com.clipsy.android.debug:attr/itemPaddingBottom = 0x7f040272
com.clipsy.android.debug:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f1201ab
com.clipsy.android.debug:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f08007f
com.clipsy.android.debug:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f120289
com.clipsy.android.debug:macro/m3_comp_assist_chip_container_shape = 0x7f0d0000
com.clipsy.android.debug:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0601c7
com.clipsy.android.debug:id/mtrl_internal_children_alpha_tag = 0x7f090141
com.clipsy.android.debug:anim/m3_motion_fade_enter = 0x7f010023
com.clipsy.android.debug:attr/minHeight = 0x7f040338
com.clipsy.android.debug:string/mtrl_checkbox_button_icon_path_checked = 0x7f110081
com.clipsy.android.debug:attr/imageZoom = 0x7f040257
com.clipsy.android.debug:color/design_default_color_on_background = 0x7f06005c
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0601aa
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f12002c
com.clipsy.android.debug:drawable/mtrl_checkbox_button_icon = 0x7f0800cc
com.clipsy.android.debug:attr/min = 0x7f040337
com.clipsy.android.debug:style/TextAppearance.Material3.BodyMedium = 0x7f120212
com.clipsy.android.debug:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.clipsy.android.debug:attr/menuGravity = 0x7f040334
com.clipsy.android.debug:attr/simpleItemSelectedColor = 0x7f040413
com.clipsy.android.debug:attr/badgeShapeAppearance = 0x7f04005e
com.clipsy.android.debug:dimen/abc_action_bar_stacked_tab_max_width = 0x7f07000a
com.clipsy.android.debug:attr/paddingTopSystemWindowInsets = 0x7f040394
com.clipsy.android.debug:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f08005d
com.clipsy.android.debug:attr/maxCharacterCount = 0x7f04032a
com.clipsy.android.debug:attr/centerIfNoTextEnabled = 0x7f0400b5
com.clipsy.android.debug:drawable/abc_btn_radio_material_anim = 0x7f080032
com.clipsy.android.debug:style/Widget.Material3.Button.IconButton.Filled = 0x7f12037e
com.clipsy.android.debug:attr/textAppearanceDisplayLarge = 0x7f04048b
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0d00b4
com.clipsy.android.debug:dimen/m3_comp_slider_inactive_track_height = 0x7f07018e
com.clipsy.android.debug:attr/wavePeriod = 0x7f040524
com.clipsy.android.debug:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f1201aa
com.clipsy.android.debug:layout/abc_screen_content_include = 0x7f0c0014
com.clipsy.android.debug:attr/maxButtonHeight = 0x7f040329
com.clipsy.android.debug:string/material_clock_toggle_content_description = 0x7f11006b
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_secondary = 0x7f06019c
com.clipsy.android.debug:attr/motionEffect_strict = 0x7f040364
com.clipsy.android.debug:attr/maxActionInlineWidth = 0x7f040328
com.clipsy.android.debug:animator/m3_chip_state_list_anim = 0x7f02000e
com.clipsy.android.debug:attr/maxAcceleration = 0x7f040327
com.clipsy.android.debug:styleable/ViewTransition = 0x7f1300b7
com.clipsy.android.debug:styleable/KeyCycle = 0x7f130049
com.clipsy.android.debug:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f070153
com.clipsy.android.debug:bool/abc_action_bar_embed_tabs = 0x7f050000
com.clipsy.android.debug:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1200e8
com.clipsy.android.debug:macro/m3_comp_filter_chip_container_shape = 0x7f0d0058
com.clipsy.android.debug:attr/materialTimePickerTitleStyle = 0x7f040326
com.clipsy.android.debug:attr/popUpToInclusive = 0x7f0403af
com.clipsy.android.debug:attr/collapsedSize = 0x7f0400f3
com.clipsy.android.debug:attr/autoCompleteTextViewStyle = 0x7f040047
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Small = 0x7f12002f
com.clipsy.android.debug:attr/materialTimePickerStyle = 0x7f040324
com.clipsy.android.debug:attr/materialSwitchStyle = 0x7f040322
com.clipsy.android.debug:attr/tabInlineLabel = 0x7f04046d
com.clipsy.android.debug:attr/materialSearchViewToolbarHeight = 0x7f040320
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0d0090
com.clipsy.android.debug:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f0600b4
com.clipsy.android.debug:attr/splitRatio = 0x7f040426
com.clipsy.android.debug:attr/materialSearchViewStyle = 0x7f04031f
com.clipsy.android.debug:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f070195
com.clipsy.android.debug:attr/materialSearchViewPrefixStyle = 0x7f04031e
com.clipsy.android.debug:dimen/mtrl_extended_fab_translation_z_base = 0x7f0702b8
com.clipsy.android.debug:color/material_dynamic_neutral40 = 0x7f060226
com.clipsy.android.debug:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0d00fc
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant80 = 0x7f060135
com.clipsy.android.debug:style/Widget.Material3.CompoundButton.CheckBox = 0x7f1203a2
com.clipsy.android.debug:attr/tabIndicatorColor = 0x7f040469
com.clipsy.android.debug:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f12008f
com.clipsy.android.debug:attr/materialClockStyle = 0x7f040315
com.clipsy.android.debug:attr/materialCardViewFilledStyle = 0x7f040311
com.clipsy.android.debug:attr/layout_constraintTop_creator = 0x7f0402bd
com.clipsy.android.debug:attr/colorErrorContainer = 0x7f040105
com.clipsy.android.debug:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f120052
com.clipsy.android.debug:macro/m3_comp_fab_primary_container_color = 0x7f0d0037
com.clipsy.android.debug:attr/barrierMargin = 0x7f04006f
com.clipsy.android.debug:color/dim_foreground_disabled_material_dark = 0x7f060071
com.clipsy.android.debug:color/abc_btn_colored_text_material = 0x7f060003
com.clipsy.android.debug:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.clipsy.android.debug:style/Widget.Material3.BottomSheet.DragHandle = 0x7f120377
com.clipsy.android.debug:attr/materialCalendarTheme = 0x7f04030e
com.clipsy.android.debug:attr/materialCalendarHeaderTitle = 0x7f040309
com.clipsy.android.debug:drawable/abc_action_bar_item_background_material = 0x7f080029
com.clipsy.android.debug:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1200f7
com.clipsy.android.debug:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0701cc
com.clipsy.android.debug:attr/materialCalendarHeaderDivider = 0x7f040306
com.clipsy.android.debug:style/TextAppearance.Design.Error = 0x7f1201f8
com.clipsy.android.debug:attr/materialCalendarDayOfWeekLabel = 0x7f040302
com.clipsy.android.debug:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f070006
com.clipsy.android.debug:attr/cornerFamily = 0x7f040156
com.clipsy.android.debug:anim/abc_popup_exit = 0x7f010004
com.clipsy.android.debug:attr/materialCalendarDay = 0x7f040301
com.clipsy.android.debug:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0601cf
com.clipsy.android.debug:attr/materialAlertDialogTitleIconStyle = 0x7f0402fb
com.clipsy.android.debug:string/mtrl_picker_range_header_only_end_selected = 0x7f1100a2
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0d00cf
com.clipsy.android.debug:layout/preference_information = 0x7f0c0079
com.clipsy.android.debug:attr/materialAlertDialogTheme = 0x7f0402fa
com.clipsy.android.debug:dimen/button_stroke_width = 0x7f07005c
com.clipsy.android.debug:id/auto = 0x7f090057
com.clipsy.android.debug:animator/fragment_close_enter = 0x7f020003
com.clipsy.android.debug:drawable/ic_phone = 0x7f0800a9
com.clipsy.android.debug:attr/tabBackground = 0x7f040461
com.clipsy.android.debug:style/Widget.MaterialComponents.ProgressIndicator = 0x7f120464
com.clipsy.android.debug:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f120114
com.clipsy.android.debug:attr/searchHintIcon = 0x7f0403e9
com.clipsy.android.debug:macro/m3_sys_color_dark_surface_tint = 0x7f0d0175
com.clipsy.android.debug:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f080025
com.clipsy.android.debug:dimen/mtrl_btn_icon_padding = 0x7f070266
com.clipsy.android.debug:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f070145
com.clipsy.android.debug:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0701af
com.clipsy.android.debug:dimen/design_fab_translation_z_hovered_focused = 0x7f07007f
com.clipsy.android.debug:attr/materialCalendarHeaderCancelButton = 0x7f040304
com.clipsy.android.debug:attr/marginTopSystemWindowInsets = 0x7f0402f7
com.clipsy.android.debug:attr/marginRightSystemWindowInsets = 0x7f0402f6
com.clipsy.android.debug:string/action_copy = 0x7f11001d
com.clipsy.android.debug:attr/marginLeftSystemWindowInsets = 0x7f0402f5
com.clipsy.android.debug:dimen/abc_dialog_padding_material = 0x7f070024
com.clipsy.android.debug:attr/selectableItemBackgroundBorderless = 0x7f0403f4
com.clipsy.android.debug:attr/thickness = 0x7f0404c5
com.clipsy.android.debug:color/m3_sys_color_light_surface_container_lowest = 0x7f0601f0
com.clipsy.android.debug:styleable/KeyAttribute = 0x7f130048
com.clipsy.android.debug:dimen/mtrl_btn_text_btn_padding_right = 0x7f070273
com.clipsy.android.debug:macro/m3_comp_time_picker_container_shape = 0x7f0d0150
com.clipsy.android.debug:attr/logoScaleType = 0x7f0402f3
com.clipsy.android.debug:color/secondary_text_disabled_material_dark = 0x7f060303
com.clipsy.android.debug:attr/listPreferredItemPaddingRight = 0x7f0402ee
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f120075
com.clipsy.android.debug:attr/toolbarNavigationButtonStyle = 0x7f0404ef
com.clipsy.android.debug:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f080058
com.clipsy.android.debug:attr/colorSurfaceContainerLowest = 0x7f040130
com.clipsy.android.debug:attr/listPreferredItemPaddingEnd = 0x7f0402ec
com.clipsy.android.debug:string/mtrl_timepicker_cancel = 0x7f1100bc
com.clipsy.android.debug:dimen/m3_comp_outlined_card_outline_width = 0x7f070161
com.clipsy.android.debug:dimen/m3_comp_filled_button_container_elevation = 0x7f07012f
com.clipsy.android.debug:string/mtrl_picker_text_input_year_abbr = 0x7f1100ae
com.clipsy.android.debug:attr/materialTimePickerTheme = 0x7f040325
com.clipsy.android.debug:attr/listMenuViewStyle = 0x7f0402e7
com.clipsy.android.debug:style/Base.Widget.Material3.ActionBar.Solid = 0x7f120101
com.clipsy.android.debug:drawable/abc_list_selector_background_transition_holo_light = 0x7f080053
com.clipsy.android.debug:attr/mock_label = 0x7f04033e
com.clipsy.android.debug:style/TextAppearance.AppCompat.Caption = 0x7f1201c4
com.clipsy.android.debug:attr/guidelineUseRtl = 0x7f040230
com.clipsy.android.debug:id/home = 0x7f0900ed
com.clipsy.android.debug:attr/listLayout = 0x7f0402e6
com.clipsy.android.debug:styleable/ChipGroup = 0x7f130023
com.clipsy.android.debug:attr/enableCopying = 0x7f0401b9
com.clipsy.android.debug:attr/listChoiceIndicatorMultipleAnimated = 0x7f0402e2
com.clipsy.android.debug:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f12047c
com.clipsy.android.debug:color/material_harmonized_color_on_error = 0x7f06026b
com.clipsy.android.debug:attr/colorOnContainer = 0x7f040107
com.clipsy.android.debug:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f12028d
com.clipsy.android.debug:attr/listChoiceBackgroundIndicator = 0x7f0402e1
com.clipsy.android.debug:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f07014e
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f120447
com.clipsy.android.debug:attr/linearProgressIndicatorStyle = 0x7f0402e0
com.clipsy.android.debug:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0d0032
com.clipsy.android.debug:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0700af
com.clipsy.android.debug:attr/limitBoundsTo = 0x7f0402dd
com.clipsy.android.debug:dimen/design_bottom_sheet_elevation = 0x7f070077
com.clipsy.android.debug:attr/state_indeterminate = 0x7f04043d
com.clipsy.android.debug:anim/abc_popup_enter = 0x7f010003
com.clipsy.android.debug:dimen/design_snackbar_padding_horizontal = 0x7f070091
com.clipsy.android.debug:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f120324
com.clipsy.android.debug:style/TextAppearance.Design.Placeholder = 0x7f1201fb
com.clipsy.android.debug:attr/layout_scrollInterpolator = 0x7f0402d8
com.clipsy.android.debug:attr/layout_marginBaseline = 0x7f0402d4
com.clipsy.android.debug:attr/layout_keyline = 0x7f0402d3
com.clipsy.android.debug:color/m3_navigation_bar_ripple_color_selector = 0x7f0600ae
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Display2 = 0x7f120020
com.clipsy.android.debug:string/mtrl_picker_invalid_range = 0x7f11009e
com.clipsy.android.debug:dimen/material_textinput_default_width = 0x7f070245
com.clipsy.android.debug:style/Widget.Material3.PopupMenu = 0x7f1203e4
com.clipsy.android.debug:attr/layout_goneMarginLeft = 0x7f0402ce
com.clipsy.android.debug:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f12043a
com.clipsy.android.debug:color/material_personalized_color_on_secondary_container = 0x7f060285
com.clipsy.android.debug:attr/framePosition = 0x7f04022b
com.clipsy.android.debug:attr/layout_goneMarginBottom = 0x7f0402cc
com.clipsy.android.debug:id/dimensions = 0x7f0900a9
com.clipsy.android.debug:attr/layout_editor_absoluteY = 0x7f0402ca
com.clipsy.android.debug:color/design_default_color_on_primary = 0x7f06005e
com.clipsy.android.debug:dimen/m3_btn_icon_only_default_size = 0x7f0700e0
com.clipsy.android.debug:attr/layout_constraintWidth_percent = 0x7f0402c7
com.clipsy.android.debug:attr/buttonStyleSmall = 0x7f0400a0
com.clipsy.android.debug:attr/layout_constraintVertical_bias = 0x7f0402c0
com.clipsy.android.debug:attr/font = 0x7f04021b
com.clipsy.android.debug:color/abc_search_url_text = 0x7f06000d
com.clipsy.android.debug:attr/textPanX = 0x7f0404bd
com.clipsy.android.debug:attr/materialCalendarStyle = 0x7f04030d
com.clipsy.android.debug:color/material_dynamic_secondary20 = 0x7f06024b
com.clipsy.android.debug:style/Widget.MaterialComponents.TimePicker.Button = 0x7f12047b
com.clipsy.android.debug:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0401e3
com.clipsy.android.debug:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0d00ac
com.clipsy.android.debug:attr/windowNoTitle = 0x7f040532
com.clipsy.android.debug:attr/errorAccessibilityLabel = 0x7f0401ca
com.clipsy.android.debug:dimen/design_snackbar_max_width = 0x7f07008f
com.clipsy.android.debug:attr/actionBarStyle = 0x7f040008
com.clipsy.android.debug:style/Widget.AppCompat.Toolbar = 0x7f120359
com.clipsy.android.debug:style/Theme.Material3.Light.Dialog = 0x7f12026b
com.clipsy.android.debug:attr/layout_constraintLeft_toLeftOf = 0x7f0402b5
com.clipsy.android.debug:color/m3_sys_color_dark_tertiary = 0x7f060189
com.clipsy.android.debug:color/background_floating_material_light = 0x7f06001f
com.clipsy.android.debug:attr/colorOnPrimaryFixed = 0x7f04010d
com.clipsy.android.debug:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1201f5
com.clipsy.android.debug:attr/entryValues = 0x7f0401c9
com.clipsy.android.debug:dimen/mtrl_badge_horizontal_edge_offset = 0x7f070250
com.clipsy.android.debug:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f07015c
com.clipsy.android.debug:dimen/button_padding_vertical = 0x7f07005a
com.clipsy.android.debug:styleable/PopupWindow = 0x7f130082
com.clipsy.android.debug:attr/layout_constraintHorizontal_chainStyle = 0x7f0402b2
com.clipsy.android.debug:attr/enterAnim = 0x7f0401c7
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f12017a
com.clipsy.android.debug:color/material_personalized_color_tertiary = 0x7f0602a0
com.clipsy.android.debug:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0702ad
com.clipsy.android.debug:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f120397
com.clipsy.android.debug:color/clipsy_text_secondary = 0x7f060049
com.clipsy.android.debug:dimen/m3_comp_elevated_card_icon_size = 0x7f070118
com.clipsy.android.debug:dimen/abc_text_size_display_2_material = 0x7f070044
com.clipsy.android.debug:color/m3_sys_color_dark_inverse_primary = 0x7f06016d
com.clipsy.android.debug:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0d0067
com.clipsy.android.debug:attr/layout_constraintHeight_default = 0x7f0402ad
com.clipsy.android.debug:attr/layout_constraintGuide_end = 0x7f0402aa
com.clipsy.android.debug:style/ThemeOverlay.AppCompat.Light = 0x7f1202a8
com.clipsy.android.debug:string/call_notification_screening_text = 0x7f110035
com.clipsy.android.debug:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f070199
com.clipsy.android.debug:dimen/mtrl_progress_circular_inset = 0x7f0702d9
com.clipsy.android.debug:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f12008b
com.clipsy.android.debug:color/background_material_dark = 0x7f060020
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_primary = 0x7f06019a
com.clipsy.android.debug:style/Theme.MaterialComponents.CompactMenu = 0x7f120274
com.clipsy.android.debug:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f080028
com.clipsy.android.debug:attr/contentInsetStart = 0x7f040148
com.clipsy.android.debug:attr/icon = 0x7f040247
com.clipsy.android.debug:drawable/mtrl_ic_indeterminate = 0x7f0800dd
com.clipsy.android.debug:attr/layout_constraintDimensionRatio = 0x7f0402a6
com.clipsy.android.debug:id/horizontal_only = 0x7f0900f0
com.clipsy.android.debug:drawable/ic_call_answer_video = 0x7f080091
com.clipsy.android.debug:attr/errorEnabled = 0x7f0401cd
com.clipsy.android.debug:color/material_dynamic_secondary30 = 0x7f06024c
com.clipsy.android.debug:styleable/MaterialRadioButton = 0x7f130061
com.clipsy.android.debug:attr/layout_goneMarginEnd = 0x7f0402cd
com.clipsy.android.debug:attr/layout_constraintBottom_toTopOf = 0x7f0402a2
com.clipsy.android.debug:attr/layout_constraintBottom_toBottomOf = 0x7f0402a1
com.clipsy.android.debug:macro/m3_comp_slider_label_label_text_color = 0x7f0d0113
com.clipsy.android.debug:anim/abc_tooltip_enter = 0x7f01000a
com.clipsy.android.debug:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f070152
com.clipsy.android.debug:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0d0076
com.clipsy.android.debug:attr/maxVelocity = 0x7f04032f
com.clipsy.android.debug:attr/applyMotionScene = 0x7f04003f
com.clipsy.android.debug:attr/motionDurationLong3 = 0x7f04034a
com.clipsy.android.debug:attr/chipStyle = 0x7f0400d9
com.clipsy.android.debug:id/mtrl_picker_text_input_date = 0x7f090148
com.clipsy.android.debug:attr/layout_constraintBaseline_creator = 0x7f04029c
com.clipsy.android.debug:attr/behavior_draggable = 0x7f040072
com.clipsy.android.debug:attr/flow_firstHorizontalBias = 0x7f040208
com.clipsy.android.debug:attr/buttonIconDimen = 0x7f04009b
com.clipsy.android.debug:layout/material_clock_display_divider = 0x7f0c0041
com.clipsy.android.debug:attr/layout_constrainedWidth = 0x7f04029b
com.clipsy.android.debug:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0d0158
com.clipsy.android.debug:id/grouping = 0x7f0900e6
com.clipsy.android.debug:dimen/mtrl_btn_dialog_btn_min_width = 0x7f07025f
com.clipsy.android.debug:color/m3_ref_palette_secondary90 = 0x7f060150
com.clipsy.android.debug:attr/action = 0x7f040002
com.clipsy.android.debug:attr/rippleColor = 0x7f0403df
com.clipsy.android.debug:attr/layout_collapseParallaxMultiplier = 0x7f040299
com.clipsy.android.debug:attr/contentInsetRight = 0x7f040147
com.clipsy.android.debug:attr/layout_behavior = 0x7f040297
com.clipsy.android.debug:attr/lineSpacing = 0x7f0402df
com.clipsy.android.debug:attr/layout_anchor = 0x7f040295
com.clipsy.android.debug:drawable/abc_tab_indicator_mtrl_alpha = 0x7f08006c
com.clipsy.android.debug:id/spread = 0x7f0901cf
com.clipsy.android.debug:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f070294
com.clipsy.android.debug:id/indicator_online = 0x7f0900fd
com.clipsy.android.debug:attr/layout_collapseMode = 0x7f040298
com.clipsy.android.debug:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f120134
com.clipsy.android.debug:id/dragAnticlockwise = 0x7f0900b0
com.clipsy.android.debug:drawable/mtrl_dialog_background = 0x7f0800d4
com.clipsy.android.debug:attr/layoutManager = 0x7f040294
com.clipsy.android.debug:attr/switchTextAppearance = 0x7f04045e
com.clipsy.android.debug:dimen/mtrl_shape_corner_size_small_component = 0x7f0702e9
com.clipsy.android.debug:layout/design_navigation_menu_item = 0x7f0c002b
com.clipsy.android.debug:drawable/abc_text_cursor_material = 0x7f08006d
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f06018c
com.clipsy.android.debug:interpolator/m3_sys_motion_easing_standard = 0x7f0b000b
com.clipsy.android.debug:attr/titleMarginTop = 0x7f0404e6
com.clipsy.android.debug:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f120064
com.clipsy.android.debug:attr/collapsingToolbarLayoutLargeStyle = 0x7f0400f8
com.clipsy.android.debug:style/Widget.Material3.Snackbar.TextView = 0x7f1203f7
com.clipsy.android.debug:bool/enable_system_alarm_service_default = 0x7f050003
com.clipsy.android.debug:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0702aa
com.clipsy.android.debug:attr/materialCardViewOutlinedStyle = 0x7f040312
com.clipsy.android.debug:id/enterAlways = 0x7f0900c7
com.clipsy.android.debug:dimen/abc_switch_padding = 0x7f07003e
com.clipsy.android.debug:color/material_dynamic_tertiary90 = 0x7f06025f
com.clipsy.android.debug:attr/layout_anchorGravity = 0x7f040296
com.clipsy.android.debug:attr/layout_constraintTop_toTopOf = 0x7f0402bf
com.clipsy.android.debug:attr/lastItemDecorated = 0x7f04028f
com.clipsy.android.debug:styleable/ThemeEnforcement = 0x7f1300ad
com.clipsy.android.debug:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f12023e
com.clipsy.android.debug:id/accessibility_custom_action_7 = 0x7f09002d
com.clipsy.android.debug:drawable/$m3_avd_hide_password__2 = 0x7f080008
com.clipsy.android.debug:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0701ab
com.clipsy.android.debug:color/material_personalized_color_on_error_container = 0x7f060281
com.clipsy.android.debug:color/material_personalized_color_surface = 0x7f060296
com.clipsy.android.debug:style/Base.Theme.AppCompat = 0x7f12004f
com.clipsy.android.debug:color/m3_ref_palette_neutral40 = 0x7f06011e
com.clipsy.android.debug:attr/counterOverflowTextColor = 0x7f040164
com.clipsy.android.debug:color/material_dynamic_neutral_variant100 = 0x7f060230
com.clipsy.android.debug:attr/addElevationShadow = 0x7f04002b
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f120028
com.clipsy.android.debug:attr/enabled = 0x7f0401bb
com.clipsy.android.debug:attr/borderlessButtonStyle = 0x7f040080
com.clipsy.android.debug:layout/abc_action_mode_bar = 0x7f0c0004
com.clipsy.android.debug:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f070209
com.clipsy.android.debug:color/m3_fab_ripple_color_selector = 0x7f0600a7
com.clipsy.android.debug:style/Widget.MaterialComponents.BottomAppBar = 0x7f120418
com.clipsy.android.debug:id/mtrl_picker_header = 0x7f090144
com.clipsy.android.debug:attr/flow_lastVerticalStyle = 0x7f040213
com.clipsy.android.debug:id/jumpToStart = 0x7f090104
com.clipsy.android.debug:attr/labelStyle = 0x7f04028b
com.clipsy.android.debug:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010020
com.clipsy.android.debug:attr/singleSelection = 0x7f040419
com.clipsy.android.debug:attr/lStar = 0x7f040289
com.clipsy.android.debug:attr/nestedScrollViewStyle = 0x7f04037b
com.clipsy.android.debug:color/abc_color_highlight_material = 0x7f060004
com.clipsy.android.debug:dimen/mtrl_calendar_header_text_padding = 0x7f070289
com.clipsy.android.debug:attr/overlay = 0x7f04038b
com.clipsy.android.debug:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f120094
com.clipsy.android.debug:attr/layout_constraintBaseline_toTopOf = 0x7f04029f
com.clipsy.android.debug:attr/textAppearanceHeadlineSmall = 0x7f040496
com.clipsy.android.debug:attr/errorIconDrawable = 0x7f0401ce
com.clipsy.android.debug:attr/keyPositionType = 0x7f040286
com.clipsy.android.debug:attr/itemSpacing = 0x7f04027c
com.clipsy.android.debug:color/m3_sys_color_light_tertiary_container = 0x7f0601f4
com.clipsy.android.debug:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f120018
com.clipsy.android.debug:attr/colorPrimarySurface = 0x7f040123
com.clipsy.android.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f120198
com.clipsy.android.debug:id/notification_background = 0x7f090162
com.clipsy.android.debug:attr/goIcon = 0x7f04022e
com.clipsy.android.debug:color/material_dynamic_secondary60 = 0x7f06024f
com.clipsy.android.debug:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1202d6
com.clipsy.android.debug:attr/order = 0x7f040388
com.clipsy.android.debug:attr/itemPadding = 0x7f040271
com.clipsy.android.debug:attr/colorOnPrimaryFixedVariant = 0x7f04010e
com.clipsy.android.debug:attr/itemMaxLines = 0x7f04026f
com.clipsy.android.debug:dimen/compat_notification_large_icon_max_width = 0x7f070068
com.clipsy.android.debug:layout/material_timepicker = 0x7f0c004a
com.clipsy.android.debug:attr/flow_verticalAlign = 0x7f040216
com.clipsy.android.debug:attr/preferenceFragmentStyle = 0x7f0403bb
com.clipsy.android.debug:attr/haloRadius = 0x7f040232
com.clipsy.android.debug:color/material_dynamic_tertiary20 = 0x7f060258
com.clipsy.android.debug:attr/colorPrimaryInverse = 0x7f040122
com.clipsy.android.debug:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f12045b
com.clipsy.android.debug:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1202d1
com.clipsy.android.debug:color/bright_foreground_material_dark = 0x7f06002a
com.clipsy.android.debug:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0702ac
com.clipsy.android.debug:dimen/mtrl_card_elevation = 0x7f0702a4
com.clipsy.android.debug:attr/insetForeground = 0x7f040261
com.clipsy.android.debug:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f07029a
com.clipsy.android.debug:dimen/material_clock_period_toggle_horizontal_gap = 0x7f070230
com.clipsy.android.debug:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f070113
com.clipsy.android.debug:macro/m3_comp_text_button_label_text_type = 0x7f0d0146
com.clipsy.android.debug:attr/layout_constraintEnd_toEndOf = 0x7f0402a7
com.clipsy.android.debug:attr/selectable = 0x7f0403f2
com.clipsy.android.debug:attr/layout_constraintCircleAngle = 0x7f0402a4
com.clipsy.android.debug:dimen/abc_text_size_button_material = 0x7f070041
com.clipsy.android.debug:attr/textAppearanceListItem = 0x7f04049c
com.clipsy.android.debug:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
com.clipsy.android.debug:attr/badgeGravity = 0x7f04005b
com.clipsy.android.debug:array/history_limit_entries = 0x7f030000
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0d007b
com.clipsy.android.debug:integer/m3_sys_motion_duration_extra_long1 = 0x7f0a0010
com.clipsy.android.debug:attr/circularflow_defaultAngle = 0x7f0400de
com.clipsy.android.debug:attr/indicatorDirectionLinear = 0x7f04025c
com.clipsy.android.debug:string/mtrl_picker_range_header_title = 0x7f1100a5
com.clipsy.android.debug:color/m3_ref_palette_secondary30 = 0x7f06014a
com.clipsy.android.debug:attr/hoveredFocusedTranslationZ = 0x7f040246
com.clipsy.android.debug:attr/ifTagNotSet = 0x7f040251
com.clipsy.android.debug:style/Preference.DropDown = 0x7f120159
com.clipsy.android.debug:attr/background = 0x7f040050
com.clipsy.android.debug:attr/iconTintMode = 0x7f04024f
com.clipsy.android.debug:string/m3_sys_motion_easing_standard_accelerate = 0x7f110068
com.clipsy.android.debug:attr/logo = 0x7f0402f0
com.clipsy.android.debug:attr/iconPadding = 0x7f04024a
com.clipsy.android.debug:attr/colorBackgroundFloating = 0x7f0400fe
com.clipsy.android.debug:attr/strokeWidth = 0x7f040445
com.clipsy.android.debug:string/device_disconnected = 0x7f110042
com.clipsy.android.debug:macro/m3_comp_time_picker_container_color = 0x7f0d014f
com.clipsy.android.debug:attr/horizontalOffset = 0x7f040244
com.clipsy.android.debug:attr/showDelay = 0x7f040408
com.clipsy.android.debug:dimen/m3_btn_max_width = 0x7f0700e4
com.clipsy.android.debug:attr/homeAsUpIndicator = 0x7f040242
com.clipsy.android.debug:attr/dayTodayStyle = 0x7f04017b
com.clipsy.android.debug:dimen/abc_list_item_height_small_material = 0x7f070032
com.clipsy.android.debug:attr/clearsTag = 0x7f0400e3
com.clipsy.android.debug:id/neverCompleteToEnd = 0x7f09015b
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant100 = 0x7f06012e
com.clipsy.android.debug:macro/m3_comp_navigation_bar_container_color = 0x7f0d006d
com.clipsy.android.debug:attr/hideMotionSpec = 0x7f04023a
com.clipsy.android.debug:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0701c1
com.clipsy.android.debug:attr/hideAnimationBehavior = 0x7f040239
com.clipsy.android.debug:attr/errorIconTintMode = 0x7f0401d0
com.clipsy.android.debug:drawable/ic_devices_vector = 0x7f08009d
com.clipsy.android.debug:dimen/m3_card_elevated_hovered_z = 0x7f0700f5
com.clipsy.android.debug:attr/flow_lastHorizontalBias = 0x7f040210
com.clipsy.android.debug:attr/materialIconButtonFilledTonalStyle = 0x7f04031a
com.clipsy.android.debug:attr/itemIconPadding = 0x7f04026c
com.clipsy.android.debug:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f07017b
com.clipsy.android.debug:dimen/m3_side_sheet_standard_elevation = 0x7f0701eb
com.clipsy.android.debug:attr/fontFamily = 0x7f04021c
com.clipsy.android.debug:dimen/m3_btn_icon_only_min_width = 0x7f0700e2
com.clipsy.android.debug:attr/graph = 0x7f04022f
com.clipsy.android.debug:attr/gapBetweenBars = 0x7f04022c
com.clipsy.android.debug:attr/drawerArrowStyle = 0x7f0401a7
com.clipsy.android.debug:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0d0159
com.clipsy.android.debug:drawable/abc_list_selector_disabled_holo_light = 0x7f080055
com.clipsy.android.debug:attr/cornerSize = 0x7f04015c
com.clipsy.android.debug:attr/cornerFamilyBottomRight = 0x7f040158
com.clipsy.android.debug:attr/actionModeSplitBackground = 0x7f04001e
com.clipsy.android.debug:attr/chipIconVisible = 0x7f0400cf
com.clipsy.android.debug:attr/forceDefaultNavigationOnClickListener = 0x7f040228
com.clipsy.android.debug:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
com.clipsy.android.debug:color/material_dynamic_neutral_variant80 = 0x7f060237
com.clipsy.android.debug:dimen/m3_badge_size = 0x7f0700c1
com.clipsy.android.debug:style/Widget.MaterialComponents.Slider = 0x7f120466
com.clipsy.android.debug:attr/textAppearanceHeadlineMedium = 0x7f040495
com.clipsy.android.debug:dimen/m3_btn_disabled_translation_z = 0x7f0700da
com.clipsy.android.debug:attr/actionBarItemBackground = 0x7f040004
com.clipsy.android.debug:interpolator/m3_sys_motion_easing_linear = 0x7f0b000a
com.clipsy.android.debug:attr/badgeText = 0x7f040061
com.clipsy.android.debug:style/Widget.MaterialComponents.CardView = 0x7f12042c
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0601bd
com.clipsy.android.debug:color/bright_foreground_disabled_material_light = 0x7f060027
com.clipsy.android.debug:attr/navigationIcon = 0x7f040374
com.clipsy.android.debug:attr/layout_constraintRight_toLeftOf = 0x7f0402b8
com.clipsy.android.debug:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0d00e1
com.clipsy.android.debug:attr/actionDropDownStyle = 0x7f04000f
com.clipsy.android.debug:attr/materialDividerStyle = 0x7f040318
com.clipsy.android.debug:attr/fontProviderSystemFontFamily = 0x7f040223
com.clipsy.android.debug:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f070276
com.clipsy.android.debug:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0601c8
com.clipsy.android.debug:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1202e5
com.clipsy.android.debug:id/layout = 0x7f090106
com.clipsy.android.debug:attr/customPixelDimension = 0x7f040173
com.clipsy.android.debug:attr/popExitAnim = 0x7f0403ad
com.clipsy.android.debug:attr/elevation = 0x7f0401b4
com.clipsy.android.debug:attr/lastBaselineToBottomHeight = 0x7f04028e
com.clipsy.android.debug:attr/backgroundInsetTop = 0x7f040055
com.clipsy.android.debug:dimen/m3_navigation_rail_item_padding_top = 0x7f0701d7
com.clipsy.android.debug:style/PreferenceFragment.Material = 0x7f120168
com.clipsy.android.debug:attr/chipBackgroundColor = 0x7f0400c7
com.clipsy.android.debug:anim/abc_slide_out_top = 0x7f010009
com.clipsy.android.debug:attr/dividerInsetEnd = 0x7f040195
com.clipsy.android.debug:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0701a9
com.clipsy.android.debug:color/dim_foreground_disabled_material_light = 0x7f060072
com.clipsy.android.debug:attr/closeIconSize = 0x7f0400ec
com.clipsy.android.debug:attr/dialogPreferenceStyle = 0x7f04018c
com.clipsy.android.debug:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f07017c
com.clipsy.android.debug:styleable/View = 0x7f1300b3
com.clipsy.android.debug:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0d00ff
com.clipsy.android.debug:id/switch_sync = 0x7f0901e4
com.clipsy.android.debug:attr/closeIconTint = 0x7f0400ee
com.clipsy.android.debug:attr/textAppearanceCaption = 0x7f04048a
com.clipsy.android.debug:color/m3_selection_control_ripple_color_selector = 0x7f060161
com.clipsy.android.debug:style/Widget.Material3.Button.TonalButton.Icon = 0x7f12038a
com.clipsy.android.debug:attr/chipIcon = 0x7f0400cb
com.clipsy.android.debug:attr/measureWithLargestChild = 0x7f040331
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0d0080
com.clipsy.android.debug:id/homeAsUp = 0x7f0900ee
com.clipsy.android.debug:attr/flow_verticalBias = 0x7f040217
com.clipsy.android.debug:attr/toolbarStyle = 0x7f0404f0
com.clipsy.android.debug:color/m3_sys_color_dark_on_primary = 0x7f060172
com.clipsy.android.debug:color/design_default_color_secondary_variant = 0x7f060065
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f120030
com.clipsy.android.debug:dimen/button_corner_radius = 0x7f070052
com.clipsy.android.debug:attr/motionProgress = 0x7f04036b
com.clipsy.android.debug:string/mtrl_checkbox_state_description_indeterminate = 0x7f11008a
com.clipsy.android.debug:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0701d8
com.clipsy.android.debug:integer/mtrl_card_anim_delay_ms = 0x7f0a0034
com.clipsy.android.debug:attr/flow_maxElementsWrap = 0x7f040214
com.clipsy.android.debug:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f120193
com.clipsy.android.debug:attr/windowFixedHeightMinor = 0x7f04052d
com.clipsy.android.debug:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1203da
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary0 = 0x7f0600e0
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f120200
com.clipsy.android.debug:attr/autoSizePresetSizes = 0x7f04004b
com.clipsy.android.debug:attr/flow_horizontalStyle = 0x7f04020f
com.clipsy.android.debug:attr/checkedIconTint = 0x7f0400c3
com.clipsy.android.debug:attr/flow_horizontalGap = 0x7f04020e
com.clipsy.android.debug:color/highlighted_text_material_light = 0x7f06007c
com.clipsy.android.debug:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f1201b7
com.clipsy.android.debug:attr/fabCradleMargin = 0x7f0401ec
com.clipsy.android.debug:color/m3_dark_primary_text_disable_only = 0x7f060096
com.clipsy.android.debug:attr/iconifiedByDefault = 0x7f040250
com.clipsy.android.debug:dimen/design_fab_elevation = 0x7f07007b
com.clipsy.android.debug:styleable/TextInputLayout = 0x7f1300ac
com.clipsy.android.debug:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0d0095
com.clipsy.android.debug:attr/flow_horizontalBias = 0x7f04020d
com.clipsy.android.debug:attr/fastScrollHorizontalThumbDrawable = 0x7f0401f2
com.clipsy.android.debug:dimen/mtrl_calendar_landscape_header_width = 0x7f07028c
com.clipsy.android.debug:id/dragEnd = 0x7f0900b3
com.clipsy.android.debug:attr/firstBaselineToTopHeight = 0x7f0401f8
com.clipsy.android.debug:attr/warmth = 0x7f040521
com.clipsy.android.debug:attr/singleLine = 0x7f040417
com.clipsy.android.debug:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.clipsy.android.debug:attr/flow_firstVerticalStyle = 0x7f04020b
com.clipsy.android.debug:attr/circularflow_viewCenter = 0x7f0400e1
com.clipsy.android.debug:attr/counterEnabled = 0x7f040161
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f12044f
com.clipsy.android.debug:id/selected = 0x7f0901b6
com.clipsy.android.debug:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0602a6
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1202f5
com.clipsy.android.debug:attr/helperTextEnabled = 0x7f040236
com.clipsy.android.debug:attr/floatingActionButtonStyle = 0x7f040205
com.clipsy.android.debug:id/progress_horizontal = 0x7f09018d
com.clipsy.android.debug:dimen/notification_small_icon_background_padding = 0x7f07031a
com.clipsy.android.debug:attr/floatingActionButtonSmallSurfaceStyle = 0x7f040203
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f12030d
com.clipsy.android.debug:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0401fd
com.clipsy.android.debug:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f070203
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f12027d
com.clipsy.android.debug:attr/tabIconTintMode = 0x7f040465
com.clipsy.android.debug:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f120299
com.clipsy.android.debug:attr/singleChoiceItemLayout = 0x7f040416
com.clipsy.android.debug:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0401fa
com.clipsy.android.debug:attr/indeterminateProgressStyle = 0x7f040259
com.clipsy.android.debug:drawable/mtrl_ic_arrow_drop_up = 0x7f0800d7
com.clipsy.android.debug:attr/actionModeCloseDrawable = 0x7f040016
com.clipsy.android.debug:attr/autoSizeMaxTextSize = 0x7f040049
com.clipsy.android.debug:attr/finishSecondaryWithPrimary = 0x7f0401f7
com.clipsy.android.debug:attr/flow_lastHorizontalStyle = 0x7f040211
com.clipsy.android.debug:attr/finishPrimaryWithSecondary = 0x7f0401f6
com.clipsy.android.debug:style/Preference.PreferenceScreen.Material = 0x7f12015f
com.clipsy.android.debug:dimen/mtrl_fab_min_touch_target = 0x7f0702bc
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f120038
com.clipsy.android.debug:attr/autoCompleteMode = 0x7f040046
com.clipsy.android.debug:style/Base.Widget.AppCompat.RatingBar = 0x7f1200f3
com.clipsy.android.debug:layout/abc_select_dialog_material = 0x7f0c001a
com.clipsy.android.debug:color/dim_foreground_material_dark = 0x7f060073
com.clipsy.android.debug:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1202e0
com.clipsy.android.debug:color/dim_foreground_material_light = 0x7f060074
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_surface = 0x7f0601bc
com.clipsy.android.debug:attr/indicatorSize = 0x7f04025e
com.clipsy.android.debug:color/primary_color = 0x7f0602f2
com.clipsy.android.debug:attr/constraintSetEnd = 0x7f04013d
com.clipsy.android.debug:attr/materialSearchBarStyle = 0x7f04031d
com.clipsy.android.debug:string/call_notification_decline_action = 0x7f110031
com.clipsy.android.debug:attr/fabCradleVerticalOffset = 0x7f0401ee
com.clipsy.android.debug:color/m3_sys_color_dark_on_primary_container = 0x7f060173
com.clipsy.android.debug:string/mtrl_picker_announce_current_selection_none = 0x7f110093
com.clipsy.android.debug:attr/fabAnchorMode = 0x7f0401ea
com.clipsy.android.debug:attr/textAppearanceHeadline4 = 0x7f040491
com.clipsy.android.debug:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f120061
com.clipsy.android.debug:color/abc_search_url_text_normal = 0x7f06000e
com.clipsy.android.debug:attr/extendedFloatingActionButtonStyle = 0x7f0401e4
com.clipsy.android.debug:dimen/mtrl_chip_pressed_translation_z = 0x7f0702a6
com.clipsy.android.debug:style/Theme.Material3.DayNight = 0x7f12025e
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f12003c
com.clipsy.android.debug:attr/shapeAppearanceSmallComponent = 0x7f040401
com.clipsy.android.debug:attr/layout_scrollEffect = 0x7f0402d6
com.clipsy.android.debug:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1203df
com.clipsy.android.debug:attr/itemBackground = 0x7f040268
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f080013
com.clipsy.android.debug:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f1201b3
com.clipsy.android.debug:drawable/design_snackbar_background = 0x7f08008a
com.clipsy.android.debug:attr/actionModeCopyDrawable = 0x7f040017
com.clipsy.android.debug:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0401e2
com.clipsy.android.debug:attr/textureEffect = 0x7f0404c1
com.clipsy.android.debug:layout/select_dialog_item_material = 0x7f0c0083
com.clipsy.android.debug:attr/switchTextOff = 0x7f04045f
com.clipsy.android.debug:attr/homeLayout = 0x7f040243
com.clipsy.android.debug:layout/design_text_input_end_icon = 0x7f0c002c
com.clipsy.android.debug:attr/layout_constraintVertical_weight = 0x7f0402c2
com.clipsy.android.debug:style/Widget.Material3.Chip.Assist.Elevated = 0x7f120391
com.clipsy.android.debug:string/m3_ref_typeface_plain_regular = 0x7f11005e
com.clipsy.android.debug:attr/expandedTitleTextAppearance = 0x7f0401de
com.clipsy.android.debug:dimen/mtrl_btn_padding_top = 0x7f07026d
com.clipsy.android.debug:attr/barrierDirection = 0x7f04006e
com.clipsy.android.debug:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f070213
com.clipsy.android.debug:attr/expandedTitleMarginStart = 0x7f0401dc
com.clipsy.android.debug:attr/theme = 0x7f0404c4
com.clipsy.android.debug:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f120363
com.clipsy.android.debug:color/m3_ref_palette_tertiary60 = 0x7f06015a
com.clipsy.android.debug:attr/expandedTitleMarginEnd = 0x7f0401db
com.clipsy.android.debug:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0701d1
com.clipsy.android.debug:style/Widget.Material3.Button.TextButton.Dialog = 0x7f120384
com.clipsy.android.debug:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0d0008
com.clipsy.android.debug:attr/widgetLayout = 0x7f040528
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f120205
com.clipsy.android.debug:attr/startIconDrawable = 0x7f040433
com.clipsy.android.debug:attr/materialThemeOverlay = 0x7f040323
com.clipsy.android.debug:color/preference_fallback_accent_color = 0x7f0602f1
com.clipsy.android.debug:string/material_minute_selection = 0x7f11006f
com.clipsy.android.debug:attr/expandedTitleMarginBottom = 0x7f0401da
com.clipsy.android.debug:dimen/mtrl_calendar_day_today_stroke = 0x7f07027e
com.clipsy.android.debug:color/m3_chip_assist_text_color = 0x7f06008d
com.clipsy.android.debug:color/m3_sys_color_secondary_fixed = 0x7f0601fd
com.clipsy.android.debug:attr/touchAnchorSide = 0x7f0404f8
com.clipsy.android.debug:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0d0147
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_primary_container = 0x7f06019b
com.clipsy.android.debug:attr/listItemLayout = 0x7f0402e5
com.clipsy.android.debug:attr/expandedHintEnabled = 0x7f0401d7
com.clipsy.android.debug:dimen/m3_bottom_nav_min_height = 0x7f0700cd
com.clipsy.android.debug:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0b0002
com.clipsy.android.debug:attr/scrimVisibleHeightTrigger = 0x7f0403e8
com.clipsy.android.debug:attr/expanded = 0x7f0401d6
com.clipsy.android.debug:attr/carousel_firstView = 0x7f0400ad
com.clipsy.android.debug:attr/tabPaddingStart = 0x7f040474
com.clipsy.android.debug:string/mtrl_picker_invalid_format = 0x7f11009b
com.clipsy.android.debug:attr/activeIndicatorLabelPadding = 0x7f040027
com.clipsy.android.debug:attr/errorIconTint = 0x7f0401cf
com.clipsy.android.debug:attr/swipeRefreshLayoutProgressSpinnerBackgroundColor = 0x7f040458
com.clipsy.android.debug:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0d00e0
com.clipsy.android.debug:attr/brightness = 0x7f040092
com.clipsy.android.debug:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f080020
com.clipsy.android.debug:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1202c7
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0d00bf
com.clipsy.android.debug:dimen/material_divider_thickness = 0x7f070236
com.clipsy.android.debug:attr/paddingTopNoTitle = 0x7f040393
com.clipsy.android.debug:dimen/preference_icon_minWidth = 0x7f070320
com.clipsy.android.debug:attr/fabAlignmentMode = 0x7f0401e8
com.clipsy.android.debug:style/TextAppearance.Material3.LabelMedium = 0x7f12021b
com.clipsy.android.debug:drawable/abc_ic_clear_material = 0x7f08003f
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Body2 = 0x7f12001c
com.clipsy.android.debug:attr/buttonIcon = 0x7f04009a
com.clipsy.android.debug:color/m3_sys_color_dark_error = 0x7f06016a
com.clipsy.android.debug:color/m3_sys_color_light_surface = 0x7f0601ea
com.clipsy.android.debug:attr/materialDisplayDividerStyle = 0x7f040316
com.clipsy.android.debug:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f12036c
com.clipsy.android.debug:attr/maxWidth = 0x7f040330
com.clipsy.android.debug:attr/errorAccessibilityLiveRegion = 0x7f0401cb
com.clipsy.android.debug:style/TextAppearance.AppCompat.Medium = 0x7f1201d1
com.clipsy.android.debug:color/m3_assist_chip_icon_tint_color = 0x7f06007e
com.clipsy.android.debug:style/Base.V21.Theme.AppCompat.Light = 0x7f1200a7
com.clipsy.android.debug:attr/fabAnimationMode = 0x7f0401eb
com.clipsy.android.debug:color/m3_sys_color_light_secondary = 0x7f0601e8
com.clipsy.android.debug:attr/ensureMinTouchTargetSize = 0x7f0401c6
com.clipsy.android.debug:layout/abc_activity_chooser_view = 0x7f0c0006
com.clipsy.android.debug:attr/endIconTintMode = 0x7f0401c3
com.clipsy.android.debug:attr/transitionPathRotate = 0x7f04050a
com.clipsy.android.debug:attr/endIconTint = 0x7f0401c2
com.clipsy.android.debug:color/bright_foreground_material_light = 0x7f06002b
com.clipsy.android.debug:attr/endIconScaleType = 0x7f0401c1
com.clipsy.android.debug:attr/checkMarkCompat = 0x7f0400b8
com.clipsy.android.debug:animator/design_appbar_state_list_animator = 0x7f020000
com.clipsy.android.debug:attr/endIconMode = 0x7f0401c0
com.clipsy.android.debug:id/row_index_key = 0x7f09019d
com.clipsy.android.debug:id/material_timepicker_view = 0x7f090129
com.clipsy.android.debug:dimen/m3_btn_stroke_size = 0x7f0700e9
com.clipsy.android.debug:attr/deltaPolarRadius = 0x7f040184
com.clipsy.android.debug:attr/endIconMinSize = 0x7f0401bf
com.clipsy.android.debug:attr/windowFixedWidthMinor = 0x7f04052f
com.clipsy.android.debug:macro/m3_comp_input_chip_container_shape = 0x7f0d005c
com.clipsy.android.debug:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f070173
com.clipsy.android.debug:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f120473
com.clipsy.android.debug:attr/tint = 0x7f0404db
com.clipsy.android.debug:attr/tabIndicatorHeight = 0x7f04046c
com.clipsy.android.debug:attr/itemShapeInsetStart = 0x7f04027a
com.clipsy.android.debug:anim/abc_fade_out = 0x7f010001
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0d00d3
com.clipsy.android.debug:attr/behavior_autoShrink = 0x7f040071
com.clipsy.android.debug:attr/fontProviderCerts = 0x7f04021e
com.clipsy.android.debug:attr/tabSelectedTextAppearance = 0x7f040478
com.clipsy.android.debug:attr/indicatorDirectionCircular = 0x7f04025b
com.clipsy.android.debug:attr/endIconDrawable = 0x7f0401be
com.clipsy.android.debug:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
com.clipsy.android.debug:attr/daySelectedStyle = 0x7f040179
com.clipsy.android.debug:attr/allowStacking = 0x7f040034
com.clipsy.android.debug:id/easeInOut = 0x7f0900ba
com.clipsy.android.debug:dimen/m3_navigation_rail_icon_size = 0x7f0701d0
com.clipsy.android.debug:dimen/disabled_alpha_material_light = 0x7f07009b
com.clipsy.android.debug:attr/title = 0x7f0404de
com.clipsy.android.debug:id/dragRight = 0x7f0900b5
com.clipsy.android.debug:id/direct = 0x7f0900aa
com.clipsy.android.debug:attr/actionBarTheme = 0x7f04000c
com.clipsy.android.debug:attr/summaryOn = 0x7f040457
com.clipsy.android.debug:attr/textFillColor = 0x7f0404b1
com.clipsy.android.debug:attr/fontProviderFetchTimeout = 0x7f040220
com.clipsy.android.debug:attr/snackbarStyle = 0x7f04041d
com.clipsy.android.debug:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f07023c
com.clipsy.android.debug:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f120338
com.clipsy.android.debug:attr/elevationOverlayAccentColor = 0x7f0401b5
com.clipsy.android.debug:dimen/m3_comp_fab_primary_container_elevation = 0x7f070122
com.clipsy.android.debug:animator/mtrl_btn_state_list_anim = 0x7f020015
com.clipsy.android.debug:id/recycler_view = 0x7f090192
com.clipsy.android.debug:attr/cornerSizeTopLeft = 0x7f04015f
com.clipsy.android.debug:color/m3_sys_color_dark_on_secondary_container = 0x7f060175
com.clipsy.android.debug:attr/editTextPreferenceStyle = 0x7f0401b2
com.clipsy.android.debug:attr/editTextBackground = 0x7f0401b0
com.clipsy.android.debug:attr/textInputStyle = 0x7f0404b9
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1202ff
com.clipsy.android.debug:attr/textAppearanceSearchResultTitle = 0x7f0404a2
com.clipsy.android.debug:style/PreferenceThemeOverlay = 0x7f12016c
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f08001f
com.clipsy.android.debug:attr/carousel_forwardTransition = 0x7f0400ae
com.clipsy.android.debug:attr/enforceTextAppearance = 0x7f0401c5
com.clipsy.android.debug:attr/dynamicColorThemeOverlay = 0x7f0401af
com.clipsy.android.debug:dimen/m3_bottomappbar_horizontal_padding = 0x7f0700d6
com.clipsy.android.debug:attr/layout_constraintRight_toRightOf = 0x7f0402b9
com.clipsy.android.debug:integer/m3_sys_motion_duration_extra_long2 = 0x7f0a0011
com.clipsy.android.debug:attr/maxNumber = 0x7f04032e
com.clipsy.android.debug:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0d013e
com.clipsy.android.debug:attr/duration = 0x7f0401ae
com.clipsy.android.debug:attr/customReference = 0x7f040174
com.clipsy.android.debug:animator/nav_default_exit_anim = 0x7f020023
com.clipsy.android.debug:color/m3_ref_palette_neutral95 = 0x7f060128
com.clipsy.android.debug:attr/layout_constraintCircle = 0x7f0402a3
com.clipsy.android.debug:attr/dividerColor = 0x7f040193
com.clipsy.android.debug:string/fab_transformation_sheet_behavior = 0x7f110050
com.clipsy.android.debug:attr/preferenceCategoryStyle = 0x7f0403b6
com.clipsy.android.debug:style/Widget.AppCompat.ImageButton = 0x7f12032b
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary10 = 0x7f0600ee
com.clipsy.android.debug:styleable/MaterialCardView = 0x7f13005d
com.clipsy.android.debug:attr/colorOnSurfaceVariant = 0x7f040116
com.clipsy.android.debug:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f12046d
com.clipsy.android.debug:attr/drawerLayoutCornerSize = 0x7f0401a8
com.clipsy.android.debug:color/m3_radiobutton_ripple_tint = 0x7f0600b9
com.clipsy.android.debug:attr/actionBarPopupTheme = 0x7f040005
com.clipsy.android.debug:attr/contentInsetEndWithActions = 0x7f040145
com.clipsy.android.debug:attr/drawableTopCompat = 0x7f0401a6
com.clipsy.android.debug:id/never = 0x7f09015a
com.clipsy.android.debug:dimen/mtrl_btn_max_width = 0x7f070269
com.clipsy.android.debug:attr/drawableStartCompat = 0x7f0401a3
com.clipsy.android.debug:styleable/FragmentContainerView = 0x7f130042
com.clipsy.android.debug:attr/dialogCornerRadius = 0x7f040188
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_container_shape = 0x7f0d000f
com.clipsy.android.debug:attr/dialogLayout = 0x7f04018a
com.clipsy.android.debug:color/m3_sys_color_light_surface_variant = 0x7f0601f2
com.clipsy.android.debug:color/m3_sys_color_light_primary = 0x7f0601e6
com.clipsy.android.debug:id/open_search_view_clear_button = 0x7f09016a
com.clipsy.android.debug:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0401e6
com.clipsy.android.debug:attr/errorContentDescription = 0x7f0401cc
com.clipsy.android.debug:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f1202b6
com.clipsy.android.debug:dimen/abc_config_prefDialogWidth = 0x7f070017
com.clipsy.android.debug:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f1202ae
com.clipsy.android.debug:attr/dragDirection = 0x7f04019a
com.clipsy.android.debug:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0800ea
com.clipsy.android.debug:attr/splitTrack = 0x7f040427
com.clipsy.android.debug:string/device_manual_name_hint = 0x7f110044
com.clipsy.android.debug:attr/itemTextAppearanceInactive = 0x7f040282
com.clipsy.android.debug:attr/displayOptions = 0x7f040191
com.clipsy.android.debug:attr/disableDependentsState = 0x7f040190
com.clipsy.android.debug:attr/materialCircleRadius = 0x7f040314
com.clipsy.android.debug:attr/itemShapeAppearance = 0x7f040275
com.clipsy.android.debug:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f120481
com.clipsy.android.debug:attr/dialogTitle = 0x7f04018f
com.clipsy.android.debug:attr/liftOnScroll = 0x7f0402da
com.clipsy.android.debug:styleable/SwitchPreference = 0x7f1300a5
com.clipsy.android.debug:color/m3_sys_color_light_outline_variant = 0x7f0601e5
com.clipsy.android.debug:attr/dialogPreferredPadding = 0x7f04018d
com.clipsy.android.debug:id/design_menu_item_action_area_stub = 0x7f0900a5
com.clipsy.android.debug:attr/enforceMaterialTheme = 0x7f0401c4
com.clipsy.android.debug:dimen/design_bottom_navigation_shadow_height = 0x7f070075
com.clipsy.android.debug:attr/checkMarkTintMode = 0x7f0400ba
com.clipsy.android.debug:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0d0131
com.clipsy.android.debug:attr/dialogMessage = 0x7f04018b
com.clipsy.android.debug:id/open_search_view_content_container = 0x7f09016b
com.clipsy.android.debug:dimen/abc_button_padding_horizontal_material = 0x7f070014
com.clipsy.android.debug:style/Widget.Material3.CompoundButton.RadioButton = 0x7f1203a4
com.clipsy.android.debug:color/material_personalized_color_control_normal = 0x7f06027c
com.clipsy.android.debug:attr/argType = 0x7f040041
com.clipsy.android.debug:dimen/mtrl_min_touch_target_size = 0x7f0702c7
com.clipsy.android.debug:attr/drawableSize = 0x7f0401a2
com.clipsy.android.debug:string/nav_history = 0x7f1100c1
com.clipsy.android.debug:attr/actionModeShareDrawable = 0x7f04001d
com.clipsy.android.debug:drawable/mtrl_switch_thumb_pressed = 0x7f0800e5
com.clipsy.android.debug:dimen/material_helper_text_font_1_3_padding_top = 0x7f070243
com.clipsy.android.debug:string/pref_websocket_port_summary = 0x7f1100dd
com.clipsy.android.debug:id/floating = 0x7f0900da
com.clipsy.android.debug:color/m3_dark_default_color_primary_text = 0x7f060092
com.clipsy.android.debug:attr/deltaPolarAngle = 0x7f040183
com.clipsy.android.debug:style/Widget.AppCompat.SearchView = 0x7f12034f
com.clipsy.android.debug:color/m3_sys_color_tertiary_fixed = 0x7f0601ff
com.clipsy.android.debug:attr/paddingStart = 0x7f040391
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f06018e
com.clipsy.android.debug:color/m3_ref_palette_primary60 = 0x7f060140
com.clipsy.android.debug:attr/helperText = 0x7f040235
com.clipsy.android.debug:dimen/mtrl_fab_translation_z_pressed = 0x7f0702be
com.clipsy.android.debug:attr/motionTarget = 0x7f04036d
com.clipsy.android.debug:integer/m3_chip_anim_duration = 0x7f0a000f
com.clipsy.android.debug:color/material_dynamic_tertiary70 = 0x7f06025d
com.clipsy.android.debug:attr/badgeStyle = 0x7f040060
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f120192
com.clipsy.android.debug:layout/mtrl_alert_dialog_actions = 0x7f0c004e
com.clipsy.android.debug:dimen/preferences_detail_width = 0x7f070324
com.clipsy.android.debug:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0d00cb
com.clipsy.android.debug:attr/defaultQueryHint = 0x7f04017f
com.clipsy.android.debug:xml/data_extraction_rules = 0x7f140001
com.clipsy.android.debug:id/material_timepicker_cancel_button = 0x7f090125
com.clipsy.android.debug:attr/trackTint = 0x7f040504
com.clipsy.android.debug:id/tag_on_receive_content_listener = 0x7f0901eb
com.clipsy.android.debug:id/snackbar_text = 0x7f0901c6
com.clipsy.android.debug:attr/layout_constraintHeight_min = 0x7f0402af
com.clipsy.android.debug:attr/defaultMarginsEnabled = 0x7f04017d
com.clipsy.android.debug:attr/elevationOverlayColor = 0x7f0401b6
com.clipsy.android.debug:attr/imageButtonStyle = 0x7f040253
com.clipsy.android.debug:id/noScroll = 0x7f09015d
com.clipsy.android.debug:id/chains = 0x7f090081
com.clipsy.android.debug:attr/colorOutline = 0x7f04011b
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f060191
com.clipsy.android.debug:attr/dayStyle = 0x7f04017a
com.clipsy.android.debug:attr/dayInvalidStyle = 0x7f040178
com.clipsy.android.debug:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0701d2
com.clipsy.android.debug:attr/dropdownPreferenceStyle = 0x7f0401ad
com.clipsy.android.debug:attr/currentState = 0x7f040168
com.clipsy.android.debug:id/edit_text_id = 0x7f0900bf
com.clipsy.android.debug:dimen/hint_pressed_alpha_material_light = 0x7f0700a5
com.clipsy.android.debug:attr/motionDurationLong2 = 0x7f040349
com.clipsy.android.debug:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f120059
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral92 = 0x7f0600cd
com.clipsy.android.debug:attr/customNavigationLayout = 0x7f040172
com.clipsy.android.debug:attr/contentDescription = 0x7f040143
com.clipsy.android.debug:attr/deriveConstraintsFrom = 0x7f040186
com.clipsy.android.debug:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0702b0
com.clipsy.android.debug:styleable/DrawerLayout = 0x7f130037
com.clipsy.android.debug:attr/extendMotionSpec = 0x7f0401e0
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Overline = 0x7f120230
com.clipsy.android.debug:string/material_clock_display_divider = 0x7f11006a
com.clipsy.android.debug:attr/customFloatValue = 0x7f040170
com.clipsy.android.debug:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f120097
com.clipsy.android.debug:attr/customDimension = 0x7f04016f
com.clipsy.android.debug:attr/dividerVertical = 0x7f040199
com.clipsy.android.debug:attr/customBoolean = 0x7f04016c
com.clipsy.android.debug:attr/drawableTint = 0x7f0401a4
com.clipsy.android.debug:attr/constraintSetStart = 0x7f04013e
com.clipsy.android.debug:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0700c8
com.clipsy.android.debug:attr/counterTextColor = 0x7f040166
com.clipsy.android.debug:attr/motionDurationMedium3 = 0x7f04034e
com.clipsy.android.debug:attr/state_error = 0x7f04043c
com.clipsy.android.debug:color/m3_sys_color_dark_outline = 0x7f06017a
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f120449
com.clipsy.android.debug:attr/counterOverflowTextAppearance = 0x7f040163
com.clipsy.android.debug:styleable/SwitchPreferenceCompat = 0x7f1300a6
com.clipsy.android.debug:style/Theme.Material3.Dark.SideSheetDialog = 0x7f12025d
com.clipsy.android.debug:attr/chipSurfaceColor = 0x7f0400da
com.clipsy.android.debug:attr/layout_constraintRight_creator = 0x7f0402b7
com.clipsy.android.debug:color/material_personalized_color_surface_variant = 0x7f06029f
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0d0013
com.clipsy.android.debug:attr/badgeVerticalPadding = 0x7f040064
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0601b3
com.clipsy.android.debug:attr/coplanarSiblingViewId = 0x7f040155
com.clipsy.android.debug:color/material_slider_halo_color = 0x7f0602ad
com.clipsy.android.debug:attr/fontStyle = 0x7f040224
com.clipsy.android.debug:dimen/material_clock_display_width = 0x7f070229
com.clipsy.android.debug:animator/m3_btn_state_list_anim = 0x7f02000b
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Medium = 0x7f120029
com.clipsy.android.debug:attr/bottomSheetDialogTheme = 0x7f040084
com.clipsy.android.debug:color/abc_tint_default = 0x7f060014
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f120173
com.clipsy.android.debug:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f070200
com.clipsy.android.debug:attr/contentPaddingTop = 0x7f040150
com.clipsy.android.debug:macro/m3_comp_elevated_card_container_color = 0x7f0d002b
com.clipsy.android.debug:attr/contentPaddingStart = 0x7f04014f
com.clipsy.android.debug:color/design_dark_default_color_primary = 0x7f060054
com.clipsy.android.debug:attr/isLightTheme = 0x7f040262
com.clipsy.android.debug:attr/listPreferredItemPaddingLeft = 0x7f0402ed
com.clipsy.android.debug:attr/carousel_backwardTransition = 0x7f0400ab
com.clipsy.android.debug:dimen/mtrl_bottomappbar_height = 0x7f07025d
com.clipsy.android.debug:attr/chipMinHeight = 0x7f0400d0
com.clipsy.android.debug:id/unchecked = 0x7f090227
com.clipsy.android.debug:attr/switchTextOn = 0x7f040460
com.clipsy.android.debug:attr/chainUseRtl = 0x7f0400b6
com.clipsy.android.debug:style/Widget.MaterialComponents.ActionMode = 0x7f12040f
com.clipsy.android.debug:bool/enable_system_job_service_default = 0x7f050005
com.clipsy.android.debug:attr/tabPaddingBottom = 0x7f040472
com.clipsy.android.debug:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f120185
com.clipsy.android.debug:id/parent_matrix = 0x7f09017e
com.clipsy.android.debug:attr/boxCornerRadiusTopStart = 0x7f04008d
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f120207
com.clipsy.android.debug:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0d015c
com.clipsy.android.debug:attr/layout_constraintTop_toBottomOf = 0x7f0402be
com.clipsy.android.debug:dimen/preference_seekbar_padding_horizontal = 0x7f070321
com.clipsy.android.debug:attr/nestedScrollFlags = 0x7f04037a
com.clipsy.android.debug:attr/constraintSet = 0x7f04013c
com.clipsy.android.debug:attr/colorSwitchThumbNormal = 0x7f040134
com.clipsy.android.debug:attr/paddingBottomNoButtons = 0x7f04038c
com.clipsy.android.debug:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0601ca
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0600d3
com.clipsy.android.debug:id/password_toggle = 0x7f09017f
com.clipsy.android.debug:attr/colorSurfaceContainerLow = 0x7f04012f
com.clipsy.android.debug:attr/flow_wrapMode = 0x7f04021a
com.clipsy.android.debug:id/embed = 0x7f0900c4
com.clipsy.android.debug:attr/colorSurfaceContainerHighest = 0x7f04012e
com.clipsy.android.debug:id/decelerate = 0x7f09009d
com.clipsy.android.debug:drawable/notification_template_icon_low_bg = 0x7f0800f9
com.clipsy.android.debug:attr/colorSurfaceContainerHigh = 0x7f04012d
com.clipsy.android.debug:style/Base.V21.Theme.MaterialComponents = 0x7f1200a9
com.clipsy.android.debug:layout/abc_alert_dialog_material = 0x7f0c0009
com.clipsy.android.debug:attr/closeIconEnabled = 0x7f0400ea
com.clipsy.android.debug:attr/defaultScrollFlagsEnabled = 0x7f040180
com.clipsy.android.debug:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f12010e
com.clipsy.android.debug:color/material_personalized_color_primary_text_inverse = 0x7f060291
com.clipsy.android.debug:attr/queryPatterns = 0x7f0403cd
com.clipsy.android.debug:integer/m3_sys_motion_duration_short1 = 0x7f0a001c
com.clipsy.android.debug:attr/allowDividerBelow = 0x7f040033
com.clipsy.android.debug:attr/badgeWithTextRadius = 0x7f040068
com.clipsy.android.debug:styleable/NavGraphNavigator = 0x7f130077
com.clipsy.android.debug:dimen/material_time_picker_minimum_screen_height = 0x7f070248
com.clipsy.android.debug:attr/imagePanX = 0x7f040254
com.clipsy.android.debug:styleable/MenuItem = 0x7f130069
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_background = 0x7f0601a9
com.clipsy.android.debug:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1202dc
com.clipsy.android.debug:color/button_text_primary = 0x7f060035
com.clipsy.android.debug:attr/materialAlertDialogTitlePanelStyle = 0x7f0402fc
com.clipsy.android.debug:attr/colorOnPrimarySurface = 0x7f04010f
com.clipsy.android.debug:color/m3_fab_efab_foreground_color_selector = 0x7f0600a6
com.clipsy.android.debug:attr/motionEasingStandardDecelerateInterpolator = 0x7f04035e
com.clipsy.android.debug:attr/colorSecondaryFixedDim = 0x7f040128
com.clipsy.android.debug:dimen/m3_carousel_gone_size = 0x7f0700fb
com.clipsy.android.debug:attr/materialCalendarMonth = 0x7f04030b
com.clipsy.android.debug:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f120425
com.clipsy.android.debug:attr/suffixTextAppearance = 0x7f040452
com.clipsy.android.debug:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1200fd
com.clipsy.android.debug:attr/colorSecondaryFixed = 0x7f040127
com.clipsy.android.debug:color/button_stroke_secondary = 0x7f060033
com.clipsy.android.debug:style/ThemeOverlay.AppCompat.DayNight = 0x7f1202a4
com.clipsy.android.debug:color/m3_ref_palette_neutral80 = 0x7f060123
com.clipsy.android.debug:attr/dataPattern = 0x7f040177
com.clipsy.android.debug:attr/colorSecondaryContainer = 0x7f040126
com.clipsy.android.debug:color/material_dynamic_neutral_variant10 = 0x7f06022f
com.clipsy.android.debug:attr/colorPrimaryVariant = 0x7f040124
com.clipsy.android.debug:dimen/mtrl_btn_focused_z = 0x7f070263
com.clipsy.android.debug:attr/colorPrimaryFixed = 0x7f040120
com.clipsy.android.debug:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1200b8
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0600dd
com.clipsy.android.debug:attr/backgroundInsetEnd = 0x7f040053
com.clipsy.android.debug:id/accessibility_custom_action_14 = 0x7f090016
com.clipsy.android.debug:attr/textAppearanceBodySmall = 0x7f040488
com.clipsy.android.debug:attr/flow_verticalGap = 0x7f040218
com.clipsy.android.debug:attr/colorSecondary = 0x7f040125
com.clipsy.android.debug:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f07011f
com.clipsy.android.debug:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
com.clipsy.android.debug:anim/linear_indeterminate_line1_tail_interpolator = 0x7f01001e
com.clipsy.android.debug:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f070121
com.clipsy.android.debug:color/material_personalized_primary_text_disable_only = 0x7f0602aa
com.clipsy.android.debug:attr/listPreferredItemHeight = 0x7f0402e9
com.clipsy.android.debug:color/switch_thumb_disabled_material_dark = 0x7f06030b
com.clipsy.android.debug:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.clipsy.android.debug:macro/m3_comp_time_picker_clock_dial_color = 0x7f0d014d
com.clipsy.android.debug:attr/checkboxStyle = 0x7f0400bb
com.clipsy.android.debug:string/material_motion_easing_linear = 0x7f110074
com.clipsy.android.debug:attr/tabIndicatorAnimationMode = 0x7f040468
com.clipsy.android.debug:attr/colorOnSecondaryFixed = 0x7f040112
com.clipsy.android.debug:color/mtrl_btn_text_btn_ripple_color = 0x7f0602bc
com.clipsy.android.debug:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1200a6
com.clipsy.android.debug:attr/boxStrokeColor = 0x7f04008e
com.clipsy.android.debug:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0d0050
com.clipsy.android.debug:attr/colorOnSecondaryContainer = 0x7f040111
com.clipsy.android.debug:dimen/m3_comp_search_bar_container_height = 0x7f07017a
com.clipsy.android.debug:attr/compatShadowEnabled = 0x7f04013a
com.clipsy.android.debug:id/chronometer = 0x7f090084
com.clipsy.android.debug:attr/colorOnPrimary = 0x7f04010b
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f120201
com.clipsy.android.debug:string/settings_device_name_summary = 0x7f1100ea
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0d016b
com.clipsy.android.debug:anim/abc_tooltip_exit = 0x7f01000b
com.clipsy.android.debug:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1203e8
com.clipsy.android.debug:attr/maxLines = 0x7f04032d
com.clipsy.android.debug:dimen/notification_right_icon_size = 0x7f070318
com.clipsy.android.debug:layout/material_radial_view_group = 0x7f0c0046
com.clipsy.android.debug:dimen/design_snackbar_padding_vertical = 0x7f070092
com.clipsy.android.debug:attr/circularflow_radiusInDP = 0x7f0400e0
com.clipsy.android.debug:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f1203bd
com.clipsy.android.debug:attr/editTextStyle = 0x7f0401b3
com.clipsy.android.debug:attr/itemStrokeColor = 0x7f04027d
com.clipsy.android.debug:string/call_notification_ongoing_text = 0x7f110034
com.clipsy.android.debug:attr/allowDividerAfterLastItem = 0x7f040032
com.clipsy.android.debug:color/material_personalized_color_on_surface_variant = 0x7f060288
com.clipsy.android.debug:attr/buttonBarStyle = 0x7f040097
com.clipsy.android.debug:attr/textAppearanceHeadline6 = 0x7f040493
com.clipsy.android.debug:attr/hideOnContentScroll = 0x7f04023c
com.clipsy.android.debug:dimen/abc_action_bar_content_inset_material = 0x7f070000
com.clipsy.android.debug:color/medium_contrast_background = 0x7f0602b6
com.clipsy.android.debug:drawable/mtrl_popupmenu_background = 0x7f0800df
com.clipsy.android.debug:attr/backgroundSplit = 0x7f040057
com.clipsy.android.debug:attr/floatingActionButtonSecondaryStyle = 0x7f0401ff
com.clipsy.android.debug:drawable/ic_keyboard_black_24dp = 0x7f0800a0
com.clipsy.android.debug:attr/checkedIcon = 0x7f0400be
com.clipsy.android.debug:color/mtrl_switch_thumb_tint = 0x7f0602e1
com.clipsy.android.debug:attr/collapsingToolbarLayoutMediumSize = 0x7f0400f9
com.clipsy.android.debug:anim/design_snackbar_in = 0x7f01001a
com.clipsy.android.debug:id/spline = 0x7f0901cd
com.clipsy.android.debug:anim/m3_side_sheet_exit_to_left = 0x7f010027
com.clipsy.android.debug:macro/m3_comp_search_view_header_input_text_color = 0x7f0d00f6
com.clipsy.android.debug:attr/collapsingToolbarLayoutLargeSize = 0x7f0400f7
com.clipsy.android.debug:color/m3_tabs_ripple_color = 0x7f060203
com.clipsy.android.debug:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f120068
com.clipsy.android.debug:color/m3_simple_item_ripple_color = 0x7f060162
com.clipsy.android.debug:attr/motionDurationMedium2 = 0x7f04034d
com.clipsy.android.debug:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f07025c
com.clipsy.android.debug:attr/collapsedTitleTextColor = 0x7f0400f6
com.clipsy.android.debug:string/material_hour_24h_suffix = 0x7f11006c
com.clipsy.android.debug:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f07018b
com.clipsy.android.debug:dimen/abc_text_size_display_4_material = 0x7f070046
com.clipsy.android.debug:attr/collapsedTitleGravity = 0x7f0400f4
com.clipsy.android.debug:color/m3_chip_stroke_color = 0x7f060090
com.clipsy.android.debug:attr/collapseContentDescription = 0x7f0400f1
com.clipsy.android.debug:attr/autoSizeMinTextSize = 0x7f04004a
com.clipsy.android.debug:string/share = 0x7f1100f4
com.clipsy.android.debug:attr/closeIconVisible = 0x7f0400ef
com.clipsy.android.debug:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f120132
com.clipsy.android.debug:layout/preference = 0x7f0c0073
com.clipsy.android.debug:attr/motionDurationShort1 = 0x7f040350
com.clipsy.android.debug:dimen/mtrl_btn_padding_bottom = 0x7f07026a
com.clipsy.android.debug:attr/textInputOutlinedDenseStyle = 0x7f0404b6
com.clipsy.android.debug:color/mtrl_btn_text_btn_bg_color_selector = 0x7f0602bb
com.clipsy.android.debug:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
com.clipsy.android.debug:color/design_default_color_on_secondary = 0x7f06005f
com.clipsy.android.debug:drawable/ic_clipsy_status = 0x7f08009a
com.clipsy.android.debug:color/design_default_color_primary = 0x7f060061
com.clipsy.android.debug:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f1202bb
com.clipsy.android.debug:string/history_clear = 0x7f110052
com.clipsy.android.debug:dimen/m3_comp_circular_progress_indicator_active_indicator_width = 0x7f070110
com.clipsy.android.debug:dimen/m3_btn_icon_btn_padding_right = 0x7f0700de
com.clipsy.android.debug:attr/commitIcon = 0x7f040139
com.clipsy.android.debug:attr/colorOnSecondary = 0x7f040110
com.clipsy.android.debug:attr/actionMenuTextAppearance = 0x7f040011
com.clipsy.android.debug:attr/badgeWithTextWidth = 0x7f04006b
com.clipsy.android.debug:attr/clockIcon = 0x7f0400e7
com.clipsy.android.debug:anim/design_bottom_sheet_slide_out = 0x7f010019
com.clipsy.android.debug:dimen/m3_searchbar_margin_horizontal = 0x7f0701e0
com.clipsy.android.debug:attr/clockFaceBackgroundColor = 0x7f0400e5
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Headline1 = 0x7f12022a
com.clipsy.android.debug:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f070293
com.clipsy.android.debug:attr/buttonIconTintMode = 0x7f04009d
com.clipsy.android.debug:attr/telltales_tailColor = 0x7f040480
com.clipsy.android.debug:color/m3_ref_palette_neutral94 = 0x7f060127
com.clipsy.android.debug:attr/floatingActionButtonSurfaceStyle = 0x7f040206
com.clipsy.android.debug:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0d002e
com.clipsy.android.debug:id/parallax = 0x7f09017a
com.clipsy.android.debug:attr/percentX = 0x7f0403a1
com.clipsy.android.debug:macro/m3_comp_snackbar_container_shape = 0x7f0d0115
com.clipsy.android.debug:attr/carousel_previousState = 0x7f0400b1
com.clipsy.android.debug:attr/boxCornerRadiusTopEnd = 0x7f04008c
com.clipsy.android.debug:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f12004b
com.clipsy.android.debug:attr/materialCalendarHeaderLayout = 0x7f040307
com.clipsy.android.debug:attr/carousel_touchUp_velocityThreshold = 0x7f0400b4
com.clipsy.android.debug:interpolator/fast_out_slow_in = 0x7f0b0006
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral40 = 0x7f0600c5
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f12044e
com.clipsy.android.debug:attr/iconSize = 0x7f04024b
com.clipsy.android.debug:string/character_counter_content_description = 0x7f110037
com.clipsy.android.debug:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f07026f
com.clipsy.android.debug:color/m3_appbar_overlay_color = 0x7f06007d
com.clipsy.android.debug:styleable/AppCompatEmojiHelper = 0x7f130010
com.clipsy.android.debug:dimen/abc_seekbar_track_progress_height_material = 0x7f070039
com.clipsy.android.debug:dimen/mtrl_slider_label_padding = 0x7f0702eb
com.clipsy.android.debug:attr/isMaterialTheme = 0x7f040265
com.clipsy.android.debug:integer/m3_sys_motion_duration_short2 = 0x7f0a001d
com.clipsy.android.debug:attr/chipStrokeWidth = 0x7f0400d8
com.clipsy.android.debug:attr/badgeWithTextHeight = 0x7f040067
com.clipsy.android.debug:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0702a9
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral17 = 0x7f0600bf
com.clipsy.android.debug:dimen/m3_searchbar_height = 0x7f0701df
com.clipsy.android.debug:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f12046c
com.clipsy.android.debug:dimen/m3_bottomappbar_fab_end_margin = 0x7f0700d4
com.clipsy.android.debug:attr/colorError = 0x7f040104
com.clipsy.android.debug:drawable/abc_textfield_activated_mtrl_alpha = 0x7f080071
com.clipsy.android.debug:dimen/notification_small_icon_size_as_large = 0x7f07031b
com.clipsy.android.debug:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0b0003
com.clipsy.android.debug:attr/chipSpacingVertical = 0x7f0400d4
com.clipsy.android.debug:attr/listPreferredItemHeightLarge = 0x7f0402ea
com.clipsy.android.debug:macro/m3_comp_filled_text_field_container_color = 0x7f0d004c
com.clipsy.android.debug:attr/blendSrc = 0x7f04007c
com.clipsy.android.debug:attr/tabGravity = 0x7f040463
com.clipsy.android.debug:macro/m3_comp_filled_card_container_shape = 0x7f0d0048
com.clipsy.android.debug:attr/boxBackgroundMode = 0x7f040088
com.clipsy.android.debug:drawable/abc_dialog_material_background = 0x7f08003b
com.clipsy.android.debug:anim/mtrl_bottom_sheet_slide_out = 0x7f01002a
com.clipsy.android.debug:attr/colorAccent = 0x7f0400fd
com.clipsy.android.debug:attr/chipSpacing = 0x7f0400d2
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f120281
com.clipsy.android.debug:attr/chipMinTouchTargetSize = 0x7f0400d1
com.clipsy.android.debug:string/pref_auto_sync_summary = 0x7f1100d1
com.clipsy.android.debug:attr/tintMode = 0x7f0404dc
com.clipsy.android.debug:drawable/ic_status_vector = 0x7f0800b0
com.clipsy.android.debug:bool/workmanager_test_configuration = 0x7f050007
com.clipsy.android.debug:attr/setsTag = 0x7f0403f7
com.clipsy.android.debug:attr/key = 0x7f040285
com.clipsy.android.debug:layout/mtrl_alert_dialog_title = 0x7f0c004f
com.clipsy.android.debug:attr/counterMaxLength = 0x7f040162
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary70 = 0x7f0600f5
com.clipsy.android.debug:style/TextAppearance.Compat.Notification = 0x7f1201f0
com.clipsy.android.debug:attr/checkedIconGravity = 0x7f0400c0
com.clipsy.android.debug:color/material_dynamic_secondary100 = 0x7f06024a
com.clipsy.android.debug:attr/editTextColor = 0x7f0401b1
com.clipsy.android.debug:style/Widget.Material3.Chip.Input.Elevated = 0x7f120395
com.clipsy.android.debug:color/material_personalized_color_secondary = 0x7f060292
com.clipsy.android.debug:integer/m3_sys_shape_corner_medium_corner_family = 0x7f0a0025
com.clipsy.android.debug:attr/fontProviderAuthority = 0x7f04021d
com.clipsy.android.debug:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0d009d
com.clipsy.android.debug:attr/checkedIconEnabled = 0x7f0400bf
com.clipsy.android.debug:attr/layoutDuringTransition = 0x7f040293
com.clipsy.android.debug:attr/layout_constraintBaseline_toBaselineOf = 0x7f04029d
com.clipsy.android.debug:integer/m3_btn_anim_delay_ms = 0x7f0a000b
com.clipsy.android.debug:id/allStates = 0x7f09004d
com.clipsy.android.debug:color/material_grey_900 = 0x7f060268
com.clipsy.android.debug:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f120372
com.clipsy.android.debug:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f12014a
com.clipsy.android.debug:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1200ef
com.clipsy.android.debug:string/abc_searchview_description_query = 0x7f110014
com.clipsy.android.debug:attr/alphabeticModifiers = 0x7f040036
com.clipsy.android.debug:attr/rangeFillColor = 0x7f0403cf
com.clipsy.android.debug:style/Widget.MaterialComponents.NavigationRailView = 0x7f12045a
com.clipsy.android.debug:dimen/m3_chip_corner_size = 0x7f070100
com.clipsy.android.debug:attr/actionModeCutDrawable = 0x7f040018
com.clipsy.android.debug:id/CTRL = 0x7f090003
com.clipsy.android.debug:dimen/mtrl_navigation_item_icon_padding = 0x7f0702cc
com.clipsy.android.debug:attr/itemStrokeWidth = 0x7f04027e
com.clipsy.android.debug:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0d000c
com.clipsy.android.debug:layout/abc_screen_toolbar = 0x7f0c0017
com.clipsy.android.debug:attr/motionDurationShort2 = 0x7f040351
com.clipsy.android.debug:drawable/ic_send_arrow = 0x7f0800ae
com.clipsy.android.debug:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f07016f
com.clipsy.android.debug:attr/height = 0x7f040234
com.clipsy.android.debug:color/m3_dynamic_dark_default_color_secondary_text = 0x7f06009a
com.clipsy.android.debug:attr/carousel_nextState = 0x7f0400b0
com.clipsy.android.debug:attr/autoSizeStepGranularity = 0x7f04004c
com.clipsy.android.debug:drawable/$avd_show_password__2 = 0x7f080005
com.clipsy.android.debug:color/clipsy_accent = 0x7f06003f
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f120176
com.clipsy.android.debug:dimen/abc_action_button_min_height_material = 0x7f07000d
com.clipsy.android.debug:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0d0009
com.clipsy.android.debug:attr/listPreferredItemPaddingStart = 0x7f0402ef
com.clipsy.android.debug:attr/carousel_infinite = 0x7f0400af
com.clipsy.android.debug:color/material_dynamic_tertiary100 = 0x7f060257
com.clipsy.android.debug:string/abc_menu_delete_shortcut_label = 0x7f11000a
com.clipsy.android.debug:attr/adjustable = 0x7f04002c
com.clipsy.android.debug:anim/linear_indeterminate_line2_head_interpolator = 0x7f01001f
com.clipsy.android.debug:style/TextAppearance.Material3.LabelLarge = 0x7f12021a
com.clipsy.android.debug:macro/m3_comp_filled_tonal_button_container_color = 0x7f0d0053
com.clipsy.android.debug:id/tag_accessibility_heading = 0x7f0901e8
com.clipsy.android.debug:attr/circularProgressIndicatorStyle = 0x7f0400dc
com.clipsy.android.debug:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1202c4
com.clipsy.android.debug:attr/waveDecay = 0x7f040522
com.clipsy.android.debug:attr/contentPaddingRight = 0x7f04014e
com.clipsy.android.debug:attr/backgroundOverlayColorAlpha = 0x7f040056
com.clipsy.android.debug:dimen/m3_badge_with_text_vertical_padding = 0x7f0700c7
com.clipsy.android.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f120177
com.clipsy.android.debug:attr/showDividers = 0x7f040409
com.clipsy.android.debug:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f120382
com.clipsy.android.debug:attr/appBarLayoutStyle = 0x7f04003e
com.clipsy.android.debug:color/mtrl_btn_bg_color_selector = 0x7f0602b8
com.clipsy.android.debug:attr/cardElevation = 0x7f0400a5
com.clipsy.android.debug:attr/color = 0x7f0400fc
com.clipsy.android.debug:attr/boxCornerRadiusBottomStart = 0x7f04008b
com.clipsy.android.debug:attr/layout_constraintHorizontal_weight = 0x7f0402b3
com.clipsy.android.debug:drawable/btn_checkbox_checked_mtrl = 0x7f08007a
com.clipsy.android.debug:color/m3_dark_highlighted_text = 0x7f060094
com.clipsy.android.debug:attr/showMotionSpec = 0x7f04040a
com.clipsy.android.debug:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
com.clipsy.android.debug:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1202e8
com.clipsy.android.debug:integer/abc_config_activityShortDur = 0x7f0a0001
com.clipsy.android.debug:dimen/mtrl_snackbar_padding_horizontal = 0x7f0702f9
com.clipsy.android.debug:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f120277
com.clipsy.android.debug:id/mtrl_picker_title_text = 0x7f09014b
com.clipsy.android.debug:attr/cardCornerRadius = 0x7f0400a4
com.clipsy.android.debug:id/material_clock_period_pm_button = 0x7f09011d
com.clipsy.android.debug:attr/autoTransition = 0x7f04004e
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_on_surface = 0x7f0601b2
com.clipsy.android.debug:attr/constraintRotate = 0x7f04013b
com.clipsy.android.debug:attr/passwordToggleTintMode = 0x7f04039c
com.clipsy.android.debug:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
com.clipsy.android.debug:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f0702f4
com.clipsy.android.debug:attr/layout_constraintHeight = 0x7f0402ac
com.clipsy.android.debug:attr/carousel_touchUpMode = 0x7f0400b2
com.clipsy.android.debug:string/error_invalid_ip = 0x7f11004a
com.clipsy.android.debug:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f080022
com.clipsy.android.debug:attr/summaryOff = 0x7f040456
com.clipsy.android.debug:id/scrollable = 0x7f0901a8
com.clipsy.android.debug:attr/navigationViewStyle = 0x7f040378
com.clipsy.android.debug:id/clip_horizontal = 0x7f090087
com.clipsy.android.debug:attr/motionEffect_translationY = 0x7f040366
com.clipsy.android.debug:color/notification_icon_bg_color = 0x7f0602f0
com.clipsy.android.debug:attr/buttonStyle = 0x7f04009f
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f120455
com.clipsy.android.debug:attr/menuAlignmentMode = 0x7f040333
com.clipsy.android.debug:attr/thumbRadius = 0x7f0404cc
com.clipsy.android.debug:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f120434
com.clipsy.android.debug:id/cache_measures = 0x7f090072
com.clipsy.android.debug:attr/buttonIconTint = 0x7f04009c
com.clipsy.android.debug:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1200ff
com.clipsy.android.debug:attr/thumbElevation = 0x7f0404c7
com.clipsy.android.debug:attr/itemTextAppearanceActive = 0x7f040280
com.clipsy.android.debug:style/AlertDialog.Clipsy.Button = 0x7f120004
com.clipsy.android.debug:dimen/m3_navigation_rail_default_width = 0x7f0701ce
com.clipsy.android.debug:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0d014e
com.clipsy.android.debug:attr/buttonGravity = 0x7f040099
com.clipsy.android.debug:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0d00a7
com.clipsy.android.debug:color/design_dark_default_color_on_primary = 0x7f060051
com.clipsy.android.debug:attr/buttonBarNeutralButtonStyle = 0x7f040095
com.clipsy.android.debug:attr/actionTextColorAlpha = 0x7f040025
com.clipsy.android.debug:attr/actionOverflowMenuStyle = 0x7f040023
com.clipsy.android.debug:attr/buttonBarNegativeButtonStyle = 0x7f040094
com.clipsy.android.debug:color/material_dynamic_tertiary95 = 0x7f060260
com.clipsy.android.debug:anim/nav_default_pop_enter_anim = 0x7f01002e
com.clipsy.android.debug:dimen/abc_button_inset_vertical_material = 0x7f070013
com.clipsy.android.debug:color/m3_ref_palette_tertiary95 = 0x7f06015e
com.clipsy.android.debug:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f1203aa
com.clipsy.android.debug:attr/boxStrokeWidth = 0x7f040090
com.clipsy.android.debug:macro/m3_comp_fab_secondary_container_color = 0x7f0d003c
com.clipsy.android.debug:attr/drawPath = 0x7f04019d
com.clipsy.android.debug:style/Preference = 0x7f120150
com.clipsy.android.debug:attr/activityAction = 0x7f040028
com.clipsy.android.debug:attr/trackColorInactive = 0x7f0404fd
com.clipsy.android.debug:color/m3_ref_palette_primary50 = 0x7f06013f
com.clipsy.android.debug:attr/badgeWithTextShapeAppearance = 0x7f040069
com.clipsy.android.debug:drawable/abc_vector_test = 0x7f080076
com.clipsy.android.debug:attr/badgeShapeAppearanceOverlay = 0x7f04005f
com.clipsy.android.debug:attr/layout_goneMarginRight = 0x7f0402cf
com.clipsy.android.debug:color/m3_filled_icon_button_container_color_selector = 0x7f0600a8
com.clipsy.android.debug:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f120249
com.clipsy.android.debug:attr/actionMenuTextColor = 0x7f040012
com.clipsy.android.debug:id/animateToStart = 0x7f090051
com.clipsy.android.debug:dimen/abc_dialog_min_width_minor = 0x7f070023
com.clipsy.android.debug:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0d00fa
com.clipsy.android.debug:color/m3_ref_palette_error40 = 0x7f06010c
com.clipsy.android.debug:attr/colorSecondaryVariant = 0x7f040129
com.clipsy.android.debug:integer/mtrl_calendar_selection_text_lines = 0x7f0a0032
com.clipsy.android.debug:color/m3_ref_palette_error0 = 0x7f060107
com.clipsy.android.debug:attr/chipIconSize = 0x7f0400cd
com.clipsy.android.debug:color/abc_search_url_text_selected = 0x7f060010
com.clipsy.android.debug:dimen/mtrl_navigation_rail_active_text_size = 0x7f0702d0
com.clipsy.android.debug:id/text_description = 0x7f0901fe
com.clipsy.android.debug:color/material_blue_grey_900 = 0x7f06021b
com.clipsy.android.debug:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0701c4
com.clipsy.android.debug:color/m3_sys_color_dark_error_container = 0x7f06016b
com.clipsy.android.debug:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f12013d
com.clipsy.android.debug:attr/endIconCheckable = 0x7f0401bc
com.clipsy.android.debug:attr/clockHandColor = 0x7f0400e6
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f120453
com.clipsy.android.debug:color/m3_ref_palette_error10 = 0x7f060108
com.clipsy.android.debug:string/m3_sys_motion_easing_legacy = 0x7f110063
com.clipsy.android.debug:attr/bottomInsetScrimEnabled = 0x7f040082
com.clipsy.android.debug:style/ShapeAppearance.Material3.Corner.Medium = 0x7f12019e
com.clipsy.android.debug:string/mtrl_badge_numberless_content_description = 0x7f110080
com.clipsy.android.debug:dimen/m3_card_stroke_width = 0x7f0700f8
com.clipsy.android.debug:attr/itemHorizontalTranslationEnabled = 0x7f04026b
com.clipsy.android.debug:attr/buttonBarButtonStyle = 0x7f040093
com.clipsy.android.debug:attr/colorContainer = 0x7f040100
com.clipsy.android.debug:animator/design_fab_show_motion_spec = 0x7f020002
com.clipsy.android.debug:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f120477
com.clipsy.android.debug:attr/textAppearanceTitleMedium = 0x7f0404a7
com.clipsy.android.debug:drawable/badge_background = 0x7f080079
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_headline_color = 0x7f0d0087
com.clipsy.android.debug:attr/behavior_saveFlags = 0x7f040079
com.clipsy.android.debug:styleable/StateListDrawable = 0x7f13009f
com.clipsy.android.debug:id/disableScroll = 0x7f0900ae
com.clipsy.android.debug:attr/behavior_overlapTop = 0x7f040077
com.clipsy.android.debug:dimen/m3_side_sheet_margin_detached = 0x7f0701e9
com.clipsy.android.debug:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700d2
com.clipsy.android.debug:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.clipsy.android.debug:id/radio = 0x7f09018f
com.clipsy.android.debug:attr/behavior_expandedOffset = 0x7f040073
com.clipsy.android.debug:attr/itemTextColor = 0x7f040283
com.clipsy.android.debug:macro/m3_comp_navigation_rail_label_text_type = 0x7f0d00a1
com.clipsy.android.debug:attr/colorPrimary = 0x7f04011d
com.clipsy.android.debug:color/m3_timepicker_display_text_color = 0x7f060215
com.clipsy.android.debug:attr/isMaterial3DynamicColorApplied = 0x7f040263
com.clipsy.android.debug:attr/expandedTitleMargin = 0x7f0401d9
com.clipsy.android.debug:id/notification_main_column_container = 0x7f090164
com.clipsy.android.debug:attr/backHandlingEnabled = 0x7f04004f
com.clipsy.android.debug:color/secondary_color = 0x7f060300
com.clipsy.android.debug:anim/design_snackbar_out = 0x7f01001b
com.clipsy.android.debug:attr/motionEasingEmphasized = 0x7f040356
com.clipsy.android.debug:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f080060
com.clipsy.android.debug:styleable/FragmentNavigator = 0x7f130043
com.clipsy.android.debug:color/m3_chip_ripple_color = 0x7f06008f
com.clipsy.android.debug:attr/hintEnabled = 0x7f04023f
com.clipsy.android.debug:attr/initialExpandedChildrenCount = 0x7f040260
com.clipsy.android.debug:id/alertTitle = 0x7f09004a
com.clipsy.android.debug:attr/extendStrategy = 0x7f0401e1
com.clipsy.android.debug:attr/layout_constraintEnd_toStartOf = 0x7f0402a8
com.clipsy.android.debug:attr/flow_firstVerticalBias = 0x7f04020a
com.clipsy.android.debug:style/Base.V7.Theme.AppCompat.Light = 0x7f1200c0
com.clipsy.android.debug:layout/image_frame = 0x7f0c0034
com.clipsy.android.debug:id/layout_empty = 0x7f090107
com.clipsy.android.debug:color/m3_chip_background_color = 0x7f06008e
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f060193
com.clipsy.android.debug:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f120078
com.clipsy.android.debug:color/material_grey_600 = 0x7f060265
com.clipsy.android.debug:color/m3_ref_palette_dynamic_primary99 = 0x7f0600ec
com.clipsy.android.debug:attr/constraint_referenced_ids = 0x7f04013f
com.clipsy.android.debug:animator/m3_card_elevated_state_list_anim = 0x7f02000c
com.clipsy.android.debug:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f12046e
com.clipsy.android.debug:attr/spinnerDropDownItemStyle = 0x7f040421
com.clipsy.android.debug:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1202c0
com.clipsy.android.debug:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1200d0
com.clipsy.android.debug:drawable/abc_ic_voice_search_api_material = 0x7f080049
com.clipsy.android.debug:attr/motionInterpolator = 0x7f040368
com.clipsy.android.debug:animator/mtrl_fab_show_motion_spec = 0x7f02001f
com.clipsy.android.debug:style/Widget.MaterialComponents.BottomSheet = 0x7f12041e
com.clipsy.android.debug:string/mtrl_picker_cancel = 0x7f110094
com.clipsy.android.debug:attr/hideNavigationIcon = 0x7f04023b
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0601b1
com.clipsy.android.debug:attr/cardPreventCornerOverlap = 0x7f0400a8
com.clipsy.android.debug:attr/bottomAppBarStyle = 0x7f040081
com.clipsy.android.debug:drawable/mtrl_switch_thumb_unchecked = 0x7f0800e8
com.clipsy.android.debug:attr/badgeTextColor = 0x7f040063
com.clipsy.android.debug:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1202d0
com.clipsy.android.debug:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0d012e
com.clipsy.android.debug:attr/minWidth = 0x7f04033c
com.clipsy.android.debug:attr/actionBarTabStyle = 0x7f04000a
com.clipsy.android.debug:animator/nav_default_pop_enter_anim = 0x7f020024
com.clipsy.android.debug:color/material_dynamic_neutral30 = 0x7f060225
com.clipsy.android.debug:attr/backgroundInsetBottom = 0x7f040052
com.clipsy.android.debug:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1200e7
com.clipsy.android.debug:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0d00be
com.clipsy.android.debug:attr/tickMark = 0x7f0404d5
com.clipsy.android.debug:styleable/PopupWindowBackgroundState = 0x7f130083
com.clipsy.android.debug:attr/textAppearanceLabelSmall = 0x7f040499
com.clipsy.android.debug:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.clipsy.android.debug:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f1202b2
com.clipsy.android.debug:layout/design_layout_tab_text = 0x7f0c0024
com.clipsy.android.debug:attr/state_lifted = 0x7f04043f
com.clipsy.android.debug:drawable/ic_call_answer = 0x7f08008f
com.clipsy.android.debug:dimen/m3_simple_item_color_hovered_alpha = 0x7f0701ed
com.clipsy.android.debug:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f070290
com.clipsy.android.debug:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0d00a9
com.clipsy.android.debug:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0d008b
com.clipsy.android.debug:attr/horizontalOffsetWithText = 0x7f040245
com.clipsy.android.debug:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f070198
com.clipsy.android.debug:attr/backgroundTintMode = 0x7f04005a
com.clipsy.android.debug:color/background_material_light = 0x7f060021
com.clipsy.android.debug:style/ThemeOverlay.Material3.Snackbar = 0x7f1202e1
com.clipsy.android.debug:color/design_fab_shadow_end_color = 0x7f060068
com.clipsy.android.debug:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0d0148
com.clipsy.android.debug:id/button_unpair = 0x7f090071
com.clipsy.android.debug:anim/fragment_fast_out_extra_slow_in = 0x7f01001c
com.clipsy.android.debug:attr/backgroundStacked = 0x7f040058
com.clipsy.android.debug:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f120478
com.clipsy.android.debug:string/action_connect = 0x7f11001c
com.clipsy.android.debug:attr/actionBarWidgetTheme = 0x7f04000d
com.clipsy.android.debug:attr/attributeName = 0x7f040044
com.clipsy.android.debug:attr/tabIndicatorAnimationDuration = 0x7f040467
com.clipsy.android.debug:attr/altSrc = 0x7f040037
com.clipsy.android.debug:attr/gestureInsetBottomIgnored = 0x7f04022d
com.clipsy.android.debug:color/m3_dynamic_primary_text_disable_only = 0x7f0600a2
com.clipsy.android.debug:animator/fragment_open_exit = 0x7f020008
com.clipsy.android.debug:dimen/def_drawer_elevation = 0x7f070069
com.clipsy.android.debug:attr/borderRound = 0x7f04007d
com.clipsy.android.debug:attr/textEndPadding = 0x7f0404b0
com.clipsy.android.debug:color/m3_ref_palette_neutral12 = 0x7f060117
com.clipsy.android.debug:color/m3_timepicker_display_ripple_color = 0x7f060214
com.clipsy.android.debug:color/material_dynamic_secondary80 = 0x7f060251
com.clipsy.android.debug:attr/prefixTextColor = 0x7f0403c2
com.clipsy.android.debug:attr/chipGroupStyle = 0x7f0400ca
com.clipsy.android.debug:macro/m3_comp_search_bar_container_color = 0x7f0d00e6
com.clipsy.android.debug:attr/visibilityMode = 0x7f04051f
com.clipsy.android.debug:attr/arcMode = 0x7f040040
com.clipsy.android.debug:attr/colorOnTertiaryFixed = 0x7f040119
com.clipsy.android.debug:style/Widget.Material3.SearchBar.Outlined = 0x7f1203eb
com.clipsy.android.debug:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1203c3
com.clipsy.android.debug:color/mtrl_navigation_item_icon_tint = 0x7f0602d8
com.clipsy.android.debug:attr/showTitle = 0x7f04040e
com.clipsy.android.debug:attr/fontProviderQuery = 0x7f040222
com.clipsy.android.debug:animator/m3_appbar_state_list_animator = 0x7f020009
com.clipsy.android.debug:attr/alpha = 0x7f040035
com.clipsy.android.debug:color/material_personalized_color_text_hint_foreground_inverse = 0x7f0602a2
com.clipsy.android.debug:attr/navigationMode = 0x7f040376
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f080018
com.clipsy.android.debug:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0d001b
com.clipsy.android.debug:attr/actionOverflowButtonStyle = 0x7f040022
com.clipsy.android.debug:attr/colorTertiary = 0x7f040135
com.clipsy.android.debug:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1203ba
com.clipsy.android.debug:attr/transitionDisable = 0x7f040507
com.clipsy.android.debug:styleable/AnimatedStateListDrawableCompat = 0x7f13000a
com.clipsy.android.debug:attr/popEnterAnim = 0x7f0403ac
com.clipsy.android.debug:attr/badgeTextAppearance = 0x7f040062
com.clipsy.android.debug:drawable/mtrl_popupmenu_background_overlay = 0x7f0800e0
com.clipsy.android.debug:attr/textAppearanceBody1 = 0x7f040484
com.clipsy.android.debug:macro/m3_comp_filled_button_label_text_type = 0x7f0d0046
com.clipsy.android.debug:attr/layout_constrainedHeight = 0x7f04029a
com.clipsy.android.debug:id/accessibility_custom_action_23 = 0x7f090020
com.clipsy.android.debug:attr/animateNavigationIcon = 0x7f04003b
com.clipsy.android.debug:styleable/BottomAppBar = 0x7f130019
com.clipsy.android.debug:color/bright_foreground_disabled_material_dark = 0x7f060026
com.clipsy.android.debug:animator/fragment_fade_exit = 0x7f020006
com.clipsy.android.debug:macro/m3_comp_fab_tertiary_container_color = 0x7f0d0040
com.clipsy.android.debug:attr/layout_constraintWidth_max = 0x7f0402c5
com.clipsy.android.debug:attr/contentPaddingBottom = 0x7f04014b
com.clipsy.android.debug:attr/allowDividerAbove = 0x7f040031
com.clipsy.android.debug:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1200dc
com.clipsy.android.debug:dimen/m3_large_fab_size = 0x7f0701c0
com.clipsy.android.debug:id/expand_activities_button = 0x7f0900ca
com.clipsy.android.debug:attr/staggered = 0x7f04042f
com.clipsy.android.debug:anim/mtrl_bottom_sheet_slide_in = 0x7f010029
com.clipsy.android.debug:attr/colorOnTertiary = 0x7f040117
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1201de
com.clipsy.android.debug:attr/colorSurfaceBright = 0x7f04012b
com.clipsy.android.debug:attr/coordinatorLayoutStyle = 0x7f040154
com.clipsy.android.debug:dimen/design_bottom_navigation_icon_size = 0x7f070070
com.clipsy.android.debug:id/toggle = 0x7f090217
com.clipsy.android.debug:attr/behavior_fitToContents = 0x7f040074
com.clipsy.android.debug:attr/itemTextAppearance = 0x7f04027f
com.clipsy.android.debug:style/Widget.MaterialComponents.Chip.Choice = 0x7f12042f
com.clipsy.android.debug:color/material_personalized_hint_foreground = 0x7f0602a7
com.clipsy.android.debug:attr/alertDialogButtonGroupStyle = 0x7f04002d
com.clipsy.android.debug:attr/iconTint = 0x7f04024e
com.clipsy.android.debug:attr/colorOnContainerUnchecked = 0x7f040108
com.clipsy.android.debug:id/bottom = 0x7f090061
com.clipsy.android.debug:attr/tabMode = 0x7f040470
com.clipsy.android.debug:xml/network_security_config = 0x7f140002
com.clipsy.android.debug:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
com.clipsy.android.debug:drawable/notify_panel_notification_icon_bg = 0x7f0800fb
com.clipsy.android.debug:attr/autoSizeTextType = 0x7f04004d
com.clipsy.android.debug:styleable/ActivityRule = 0x7f130008
com.clipsy.android.debug:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f070219
com.clipsy.android.debug:attr/actionModePasteDrawable = 0x7f04001a
com.clipsy.android.debug:attr/textureBlurFactor = 0x7f0404c0
com.clipsy.android.debug:attr/flow_firstHorizontalStyle = 0x7f040209
com.clipsy.android.debug:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
com.clipsy.android.debug:color/clipsy_text_primary = 0x7f060048
com.clipsy.android.debug:style/Animation.AppCompat.Tooltip = 0x7f120008
com.clipsy.android.debug:id/swipe_refresh_layout = 0x7f0901e2
com.clipsy.android.debug:color/m3_ref_palette_dynamic_tertiary50 = 0x7f060100
com.clipsy.android.debug:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f12040b
com.clipsy.android.debug:dimen/material_emphasis_high_type = 0x7f070239
com.clipsy.android.debug:attr/voiceIcon = 0x7f040520
com.clipsy.android.debug:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0701d3
com.clipsy.android.debug:attr/showAsAction = 0x7f040407
com.clipsy.android.debug:attr/actionLayout = 0x7f040010
com.clipsy.android.debug:styleable/KeyPosition = 0x7f13004d
com.clipsy.android.debug:attr/layout_constraintTag = 0x7f0402bc
com.clipsy.android.debug:attr/startIconTint = 0x7f040436
com.clipsy.android.debug:attr/actionModePopupWindowStyle = 0x7f04001b
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f120206
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Display3 = 0x7f120021
com.clipsy.android.debug:string/abc_searchview_description_voice = 0x7f110017
com.clipsy.android.debug:dimen/abc_list_item_height_large_material = 0x7f070030
com.clipsy.android.debug:attr/pivotAnchor = 0x7f0403a5
com.clipsy.android.debug:id/search_bar = 0x7f0901aa
com.clipsy.android.debug:attr/itemShapeInsetBottom = 0x7f040278
com.clipsy.android.debug:dimen/mtrl_slider_track_side_padding = 0x7f0702f2
com.clipsy.android.debug:style/Base.V21.Theme.AppCompat = 0x7f1200a5
com.clipsy.android.debug:animator/nav_default_enter_anim = 0x7f020022
com.clipsy.android.debug:attr/actionModeBackground = 0x7f040013
com.clipsy.android.debug:attr/actionButtonStyle = 0x7f04000e
com.clipsy.android.debug:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f1203fd
com.clipsy.android.debug:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
com.clipsy.android.debug:dimen/material_clock_hand_stroke_width = 0x7f07022d
com.clipsy.android.debug:styleable/CollapsingToolbarLayout_Layout = 0x7f130028
com.clipsy.android.debug:dimen/abc_star_big = 0x7f07003b
com.clipsy.android.debug:macro/m3_comp_menu_container_color = 0x7f0d0060
com.clipsy.android.debug:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f040358
com.clipsy.android.debug:attr/colorOnErrorContainer = 0x7f04010a
com.clipsy.android.debug:attr/chipSpacingHorizontal = 0x7f0400d3
com.clipsy.android.debug:string/mtrl_exceed_max_badge_number_suffix = 0x7f11008e
com.clipsy.android.debug:dimen/abc_text_size_display_1_material = 0x7f070043
com.clipsy.android.debug:attr/cornerSizeBottomRight = 0x7f04015e
com.clipsy.android.debug:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0702c5
com.clipsy.android.debug:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f070176
com.clipsy.android.debug:color/material_dynamic_neutral_variant40 = 0x7f060233
com.clipsy.android.debug:id/standard = 0x7f0901d6
com.clipsy.android.debug:color/m3_ref_palette_neutral_variant90 = 0x7f060136
com.clipsy.android.debug:attr/actionBarTabBarStyle = 0x7f040009
com.clipsy.android.debug:color/material_cursor_color = 0x7f06021d
com.clipsy.android.debug:color/m3_ref_palette_dynamic_secondary95 = 0x7f0600f8
com.clipsy.android.debug:attr/waveVariesBy = 0x7f040527
com.clipsy.android.debug:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.clipsy.android.debug:style/Base.Theme.Material3.Light = 0x7f120063
com.clipsy.android.debug:attr/drawableEndCompat = 0x7f04019f
com.clipsy.android.debug:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.clipsy.android.debug:attr/cardMaxElevation = 0x7f0400a7
com.clipsy.android.debug:layout/preference_category = 0x7f0c0074
com.clipsy.android.debug:attr/materialCalendarMonthNavigationButton = 0x7f04030c
com.clipsy.android.debug:dimen/material_emphasis_disabled_background = 0x7f070238
com.clipsy.android.debug:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0401fc
com.clipsy.android.debug:attr/colorOnBackground = 0x7f040106
com.clipsy.android.debug:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f070240
com.clipsy.android.debug:dimen/compat_notification_large_icon_max_height = 0x7f070067
com.clipsy.android.debug:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f0600b3
com.clipsy.android.debug:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0d013a
com.clipsy.android.debug:attr/contentInsetLeft = 0x7f040146
com.clipsy.android.debug:id/recycler_view_connected_devices = 0x7f090194
com.clipsy.android.debug:attr/materialCalendarFullscreenTheme = 0x7f040303
com.clipsy.android.debug:styleable/AppBarLayout_Layout = 0x7f13000f
com.clipsy.android.debug:color/m3_ref_palette_dynamic_neutral0 = 0x7f0600bb
com.clipsy.android.debug:attr/transitionShapeAppearance = 0x7f04050b
com.clipsy.android.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f120037
com.clipsy.android.debug:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0d0064
com.clipsy.android.debug:attr/borderWidth = 0x7f04007f
com.clipsy.android.debug:anim/m3_bottom_sheet_slide_in = 0x7f010021
com.clipsy.android.debug:attr/fabSize = 0x7f0401f0
com.clipsy.android.debug:style/ShapeAppearance.Material3.LargeComponent = 0x7f1201a1
com.clipsy.android.debug:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0800c6
com.clipsy.android.debug:dimen/compat_button_inset_vertical_material = 0x7f070063
com.clipsy.android.debug:attr/collapsedTitleTextAppearance = 0x7f0400f5
com.clipsy.android.debug:attr/region_widthMoreThan = 0x7f0403db
com.clipsy.android.debug:attr/behavior_significantVelocityThreshold = 0x7f04007a
com.clipsy.android.debug:drawable/ic_mtrl_checked_circle = 0x7f0800a4
com.clipsy.android.debug:dimen/m3_sys_elevation_level1 = 0x7f0701f6
com.clipsy.android.debug:attr/boxStrokeErrorColor = 0x7f04008f
com.clipsy.android.debug:style/Widget.AppCompat.ActionMode = 0x7f12031a
com.clipsy.android.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1201e4
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f0601a1
com.clipsy.android.debug:attr/negativeButtonText = 0x7f040379
com.clipsy.android.debug:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f12034b
com.clipsy.android.debug:color/m3_text_button_background_color_selector = 0x7f060207
com.clipsy.android.debug:attr/floatingActionButtonLargeStyle = 0x7f0401fb
com.clipsy.android.debug:attr/colorOnPrimaryContainer = 0x7f04010c
com.clipsy.android.debug:anim/abc_fade_in = 0x7f010000
com.clipsy.android.debug:integer/m3_sys_motion_duration_short3 = 0x7f0a001e
com.clipsy.android.debug:dimen/design_bottom_navigation_height = 0x7f07006f
com.clipsy.android.debug:id/split_action_bar = 0x7f0901ce
com.clipsy.android.debug:attr/textAppearanceHeadline1 = 0x7f04048e
com.clipsy.android.debug:attr/dropdownListPreferredItemHeight = 0x7f0401ac
com.clipsy.android.debug:style/AlertDialog.AppCompat.Light = 0x7f120001
com.clipsy.android.debug:attr/floatingActionButtonLargePrimaryStyle = 0x7f0401f9
com.clipsy.android.debug:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0d0075
com.clipsy.android.debug:color/material_dynamic_neutral90 = 0x7f06022b
com.clipsy.android.debug:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f1203f2
com.clipsy.android.debug:id/accessibility_custom_action_10 = 0x7f090012
com.clipsy.android.debug:color/m3_ref_palette_neutral100 = 0x7f060116
com.clipsy.android.debug:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.clipsy.android.debug:styleable/KeyTimeCycle = 0x7f13004e
com.clipsy.android.debug:attr/errorTextColor = 0x7f0401d3
com.clipsy.android.debug:style/Widget.Material3.BottomSheet.Modal = 0x7f120378
com.clipsy.android.debug:dimen/compat_button_padding_horizontal_material = 0x7f070064
com.clipsy.android.debug:string/nav_app_bar_open_drawer_description = 0x7f1100bf
com.clipsy.android.debug:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0d00f8
com.clipsy.android.debug:id/fill = 0x7f0900cf
com.clipsy.android.debug:attr/behavior_peekHeight = 0x7f040078
com.clipsy.android.debug:attr/useDrawerArrowDrawable = 0x7f040514
com.clipsy.android.debug:drawable/$avd_hide_password__2 = 0x7f080002
com.clipsy.android.debug:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1202c6
com.clipsy.android.debug:dimen/abc_list_item_height_material = 0x7f070031
com.clipsy.android.debug:anim/design_bottom_sheet_slide_in = 0x7f010018
com.clipsy.android.debug:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f070124
com.clipsy.android.debug:styleable/TabItem = 0x7f1300a7
com.clipsy.android.debug:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0d0168
com.clipsy.android.debug:attr/actionViewClass = 0x7f040026
com.clipsy.android.debug:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1202f8
com.clipsy.android.debug:id/italic = 0x7f090101
com.clipsy.android.debug:attr/buttonTintMode = 0x7f0400a2
com.clipsy.android.debug:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f06019f
com.clipsy.android.debug:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
com.clipsy.android.debug:attr/content = 0x7f040142
com.clipsy.android.debug:drawable/mtrl_ic_error = 0x7f0800dc
com.clipsy.android.debug:attr/navigationContentDescription = 0x7f040373
com.clipsy.android.debug:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f070253
com.clipsy.android.debug:attr/divider = 0x7f040192
com.clipsy.android.debug:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0700d1
com.clipsy.android.debug:style/Widget.MaterialComponents.Tooltip = 0x7f120488
com.clipsy.android.debug:attr/colorControlHighlight = 0x7f040102
com.clipsy.android.debug:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0702c8
com.clipsy.android.debug:attr/floatingActionButtonTertiaryStyle = 0x7f040207
com.clipsy.android.debug:color/primary_dark_material_dark = 0x7f0602f3
com.clipsy.android.debug:styleable/PreferenceFragment = 0x7f130085
com.clipsy.android.debug:attr/fastScrollHorizontalTrackDrawable = 0x7f0401f3
com.clipsy.android.debug:styleable/Motion = 0x7f13006c
com.clipsy.android.debug:animator/mtrl_card_state_list_anim = 0x7f020017
com.clipsy.android.debug:color/notification_action_color_filter = 0x7f0602ef
com.clipsy.android.debug:color/black = 0x7f060022
com.clipsy.android.debug:dimen/m3_navigation_item_vertical_padding = 0x7f0701cb
com.clipsy.android.debug:attr/customColorValue = 0x7f04016e
com.clipsy.android.debug:dimen/design_navigation_separator_vertical_padding = 0x7f070089
com.clipsy.android.debug:string/not_set = 0x7f1100c4
com.clipsy.android.debug:attr/boxCollapsedPaddingTop = 0x7f040089
com.clipsy.android.debug:dimen/m3_side_sheet_width = 0x7f0701ec
com.clipsy.android.debug:color/m3_primary_text_disable_only = 0x7f0600b7
com.clipsy.android.debug:macro/m3_comp_fab_surface_container_color = 0x7f0d003e
com.clipsy.android.debug:id/nav_controller_view_tag = 0x7f09014e
com.clipsy.android.debug:attr/labelVisibilityMode = 0x7f04028c
com.clipsy.android.debug:style/ThemeOverlay.Material3.BottomAppBar = 0x7f1202b1
com.clipsy.android.debug:animator/m3_extended_fab_show_motion_spec = 0x7f020013
com.clipsy.android.debug:style/Widget.AppCompat.SeekBar = 0x7f120351
com.clipsy.android.debug:color/material_dynamic_neutral95 = 0x7f06022c
com.clipsy.android.debug:attr/badgeWidth = 0x7f040066
com.clipsy.android.debug:attr/preserveIconSpacing = 0x7f0403c3
com.clipsy.android.debug:color/m3_sys_color_light_surface_container_high = 0x7f0601ed
com.clipsy.android.debug:attr/navigationRailStyle = 0x7f040377
com.clipsy.android.debug:attr/textInputLayoutFocusedRectEnabled = 0x7f0404b5
com.clipsy.android.debug:integer/mtrl_btn_anim_delay_ms = 0x7f0a002f
com.clipsy.android.debug:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f07017f
com.clipsy.android.debug:color/mtrl_filled_background_color = 0x7f0602cf
com.clipsy.android.debug:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f07023b
com.clipsy.android.debug:dimen/m3_comp_search_view_docked_header_container_height = 0x7f07017e
com.clipsy.android.debug:attr/ratingBarStyle = 0x7f0403d0
com.clipsy.android.debug:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f08000e
com.clipsy.android.debug:attr/textAppearanceTitleSmall = 0x7f0404a8
com.clipsy.android.debug:attr/flow_verticalStyle = 0x7f040219
com.clipsy.android.debug:attr/launchSingleTop = 0x7f040290
com.clipsy.android.debug:attr/statusBarForeground = 0x7f040442
com.clipsy.android.debug:animator/design_fab_hide_motion_spec = 0x7f020001
com.clipsy.android.debug:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0601b4
com.clipsy.android.debug:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f120413
com.clipsy.android.debug:attr/floatingActionButtonSmallTertiaryStyle = 0x7f040204
com.clipsy.android.debug:id/text_device_name = 0x7f090201
com.clipsy.android.debug:dimen/mtrl_navigation_rail_margin = 0x7f0702d6
com.clipsy.android.debug:style/Preference.Information = 0x7f12015b
com.clipsy.android.debug:attr/waveOffset = 0x7f040523
com.clipsy.android.debug:style/Button.Clipsy.Secondary = 0x7f120127
com.clipsy.android.debug:anim/m3_side_sheet_exit_to_right = 0x7f010028
com.clipsy.android.debug:id/top = 0x7f090219
com.clipsy.android.debug:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f07019c
com.clipsy.android.debug:attr/paddingStartSystemWindowInsets = 0x7f040392
com.clipsy.android.debug:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f120133
com.clipsy.android.debug:color/material_dynamic_tertiary80 = 0x7f06025e
com.clipsy.android.debug:dimen/notification_action_icon_size = 0x7f070310
com.clipsy.android.debug:macro/m3_comp_search_view_container_color = 0x7f0d00f2
com.clipsy.android.debug:attr/itemFillColor = 0x7f040269
com.clipsy.android.debug:color/primary_text_default_material_light = 0x7f0602f8
com.clipsy.android.debug:color/material_personalized_primary_inverse_text_disable_only = 0x7f0602a9
com.clipsy.android.debug:layout/abc_action_bar_title_item = 0x7f0c0000
com.clipsy.android.debug:attr/spinBars = 0x7f040420
com.clipsy.android.debug:attr/elevationOverlayEnabled = 0x7f0401b7
com.clipsy.android.debug:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.clipsy.android.debug:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0d0055
com.clipsy.android.debug:attr/textPanY = 0x7f0404be
com.clipsy.android.debug:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1202cd
com.clipsy.android.debug:attr/fontProviderPackage = 0x7f040221
com.clipsy.android.debug:anim/nav_default_pop_exit_anim = 0x7f01002f
com.clipsy.android.debug:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f120320
com.clipsy.android.debug:color/material_personalized_color_on_primary_container = 0x7f060283
com.clipsy.android.debug:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f120470
com.clipsy.android.debug:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f12044a
com.clipsy.android.debug:attr/checkBoxPreferenceStyle = 0x7f0400b7
com.clipsy.android.debug:style/AlertDialog.Clipsy = 0x7f120002
com.clipsy.android.debug:color/m3_dynamic_dark_default_color_primary_text = 0x7f060099
com.clipsy.android.debug:animator/fragment_close_exit = 0x7f020004
com.clipsy.android.debug:color/mtrl_btn_text_color_selector = 0x7f0602be
com.clipsy.android.debug:attr/layout_constraintLeft_toRightOf = 0x7f0402b6
com.clipsy.android.debug:attr/autoAdjustToWithinGrandparentBounds = 0x7f040045
com.clipsy.android.debug:attr/actionModeCloseButtonStyle = 0x7f040014
com.clipsy.android.debug:id/skipCollapsed = 0x7f0901c0
com.clipsy.android.debug:dimen/mtrl_slider_thumb_radius = 0x7f0702ef
com.clipsy.android.debug:color/button_text_primary_selector = 0x7f060036
com.clipsy.android.debug:dimen/tooltip_y_offset_touch = 0x7f070330
com.clipsy.android.debug:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1202e6
com.clipsy.android.debug:id/carryVelocity = 0x7f090079
com.clipsy.android.debug:color/androidx_core_ripple_material_light = 0x7f06001b
com.clipsy.android.debug:attr/simpleItemSelectedRippleColor = 0x7f040414
com.clipsy.android.debug:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f120231
com.clipsy.android.debug:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.clipsy.android.debug:attr/shouldDisableView = 0x7f040404
com.clipsy.android.debug:attr/clockNumberTextColor = 0x7f0400e8
com.clipsy.android.debug:attr/backgroundTint = 0x7f040059
com.clipsy.android.debug:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f120208
com.clipsy.android.debug:attr/preferenceCategoryTitleTextAppearance = 0x7f0403b7
com.clipsy.android.debug:attr/badgeWithTextShapeAppearanceOverlay = 0x7f04006a
com.clipsy.android.debug:dimen/m3_comp_time_picker_container_elevation = 0x7f0701a6
com.clipsy.android.debug:anim/m3_motion_fade_exit = 0x7f010024
com.clipsy.android.debug:bool/config_materialPreferenceIconSpaceReserved = 0x7f050002
com.clipsy.android.debug:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f1202b9
com.clipsy.android.debug:attr/itemVerticalPadding = 0x7f040284
