#!/usr/bin/env python3
"""
Clipsy All Tests Runner
This script runs tests for both Android and Windows applications
"""

import sys
import os
import subprocess
import platform
from pathlib import Path

def run_command(command, cwd=None, shell=True):
    """Run a command and return the result."""
    try:
        result = subprocess.run(
            command, 
            shell=shell, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def run_windows_tests():
    """Run Windows application tests."""
    print("=== Running Windows Tests ===")
    
    script_dir = Path(__file__).parent
    test_script = script_dir / "test_windows.py"
    
    if not test_script.exists():
        print("✗ Windows test script not found")
        return False
    
    success, output = run_command([sys.executable, str(test_script)])
    print(output)
    
    if success:
        print("✓ Windows tests passed")
    else:
        print("✗ Windows tests failed")
    
    return success

def run_android_tests():
    """Run Android application tests."""
    print("\n=== Running Android Tests ===")
    
    script_dir = Path(__file__).parent
    test_script = script_dir / "test_android.sh"
    
    if not test_script.exists():
        print("✗ Android test script not found")
        return False
    
    # Make script executable on Unix systems
    if platform.system() != "Windows":
        os.chmod(test_script, 0o755)
        success, output = run_command([str(test_script)])
    else:
        # On Windows, try to run with bash if available
        try:
            success, output = run_command(["bash", str(test_script)])
        except FileNotFoundError:
            print("✗ Bash not found. Please install Git Bash or WSL to run Android tests on Windows.")
            return False
    
    print(output)
    
    if success:
        print("✓ Android tests passed")
    else:
        print("✗ Android tests failed")
    
    return success

def check_prerequisites():
    """Check if prerequisites are available."""
    print("=== Checking Prerequisites ===")
    
    # Check Python
    print(f"Python version: {sys.version}")
    
    # Check if Android SDK is available
    adb_available = False
    try:
        result = subprocess.run(["adb", "version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Android SDK (adb) found")
            adb_available = True
        else:
            print("✗ Android SDK (adb) not found")
    except FileNotFoundError:
        print("✗ Android SDK (adb) not found")
    
    # Check if Java is available
    java_available = False
    try:
        result = subprocess.run(["java", "-version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Java found")
            java_available = True
        else:
            print("✗ Java not found")
    except FileNotFoundError:
        print("✗ Java not found")
    
    return adb_available, java_available

def main():
    """Main test runner function."""
    print("=== Clipsy All Tests Runner ===")
    print("Running tests for both Android and Windows applications...")
    
    # Check prerequisites
    adb_available, java_available = check_prerequisites()
    
    results = []
    
    # Run Windows tests
    windows_success = run_windows_tests()
    results.append(("Windows", windows_success))
    
    # Run Android tests (only if prerequisites are available)
    if adb_available and java_available:
        android_success = run_android_tests()
        results.append(("Android", android_success))
    else:
        print("\n=== Skipping Android Tests ===")
        print("Android SDK or Java not found. Please install them to run Android tests.")
        results.append(("Android", None))  # None means skipped
    
    # Print summary
    print("\n" + "="*50)
    print("=== Test Results Summary ===")
    print("="*50)
    
    all_passed = True
    for platform_name, success in results:
        if success is True:
            print(f"✓ {platform_name}: PASSED")
        elif success is False:
            print(f"✗ {platform_name}: FAILED")
            all_passed = False
        else:
            print(f"⚠ {platform_name}: SKIPPED")
    
    print("="*50)
    
    if all_passed and all(result[1] is not None for result in results):
        print("🎉 All tests passed!")
        sys.exit(0)
    elif any(result[1] is False for result in results):
        print("❌ Some tests failed!")
        sys.exit(1)
    else:
        print("⚠️  Some tests were skipped due to missing prerequisites.")
        sys.exit(0)

if __name__ == "__main__":
    main()
