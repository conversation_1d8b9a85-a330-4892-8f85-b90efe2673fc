package com.clipsy.android.ui.fragments

import android.app.AlertDialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.clipsy.android.R
import com.clipsy.android.databinding.FragmentDevicesBinding
import com.clipsy.android.ui.adapters.BluetoothStyleDeviceAdapter
import com.clipsy.android.viewmodel.MainViewModel
import com.clipsy.android.ui.MainActivity
import com.clipsy.android.data.model.ConnectionStatus

import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import android.util.Log
/**
 * Fragment for displaying and managing discovered devices.
 */
class DevicesFragment : Fragment() {

    companion object {
        private const val PREFS_NAME = "clipsy_sync_prefs"
        private const val KEY_SYNC_ENABLED = "sync_enabled"
    }

    private var _binding: FragmentDevicesBinding? = null
    private val binding get() = _binding!!

    private val viewModel: MainViewModel by activityViewModels()
    private lateinit var connectedDevicesAdapter: BluetoothStyleDeviceAdapter
    private lateinit var availableDevicesAdapter: BluetoothStyleDeviceAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDevicesBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        setupObservers()
        setupClickListeners()
    }
    
    private fun setupRecyclerView() {
        // Connected devices adapter
        connectedDevicesAdapter = BluetoothStyleDeviceAdapter(
            onDeviceClick = { device ->
                handleDeviceClick(device)
            },
            onInfoClick = { device ->
                showDeviceInfoDialog(device)
            }
        )

        // Available devices adapter
        availableDevicesAdapter = BluetoothStyleDeviceAdapter(
            onDeviceClick = { device ->
                handleDeviceClick(device)
            },
            onInfoClick = { device ->
                showDeviceInfoDialog(device)
            }
        )

        binding.recyclerViewConnectedDevices.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = connectedDevicesAdapter
        }

        binding.recyclerViewAvailableDevices.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = availableDevicesAdapter
        }
    }
    
    private fun setupObservers() {
        // Discovered devices
        viewModel.discoveredDevices?.observe(viewLifecycleOwner) { devices ->
            separateDevicesByConnectionStatus(devices)
        }

        // Connection states
        viewModel.connectionStates?.observe(viewLifecycleOwner) { states ->
            connectedDevicesAdapter.updateConnectionStates(states)
            availableDevicesAdapter.updateConnectionStates(states)
            // Re-separate devices when connection states change
            viewModel.discoveredDevices?.value?.let { devices ->
                separateDevicesByConnectionStatus(devices)
            }
        }

        // Discovery status
        viewModel.isDiscovering.observe(viewLifecycleOwner) { isDiscovering ->
            binding.swipeRefreshLayout.isRefreshing = isDiscovering
            // Show/hide scanning progress bar
            binding.progressScanning.visibility = if (isDiscovering) View.VISIBLE else View.GONE
        }
    }

    private fun separateDevicesByConnectionStatus(devices: List<com.clipsy.android.data.model.Device>) {
        val connectionStates = viewModel.connectionStates?.value ?: emptyMap()

        val connectedDevices = devices.filter { device ->
            val status = connectionStates[device.getUniqueId()]?.status
            status == ConnectionStatus.CONNECTED || status == ConnectionStatus.SYNCING
        }

        val availableDevices = devices.filter { device ->
            val status = connectionStates[device.getUniqueId()]?.status
            status != ConnectionStatus.CONNECTED && status != ConnectionStatus.SYNCING
        }

        connectedDevicesAdapter.submitList(connectedDevices)
        availableDevicesAdapter.submitList(availableDevices)

        // Update section visibility
        binding.headerConnectedDevices.visibility = if (connectedDevices.isNotEmpty()) View.VISIBLE else View.GONE
        binding.recyclerViewConnectedDevices.visibility = if (connectedDevices.isNotEmpty()) View.VISIBLE else View.GONE

        // Show "No available devices" card if no available devices
        binding.cardNoAvailableDevices.visibility = if (availableDevices.isEmpty()) View.VISIBLE else View.GONE
    }
    
    private fun setupClickListeners() {
        binding.fabAddDevice.setOnClickListener {
            showAddManualDeviceDialog()
        }

        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.refreshDevices()
        }

        binding.buttonRefresh.setOnClickListener {
            viewModel.refreshDevices()
        }

        binding.cardDeviceName.setOnClickListener {
            showChangeDeviceNameDialog()
        }

        binding.switchSync.setOnCheckedChangeListener { _, isChecked ->
            // Save sync state
            saveSyncState(isChecked)

            // Handle sync toggle
            if (isChecked) {
                // Enable sync functionality
                enableSyncMode()
                Toast.makeText(requireContext(), "Sync enabled", Toast.LENGTH_SHORT).show()
            } else {
                // Disable sync functionality
                disableSyncMode()
                Toast.makeText(requireContext(), "Sync disabled", Toast.LENGTH_SHORT).show()
            }
        }

        // Initialize with saved sync state (default to enabled for first time)
        val savedSyncState = getSyncState()
        binding.switchSync.isChecked = savedSyncState
        if (savedSyncState) {
            enableSyncMode()
        } else {
            disableSyncMode()
        }
    }
    
    private fun handleDeviceClick(device: com.clipsy.android.data.model.Device) {
        val connectionStates = viewModel.connectionStates?.value ?: emptyMap()
        val status = connectionStates[device.getUniqueId()]?.status

        when (status) {
            ConnectionStatus.CONNECTED, ConnectionStatus.SYNCING -> {
                // Device is connected, show disconnect option
                showDisconnectDialog(device)
            }
            else -> {
                // Device is not connected, try to connect
                connectToDevice(device)
            }
        }
    }

    private fun showDisconnectDialog(device: com.clipsy.android.data.model.Device) {
        AlertDialog.Builder(requireContext())
            .setTitle("Disconnect Device")
            .setMessage("Disconnect from '${device.deviceName}'?")
            .setPositiveButton("Disconnect") { _, _ ->
                disconnectFromDevice(device)
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun showDeviceInfoDialog(device: com.clipsy.android.data.model.Device) {
        val connectionStates = viewModel.connectionStates?.value ?: emptyMap()
        val status = connectionStates[device.getUniqueId()]?.status ?: ConnectionStatus.DISCONNECTED

        val message = buildString {
            append("Device: ${device.deviceName}\n")
            append("IP Address: ${device.ip}\n")
            append("Type: ${device.deviceType}\n")
            append("Status: ${status.name.lowercase().replaceFirstChar { it.uppercase() }}\n")
            if (!device.manual) {
                append("Last Seen: ${device.getLastSeenFormatted()}")
            } else {
                append("Source: Manual")
            }
        }

        AlertDialog.Builder(requireContext())
            .setTitle("Device Information")
            .setMessage(message)
            .setPositiveButton("Connect") { _, _ ->
                connectToDevice(device)
            }
            .setNeutralButton("Refresh Status") { _, _ ->
                refreshDeviceStatus(device)
            }
            .setNegativeButton("Close", null)
            .show()
    }

    private fun showChangeDeviceNameDialog() {
        val editText = EditText(requireContext()).apply {
            setText(binding.textCurrentDeviceName.text)
            hint = "Enter device name"
        }

        AlertDialog.Builder(requireContext())
            .setTitle("Change Device Name")
            .setView(editText)
            .setPositiveButton("Save") { _, _ ->
                val newName = editText.text.toString().trim()
                if (newName.isNotEmpty()) {
                    binding.textCurrentDeviceName.text = newName
                    Toast.makeText(requireContext(), "Device name updated", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    private fun showAddManualDeviceDialog() {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_add_manual_device, null)
        
        val editTextIp = dialogView.findViewById<EditText>(R.id.edit_text_ip)
        val editTextName = dialogView.findViewById<EditText>(R.id.edit_text_name)
        
        AlertDialog.Builder(requireContext())
            .setTitle("Add Manual Device")
            .setView(dialogView)
            .setPositiveButton("Add") { _, _ ->
                val ip = editTextIp.text.toString().trim()
                val name = editTextName.text.toString().trim()
                
                if (ip.isNotEmpty()) {
                    viewModel.addManualDevice(ip, name)
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    private fun showRemoveDeviceDialog(device: com.clipsy.android.data.model.Device) {
        AlertDialog.Builder(requireContext())
            .setTitle("Remove Device")
            .setMessage("Are you sure you want to remove '${device.deviceName}'?")
            .setPositiveButton("Remove") { _, _ ->
                viewModel.removeDevice(device)
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun enableSyncMode() {
        // Show all UI elements below the sync toggle
        binding.cardDeviceName.visibility = View.VISIBLE
        binding.textDescription.visibility = View.VISIBLE
        binding.headerConnectedDevices.visibility = View.VISIBLE
        binding.recyclerViewConnectedDevices.visibility = View.VISIBLE
        binding.headerAvailableDevices.visibility = View.VISIBLE
        binding.recyclerViewAvailableDevices.visibility = View.VISIBLE
        binding.cardNoAvailableDevices.visibility = View.VISIBLE
        binding.fabAddDevice.visibility = View.VISIBLE
        binding.buttonRefresh.visibility = View.VISIBLE

        // Start sync service
        val connectionManager = getConnectionManager()
        connectionManager?.startSyncService()

        // Refresh devices when sync is enabled
        viewModel.refreshDevices()
    }

    private fun disableSyncMode() {
        // Hide all UI elements below the sync toggle
        binding.cardDeviceName.visibility = View.GONE
        binding.textDescription.visibility = View.GONE
        binding.headerConnectedDevices.visibility = View.GONE
        binding.recyclerViewConnectedDevices.visibility = View.GONE
        binding.headerAvailableDevices.visibility = View.GONE
        binding.recyclerViewAvailableDevices.visibility = View.GONE
        binding.cardNoAvailableDevices.visibility = View.GONE
        binding.fabAddDevice.visibility = View.GONE
        binding.buttonRefresh.visibility = View.GONE

        // Hide scanning progress bar
        binding.progressScanning.visibility = View.GONE

        // Stop sync service
        val connectionManager = getConnectionManager()
        connectionManager?.stopSyncService()

        // Disconnect from all devices
        connectionManager?.disconnectFromAllDevices()
    }

    private fun saveSyncState(enabled: Boolean) {
        val prefs = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putBoolean(KEY_SYNC_ENABLED, enabled).apply()
    }

    private fun getSyncState(): Boolean {
        val prefs = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getBoolean(KEY_SYNC_ENABLED, true) // Default to enabled
    }

    private fun getConnectionManager() = (activity as? MainActivity)?.getConnectionManager()

    private fun connectToDevice(device: com.clipsy.android.data.model.Device) {
        Log.d("DevicesFragment", "Connecting to ${device.deviceName}")

        // Use the ViewModel to trigger the actual connection
        viewModel.connectToDevice(device)
        Toast.makeText(requireContext(), "Connecting to ${device.deviceName}...", Toast.LENGTH_SHORT).show()
    }

    private fun refreshDeviceStatus(device: com.clipsy.android.data.model.Device) {
        lifecycleScope.launch {
            viewModel.getDeviceRepository()?.refreshConnectionStatus(device)
            Toast.makeText(requireContext(), "Status refreshed", Toast.LENGTH_SHORT).show()
        }
    }

    private fun disconnectFromDevice(device: com.clipsy.android.data.model.Device) {
        Log.d("DevicesFragment", "Disconnecting from ${device.deviceName}")

        // Use the ViewModel to trigger the actual disconnection
        viewModel.disconnectFromDevice(device)
        Toast.makeText(requireContext(), "Disconnected from ${device.deviceName}", Toast.LENGTH_SHORT).show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
