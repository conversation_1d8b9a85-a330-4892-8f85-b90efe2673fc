#!/usr/bin/env python3
"""
Test script to measure clipboard sync timing between PC and Android.
This script will help identify if the timing optimizations are working.
"""

import time
import pyperclip
import subprocess
import threading
import sys
from datetime import datetime

class SyncTimingTest:
    def __init__(self):
        self.test_content = f"Test sync content at {datetime.now().strftime('%H:%M:%S.%f')}"
        self.sync_detected = False
        self.sync_start_time = None
        self.sync_end_time = None
        
    def monitor_android_logs(self):
        """Monitor Android logs for clipboard sync events."""
        try:
            # Start monitoring Android logs
            process = subprocess.Popen(
                ['adb', 'logcat', '-s', 'ClipboardSyncService'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )
            
            print("📱 Monitoring Android logs for sync events...")
            
            for line in iter(process.stdout.readline, ''):
                if line.strip():
                    print(f"📱 {line.strip()}")
                    
                    # Look for clipboard update events
                    if "updateLocalClipboard called" in line:
                        if not self.sync_detected:
                            self.sync_end_time = time.time()
                            self.sync_detected = True
                            sync_duration = self.sync_end_time - self.sync_start_time
                            print(f"🎯 SYNC DETECTED! Duration: {sync_duration:.2f} seconds")
                            break
                            
        except Exception as e:
            print(f"❌ Error monitoring Android logs: {e}")
    
    def test_pc_to_android_sync(self):
        """Test clipboard sync from PC to Android."""
        print("\n🔄 Testing PC to Android sync...")
        print(f"📋 Test content: {self.test_content}")
        
        # Start monitoring Android logs in background
        log_thread = threading.Thread(target=self.monitor_android_logs, daemon=True)
        log_thread.start()
        
        # Wait a moment for log monitoring to start
        time.sleep(1)
        
        # Record start time and copy to PC clipboard
        self.sync_start_time = time.time()
        print(f"⏰ Starting sync at {datetime.now().strftime('%H:%M:%S.%f')}")
        
        pyperclip.copy(self.test_content)
        print("📋 Content copied to PC clipboard")
        
        # Wait for sync detection or timeout
        timeout = 30  # 30 seconds timeout
        start_wait = time.time()
        
        while not self.sync_detected and (time.time() - start_wait) < timeout:
            time.sleep(0.1)
        
        if self.sync_detected:
            sync_duration = self.sync_end_time - self.sync_start_time
            print(f"✅ Sync completed in {sync_duration:.2f} seconds")
            
            if sync_duration < 5:
                print("🚀 EXCELLENT: Sync is very fast!")
            elif sync_duration < 15:
                print("👍 GOOD: Sync is reasonably fast")
            elif sync_duration < 60:
                print("⚠️ SLOW: Sync took longer than expected")
            else:
                print("❌ VERY SLOW: Sync took too long")
        else:
            print(f"❌ Sync not detected within {timeout} seconds")
            
        return self.sync_detected, self.sync_end_time - self.sync_start_time if self.sync_detected else None

def main():
    print("Clipboard Sync Timing Test")
    print("=" * 50)
    
    # Check if ADB is available
    try:
        subprocess.run(['adb', 'devices'], capture_output=True, check=True)
        print("ADB is available")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("ADB not found. Please ensure Android device is connected and ADB is in PATH.")
        sys.exit(1)

    # Check if pyperclip is available
    try:
        import pyperclip
        print("pyperclip is available")
    except ImportError:
        print("pyperclip not found. Please install: pip install pyperclip")
        sys.exit(1)
    
    # Run the test
    test = SyncTimingTest()
    success, duration = test.test_pc_to_android_sync()
    
    print("\n📊 Test Results:")
    print(f"Success: {success}")
    if duration:
        print(f"Duration: {duration:.2f} seconds")
    
    print("\n💡 Tips to improve sync speed:")
    print("- Ensure Clipsy app is in foreground on Android")
    print("- Check that both devices are on the same WiFi network")
    print("- Restart both PC and Android apps if sync is slow")

if __name__ == "__main__":
    main()
