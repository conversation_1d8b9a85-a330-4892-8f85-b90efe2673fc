#!/usr/bin/env python3
"""
Test client for minimal WebSocket server
"""

import asyncio
import websockets
import logging
import sys

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_connection():
    """Test connection to minimal WebSocket server"""
    url = "ws://192.168.1.161:8766"
    
    logger.info(f"Connecting to {url}")
    
    try:
        async with websockets.connect(url, timeout=5) as websocket:
            logger.info("✅ Connected successfully!")
            
            # Receive welcome message
            welcome = await websocket.recv()
            logger.info(f"📥 Received: {welcome}")
            
            # Send test message
            test_msg = "Hello from client!"
            logger.info(f"📤 Sending: {test_msg}")
            await websocket.send(test_msg)
            
            # Receive echo
            echo = await websocket.recv()
            logger.info(f"📥 Received echo: {echo}")
            
            logger.info("✅ Test completed successfully!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_connection())
    sys.exit(0 if success else 1)
