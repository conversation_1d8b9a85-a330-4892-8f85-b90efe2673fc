package com.clipsy.android.network

import android.content.Context
import android.net.wifi.WifiManager
import android.util.Log
import com.clipsy.android.data.model.Device
import com.clipsy.android.data.model.ConnectionStatus
import com.clipsy.android.data.repository.DeviceRepository
import kotlinx.coroutines.*
import org.json.JSONObject
import java.net.*
/**
 * Handles device discovery using UDP broadcast.
 */
class DeviceDiscovery(
    private val context: Context,
    private val deviceRepository: DeviceRepository
) {
    companion object {
        private const val TAG = "DeviceDiscovery"
        private const val DISCOVERY_PORT = 8765
        private const val BROADCAST_INTERVAL = 2000L // 2 seconds - faster discovery
        private const val DISCOVERY_TIMEOUT = 10000L // 10 seconds - faster timeout
    }

    private var discoveryJob: Job? = null
    private var broadcastJob: Job? = null
    private var socket: DatagramSocket? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    /**
     * Start device discovery using network scanning.
     */
    fun startDiscovery() {
        stopDiscovery() // Stop any existing discovery

        discoveryJob = scope.launch {
            try {
                // Setup UDP socket for listening to PC discovery requests
                setupSocket()

                // Start listening for PC discovery requests
                launch { startListening() }

                // Start network scanning to find PC
                startNetworkScanning()
            } catch (e: Exception) {
                Log.e(TAG, "Error in device discovery", e)
            }
        }
    }

    /**
     * Stop device discovery.
     */
    fun stopDiscovery() {
        discoveryJob?.cancel()
        broadcastJob?.cancel()

        socket?.close()
        socket = null
    }

    /**
     * Setup UDP socket for discovery.
     */
    private suspend fun setupSocket() = withContext(Dispatchers.IO) {
        try {
            // Create socket to listen for broadcasts on discovery port
            socket = DatagramSocket(DISCOVERY_PORT).apply {
                broadcast = true
                reuseAddress = true
                soTimeout = 2000 // 2 second timeout for receive operations - faster
            }
        } catch (e: Exception) {
            // Try alternative approach - create unbound socket
            try {
                val unboundSocket = DatagramSocket().apply {
                    broadcast = true
                    reuseAddress = true
                    soTimeout = 2000 // Faster timeout
                }
                socket = unboundSocket
            } catch (e2: Exception) {
                throw e2
            }
        }
    }

    /**
     * Start broadcasting discovery messages.
     */
    private fun startBroadcasting() {
        broadcastJob = scope.launch {
            while (isActive) {
                try {
                    sendDiscoveryBroadcast()
                    delay(BROADCAST_INTERVAL)
                } catch (e: Exception) {
                    delay(BROADCAST_INTERVAL)
                }
            }
        }
    }

    /**
     * Start listening for discovery responses.
     */
    private suspend fun startListening() = withContext(Dispatchers.IO) {
        val buffer = ByteArray(1024)
        val packet = DatagramPacket(buffer, buffer.size)

        while (isActive) {
            try {
                socket?.receive(packet)
                val message = String(packet.data, 0, packet.length)
                val senderIp = packet.address.hostAddress

                if (senderIp != null && senderIp != getLocalIpAddress()) {
                    handleDiscoveryMessage(message, senderIp)
                }
            } catch (e: SocketTimeoutException) {
                // Normal timeout, continue listening
            } catch (e: Exception) {
                // Ignore receive errors when inactive
            }
        }
    }

    /**
     * Send discovery broadcast message.
     */
    private suspend fun sendDiscoveryBroadcast() {
        withContext(Dispatchers.IO) {
        try {
            val message = createDiscoveryMessage()
            val broadcastAddress = getBroadcastAddress()

            if (broadcastAddress != null) {
                val data = message.toByteArray()
                val packet = DatagramPacket(
                    data,
                    data.size,
                    broadcastAddress,
                    DISCOVERY_PORT
                )

                socket?.send(packet)
            }
        } catch (e: Exception) {
            // Ignore broadcast errors
        }
        }
    }

    /**
     * Handle received discovery message.
     */
    private suspend fun handleDiscoveryMessage(message: String, senderIp: String) {
        try {
            val json = JSONObject(message)
            val type = json.getString("type")

            when (type) {
                "discovery" -> {
                    // PC is broadcasting its presence, respond back
                    sendDiscoveryResponse(senderIp)

                    // Also process this as a discovered PC device
                    processDiscoveryResponse(json, senderIp)
                }
                "discovery_request" -> {
                    // Respond to discovery request
                    sendDiscoveryResponse(senderIp)
                }
                "discovery_response" -> {
                    // Process discovery response
                    processDiscoveryResponse(json, senderIp)
                }
            }
        } catch (e: Exception) {
            // Ignore message handling errors
        }
    }

    /**
     * Send discovery response to a specific IP.
     */
    private suspend fun sendDiscoveryResponse(targetIp: String) = withContext(Dispatchers.IO) {
        try {
            val message = createDiscoveryResponse()
            val data = message.toByteArray()
            val packet = DatagramPacket(
                data,
                data.size,
                InetAddress.getByName(targetIp),
                DISCOVERY_PORT
            )

            socket?.send(packet)
        } catch (e: Exception) {
            // Ignore response send errors
        }
    }

    /**
     * Process discovery response and add device.
     */
    private suspend fun processDiscoveryResponse(json: JSONObject, senderIp: String) {
        try {
            val deviceName = json.getString("device_name")
            val deviceType = json.getString("device_type")
            val websocketPort = json.getInt("websocket_port")
            val deviceId = json.optString("device_id", "")

            // Check if we already have a manual device for this IP
            val existingDevice = deviceRepository.getDeviceByIp(senderIp)

            val device = Device(
                ip = senderIp,
                deviceName = deviceName,
                deviceType = deviceType,
                websocketPort = websocketPort,
                deviceId = deviceId.ifEmpty { null }, // Let getUniqueId() use device name for deduplication
                lastSeen = System.currentTimeMillis(),
                manual = existingDevice?.manual ?: false
            )

            deviceRepository.addOrUpdateDevice(device)

            // Auto-connect only to known devices for security
            val connectionManager = deviceRepository.getConnectionManager()
            if (connectionManager != null && connectionManager.isDeviceConnected(device.getUniqueId())) {
                // Device is already connected
            }

        } catch (e: Exception) {
            // Ignore processing errors
        }
    }

    /**
     * Create discovery request message.
     */
    private fun createDiscoveryMessage(): String {
        return JSONObject().apply {
            put("type", "discovery")  // Fixed: Changed from "discovery_request" to "discovery"
            put("device_name", getDeviceName())
            put("device_type", "android")
            put("websocket_port", Device.ANDROID_SERVER_PORT)  // Android's server port
            put("device_id", getDeviceId())
            put("timestamp", System.currentTimeMillis())
        }.toString()
    }

    /**
     * Create discovery response message.
     */
    private fun createDiscoveryResponse(): String {
        return JSONObject().apply {
            put("type", "discovery")  // PC expects "discovery" type
            put("device_name", getDeviceName())
            put("device_type", "android")
            put("websocket_port", Device.ANDROID_SERVER_PORT)  // Android's server port
            put("device_id", getDeviceId())
            put("timestamp", System.currentTimeMillis())
        }.toString()
    }

    /**
     * Get local IP address.
     */
    fun getLocalIpAddress(): String? {
        try {
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val wifiInfo = wifiManager.connectionInfo
            val ipInt = wifiInfo.ipAddress

            return if (ipInt != 0) {
                String.format(
                    "%d.%d.%d.%d",
                    ipInt and 0xff,
                    ipInt shr 8 and 0xff,
                    ipInt shr 16 and 0xff,
                    ipInt shr 24 and 0xff
                )
            } else {
                null
            }
        } catch (e: Exception) {
            return null
        }
    }

    /**
     * Get broadcast address.
     */
    private fun getBroadcastAddress(): InetAddress? {
        try {
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val dhcp = wifiManager.dhcpInfo

            val broadcast = (dhcp.ipAddress and dhcp.netmask) or dhcp.netmask.inv()
            val bytes = ByteArray(4)
            for (k in 0..3) {
                bytes[k] = (broadcast shr k * 8 and 0xFF).toByte()
            }

            return InetAddress.getByAddress(bytes)
        } catch (e: Exception) {
            return null
        }
    }

    /**
     * Get device name.
     */
    private fun getDeviceName(): String {
        return android.os.Build.MODEL ?: "Android Device"
    }

    /**
     * Get device ID.
     */
    private fun getDeviceId(): String {
        return android.provider.Settings.Secure.getString(
            context.contentResolver,
            android.provider.Settings.Secure.ANDROID_ID
        ) ?: "unknown"
    }

    /**
     * Start network scanning to discover devices.
     */
    private suspend fun startNetworkScanning() = withContext(Dispatchers.IO) {
        try {
            val localIp = getLocalIpAddress()
            if (localIp == null) {
                return@withContext
            }

            // Get all network ranges to scan
            val networkRanges = getNetworkRangesToScan(localIp)

            // Scan all network ranges
            val scanJobs = mutableListOf<Job>()

            for (networkPrefix in networkRanges) {
                for (i in 1..254) {
                    val targetIp = "$networkPrefix.$i"

                    // Skip our own IP
                    if (targetIp == localIp) continue

                    val job = scope.launch {
                        try {
                            scanDeviceAt(targetIp)
                        } catch (e: Exception) {
                            // Ignore individual scan failures
                        }
                    }
                    scanJobs.add(job)

                    // Limit concurrent scans to avoid overwhelming the network
                    if (scanJobs.size >= 30) {
                        scanJobs.joinAll()
                        scanJobs.clear()
                    }
                }
            }

            // Wait for remaining scans
            scanJobs.joinAll()

        } catch (e: Exception) {
            // Ignore scanning errors
        }
    }

    /**
     * Get network ranges to scan based on local IP and common network configurations.
     */
    private fun getNetworkRangesToScan(localIp: String): List<String> {
        val ranges = mutableSetOf<String>()

        // Add the current network range
        val currentNetwork = localIp.substringBeforeLast(".")
        ranges.add(currentNetwork)

        // Add common network ranges that might be used
        ranges.add("192.168.1")    // Common home Wi-Fi
        ranges.add("192.168.0")    // Common home Wi-Fi
        ranges.add("169.254")      // Auto-configuration (APIPA)
        ranges.add("10.0.0")       // Common private network
        ranges.add("172.16.0")     // Private network range

        return ranges.toList()
    }

    /**
     * Scan a specific IP address for Clipsy service.
     */
    private suspend fun scanDeviceAt(ipAddress: String) = withContext(Dispatchers.IO) {
        // Try both Windows (8766) and Android (8765) ports
        val portsToScan = listOf(
            Pair(8766, "windows"),
            Pair(8765, "android")
        )

        for ((port, deviceType) in portsToScan) {
            try {
                val socket = java.net.Socket()
                socket.connect(java.net.InetSocketAddress(ipAddress, port), 1000) // 1 second timeout - faster
                socket.close()

                // If connection succeeds, this is likely a Clipsy device
                val deviceName = when (deviceType) {
                    "windows" -> "Windows-PC"
                    "android" -> "Android-Device"
                    else -> "Unknown-Device"
                }

                // Create device with null deviceId so getUniqueId() uses device name
                val device = Device(
                    ip = ipAddress,
                    deviceName = deviceName,
                    deviceType = deviceType,
                    websocketPort = port,
                    lastSeen = System.currentTimeMillis(),
                    deviceId = null  // Use device name for deduplication
                )

                // Add discovered device
                deviceRepository.addOrUpdateDevice(device)

                // Auto-connect only to known devices for security
                val connectionManager = deviceRepository.getConnectionManager()
                if (connectionManager != null && connectionManager.isDeviceConnected(device.getUniqueId())) {
                    // Device is already connected
                }

                // Found a device on this port, no need to check other ports for this IP
                break

            } catch (e: Exception) {
                // Connection failed - try next port
                continue
            }
        }
    }

    /**
     * Clean up resources.
     */
    fun cleanup() {
        stopDiscovery()
        scope.cancel()
    }
}
