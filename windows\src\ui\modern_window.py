"""
Modern PyQt6-based main window for Clipsy Windows application.
Features Material Design principles with clean, minimal, responsive layout.
"""

import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QListWidget, QTextEdit, QFrame, QScrollArea,
    QStackedWidget, QSplitter, QGroupBox, QFormLayout, QLineEdit,
    QCheckBox, QSpinBox, QComboBox, QProgressBar, QSystemTrayIcon,
    QMenu, QMessageBox, QListWidgetItem, QTreeWidget, QTreeWidgetItem,
    QInputDialog, QFileDialog
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QIcon, QPalette, QColor, QPixmap, QAction
import asyncio
import threading

import logging


class ModernCard(QFrame):
    """A modern card widget with Material Design styling."""
    
    def __init__(self, title: str = "", parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet("""
            ModernCard {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 16px;
                margin: 8px;
            }
            ModernCard[dark="true"] {
                background-color: #2d2d2d;
                border: 1px solid #404040;
            }
        """)
        
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(16, 16, 16, 16)
        self.layout.setSpacing(12)
        
        if title:
            self.title_label = QLabel(title)
            self.title_label.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
            self.title_label.setStyleSheet("color: #1976d2; margin-bottom: 8px;")
            self.layout.addWidget(self.title_label)


class ModernButton(QPushButton):
    """A modern button with Material Design styling."""
    
    def __init__(self, text: str, style: str = "primary", parent=None):
        super().__init__(text, parent)
        self.setMinimumHeight(36)
        self.setFont(QFont("Segoe UI", 10, QFont.Weight.Medium))
        
        if style == "primary":
            self.setStyleSheet("""
                ModernButton {
                    background-color: #1976d2;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                }
                ModernButton:hover {
                    background-color: #1565c0;
                }
                ModernButton:pressed {
                    background-color: #0d47a1;
                }
            """)
        elif style == "secondary":
            self.setStyleSheet("""
                ModernButton {
                    background-color: transparent;
                    color: #1976d2;
                    border: 1px solid #1976d2;
                    border-radius: 6px;
                    padding: 8px 16px;
                }
                ModernButton:hover {
                    background-color: #e3f2fd;
                }
                ModernButton:pressed {
                    background-color: #bbdefb;
                }
            """)


class SidebarButton(QPushButton):
    """A sidebar navigation button."""
    
    def __init__(self, text: str, icon_path: str = "", parent=None):
        super().__init__(text, parent)
        self.setCheckable(True)
        self.setMinimumHeight(48)
        self.setFont(QFont("Segoe UI", 11))
        self.setStyleSheet("""
            SidebarButton {
                text-align: left;
                padding: 12px 20px;
                border: none;
                border-radius: 6px;
                margin: 2px 8px;
                background-color: transparent;
                color: #424242;
            }
            SidebarButton:hover {
                background-color: #f5f5f5;
            }
            SidebarButton:checked {
                background-color: #e3f2fd;
                color: #1976d2;
                font-weight: bold;
            }
        """)


class StatusIndicator(QWidget):
    """A modern status indicator with color and text."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(8)
        
        self.indicator = QFrame()
        self.indicator.setFixedSize(12, 12)
        self.indicator.setStyleSheet("border-radius: 6px; background-color: #9e9e9e;")
        
        self.label = QLabel("Disconnected")
        self.label.setFont(QFont("Segoe UI", 9))
        
        self.layout.addWidget(self.indicator)
        self.layout.addWidget(self.label)
        self.layout.addStretch()
    
    def set_status(self, status: str, color: str):
        """Set the status indicator color and text."""
        self.indicator.setStyleSheet(f"border-radius: 6px; background-color: {color};")
        self.label.setText(status)


class ClipsyModernWindow(QMainWindow):
    """Modern main window for Clipsy with Material Design."""
    
    def __init__(self, config: dict, app):
        super().__init__()
        self.config = config
        self.app = app
        self.logger = logging.getLogger(__name__)
        
        # UI state
        self.current_page = "devices"
        self.is_dark_theme = config.get("ui", {}).get("theme", "light") == "dark"
        
        # Initialize UI
        self.init_ui()
        self.setup_theme()
        self.setup_connections()
        
        # Start periodic updates
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_ui)
        self.update_timer.start(1000)  # Update every second
    
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle("Clipsy - Modern Clipboard Manager")
        self.setMinimumSize(900, 600)
        self.resize(
            self.config.get("ui", {}).get("window_width", 1000),
            self.config.get("ui", {}).get("window_height", 700)
        )
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create sidebar
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)
        
        # Create main content area
        self.create_content_area()
        main_layout.addWidget(self.content_area, 1)
        
        # Create status bar
        self.create_status_bar()
    
    def create_sidebar(self):
        """Create the modern sidebar navigation."""
        self.sidebar = QFrame()
        self.sidebar.setFixedWidth(240)
        self.sidebar.setStyleSheet("""
            QFrame {
                background-color: #fafafa;
                border-right: 1px solid #e0e0e0;
            }
        """)
        
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(0, 20, 0, 20)
        sidebar_layout.setSpacing(4)
        
        # App title
        title_label = QLabel("Clipsy")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1976d2; padding: 20px; margin-bottom: 20px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        sidebar_layout.addWidget(title_label)
        
        # Navigation buttons
        self.nav_buttons = {}
        nav_items = [
            ("devices", "Devices", "🖥️"),
            ("history", "History", "📋"),
            ("settings", "Settings", "⚙️")
        ]
        
        for key, text, icon in nav_items:
            btn = SidebarButton(f"{icon}  {text}")
            btn.clicked.connect(lambda checked, k=key: self.switch_page(k))
            self.nav_buttons[key] = btn
            sidebar_layout.addWidget(btn)
        
        # Set default selection
        self.nav_buttons["devices"].setChecked(True)
        
        sidebar_layout.addStretch()
        
        # Connection status
        self.connection_status = StatusIndicator()
        sidebar_layout.addWidget(self.connection_status)
    
    def create_content_area(self):
        """Create the main content area with stacked pages."""
        self.content_area = QStackedWidget()
        
        # Create pages
        self.create_devices_page()
        self.create_history_page()
        self.create_settings_page()
        
        # Add pages to stack
        self.content_area.addWidget(self.devices_page)
        self.content_area.addWidget(self.history_page)
        self.content_area.addWidget(self.settings_page)
    
    def create_devices_page(self):
        """Create the modern devices page."""
        self.devices_page = QWidget()
        layout = QVBoxLayout(self.devices_page)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)
        
        # Page title
        title = QLabel("Device Management")
        title.setFont(QFont("Segoe UI", 20, QFont.Weight.Bold))
        title.setStyleSheet("color: #212121; margin-bottom: 8px;")
        layout.addWidget(title)
        
        # Local IP card
        ip_card = ModernCard("Local Network Information")
        self.local_ip_label = QLabel("Detecting network...")
        self.local_ip_label.setFont(QFont("Segoe UI", 11))
        self.local_ip_label.setStyleSheet("color: #424242; padding: 8px;")
        ip_card.layout.addWidget(self.local_ip_label)
        layout.addWidget(ip_card)
        
        # Discovered devices card
        devices_card = ModernCard("Discovered Devices")
        
        # Device controls
        controls_layout = QHBoxLayout()
        self.refresh_btn = ModernButton("🔄 Refresh", "primary")
        self.add_manual_btn = ModernButton("➕ Add Device", "secondary")
        controls_layout.addWidget(self.refresh_btn)
        controls_layout.addWidget(self.add_manual_btn)
        controls_layout.addStretch()
        devices_card.layout.addLayout(controls_layout)
        
        # Devices list
        self.devices_list = QListWidget()
        self.devices_list.setMinimumHeight(200)
        self.devices_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                background-color: #fafafa;
                padding: 8px;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #eeeeee;
                border-radius: 4px;
                margin: 2px;
            }
            QListWidget::item:hover {
                background-color: #f0f0f0;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        devices_card.layout.addWidget(self.devices_list)
        
        layout.addWidget(devices_card)
        layout.addStretch()

    def create_history_page(self):
        """Create the modern history page."""
        self.history_page = QWidget()
        layout = QVBoxLayout(self.history_page)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)

        # Page title
        title = QLabel("Clipboard History")
        title.setFont(QFont("Segoe UI", 20, QFont.Weight.Bold))
        title.setStyleSheet("color: #212121; margin-bottom: 8px;")
        layout.addWidget(title)

        # History controls card
        controls_card = ModernCard("Quick Actions")
        controls_layout = QHBoxLayout()

        self.clear_history_btn = ModernButton("🗑️ Clear History", "secondary")
        self.refresh_history_btn = ModernButton("🔄 Refresh", "primary")
        self.export_history_btn = ModernButton("📤 Export", "secondary")

        controls_layout.addWidget(self.clear_history_btn)
        controls_layout.addWidget(self.refresh_history_btn)
        controls_layout.addWidget(self.export_history_btn)
        controls_layout.addStretch()
        controls_card.layout.addLayout(controls_layout)
        layout.addWidget(controls_card)

        # History list card
        history_card = ModernCard("Recent Clipboard Items")

        self.history_tree = QTreeWidget()
        self.history_tree.setHeaderLabels(["Time", "Source", "Content Preview"])
        self.history_tree.setMinimumHeight(300)
        self.history_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                background-color: #fafafa;
                alternate-background-color: #f5f5f5;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eeeeee;
            }
            QTreeWidget::item:hover {
                background-color: #f0f0f0;
            }
            QTreeWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        history_card.layout.addWidget(self.history_tree)
        layout.addWidget(history_card)

        layout.addStretch()

    def create_settings_page(self):
        """Create the modern settings page."""
        self.settings_page = QWidget()

        # Create scroll area for settings
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)

        # Page title
        title = QLabel("Settings")
        title.setFont(QFont("Segoe UI", 20, QFont.Weight.Bold))
        title.setStyleSheet("color: #212121; margin-bottom: 8px;")
        layout.addWidget(title)

        # Network settings card
        network_card = ModernCard("Network Configuration")
        network_form = QFormLayout()

        self.discovery_port_input = QSpinBox()
        self.discovery_port_input.setRange(1024, 65535)
        self.discovery_port_input.setValue(self.config.get("network", {}).get("discovery_port", 8765))
        network_form.addRow("Discovery Port:", self.discovery_port_input)

        self.websocket_port_input = QSpinBox()
        self.websocket_port_input.setRange(1024, 65535)
        self.websocket_port_input.setValue(self.config.get("network", {}).get("websocket_port", 8766))
        network_form.addRow("WebSocket Port:", self.websocket_port_input)

        self.auto_discovery_check = QCheckBox("Enable automatic device discovery")
        self.auto_discovery_check.setChecked(self.config.get("network", {}).get("auto_discovery", True))
        network_form.addRow(self.auto_discovery_check)

        network_card.layout.addLayout(network_form)
        layout.addWidget(network_card)

        # Clipboard settings card
        clipboard_card = ModernCard("Clipboard Settings")
        clipboard_form = QFormLayout()

        self.auto_sync_check = QCheckBox("Enable automatic clipboard sync")
        self.auto_sync_check.setChecked(self.config.get("clipboard", {}).get("auto_sync", True))
        clipboard_form.addRow(self.auto_sync_check)

        self.history_enabled_check = QCheckBox("Enable clipboard history")
        self.history_enabled_check.setChecked(self.config.get("clipboard", {}).get("history_enabled", True))
        clipboard_form.addRow(self.history_enabled_check)

        self.history_limit_input = QSpinBox()
        self.history_limit_input.setRange(10, 1000)
        self.history_limit_input.setValue(self.config.get("clipboard", {}).get("history_limit", 100))
        clipboard_form.addRow("History Limit:", self.history_limit_input)

        clipboard_card.layout.addLayout(clipboard_form)
        layout.addWidget(clipboard_card)

        # UI settings card
        ui_card = ModernCard("User Interface")
        ui_form = QFormLayout()

        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Light", "Dark"])
        self.theme_combo.setCurrentText(self.config.get("ui", {}).get("theme", "light").title())
        ui_form.addRow("Theme:", self.theme_combo)

        self.minimize_to_tray_check = QCheckBox("Minimize to system tray")
        self.minimize_to_tray_check.setChecked(self.config.get("ui", {}).get("minimize_to_tray", True))
        ui_form.addRow(self.minimize_to_tray_check)

        self.show_notifications_check = QCheckBox("Show notifications")
        self.show_notifications_check.setChecked(self.config.get("ui", {}).get("show_notifications", True))
        ui_form.addRow(self.show_notifications_check)

        ui_card.layout.addLayout(ui_form)
        layout.addWidget(ui_card)

        # Apply button
        apply_layout = QHBoxLayout()
        apply_layout.addStretch()
        self.apply_settings_btn = ModernButton("💾 Apply Settings", "primary")
        apply_layout.addWidget(self.apply_settings_btn)
        layout.addLayout(apply_layout)

        layout.addStretch()

        scroll.setWidget(settings_widget)

        page_layout = QVBoxLayout(self.settings_page)
        page_layout.setContentsMargins(0, 0, 0, 0)
        page_layout.addWidget(scroll)

    def create_status_bar(self):
        """Create the modern status bar."""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #e0e0e0;
                padding: 8px;
            }
        """)

        # Status message
        self.status_message = QLabel("Ready")
        self.status_message.setFont(QFont("Segoe UI", 9))
        status_bar.addWidget(self.status_message)

        # Connection count
        self.connection_count = QLabel("No connections")
        self.connection_count.setFont(QFont("Segoe UI", 9))
        status_bar.addPermanentWidget(self.connection_count)

    def setup_theme(self):
        """Setup the application theme."""
        if self.is_dark_theme:
            self.apply_dark_theme()
        else:
            self.apply_light_theme()

    def apply_light_theme(self):
        """Apply light theme styling."""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ffffff;
                color: #212121;
            }
            QLabel {
                color: #212121;
            }
            QListWidget, QTreeWidget {
                background-color: #fafafa;
                border: 1px solid #e0e0e0;
            }
        """)

    def apply_dark_theme(self):
        """Apply dark theme styling."""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
            }
            QFrame {
                background-color: #2d2d2d;
                border: 1px solid #404040;
            }
            QListWidget, QTreeWidget {
                background-color: #2d2d2d;
                border: 1px solid #404040;
                color: #ffffff;
            }
            ModernCard {
                background-color: #2d2d2d;
                border: 1px solid #404040;
            }
        """)

    def setup_connections(self):
        """Setup signal connections."""
        # Navigation (already connected in create_sidebar)

        # Device page
        self.refresh_btn.clicked.connect(self.refresh_devices)
        self.add_manual_btn.clicked.connect(self.add_manual_device)

        # History page
        self.clear_history_btn.clicked.connect(self.clear_history)
        self.refresh_history_btn.clicked.connect(self.refresh_history)
        self.export_history_btn.clicked.connect(self.export_history)

        # Settings page
        self.apply_settings_btn.clicked.connect(self.apply_settings)
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)

    def switch_page(self, page_key: str):
        """Switch to a different page."""
        # Update navigation buttons
        for key, btn in self.nav_buttons.items():
            btn.setChecked(key == page_key)

        # Switch content
        page_index = {"devices": 0, "history": 1, "settings": 2}.get(page_key, 0)
        self.content_area.setCurrentIndex(page_index)
        self.current_page = page_key

    def update_ui(self):
        """Update UI elements periodically."""
        try:
            # Update local IP
            if self.app.device_discovery:
                local_ip = self.app.device_discovery.get_local_ip()
                if local_ip:
                    self.local_ip_label.setText(f"Local IP: {local_ip}")
                    self.status_message.setText("Ready")
                else:
                    self.local_ip_label.setText("Network not available")
                    self.status_message.setText("Network unavailable")

            # Update connection status
            if self.app.sync_server and self.app.sync_client:
                server_clients = self.app.sync_server.get_connected_clients_count()
                client_connections = len(self.app.sync_client.get_connected_devices())
                total_connections = server_clients + client_connections

                self.connection_count.setText(f"{total_connections} connection(s)")

                if total_connections > 0:
                    self.connection_status.set_status("Connected", "#4caf50")
                else:
                    self.connection_status.set_status("Disconnected", "#9e9e9e")

            # Update devices list
            self.update_devices_list()

            # Update history
            self.update_history_list()

        except Exception as e:
            self.logger.error(f"Error updating UI: {e}")

    def update_devices_list(self):
        """Update the devices list."""
        try:
            if not self.app.device_discovery:
                return

            # Clear current list
            self.devices_list.clear()

            # Get discovered devices
            devices = self.app.device_discovery.get_discovered_devices()

            for device_ip, device_info in devices.items():
                device_name = device_info.get('device_name', 'Unknown Device')
                device_type = device_info.get('device_type', 'unknown')
                last_seen = device_info.get('last_seen', 'Never')

                # Create list item
                item_text = f"{device_name} ({device_ip})\n{device_type.title()} • Last seen: {last_seen}"
                item = QListWidgetItem(item_text)
                item.setData(Qt.ItemDataRole.UserRole, device_ip)

                self.devices_list.addItem(item)

        except Exception as e:
            self.logger.error(f"Error updating devices list: {e}")

    def update_history_list(self):
        """Update the history list."""
        try:
            # This would be implemented based on the clipboard history system
            # For now, just ensure the tree is ready
            pass
        except Exception as e:
            self.logger.error(f"Error updating history list: {e}")

    # Action methods
    def refresh_devices(self):
        """Refresh the devices list."""
        try:
            if self.app.device_discovery:
                # Trigger device discovery refresh
                asyncio.run_coroutine_threadsafe(
                    self.app.device_discovery.refresh_discovery(),
                    self.app.loop
                )
            self.status_message.setText("Refreshing devices...")
        except Exception as e:
            self.logger.error(f"Error refreshing devices: {e}")
            QMessageBox.warning(self, "Error", f"Failed to refresh devices: {e}")

    def add_manual_device(self):
        """Add a device manually."""
        try:

            ip, ok = QInputDialog.getText(self, "Add Device", "Enter device IP address:")
            if ok and ip:
                # Add to manual IPs in config
                manual_ips = self.config.get("network", {}).get("manual_ips", [])
                if ip not in manual_ips:
                    manual_ips.append(ip)
                    self.config["network"]["manual_ips"] = manual_ips
                    self.status_message.setText(f"Added device: {ip}")
                else:
                    QMessageBox.information(self, "Info", "Device already in manual list")
        except Exception as e:
            self.logger.error(f"Error adding manual device: {e}")
            QMessageBox.warning(self, "Error", f"Failed to add device: {e}")

    def clear_history(self):
        """Clear clipboard history."""
        try:
            reply = QMessageBox.question(
                self, "Clear History",
                "Are you sure you want to clear all clipboard history?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                # Clear history (would integrate with clipboard history system)
                self.history_tree.clear()
                self.status_message.setText("History cleared")
        except Exception as e:
            self.logger.error(f"Error clearing history: {e}")
            QMessageBox.warning(self, "Error", f"Failed to clear history: {e}")

    def refresh_history(self):
        """Refresh clipboard history."""
        try:
            self.update_history_list()
            self.status_message.setText("History refreshed")
        except Exception as e:
            self.logger.error(f"Error refreshing history: {e}")
            QMessageBox.warning(self, "Error", f"Failed to refresh history: {e}")

    def export_history(self):
        """Export clipboard history."""
        try:

            filename, _ = QFileDialog.getSaveFileName(
                self, "Export History", "clipboard_history.txt", "Text Files (*.txt)"
            )
            if filename:
                # Export history (would integrate with clipboard history system)
                self.status_message.setText(f"History exported to {filename}")
        except Exception as e:
            self.logger.error(f"Error exporting history: {e}")
            QMessageBox.warning(self, "Error", f"Failed to export history: {e}")

    def apply_settings(self):
        """Apply settings changes."""
        try:
            # Update config with new values
            self.config["network"]["discovery_port"] = self.discovery_port_input.value()
            self.config["network"]["websocket_port"] = self.websocket_port_input.value()
            self.config["network"]["auto_discovery"] = self.auto_discovery_check.isChecked()

            self.config["clipboard"]["auto_sync"] = self.auto_sync_check.isChecked()
            self.config["clipboard"]["history_enabled"] = self.history_enabled_check.isChecked()
            self.config["clipboard"]["history_limit"] = self.history_limit_input.value()

            self.config["ui"]["theme"] = self.theme_combo.currentText().lower()
            self.config["ui"]["minimize_to_tray"] = self.minimize_to_tray_check.isChecked()
            self.config["ui"]["show_notifications"] = self.show_notifications_check.isChecked()

            # Save config to file
            import json
            config_path = "config.json"
            with open(config_path, 'w') as f:
                json.dump(self.config, f, indent=2)

            self.status_message.setText("Settings applied successfully")
            QMessageBox.information(self, "Settings", "Settings have been applied successfully!")

        except Exception as e:
            self.logger.error(f"Error applying settings: {e}")
            QMessageBox.warning(self, "Error", f"Failed to apply settings: {e}")

    def on_theme_changed(self, theme_text: str):
        """Handle theme change."""
        try:
            self.is_dark_theme = theme_text.lower() == "dark"
            self.setup_theme()
        except Exception as e:
            self.logger.error(f"Error changing theme: {e}")

    def closeEvent(self, event):
        """Handle window close event."""
        try:
            if self.config.get("ui", {}).get("minimize_to_tray", True):
                event.ignore()
                self.hide()
                # Would show tray notification here
            else:
                event.accept()
        except Exception as e:
            self.logger.error(f"Error handling close event: {e}")
            event.accept()
