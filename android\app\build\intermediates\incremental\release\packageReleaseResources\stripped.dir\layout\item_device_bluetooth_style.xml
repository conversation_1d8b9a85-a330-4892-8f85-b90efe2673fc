<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="4dp"
    app:cardBackgroundColor="@color/clipsy_card_dark"
    app:cardCornerRadius="12dp"
    app:cardElevation="0dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical"
        android:minHeight="48dp">

        <!-- Device Icon -->
        <ImageView
            android:id="@+id/icon_device_type"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginEnd="10dp"
            android:src="@drawable/ic_computer"
            android:tint="@color/white"
            tools:ignore="ContentDescription" />

        <!-- Device Information -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_device_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/clipsy_text_primary"
                android:textSize="15sp"
                android:textStyle="normal"
                tools:text="Windows PC" />

            <TextView
                android:id="@+id/text_connection_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="@color/clipsy_text_secondary"
                android:textSize="13sp"
                android:visibility="gone"
                tools:text="Connecting..."
                tools:visibility="visible" />

        </LinearLayout>

        <!-- Info Button -->
        <ImageButton
            android:id="@+id/button_info"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_info"
            android:tint="@color/white"
            android:contentDescription="Device info"
            tools:ignore="ContentDescription" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
