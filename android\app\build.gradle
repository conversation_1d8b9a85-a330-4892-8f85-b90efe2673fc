plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-parcelize'
}

android {
    namespace 'com.clipsy.android'
    compileSdk rootProject.ext.compileSdkVersion

    defaultConfig {
        applicationId "com.clipsy.android"
        minSdk rootProject.ext.minSdkVersion
        targetSdk rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            debuggable true
            applicationIdSuffix ".debug"
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }
    
    buildFeatures {
        viewBinding true
        // dataBinding true  // Disabled for now to avoid KAPT issues
    }
}

dependencies {
    // Core Android
    implementation "androidx.core:core-ktx:1.12.0"
    implementation "androidx.appcompat:appcompat:$rootProject.ext.appCompatVersion"
    implementation "com.google.android.material:material:$rootProject.ext.materialVersion"
    implementation "androidx.constraintlayout:constraintlayout:$rootProject.ext.constraintLayoutVersion"
    
    // Lifecycle
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$rootProject.ext.lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$rootProject.ext.lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$rootProject.ext.lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-service:$rootProject.ext.lifecycleVersion"
    
    // Fragment and Navigation
    implementation "androidx.fragment:fragment-ktx:$rootProject.ext.fragmentVersion"
    implementation "androidx.navigation:navigation-fragment-ktx:$rootProject.ext.navigationVersion"
    implementation "androidx.navigation:navigation-ui-ktx:$rootProject.ext.navigationVersion"

    // SwipeRefreshLayout
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
    
    // WorkManager for background tasks
    implementation "androidx.work:work-runtime-ktx:$rootProject.ext.workManagerVersion"
    
    // Room database - Disabled for now to avoid KAPT issues
    // implementation "androidx.room:room-runtime:$rootProject.ext.roomVersion"
    // implementation "androidx.room:room-ktx:$rootProject.ext.roomVersion"
    // kapt "androidx.room:room-compiler:$rootProject.ext.roomVersion"
    
    // Networking
    implementation "com.squareup.retrofit2:retrofit:$rootProject.ext.retrofitVersion"
    implementation "com.squareup.retrofit2:converter-gson:$rootProject.ext.retrofitVersion"
    implementation "com.squareup.okhttp3:okhttp:$rootProject.ext.okhttpVersion"
    implementation "com.squareup.okhttp3:logging-interceptor:$rootProject.ext.okhttpVersion"
    
    // WebSocket
    implementation "org.java-websocket:Java-WebSocket:1.5.4"
    
    // Coroutines
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$rootProject.ext.coroutinesVersion"
    
    // Preferences
    implementation "androidx.preference:preference-ktx:$rootProject.ext.preferenceVersion"
    
    // JSON
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // Network discovery
    implementation 'org.jmdns:jmdns:3.5.8'
    
    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation "androidx.work:work-testing:$rootProject.ext.workManagerVersion"
}
