"""
WebSocket client for clipboard synchronization.
Connects to other Clipsy devices and handles clipboard sync.
"""

import json
import asyncio
import logging
import websockets
from typing import Dict, Callable
from datetime import datetime


class SyncClient:
    """WebSocket client for connecting to other Clipsy devices."""
    
    def __init__(self, config: dict, pairing_manager=None):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.pairing_manager = pairing_manager
        
        # Client configuration
        self.websocket_port = config.get("websocket_port", 8766)
        self.connection_timeout = config.get("connection_timeout", 10)
        
        # Connection state
        self.connections: Dict[str, websockets.WebSocketClientProtocol] = {}
        self.connection_tasks: Dict[str, asyncio.Task] = {}

        # Track manually disconnected devices to prevent automatic reconnection
        self.manually_disconnected_devices = set()

        # Message handlers
        self.message_handlers = {
            "welcome": self._handle_welcome,
            "clipboard_sync": self._handle_clipboard_sync,
            "history_response": self._handle_history_response,
            "pong": self._handle_pong,
            "pairing_response": self._handle_pairing_response,
            "error": self._handle_error
        }
        
        # Callbacks
        self.clipboard_callback = None
        self.history_callback = None
        self.connection_callback = None
    
    def set_clipboard_callback(self, callback: Callable[[str, dict], None]):
        """Set callback for clipboard updates from servers."""
        self.clipboard_callback = callback
    
    def set_history_callback(self, callback: Callable[[str, list], None]):
        """Set callback for history responses."""
        self.history_callback = callback
    
    def set_connection_callback(self, callback: Callable[[str, str, dict], None]):
        """Set callback for connection events (event_type, device_ip, info)."""
        self.connection_callback = callback
    
    async def connect_to_device(self, device_ip: str, device_info: dict = None):
        """Connect to a specific device."""
        if device_ip in self.connections:
            self.logger.warning(f"Already connected to {device_ip}")
            return

        # Allow reconnection if this was manually disconnected
        if device_ip in self.manually_disconnected_devices:
            self.allow_reconnection(device_ip)

        try:
            # Use the websocket_port from device_info if available, otherwise use default
            target_port = self.websocket_port
            if device_info and "websocket_port" in device_info:
                target_port = device_info["websocket_port"]

            uri = f"ws://{device_ip}:{target_port}"
            self.logger.info(f"🔗 Attempting connection to {device_ip}:{target_port}...")

            # Create connection task
            task = asyncio.create_task(self._maintain_connection(device_ip, uri, device_info))
            self.connection_tasks[device_ip] = task

        except Exception as e:
            self.logger.error(f"Failed to connect to {device_ip}: {e}")
    
    async def disconnect_from_device(self, device_ip: str):
        """Disconnect from a specific device and stop all sync operations."""
        self.logger.info(f"🔴 MANUAL DISCONNECT from device: {device_ip}")

        # Mark as manually disconnected to prevent automatic reconnection
        self.manually_disconnected_devices.add(device_ip)
        self.logger.info(f"🔴 Added {device_ip} to manually disconnected devices")

        # Cancel connection task
        if device_ip in self.connection_tasks:
            self.connection_tasks[device_ip].cancel()
            del self.connection_tasks[device_ip]

        # Close connection
        if device_ip in self.connections:
            try:
                await self.connections[device_ip].close()
            except Exception:
                pass
            del self.connections[device_ip]

        self.logger.info(f"🔴 DISCONNECT COMPLETE for {device_ip}")

        if self.connection_callback:
            self.connection_callback("disconnected", device_ip, {})

    def allow_reconnection(self, device_ip: str):
        """Allow reconnection to a manually disconnected device."""
        self.manually_disconnected_devices.discard(device_ip)
        self.logger.info(f"🟢 Removed {device_ip} from manually disconnected devices - reconnection allowed")
    
    async def disconnect_all(self):
        """Disconnect from all devices."""
        device_ips = list(self.connections.keys())
        await asyncio.gather(
            *[self.disconnect_from_device(ip) for ip in device_ips],
            return_exceptions=True
        )
    
    async def _maintain_connection(self, device_ip: str, uri: str, device_info: dict = None):
        """Maintain connection to a device - manual control only (no auto-reconnect)."""
        try:
            async with websockets.connect(
                uri,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=5
            ) as websocket:

                self.connections[device_ip] = websocket
                self.logger.info(f"✅ Successfully connected to {device_ip}")

                if self.connection_callback:
                    self.connection_callback("connected", device_ip, device_info or {})

                # No handshake needed - Android server doesn't expect it
                # Connection is established, start handling messages

                # Handle messages
                async for message in websocket:
                    await self._handle_message(device_ip, message)

        except asyncio.CancelledError:
            self.logger.info(f"Connection to {device_ip} was cancelled")
        except ConnectionRefusedError as e:
            self.logger.error(f"❌ Connection refused by {device_ip}: {e}")

            # Remove from active connections
            if device_ip in self.connections:
                del self.connections[device_ip]

            if self.connection_callback:
                self.connection_callback("error", device_ip, {"error": f"Connection refused: {str(e)}"})
        except Exception as e:
            self.logger.error(f"❌ Connection error with {device_ip}: {type(e).__name__}: {e}")

            # Remove from active connections
            if device_ip in self.connections:
                del self.connections[device_ip]

            if self.connection_callback:
                self.connection_callback("error", device_ip, {"error": str(e)})
        finally:
            # Clean up connection
            if device_ip in self.connections:
                del self.connections[device_ip]
            if device_ip in self.connection_tasks:
                del self.connection_tasks[device_ip]

            if self.connection_callback:
                self.connection_callback("disconnected", device_ip, {})
    
    async def _handle_message(self, device_ip: str, message: str):
        """Handle message from server."""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            if message_type in self.message_handlers:
                await self.message_handlers[message_type](device_ip, data)
            else:
                self.logger.warning(f"Unknown message type from {device_ip}: {message_type}")
                
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON from {device_ip}")
        except Exception as e:
            self.logger.error(f"Error handling message from {device_ip}: {e}")
    
    async def _handle_welcome(self, device_ip: str, data: dict):
        """Handle welcome message from server."""
        server_info = data.get("server_info", {})
        self.logger.info(f"Received welcome from {device_ip}: {server_info.get('device_name', 'Unknown')}")
        
        if self.connection_callback:
            self.connection_callback("welcome", device_ip, server_info)
    
    async def _handle_clipboard_sync(self, device_ip: str, data: dict):
        """Handle clipboard sync from server."""
        try:
            content = data.get("content", "")
            device_info = data.get("device_info", {})

            if content and self.clipboard_callback:
                self.logger.info(f"📋 Received clipboard sync from {device_ip}: {content[:50]}...")
                self.clipboard_callback(content, device_info)
            else:
                self.logger.warning(f"⚠️ Empty clipboard sync received from {device_ip}")

        except Exception as e:
            self.logger.error(f"❌ Error handling clipboard update from {device_ip}: {e}")
    
    async def _handle_history_response(self, device_ip: str, data: dict):
        """Handle history response from server."""
        try:
            history = data.get("history", [])
            
            if self.history_callback:
                self.history_callback(device_ip, history)
            
        except Exception as e:
            self.logger.error(f"Error handling history response from {device_ip}: {e}")
    
    async def _handle_pong(self, device_ip: str, data: dict):
        """Handle pong response from server."""
        pass
    
    async def send_clipboard_sync(self, content: str, device_info: dict = None):
        """Send clipboard sync to all connected devices."""
        if not self.connections:
            return
        
        message = {
            "type": "clipboard_sync",
            "content": content,
            "device_info": device_info or {
                "device_name": self.config.get("device_name", "Windows-PC"),
                "device_type": "windows"
            },
            "timestamp": int(datetime.now().timestamp() * 1000)
        }
        
        # Send to all connected devices
        tasks = []
        for device_ip, websocket in self.connections.items():
            tasks.append(self._send_message(device_ip, websocket, message))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def request_history(self, device_ip: str = None):
        """Request clipboard history from device(s)."""
        message = {
            "type": "history_request",
            "timestamp": datetime.now().isoformat()
        }
        
        if device_ip and device_ip in self.connections:
            # Request from specific device
            await self._send_message(device_ip, self.connections[device_ip], message)
        else:
            # Request from all devices
            tasks = []
            for ip, websocket in self.connections.items():
                tasks.append(self._send_message(ip, websocket, message))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
    
    async def send_ping(self, device_ip: str = None):
        """Send ping to device(s)."""
        message = {
            "type": "ping",
            "timestamp": datetime.now().isoformat()
        }
        
        if device_ip and device_ip in self.connections:
            await self._send_message(device_ip, self.connections[device_ip], message)
        else:
            tasks = []
            for ip, websocket in self.connections.items():
                tasks.append(self._send_message(ip, websocket, message))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _send_message(self, device_ip: str, websocket, message: dict):
        """Send message to a specific device."""
        try:
            data = json.dumps(message)
            await websocket.send(data)
        except Exception as e:
            self.logger.error(f"Error sending message to {device_ip}: {e}")
    
    def get_connected_devices(self) -> Dict[str, dict]:
        """Get information about connected devices."""
        connected_devices = {}
        for device_ip, websocket in self.connections.items():
            try:
                connected_devices[device_ip] = {
                    "state": websocket.state.name if hasattr(websocket.state, 'name') else str(websocket.state),
                    "connected": not websocket.closed
                }
            except Exception:
                continue
        return connected_devices
    
    def is_connected_to(self, device_ip: str) -> bool:
        """Check if connected to a specific device."""
        return device_ip in self.connections and not self.connections[device_ip].closed

    async def send_pairing_request(self, device_ip: str, pairing_code: str):
        """Send pairing request to a device."""
        if not self.pairing_manager:
            self.logger.error("Pairing manager not available")
            return False

        if device_ip not in self.connections:
            self.logger.error(f"Not connected to device {device_ip}")
            return False

        try:
            device_info = self.pairing_manager.get_device_info()
            message = {
                "type": "pairing_request",
                "pairing_code": pairing_code,
                "device_id": device_info["device_id"],
                "device_name": device_info["device_name"]
            }

            await self.connections[device_ip].send(json.dumps(message))
            self.logger.info(f"Sent pairing request to {device_ip}")
            return True

        except Exception as e:
            self.logger.error(f"Error sending pairing request to {device_ip}: {e}")
            return False

    async def _handle_pairing_response(self, device_ip: str, data: dict):
        """Handle pairing response from server."""
        try:
            success = data.get("success", False)
            remote_device_id = data.get("device_id")
            remote_device_name = data.get("device_name")

            if success and self.pairing_manager:
                self.logger.info(f"Pairing successful with {remote_device_name} ({device_ip})")
                # The pairing manager should have already been updated by the server
                self.pairing_manager.update_last_connected(remote_device_id)
            else:
                self.logger.warning(f"Pairing failed with device at {device_ip}")

        except Exception as e:
            self.logger.error(f"Error handling pairing response from {device_ip}: {e}")

    async def _handle_error(self, device_ip: str, data: dict):
        """Handle error message from server."""
        error_message = data.get("message", "Unknown error")
        self.logger.error(f"Error from {device_ip}: {error_message}")
