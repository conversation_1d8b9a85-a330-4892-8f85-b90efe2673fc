#!/bin/bash

echo "Clipsy Logo Resizer"
echo "=================="

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed"
    echo "Please install Python 3"
    exit 1
fi

# Check if P<PERSON>/Pillow is installed
python3 -c "import PIL" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Installing Pillow (PIL) for image processing..."
    pip3 install Pillow
fi

# Check if logo file exists
if [ ! -f "logo_original.png" ]; then
    echo "Error: logo_original.png not found"
    echo "Please save your logo image as 'logo_original.png' in this directory"
    exit 1
fi

echo "Creating icons for all platforms..."
python3 resize_logo.py logo_original.png --all

echo ""
echo "Done! Icons have been created in the following directories:"
echo "- android/app/src/main/res/ (Android app icons)"
echo "- windows/ (Windows app icons)"
echo "- web/ (Web/PWA icons)"
echo ""
