[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-mdpi_ic_clipsy_settings.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-mdpi\\ic_clipsy_settings.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-hdpi_ic_clipsy_history.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-hdpi\\ic_clipsy_history.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-mdpi_ic_notification.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-mdpi\\ic_notification.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_circle_indicator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\circle_indicator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-hdpi_ic_clipsy_status.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-hdpi\\ic_clipsy_status.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_send_arrow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_send_arrow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\color_bottom_nav_color_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\color\\bottom_nav_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xxhdpi_ic_clipsy_devices.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xxhdpi\\ic_clipsy_devices.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xhdpi_ic_clipsy_refresh.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xhdpi\\ic_clipsy_refresh.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xxhdpi_ic_clipsy_history.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xxhdpi\\ic_clipsy_history.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xxxhdpi_ic_clipsy_history.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xxxhdpi\\ic_clipsy_history.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xhdpi_ic_notification.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xhdpi\\ic_notification.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\menu_main_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\menu\\main_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\color_button_text_secondary_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\color\\button_text_secondary_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\color_switch_track_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\color\\switch_track_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-mdpi_ic_clipsy_history.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-mdpi\\ic_clipsy_history.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\layout_fragment_status.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\layout\\fragment_status.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_history_vector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_history_vector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\menu_bottom_navigation_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\menu\\bottom_navigation_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\mipmap-mdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\mipmap-mdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xhdpi_ic_clipsy_history.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xhdpi\\ic_clipsy_history.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\menu_menu_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\menu\\menu_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xxxhdpi_ic_clipsy_devices.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xxxhdpi\\ic_clipsy_devices.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xxhdpi_ic_clipsy_refresh.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xxhdpi\\ic_clipsy_refresh.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xxxhdpi_ic_notification.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xxxhdpi\\ic_notification.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\mipmap-xhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\mipmap-xhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-hdpi_ic_clipsy_settings.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-hdpi\\ic_clipsy_settings.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\color_button_background_primary_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\color\\button_background_primary_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xhdpi_ic_clipsy_devices.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xhdpi\\ic_clipsy_devices.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-mdpi_ic_clipsy_devices.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-mdpi\\ic_clipsy_devices.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\xml_preferences.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\xml\\preferences.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-hdpi_ic_clipsy_refresh.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-hdpi\\ic_clipsy_refresh.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_code_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\code_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_refresh.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_refresh.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xxxhdpi_ic_clipsy_status.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xxxhdpi\\ic_clipsy_status.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\layout_fragment_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\layout\\fragment_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\color_switch_thumb_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\color\\switch_thumb_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-hdpi_ic_notification.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-hdpi\\ic_notification.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\mipmap-xxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\mipmap-xxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_badge_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\badge_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-mdpi_ic_clipsy_status.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-mdpi\\ic_clipsy_status.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xxhdpi_ic_notification.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xxhdpi\\ic_notification.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_scan_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_scan_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\layout_dialog_pairing.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\layout\\dialog_pairing.xml"}, {"merged": "com.clipsy.android.app-debug-57:/menu_main_menu.xml.flat", "source": "com.clipsy.android.app-main-59:/menu/main_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\layout_fragment_devices.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\layout\\fragment_devices.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xxxhdpi_ic_clipsy_settings.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xxxhdpi\\ic_clipsy_settings.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\layout_item_device.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\layout\\item_device.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xhdpi_ic_clipsy_status.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xhdpi\\ic_clipsy_status.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_sync_to_pc.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_sync_to_pc.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xxhdpi_ic_clipsy_settings.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xxhdpi\\ic_clipsy_settings.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_status_vector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_status_vector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xhdpi_ic_clipsy_settings.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xhdpi\\ic_clipsy_settings.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\mipmap-hdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\mipmap-hdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xxxhdpi_ic_clipsy_refresh.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xxxhdpi\\ic_clipsy_refresh.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_arrow_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_arrow_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\color_button_text_primary_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\color\\button_text_primary_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\layout_item_device_bluetooth_style.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\layout\\item_device_bluetooth_style.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-xxhdpi_ic_clipsy_status.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-xxhdpi\\ic_clipsy_status.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_button_secondary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\button_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\layout_item_clipboard_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\layout\\item_clipboard_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-mdpi_ic_clipsy_refresh.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-mdpi\\ic_clipsy_refresh.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\layout_activity_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\layout\\activity_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_phone.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_phone.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_computer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_computer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\mipmap-xxxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\mipmap-xxxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable-hdpi_ic_clipsy_devices.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable-hdpi\\ic_clipsy_devices.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_devices_vector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_devices_vector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\drawable_ic_sync.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\drawable\\ic_sync.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-debug-57:\\layout_dialog_add_manual_device.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.clipsy.android.app-main-59:\\layout\\dialog_add_manual_device.xml"}]