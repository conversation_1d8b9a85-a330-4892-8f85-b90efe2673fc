#!/usr/bin/env python3
"""
Simple connection test to verify Windows PC accessibility
Run this from any device on the network to test connectivity
"""

import socket
import json
import time
from datetime import datetime

def test_basic_connectivity():
    """Test basic TCP connectivity to Windows PC."""
    print("Testing basic TCP connectivity...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        start_time = time.time()
        result = sock.connect_ex(("*************", 8766))
        end_time = time.time()
        
        sock.close()
        
        if result == 0:
            print(f"✅ TCP connection successful ({end_time - start_time:.2f}s)")
            return True
        else:
            print(f"❌ TCP connection failed (error code: {result})")
            return False
            
    except Exception as e:
        print(f"❌ TCP connection error: {e}")
        return False

def test_websocket_handshake():
    """Test WebSocket handshake manually."""
    print("Testing WebSocket handshake...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect(("*************", 8766))
        
        # Send WebSocket handshake request
        handshake = (
            "GET / HTTP/1.1\r\n"
            "Host: *************:8766\r\n"
            "Upgrade: websocket\r\n"
            "Connection: Upgrade\r\n"
            "Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==\r\n"
            "Sec-WebSocket-Version: 13\r\n"
            "\r\n"
        )
        
        sock.send(handshake.encode())
        
        # Read response
        response = sock.recv(1024).decode()
        sock.close()
        
        if "101 Switching Protocols" in response:
            print("✅ WebSocket handshake successful")
            print("Response headers received:")
            for line in response.split('\r\n')[:5]:
                if line.strip():
                    print(f"   {line}")
            return True
        else:
            print("❌ WebSocket handshake failed")
            print("Response received:")
            print(response[:200])
            return False
            
    except Exception as e:
        print(f"❌ WebSocket handshake error: {e}")
        return False

def test_udp_broadcast():
    """Test UDP broadcast to Windows PC."""
    print("Testing UDP broadcast discovery...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        sock.settimeout(5)
        
        # Create discovery message
        message = {
            "type": "discovery",
            "device_name": "Test-Device",
            "device_type": "test",
            "ip": "*************",  # Fake IP for testing
            "websocket_port": 8766,
            "timestamp": datetime.now().isoformat()
        }
        
        data = json.dumps(message).encode()
        
        # Send broadcast
        sock.sendto(data, ("*************", 8765))
        print("✅ UDP broadcast sent to *************:8765")
        
        # Try to receive response (Windows might respond)
        try:
            response, addr = sock.recvfrom(1024)
            print(f"✅ Received UDP response from {addr[0]}")
            try:
                response_data = json.loads(response.decode())
                print(f"   Device: {response_data.get('device_name', 'unknown')}")
                print(f"   Type: {response_data.get('device_type', 'unknown')}")
            except:
                print(f"   Raw response: {response[:100]}")
        except socket.timeout:
            print("⚠️  No UDP response received (normal for discovery)")
        
        sock.close()
        return True
        
    except Exception as e:
        print(f"❌ UDP broadcast error: {e}")
        return False

def test_network_info():
    """Display network information."""
    print("Network Information:")
    print("-" * 20)
    
    try:
        # Get local IP
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.connect(("*******", 80))
        local_ip = sock.getsockname()[0]
        sock.close()
        
        print(f"Local IP: {local_ip}")
        print(f"Target IP: *************")
        print(f"Same network: {local_ip.startswith('192.168.1.')}")
        
        if not local_ip.startswith('192.168.1.'):
            print("⚠️  WARNING: Not on same network as Windows PC!")
            print("   Make sure both devices are on the same Wi-Fi")
        
    except Exception as e:
        print(f"Could not determine local IP: {e}")

def main():
    """Run all connection tests."""
    print("Windows PC Connection Test")
    print("=" * 30)
    print("Target: *************:8766")
    print()
    
    # Test network info
    test_network_info()
    print()
    
    # Test basic connectivity
    tcp_ok = test_basic_connectivity()
    print()
    
    # Test WebSocket handshake
    ws_ok = test_websocket_handshake()
    print()
    
    # Test UDP broadcast
    udp_ok = test_udp_broadcast()
    print()
    
    # Summary
    print("=" * 30)
    print("TEST RESULTS:")
    print(f"TCP Connectivity: {'✅ PASS' if tcp_ok else '❌ FAIL'}")
    print(f"WebSocket Handshake: {'✅ PASS' if ws_ok else '❌ FAIL'}")
    print(f"UDP Broadcast: {'✅ PASS' if udp_ok else '❌ FAIL'}")
    print()
    
    if tcp_ok and ws_ok:
        print("🎉 All tests passed! Windows PC is accessible.")
        print("If Android app still fails, the issue is in the app implementation.")
    elif tcp_ok and not ws_ok:
        print("⚠️  TCP works but WebSocket handshake fails.")
        print("This suggests the Windows service might not be a WebSocket server.")
    elif not tcp_ok:
        print("❌ Basic connectivity failed.")
        print("Check:")
        print("- Windows Clipsy app is running")
        print("- Windows Firewall allows port 8766")
        print("- Both devices on same Wi-Fi network")
    
    print("\nRun this test from your Android device (using Termux or similar)")
    print("to verify connectivity from the Android perspective.")

if __name__ == "__main__":
    main()
