// Generated by view binder compiler. Do not edit!
package com.clipsy.android.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.clipsy.android.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.switchmaterial.SwitchMaterial;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDevicesBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ImageButton buttonRefresh;

  @NonNull
  public final MaterialCardView cardDeviceName;

  @NonNull
  public final MaterialCardView cardNoAvailableDevices;

  @NonNull
  public final FloatingActionButton fabAddDevice;

  @NonNull
  public final LinearLayout headerAvailableDevices;

  @NonNull
  public final TextView headerConnectedDevices;

  @NonNull
  public final ProgressBar progressScanning;

  @NonNull
  public final RecyclerView recyclerViewAvailableDevices;

  @NonNull
  public final RecyclerView recyclerViewConnectedDevices;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final SwitchMaterial switchSync;

  @NonNull
  public final TextView textCurrentDeviceName;

  @NonNull
  public final TextView textDescription;

  private FragmentDevicesBinding(@NonNull CoordinatorLayout rootView,
      @NonNull ImageButton buttonRefresh, @NonNull MaterialCardView cardDeviceName,
      @NonNull MaterialCardView cardNoAvailableDevices, @NonNull FloatingActionButton fabAddDevice,
      @NonNull LinearLayout headerAvailableDevices, @NonNull TextView headerConnectedDevices,
      @NonNull ProgressBar progressScanning, @NonNull RecyclerView recyclerViewAvailableDevices,
      @NonNull RecyclerView recyclerViewConnectedDevices,
      @NonNull SwipeRefreshLayout swipeRefreshLayout, @NonNull SwitchMaterial switchSync,
      @NonNull TextView textCurrentDeviceName, @NonNull TextView textDescription) {
    this.rootView = rootView;
    this.buttonRefresh = buttonRefresh;
    this.cardDeviceName = cardDeviceName;
    this.cardNoAvailableDevices = cardNoAvailableDevices;
    this.fabAddDevice = fabAddDevice;
    this.headerAvailableDevices = headerAvailableDevices;
    this.headerConnectedDevices = headerConnectedDevices;
    this.progressScanning = progressScanning;
    this.recyclerViewAvailableDevices = recyclerViewAvailableDevices;
    this.recyclerViewConnectedDevices = recyclerViewConnectedDevices;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.switchSync = switchSync;
    this.textCurrentDeviceName = textCurrentDeviceName;
    this.textDescription = textDescription;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDevicesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDevicesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_devices, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDevicesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_refresh;
      ImageButton buttonRefresh = ViewBindings.findChildViewById(rootView, id);
      if (buttonRefresh == null) {
        break missingId;
      }

      id = R.id.card_device_name;
      MaterialCardView cardDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (cardDeviceName == null) {
        break missingId;
      }

      id = R.id.card_no_available_devices;
      MaterialCardView cardNoAvailableDevices = ViewBindings.findChildViewById(rootView, id);
      if (cardNoAvailableDevices == null) {
        break missingId;
      }

      id = R.id.fab_add_device;
      FloatingActionButton fabAddDevice = ViewBindings.findChildViewById(rootView, id);
      if (fabAddDevice == null) {
        break missingId;
      }

      id = R.id.header_available_devices;
      LinearLayout headerAvailableDevices = ViewBindings.findChildViewById(rootView, id);
      if (headerAvailableDevices == null) {
        break missingId;
      }

      id = R.id.header_connected_devices;
      TextView headerConnectedDevices = ViewBindings.findChildViewById(rootView, id);
      if (headerConnectedDevices == null) {
        break missingId;
      }

      id = R.id.progress_scanning;
      ProgressBar progressScanning = ViewBindings.findChildViewById(rootView, id);
      if (progressScanning == null) {
        break missingId;
      }

      id = R.id.recycler_view_available_devices;
      RecyclerView recyclerViewAvailableDevices = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewAvailableDevices == null) {
        break missingId;
      }

      id = R.id.recycler_view_connected_devices;
      RecyclerView recyclerViewConnectedDevices = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewConnectedDevices == null) {
        break missingId;
      }

      id = R.id.swipe_refresh_layout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.switch_sync;
      SwitchMaterial switchSync = ViewBindings.findChildViewById(rootView, id);
      if (switchSync == null) {
        break missingId;
      }

      id = R.id.text_current_device_name;
      TextView textCurrentDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (textCurrentDeviceName == null) {
        break missingId;
      }

      id = R.id.text_description;
      TextView textDescription = ViewBindings.findChildViewById(rootView, id);
      if (textDescription == null) {
        break missingId;
      }

      return new FragmentDevicesBinding((CoordinatorLayout) rootView, buttonRefresh, cardDeviceName,
          cardNoAvailableDevices, fabAddDevice, headerAvailableDevices, headerConnectedDevices,
          progressScanning, recyclerViewAvailableDevices, recyclerViewConnectedDevices,
          swipeRefreshLayout, switchSync, textCurrentDeviceName, textDescription);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
