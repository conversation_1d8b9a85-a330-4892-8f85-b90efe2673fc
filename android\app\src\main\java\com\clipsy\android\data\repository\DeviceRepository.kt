package com.clipsy.android.data.repository

import android.content.Context
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.clipsy.android.data.model.Device
import com.clipsy.android.data.model.ConnectionState
import com.clipsy.android.data.model.ConnectionStatus
import com.clipsy.android.network.DeviceDiscovery
import com.clipsy.android.network.WebSocketClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap
/**
 * Repository for managing discovered devices and connections.
 */
class DeviceRepository(
    private val context: Context,
    private val connectionManager: com.clipsy.android.pairing.SimpleConnectionManager? = null
) {

    companion object {
        private const val TAG = "DeviceRepository"
    }
    
    private val _discoveredDevices = MutableLiveData<List<Device>>(emptyList())
    val discoveredDevices: LiveData<List<Device>> = _discoveredDevices
    
    private val _connectedDevices = MutableLiveData<List<Device>>(emptyList())
    val connectedDevices: LiveData<List<Device>> = _connectedDevices
    
    private val _connectionStates = MutableLiveData<Map<String, ConnectionState>>(emptyMap())
    val connectionStates: LiveData<Map<String, ConnectionState>> = _connectionStates
    
    // Thread-safe storage for devices
    private val deviceMap = ConcurrentHashMap<String, Device>()
    private val connectionStateMap = ConcurrentHashMap<String, ConnectionState>()

    // Network components
    private var deviceDiscovery: DeviceDiscovery? = null
    private var webSocketClient: WebSocketClient? = null
    
    /**
     * Add or update a discovered device.
     * Handle deduplication by IP address and device name, prefer the most recent/best connection.
     */
    suspend fun addOrUpdateDevice(device: Device) {
        withContext(Dispatchers.Main) {

            // First check if we already have a device with this IP address
            val existingDeviceByIp = deviceMap.values.find { it.ip == device.ip }

            if (existingDeviceByIp != null) {
                // Same IP - update the existing device with better information
                val existingKey = existingDeviceByIp.getUniqueId()

                // Prefer actual device names over generic names, and manual devices over discovered ones
                val shouldUpdate = device.manual ||
                                 (!existingDeviceByIp.manual && device.deviceName.contains("-PC") && !device.deviceName.startsWith("Home-PC")) ||
                                 (!existingDeviceByIp.manual && !device.manual && device.lastSeen > existingDeviceByIp.lastSeen)

                if (shouldUpdate) {
                    // Remove old entry and add new one with potentially different unique ID
                    deviceMap.remove(existingKey)
                    deviceMap[device.getUniqueId()] = device
                }
            } else {
                // Check by unique ID (device name)
                val deviceKey = device.getUniqueId()
                val existingDevice = deviceMap[deviceKey]

                if (existingDevice != null) {
                    // Same device name but different IP - update with newer information
                    val shouldUpdate = device.manual ||
                                     (!existingDevice.manual && device.lastSeen > existingDevice.lastSeen)

                    if (shouldUpdate) {
                        deviceMap[deviceKey] = device
                    }
                } else {
                    // Completely new device
                    deviceMap[device.getUniqueId()] = device
                }
            }

            updateDiscoveredDevicesList()
        }
    }
    
    /**
     * Remove a device.
     */
    suspend fun removeDevice(device: Device) {
        withContext(Dispatchers.Main) {
            val deviceKey = device.getUniqueId()
            deviceMap.remove(deviceKey)
            connectionStateMap.remove(deviceKey)
            updateDiscoveredDevicesList()
            updateConnectionStates()
        }
    }
    
    /**
     * Remove device by IP.
     */
    suspend fun removeDeviceByIp(ip: String) {
        withContext(Dispatchers.Main) {
            val deviceToRemove = deviceMap.values.find { it.ip == ip }
            deviceToRemove?.let { removeDevice(it) }
        }
    }
    
    /**
     * Get device by IP.
     */
    fun getDeviceByIp(ip: String): Device? {
        return deviceMap.values.find { it.ip == ip }
    }

    /**
     * Get all devices by IP (for handling duplicate devices with same IP).
     */
    fun getDevicesByIp(ip: String): List<Device> {
        return deviceMap.values.filter { it.ip == ip }
    }
    
    /**
     * Get device by unique ID.
     */
    fun getDeviceById(deviceId: String): Device? {
        return deviceMap[deviceId]
    }
    
    /**
     * Add manual device.
     */
    suspend fun addManualDevice(ip: String, name: String): Device {
        val device = Device(
            ip = ip,
            deviceName = name.ifEmpty { "Manual Device" },
            deviceType = "unknown",
            manual = true,
            deviceId = "manual_$ip"
        )
        addOrUpdateDevice(device)
        return device
    }
    
    /**
     * Update device connection status.
     */
    suspend fun updateConnectionStatus(device: Device, status: ConnectionStatus, errorMessage: String? = null) {
        withContext(Dispatchers.Main) {
            val deviceKey = device.getUniqueId()

            // Check if status actually changed to prevent unnecessary UI updates
            val currentState = connectionStateMap[deviceKey]
            if (currentState?.status == status && currentState.errorMessage == errorMessage) {
                return@withContext
            }

            val connectionState = ConnectionState(
                status = status,
                device = device,
                errorMessage = errorMessage
            )

            connectionStateMap[deviceKey] = connectionState
            updateConnectionStates()
            updateConnectedDevicesList()
        }
    }
    
    /**
     * Get connection status for a device.
     */
    fun getConnectionStatus(device: Device): ConnectionStatus {
        val deviceKey = device.getUniqueId()
        return connectionStateMap[deviceKey]?.status ?: ConnectionStatus.DISCONNECTED
    }
    
    /**
     * Get connection state for a device.
     */
    fun getConnectionState(device: Device): ConnectionState? {
        val deviceKey = device.getUniqueId()
        return connectionStateMap[deviceKey]
    }
    
    /**
     * Check if device is connected.
     */
    fun isDeviceConnected(device: Device): Boolean {
        return getConnectionStatus(device) == ConnectionStatus.CONNECTED
    }

    /**
     * Refresh connection status for a device (for debugging).
     */
    suspend fun refreshConnectionStatus(device: Device) {
        withContext(Dispatchers.Main) {
            val deviceKey = device.getUniqueId()
            val currentState = connectionStateMap[deviceKey]

            updateConnectionStates()
            updateConnectedDevicesList()
        }
    }
    
    /**
     * Get all connected devices.
     */
    fun getConnectedDevicesSync(): List<Device> {
        return connectionStateMap.values
            .filter { it.status == ConnectionStatus.CONNECTED }
            .mapNotNull { it.device }
    }
    
    /**
     * Get all discovered devices.
     */
    fun getDiscoveredDevicesSync(): List<Device> {
        return deviceMap.values.toList()
    }
    
    /**
     * Clear all devices.
     */
    suspend fun clearAllDevices() {
        withContext(Dispatchers.Main) {
            deviceMap.clear()
            connectionStateMap.clear()
            updateDiscoveredDevicesList()
            updateConnectionStates()
            updateConnectedDevicesList()
        }
    }
    
    /**
     * Remove offline devices (older than timeout).
     */
    suspend fun removeOfflineDevices(timeoutMs: Long = 30000) {
        withContext(Dispatchers.Main) {
            val currentTime = System.currentTimeMillis()
            val devicesToRemove = deviceMap.values.filter { device ->
                !device.manual && (currentTime - device.lastSeen) > timeoutMs
            }
            
            devicesToRemove.forEach { device ->
                val deviceKey = device.getUniqueId()
                deviceMap.remove(deviceKey)
                connectionStateMap.remove(deviceKey)
            }
            
            if (devicesToRemove.isNotEmpty()) {
                updateDiscoveredDevicesList()
                updateConnectionStates()
                updateConnectedDevicesList()
            }
        }
    }
    
    /**
     * Update the discovered devices LiveData.
     */
    private fun updateDiscoveredDevicesList() {
        val sortedDevices = deviceMap.values.sortedWith(
            compareByDescending<Device> { it.isOnline() }
                .thenByDescending { it.lastSeen }
                .thenBy { it.deviceName }
        )
        _discoveredDevices.value = sortedDevices
    }
    
    /**
     * Update the connected devices LiveData.
     */
    private fun updateConnectedDevicesList() {
        val connectedDevices = connectionStateMap.values
            .filter { it.status == ConnectionStatus.CONNECTED }
            .mapNotNull { it.device }
            .sortedBy { it.deviceName }
        _connectedDevices.value = connectedDevices
    }
    
    /**
     * Update the connection states LiveData.
     */
    private fun updateConnectionStates() {
        _connectionStates.value = connectionStateMap.toMap()
    }
    
    /**
     * Get device count.
     */
    fun getDeviceCount(): Int = deviceMap.size
    
    /**
     * Get connected device count.
     */
    fun getConnectedDeviceCount(): Int {
        return connectionStateMap.values.count { it.status == ConnectionStatus.CONNECTED }
    }

    /**
     * Get the connection manager instance.
     */
    fun getConnectionManager(): com.clipsy.android.pairing.SimpleConnectionManager? {
        return connectionManager
    }

    /**
     * Start device discovery.
     */
    suspend fun startDiscovery() {
        withContext(Dispatchers.IO) {
            try {
                // Initialize network components if not already done
                if (deviceDiscovery == null) {
                    deviceDiscovery = DeviceDiscovery(context, this@DeviceRepository)
                }
                if (webSocketClient == null) {
                    webSocketClient = WebSocketClient(context, this@DeviceRepository, ClipboardRepository.getInstance(), connectionManager)
                }

                // Start real UDP discovery
                deviceDiscovery?.startDiscovery()

            } catch (e: Exception) {
                // If real discovery fails, add a manual device as fallback
                val fallbackDevice = Device(
                    ip = "*************",  // Use actual PC IP
                    deviceName = "Windows PC (Manual)",
                    deviceType = "windows",
                    websocketPort = 8766,  // Use correct server port
                    lastSeen = System.currentTimeMillis(),
                    deviceId = "manual-pc",
                    manual = true
                )
                addOrUpdateDevice(fallbackDevice)
            }
        }
    }

    /**
     * Connect to a device.
     */
    suspend fun connectToDevice(device: Device) {
        withContext(Dispatchers.IO) {
            try {
                // Don't update status here - let WebSocketClient handle all status updates to prevent conflicts

                // Use the WebSocket client to connect
                if (webSocketClient == null) {
                    webSocketClient = WebSocketClient(context, this@DeviceRepository, ClipboardRepository.getInstance(), connectionManager)
                }

                webSocketClient?.connectToDevice(device)

            } catch (e: Exception) {
                Log.e(TAG, "Exception in connectToDevice: ${e.message}", e)
                updateConnectionStatus(device, ConnectionStatus.DISCONNECTED, e.message)
            }
        }
    }

    /**
     * Disconnect from a device.
     */
    suspend fun disconnectFromDevice(device: Device) {
        withContext(Dispatchers.IO) {
            try {
                // Use the WebSocket client to disconnect
                webSocketClient?.disconnectFromDevice(device)

                // Update status
                updateConnectionStatus(device, ConnectionStatus.DISCONNECTED)

            } catch (e: Exception) {
                // Still update status even if disconnect fails
                updateConnectionStatus(device, ConnectionStatus.DISCONNECTED, e.message)
            }
        }
    }

    /**
     * Allow reconnection to a manually disconnected device.
     */
    fun allowReconnection(device: Device) {
        webSocketClient?.allowReconnection(device)
    }

    /**
     * Force reconnect all devices (useful for clearing stuck connections).
     */
    suspend fun forceReconnectAllDevices() {
        withContext(Dispatchers.IO) {
            try {
                webSocketClient?.forceReconnectAll()
            } catch (e: Exception) {
                Log.e(TAG, "Error during force reconnect", e)
            }
        }
    }

    /**
     * Clear all cached devices and force rediscovery.
     */
    suspend fun clearDevicesAndRediscover() {
        withContext(Dispatchers.Main) {
            deviceMap.clear()
            connectionStateMap.clear()
            updateDiscoveredDevicesList()
            updateConnectionStates()
            updateConnectedDevicesList()
        }
        // Restart discovery
        startDiscovery()
    }

    /**
     * Get the WebSocketClient instance (create if needed).
     */
    fun getWebSocketClient(): WebSocketClient {
        if (webSocketClient == null) {
            webSocketClient = WebSocketClient(context, this@DeviceRepository, ClipboardRepository.getInstance(), connectionManager)
        }
        return webSocketClient!!
    }

    /**
     * Stop discovery and cleanup resources.
     */
    fun cleanup() {
        deviceDiscovery?.stopDiscovery()
        webSocketClient?.disconnectAll()
    }
}
