$com.clipsy.android.ClipsyApplication.com.clipsy.android.ClipsyApplication.Companion+com.clipsy.android.data.model.ClipboardItem5com.clipsy.android.data.model.ClipboardItem.Companion.com.clipsy.android.data.model.ConnectionStatus-com.clipsy.android.data.model.ConnectionState$<EMAIL>=com.clipsy.android.data.repository.DeviceRepository.Companion*com.clipsy.android.network.DeviceDiscovery4com.clipsy.android.network.DeviceDiscovery.Companion*com.clipsy.android.network.WebSocketClient4com.clipsy.android.network.WebSocketClient.CompanionBcom.clipsy.android.network.WebSocketClient.DeviceWebSocketListener*<EMAIL>&com.clipsy.android.pairing.KnownDevice*com.clipsy.android.pairing.ConnectedDevice2com.clipsy.android.pairing.SimpleConnectionManager<com.clipsy.android.pairing.SimpleConnectionManager.Companion/com.clipsy.android.service.ClipboardSyncService9com.clipsy.android.service.ClipboardSyncService.Companion;com.clipsy.android.service.ClipboardSyncService.LocalBinder"com.clipsy.android.ui.MainActivity*com.clipsy.android.ui.MainViewModelFactory&com.clipsy.android.ui.SettingsActivity7com.clipsy.android.ui.SettingsActivity.SettingsFragment:com.clipsy.android.ui.adapters.BluetoothStyleDeviceAdapterKcom.clipsy.android.ui.adapters.BluetoothStyleDeviceAdapter.DeviceViewHolderMcom.clipsy.android.ui.adapters.BluetoothStyleDeviceAdapter.DeviceDiffCallback6com.clipsy.android.ui.adapters.ClipboardHistoryAdapterHcom.clipsy.android.ui.adapters.ClipboardHistoryAdapter.HistoryViewHolderJcom.clipsy.android.ui.adapters.ClipboardHistoryAdapter.HistoryDiffCallback,com.clipsy.android.ui.adapters.DeviceAdapter=com.clipsy.android.ui.adapters.DeviceAdapter.DeviceViewHolder?com.clipsy.android.ui.adapters.DeviceAdapter.DeviceDiffCallback/com.clipsy.android.ui.fragments.DevicesFragment9com.clipsy.android.ui.fragments.DevicesFragment.Companion/com.clipsy.android.ui.fragments.HistoryFragment.com.clipsy.android.ui.fragments.StatusFragment,com.clipsy.android.ui.utils.ButtonStyleUtils8com.clipsy.android.ui.utils.ButtonStyleUtils.ButtonStyle*com.clipsy.android.viewmodel.MainViewModel2com.clipsy.android.databinding.ActivityMainBinding4com.clipsy.android.databinding.FragmentStatusBinding0com.clipsy.android.databinding.ItemDeviceBinding5com.clipsy.android.databinding.FragmentHistoryBinding:com.clipsy.android.databinding.ItemClipboardHistoryBinding5com.clipsy.android.databinding.FragmentDevicesBinding>com.clipsy.android.databinding.ItemDeviceBluetoothStyleBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  