# Clipsy Standalone Windows Application

This directory contains the standalone Windows application for Clipsy with built-in firewall management and all dependencies bundled into a single executable.

## 🚀 Quick Start

### Building the Standalone Executable

1. **Using Batch Script (Recommended)**:
   ```cmd
   cd windows
   build_standalone.bat
   ```

2. **Using PowerShell Script**:
   ```powershell
   cd windows
   .\build_standalone.ps1
   ```

3. **Manual Build**:
   ```cmd
   cd windows\src
   pip install pyinstaller websockets pywin32 pillow psutil aiohttp aiofiles requests certifi
   pyinstaller --clean ClipsyStandalone.spec
   ```

## 📦 What's Included

The standalone executable includes:

- ✅ **Complete Clipsy application** with GUI
- ✅ **Automatic firewall configuration** (Windows Firewall rules)
- ✅ **All Python dependencies** bundled (no external installation required)
- ✅ **Administrator privilege handling** (requests elevation when needed)
- ✅ **Comprehensive error handling** with user-friendly dialogs
- ✅ **Device discovery and synchronization**
- ✅ **Clipboard monitoring and history**
- ✅ **Network communication** (UDP discovery + WebSocket sync)

## 🔧 Features

### Firewall Management
- **Automatic Detection**: Checks if firewall rules exist on startup
- **User Consent**: Asks permission before making firewall changes
- **Admin Elevation**: Automatically requests administrator privileges when needed
- **Rule Creation**: Adds rules for:
  - UDP Port 8765 (Device Discovery)
  - TCP Port 8766 (WebSocket Synchronization)

### Standalone Operation
- **Single File**: No installation required, just run the .exe
- **No Dependencies**: All required libraries bundled
- **Portable**: Can be run from any location
- **Self-Contained**: Includes configuration and logging

## 🛠️ Build Requirements

- **Python 3.8+** installed on the build machine
- **pip** package manager
- **Internet connection** for downloading dependencies during build

## 📁 Output Files

After building, you'll find:

```
windows/
├── src/dist/ClipsyStandalone.exe    # Main executable (in src folder)
├── ClipsyStandalone.exe             # Copy in windows folder
└── src/logs/                        # Log files (created at runtime)
```

## 🚀 Usage Instructions

### First Run
1. **Double-click** `ClipsyStandalone.exe`
2. **Firewall Dialog**: If firewall rules don't exist, you'll see a dialog asking to configure them
3. **Admin Elevation**: If you agree, the app will restart with administrator privileges
4. **Automatic Configuration**: Firewall rules will be added automatically
5. **Normal Operation**: App starts normally after configuration

### Subsequent Runs
- Just double-click the executable
- No admin privileges needed (unless firewall rules were removed)
- App starts immediately

## 🔍 Troubleshooting

### Build Issues

**Python not found**:
```
ERROR: Python is not installed or not in PATH
```
- Install Python 3.8+ from python.org
- Make sure Python is added to PATH during installation

**Package installation fails**:
```
ERROR: Failed to install required packages
```
- Check internet connection
- Try running as administrator
- Update pip: `python -m pip install --upgrade pip`

**Build fails**:
```
ERROR: Build failed
```
- Check the detailed output above the error
- Ensure all source files are present
- Try cleaning: delete `build/` and `dist/` folders, then rebuild

### Runtime Issues

**Firewall configuration fails**:
- Run the executable as administrator manually
- Check Windows Firewall settings manually
- Ensure Windows Firewall service is running

**App won't start**:
- Check `logs/clipsy_standalone.log` for detailed error messages
- Try running from command prompt to see console output
- Ensure no antivirus is blocking the executable

**Network issues**:
- Verify firewall rules exist in Windows Firewall settings
- Check if ports 8765 and 8766 are available
- Ensure network adapter is working properly

## 📋 Technical Details

### Firewall Rules Created
```
Rule Name: Clipsy Discovery Port
- Direction: Inbound
- Protocol: UDP
- Port: 8765
- Action: Allow

Rule Name: Clipsy WebSocket Port
- Direction: Inbound
- Protocol: TCP
- Port: 8766
- Action: Allow
```

### File Structure
```
ClipsyStandalone.exe
├── Python Runtime
├── Clipsy Application Code
├── UI Components (Tkinter)
├── Network Components (WebSockets)
├── Clipboard Monitoring
├── Device Discovery
├── Firewall Management
└── All Dependencies
```

### Configuration
The app uses embedded default configuration but can also read from an external `config.json` file if present in the same directory.

## 🔒 Security Notes

- **Admin Privileges**: Only requested when firewall configuration is needed
- **Firewall Rules**: Only adds specific rules for Clipsy ports
- **Network Access**: Only communicates on local network
- **No Internet**: App doesn't require internet connection to operate

## 📝 Distribution

The `ClipsyStandalone.exe` file can be distributed as a single file:
- No installation required
- No additional files needed
- Works on Windows 10/11
- Approximately 15-25 MB in size

## 🆘 Support

If you encounter issues:
1. Check the log file in `logs/clipsy_standalone.log`
2. Run from command prompt to see any console output
3. Ensure Windows Firewall service is running
4. Try running as administrator if firewall issues persist
