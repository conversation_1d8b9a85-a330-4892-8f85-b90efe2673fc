<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/clipsy_background">

    <!-- Scanning Progress Bar -->
    <ProgressBar
        android:id="@+id/progress_scanning"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_gravity="top"
        android:indeterminate="true"
        android:indeterminateTint="@color/clipsy_primary"
        android:visibility="gone" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="3dp">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <!-- Sync Toggle Card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    app:cardBackgroundColor="@color/clipsy_card_dark"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:gravity="center_vertical"
                        android:minHeight="48dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Sync"
                            android:textColor="@color/clipsy_text_primary"
                            android:textSize="15sp"
                            android:textStyle="normal" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_sync"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:scaleX="0.8"
                            android:scaleY="0.8"
                            app:thumbTint="@color/switch_thumb_color"
                            app:trackTint="@color/switch_track_color" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Device Name Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_device_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    app:cardBackgroundColor="@color/clipsy_card_dark"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:gravity="center_vertical"
                        android:minHeight="48dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Device name"
                                android:textColor="@color/clipsy_text_primary"
                                android:textSize="15sp"
                                android:textStyle="normal" />

                            <TextView
                                android:id="@+id/text_current_device_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="2dp"
                                android:text="Android Device"
                                android:textColor="@color/clipsy_text_secondary"
                                android:textSize="13sp" />

                        </LinearLayout>

                        <ImageView
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:src="@drawable/ic_arrow_right"
                            android:tint="@color/clipsy_text_secondary"
                            tools:ignore="ContentDescription" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Description Text -->
                <TextView
                    android:id="@+id/text_description"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="This device will be identified as 'Android Device' when sync is on."
                    android:textColor="@color/clipsy_text_secondary"
                    android:textSize="12sp"
                    android:lineSpacingExtra="1dp" />

                <!-- Connected Devices Section -->
                <TextView
                    android:id="@+id/header_connected_devices"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Connected devices"
                    android:textColor="@color/clipsy_text_primary"
                    android:textSize="14sp"
                    android:textStyle="normal"
                    android:visibility="gone" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view_connected_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:nestedScrollingEnabled="false"
                    tools:listitem="@layout/item_device_bluetooth_style" />

                <!-- Available Devices Section -->
                <LinearLayout
                    android:id="@+id/header_available_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Available devices"
                        android:textColor="@color/clipsy_text_primary"
                        android:textSize="14sp"
                        android:textStyle="normal" />

                    <ImageButton
                        android:id="@+id/button_refresh"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:src="@drawable/ic_search"
                        android:tint="@color/white"
                        android:contentDescription="Scan for devices"
                        android:padding="6dp" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view_available_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    tools:listitem="@layout/item_device_bluetooth_style" />

                <!-- Empty State for Available Devices -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_no_available_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:visibility="gone"
                    app:cardBackgroundColor="@color/clipsy_card_dark"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp"
                    app:strokeWidth="0dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="12dp"
                        android:text="No available devices"
                        android:textColor="@color/clipsy_text_secondary"
                        android:textSize="13sp"
                        android:gravity="center"
                        android:minHeight="48dp" />

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </ScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_device"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/device_add_manual"
        app:backgroundTint="@color/clipsy_accent"
        app:srcCompat="@android:drawable/ic_input_add"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
