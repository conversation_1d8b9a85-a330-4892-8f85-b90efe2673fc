"""
Modern Tkinter-based main window for Clipsy Windows application.
Features Material Design-inspired styling with clean, minimal, responsive layout.
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import asyncio


import logging
import json


class ModernStyle:
    """Modern styling constants for the UI."""
    
    # Colors - Material Design inspired
    PRIMARY = "#1976d2"
    PRIMARY_DARK = "#1565c0"
    PRIMARY_LIGHT = "#e3f2fd"
    SECONDARY = "#424242"
    BACKGROUND = "#fafafa"
    SURFACE = "#ffffff"
    ERROR = "#d32f2f"
    SUCCESS = "#388e3c"
    WARNING = "#f57c00"
    
    # Typography
    FONT_FAMILY = "Segoe UI"
    FONT_SIZE_LARGE = 16
    FONT_SIZE_MEDIUM = 12
    FONT_SIZE_SMALL = 10
    
    # Spacing
    PADDING_LARGE = 20
    PADDING_MEDIUM = 12
    PADDING_SMALL = 8
    BORDER_RADIUS = 6


class ModernCard(ttk.Frame):
    """A modern card-like frame with styling."""
    
    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, **kwargs)
        self.configure(relief="solid", borderwidth=1)
        
        # Configure style
        self.configure(style="Card.TFrame")
        
        # Main container with padding
        self.container = ttk.Frame(self)
        self.container.pack(fill="both", expand=True, padx=ModernStyle.PADDING_MEDIUM, 
                           pady=ModernStyle.PADDING_MEDIUM)
        
        if title:
            self.title_label = ttk.Label(
                self.container, 
                text=title,
                font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_MEDIUM, "bold"),
                style="CardTitle.TLabel"
            )
            self.title_label.pack(anchor="w", pady=(0, ModernStyle.PADDING_SMALL))


class ModernButton(ttk.Button):
    """A modern styled button."""
    
    def __init__(self, parent, text="", style_type="primary", **kwargs):
        if style_type == "primary":
            style = "Primary.TButton"
        elif style_type == "secondary":
            style = "Secondary.TButton"
        else:
            style = "TButton"
        
        super().__init__(parent, text=text, style=style, **kwargs)


class StatusIndicator(ttk.Frame):
    """A modern status indicator with color and text."""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # Status dot (using a small label with colored background)
        self.status_dot = tk.Label(
            self, 
            text="●", 
            font=(ModernStyle.FONT_FAMILY, 12),
            fg="#9e9e9e",
            bg=ModernStyle.BACKGROUND
        )
        self.status_dot.pack(side="left", padx=(0, 5))
        
        # Status text
        self.status_label = ttk.Label(
            self, 
            text="Disconnected",
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_SMALL)
        )
        self.status_label.pack(side="left")
    
    def set_status(self, status: str, color: str):
        """Set the status indicator color and text."""
        self.status_dot.configure(fg=color)
        self.status_label.configure(text=status)


class ClipsyModernTkinterWindow:
    """Modern main window for Clipsy with Material Design-inspired Tkinter UI."""
    
    def __init__(self, config: dict, app):
        self.config = config
        self.app = app
        self.logger = logging.getLogger(__name__)
        
        # UI state
        self.current_page = "devices"
        self.is_dark_theme = config.get("ui", {}).get("theme", "light") == "dark"
        
        # UI components
        self.root = None
        self.notebook = None
        self.sidebar_frame = None
        self.content_frame = None
        
        # Navigation buttons
        self.nav_buttons = {}
        
        # Device components
        self.devices_listbox = None
        self.local_ip_label = None
        
        # History components
        self.history_tree = None
        
        # Settings components
        self.settings_vars = {}
        
        # Status components
        self.status_label = None
        self.connection_status = None
        
        # Initialize UI
        self.setup_styles()
    
    def setup_styles(self):
        """Setup modern styling for ttk widgets."""
        self.style = ttk.Style()
        
        # Configure modern styles
        self.style.configure(
            "Card.TFrame",
            background=ModernStyle.SURFACE,
            relief="solid",
            borderwidth=1
        )
        
        self.style.configure(
            "CardTitle.TLabel",
            foreground=ModernStyle.PRIMARY,
            background=ModernStyle.SURFACE,
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_MEDIUM, "bold")
        )
        
        self.style.configure(
            "Primary.TButton",
            foreground="white",
            background=ModernStyle.PRIMARY,
            borderwidth=0,
            focuscolor="none",
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_SMALL, "bold")
        )
        
        self.style.map(
            "Primary.TButton",
            background=[("active", ModernStyle.PRIMARY_DARK)]
        )
        
        self.style.configure(
            "Secondary.TButton",
            foreground=ModernStyle.PRIMARY,
            background=ModernStyle.SURFACE,
            borderwidth=1,
            focuscolor="none",
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_SMALL)
        )
        
        self.style.configure(
            "Sidebar.TButton",
            foreground=ModernStyle.SECONDARY,
            background=ModernStyle.BACKGROUND,
            borderwidth=0,
            focuscolor="none",
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_SMALL),
            anchor="w"
        )
        
        self.style.map(
            "Sidebar.TButton",
            background=[("active", ModernStyle.PRIMARY_LIGHT)]
        )
    
    def run(self):
        """Run the main window."""
        self.root = tk.Tk()
        self.root.title("Clipsy - Modern Clipboard Manager")
        self.root.geometry(f"{self.config['ui']['window_width']}x{self.config['ui']['window_height']}")
        self.root.configure(bg=ModernStyle.BACKGROUND)
        
        # Set window icon
        self._set_window_icon()
        
        # Setup UI
        self._create_widgets()
        self._setup_layout()
        
        # Start clipboard monitor
        if self.app.clipboard_monitor:
            self.app.clipboard_monitor.start()
        
        # Update UI periodically
        self._schedule_ui_updates()
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_close)
        
        # Start main loop
        self.root.mainloop()
    
    def _set_window_icon(self):
        """Set the window icon."""
        try:
            import os
            icon_path = os.path.join(os.path.dirname(__file__), "..", "..", "app_icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception:
            pass
    
    def _create_widgets(self):
        """Create all UI widgets with modern styling."""
        # Main container
        main_container = ttk.Frame(self.root)
        main_container.pack(fill="both", expand=True)
        
        # Create sidebar
        self._create_sidebar(main_container)
        
        # Create content area
        self._create_content_area(main_container)
        
        # Create status bar
        self._create_status_bar()
    
    def _create_sidebar(self, parent):
        """Create the modern sidebar navigation."""
        self.sidebar_frame = ttk.Frame(parent, style="Card.TFrame")
        self.sidebar_frame.pack(side="left", fill="y", padx=(0, 1))
        
        # App title
        title_frame = ttk.Frame(self.sidebar_frame)
        title_frame.pack(fill="x", pady=ModernStyle.PADDING_LARGE)
        
        title_label = ttk.Label(
            title_frame,
            text="Clipsy",
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_LARGE, "bold"),
            foreground=ModernStyle.PRIMARY,
            background=ModernStyle.SURFACE
        )
        title_label.pack()
        
        # Navigation buttons
        nav_frame = ttk.Frame(self.sidebar_frame)
        nav_frame.pack(fill="x", padx=ModernStyle.PADDING_SMALL)
        
        nav_items = [
            ("devices", "🖥️  Devices"),
            ("history", "📋  History"),
            ("settings", "⚙️  Settings")
        ]
        
        for key, text in nav_items:
            btn = ttk.Button(
                nav_frame,
                text=text,
                style="Sidebar.TButton",
                command=lambda k=key: self._switch_page(k)
            )
            btn.pack(fill="x", pady=2)
            self.nav_buttons[key] = btn
        
        # Connection status at bottom
        status_frame = ttk.Frame(self.sidebar_frame)
        status_frame.pack(side="bottom", fill="x", padx=ModernStyle.PADDING_SMALL, 
                         pady=ModernStyle.PADDING_SMALL)
        
        self.connection_status = StatusIndicator(status_frame)
        self.connection_status.pack(fill="x")

    def _create_content_area(self, parent):
        """Create the main content area."""
        self.content_frame = ttk.Frame(parent)
        self.content_frame.pack(side="right", fill="both", expand=True)

        # Create notebook for pages
        self.notebook = ttk.Notebook(self.content_frame)
        self.notebook.pack(fill="both", expand=True, padx=ModernStyle.PADDING_SMALL,
                          pady=ModernStyle.PADDING_SMALL)

        # Create pages
        self._create_devices_page()
        self._create_history_page()
        self._create_settings_page()

        # Hide tabs (we'll use sidebar navigation)
        self.notebook.configure(style="Hidden.TNotebook")

    def _create_devices_page(self):
        """Create the modern devices page."""
        devices_frame = ttk.Frame(self.notebook)
        self.notebook.add(devices_frame, text="Devices")

        # Page title
        title_frame = ttk.Frame(devices_frame)
        title_frame.pack(fill="x", pady=(ModernStyle.PADDING_MEDIUM, ModernStyle.PADDING_LARGE))

        title_label = ttk.Label(
            title_frame,
            text="Device Management",
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_LARGE, "bold"),
            foreground=ModernStyle.SECONDARY
        )
        title_label.pack(anchor="w")

        # Local IP card
        ip_card = ModernCard(devices_frame, "Local Network Information")
        ip_card.pack(fill="x", pady=(0, ModernStyle.PADDING_MEDIUM))

        self.local_ip_label = ttk.Label(
            ip_card.container,
            text="Detecting network...",
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_SMALL),
            foreground=ModernStyle.SECONDARY
        )
        self.local_ip_label.pack(anchor="w", pady=ModernStyle.PADDING_SMALL)

        # Discovered devices card
        devices_card = ModernCard(devices_frame, "Discovered Devices")
        devices_card.pack(fill="both", expand=True)

        # Device controls
        controls_frame = ttk.Frame(devices_card.container)
        controls_frame.pack(fill="x", pady=(0, ModernStyle.PADDING_SMALL))

        refresh_btn = ModernButton(controls_frame, "🔄 Refresh", "primary",
                                  command=self._refresh_devices)
        refresh_btn.pack(side="left", padx=(0, ModernStyle.PADDING_SMALL))

        add_manual_btn = ModernButton(controls_frame, "➕ Add Device", "secondary",
                                     command=self._add_manual_device)
        add_manual_btn.pack(side="left")

        # Devices list
        list_frame = ttk.Frame(devices_card.container)
        list_frame.pack(fill="both", expand=True)

        # Create listbox with scrollbar
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill="both", expand=True)

        scrollbar = ttk.Scrollbar(list_container)
        scrollbar.pack(side="right", fill="y")

        self.devices_listbox = tk.Listbox(
            list_container,
            yscrollcommand=scrollbar.set,
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_SMALL),
            bg=ModernStyle.SURFACE,
            fg=ModernStyle.SECONDARY,
            selectbackground=ModernStyle.PRIMARY_LIGHT,
            selectforeground=ModernStyle.PRIMARY,
            borderwidth=1,
            relief="solid"
        )
        self.devices_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.config(command=self.devices_listbox.yview)

        # Device action buttons
        device_actions = ttk.Frame(devices_card.container)
        device_actions.pack(fill="x", pady=(ModernStyle.PADDING_SMALL, 0))

        connect_btn = ModernButton(device_actions, "Connect", "primary",
                                  command=self._connect_to_selected_device)
        connect_btn.pack(side="left", padx=(0, ModernStyle.PADDING_SMALL))

        disconnect_btn = ModernButton(device_actions, "Disconnect", "secondary",
                                     command=self._disconnect_from_selected_device)
        disconnect_btn.pack(side="left")

    def _create_history_page(self):
        """Create the modern history page."""
        history_frame = ttk.Frame(self.notebook)
        self.notebook.add(history_frame, text="History")

        # Page title
        title_frame = ttk.Frame(history_frame)
        title_frame.pack(fill="x", pady=(ModernStyle.PADDING_MEDIUM, ModernStyle.PADDING_LARGE))

        title_label = ttk.Label(
            title_frame,
            text="Clipboard History",
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_LARGE, "bold"),
            foreground=ModernStyle.SECONDARY
        )
        title_label.pack(anchor="w")

        # History controls card
        controls_card = ModernCard(history_frame, "Quick Actions")
        controls_card.pack(fill="x", pady=(0, ModernStyle.PADDING_MEDIUM))

        controls_frame = ttk.Frame(controls_card.container)
        controls_frame.pack(fill="x")

        clear_btn = ModernButton(controls_frame, "🗑️ Clear History", "secondary",
                                command=self._clear_history)
        clear_btn.pack(side="left", padx=(0, ModernStyle.PADDING_SMALL))

        refresh_btn = ModernButton(controls_frame, "🔄 Refresh", "primary",
                                  command=self._refresh_history)
        refresh_btn.pack(side="left", padx=(0, ModernStyle.PADDING_SMALL))

        export_btn = ModernButton(controls_frame, "📤 Export", "secondary",
                                 command=self._export_history)
        export_btn.pack(side="left")

        # History list card
        history_card = ModernCard(history_frame, "Recent Clipboard Items")
        history_card.pack(fill="both", expand=True)

        # Create treeview for history
        tree_frame = ttk.Frame(history_card.container)
        tree_frame.pack(fill="both", expand=True)

        columns = ("Time", "Source", "Preview")
        self.history_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=10)

        # Configure columns
        self.history_tree.heading("Time", text="Time")
        self.history_tree.heading("Source", text="Source")
        self.history_tree.heading("Preview", text="Content Preview")

        self.history_tree.column("Time", width=150)
        self.history_tree.column("Source", width=100)
        self.history_tree.column("Preview", width=300)

        # Add scrollbar
        tree_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=tree_scrollbar.set)

        self.history_tree.pack(side="left", fill="both", expand=True)
        tree_scrollbar.pack(side="right", fill="y")

    def _create_settings_page(self):
        """Create the modern settings page."""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="Settings")

        # Create scrollable frame
        canvas = tk.Canvas(settings_frame, bg=ModernStyle.BACKGROUND)
        scrollbar = ttk.Scrollbar(settings_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Page title
        title_frame = ttk.Frame(scrollable_frame)
        title_frame.pack(fill="x", pady=(ModernStyle.PADDING_MEDIUM, ModernStyle.PADDING_LARGE))

        title_label = ttk.Label(
            title_frame,
            text="Settings",
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_LARGE, "bold"),
            foreground=ModernStyle.SECONDARY
        )
        title_label.pack(anchor="w")

        # Network settings card
        network_card = ModernCard(scrollable_frame, "Network Configuration")
        network_card.pack(fill="x", pady=(0, ModernStyle.PADDING_MEDIUM))

        # Network settings form
        network_form = ttk.Frame(network_card.container)
        network_form.pack(fill="x")

        # Discovery port
        ttk.Label(network_form, text="Discovery Port:").grid(row=0, column=0, sticky="w", pady=2)
        self.settings_vars["discovery_port"] = tk.StringVar(value=str(self.config.get("network", {}).get("discovery_port", 8765)))
        discovery_entry = ttk.Entry(network_form, textvariable=self.settings_vars["discovery_port"], width=10)
        discovery_entry.grid(row=0, column=1, sticky="w", padx=(10, 0), pady=2)

        # WebSocket port
        ttk.Label(network_form, text="WebSocket Port:").grid(row=1, column=0, sticky="w", pady=2)
        self.settings_vars["websocket_port"] = tk.StringVar(value=str(self.config.get("network", {}).get("websocket_port", 8766)))
        websocket_entry = ttk.Entry(network_form, textvariable=self.settings_vars["websocket_port"], width=10)
        websocket_entry.grid(row=1, column=1, sticky="w", padx=(10, 0), pady=2)

        # Auto discovery
        self.settings_vars["auto_discovery"] = tk.BooleanVar(value=self.config.get("network", {}).get("auto_discovery", True))
        auto_discovery_check = ttk.Checkbutton(network_form, text="Enable automatic device discovery",
                                              variable=self.settings_vars["auto_discovery"])
        auto_discovery_check.grid(row=2, column=0, columnspan=2, sticky="w", pady=5)

        # Clipboard settings card
        clipboard_card = ModernCard(scrollable_frame, "Clipboard Settings")
        clipboard_card.pack(fill="x", pady=(0, ModernStyle.PADDING_MEDIUM))

        clipboard_form = ttk.Frame(clipboard_card.container)
        clipboard_form.pack(fill="x")

        # Auto sync
        self.settings_vars["auto_sync"] = tk.BooleanVar(value=self.config.get("clipboard", {}).get("auto_sync", True))
        auto_sync_check = ttk.Checkbutton(clipboard_form, text="Enable automatic clipboard sync",
                                         variable=self.settings_vars["auto_sync"])
        auto_sync_check.grid(row=0, column=0, columnspan=2, sticky="w", pady=2)

        # History enabled
        self.settings_vars["history_enabled"] = tk.BooleanVar(value=self.config.get("clipboard", {}).get("history_enabled", True))
        history_check = ttk.Checkbutton(clipboard_form, text="Enable clipboard history",
                                       variable=self.settings_vars["history_enabled"])
        history_check.grid(row=1, column=0, columnspan=2, sticky="w", pady=2)

        # History limit
        ttk.Label(clipboard_form, text="History Limit:").grid(row=2, column=0, sticky="w", pady=2)
        self.settings_vars["history_limit"] = tk.StringVar(value=str(self.config.get("clipboard", {}).get("history_limit", 100)))
        history_entry = ttk.Entry(clipboard_form, textvariable=self.settings_vars["history_limit"], width=10)
        history_entry.grid(row=2, column=1, sticky="w", padx=(10, 0), pady=2)

        # UI settings card
        ui_card = ModernCard(scrollable_frame, "User Interface")
        ui_card.pack(fill="x", pady=(0, ModernStyle.PADDING_MEDIUM))

        ui_form = ttk.Frame(ui_card.container)
        ui_form.pack(fill="x")

        # Theme
        ttk.Label(ui_form, text="Theme:").grid(row=0, column=0, sticky="w", pady=2)
        self.settings_vars["theme"] = tk.StringVar(value=self.config.get("ui", {}).get("theme", "light").title())
        theme_combo = ttk.Combobox(ui_form, textvariable=self.settings_vars["theme"],
                                  values=["Light", "Dark"], state="readonly", width=10)
        theme_combo.grid(row=0, column=1, sticky="w", padx=(10, 0), pady=2)

        # Minimize to tray
        self.settings_vars["minimize_to_tray"] = tk.BooleanVar(value=self.config.get("ui", {}).get("minimize_to_tray", True))
        tray_check = ttk.Checkbutton(ui_form, text="Minimize to system tray",
                                    variable=self.settings_vars["minimize_to_tray"])
        tray_check.grid(row=1, column=0, columnspan=2, sticky="w", pady=2)

        # Show notifications
        self.settings_vars["show_notifications"] = tk.BooleanVar(value=self.config.get("ui", {}).get("show_notifications", True))
        notifications_check = ttk.Checkbutton(ui_form, text="Show notifications",
                                             variable=self.settings_vars["show_notifications"])
        notifications_check.grid(row=2, column=0, columnspan=2, sticky="w", pady=2)

        # Apply button
        apply_frame = ttk.Frame(scrollable_frame)
        apply_frame.pack(fill="x", pady=ModernStyle.PADDING_LARGE)

        apply_btn = ModernButton(apply_frame, "💾 Apply Settings", "primary",
                                command=self._apply_settings)
        apply_btn.pack(anchor="e")

        # Pack scrollable components
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def _create_status_bar(self):
        """Create the modern status bar."""
        status_frame = ttk.Frame(self.root, style="Card.TFrame")
        status_frame.pack(fill="x", side="bottom")

        # Status message
        self.status_label = ttk.Label(
            status_frame,
            text="Ready",
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_SMALL),
            foreground=ModernStyle.SECONDARY
        )
        self.status_label.pack(side="left", padx=ModernStyle.PADDING_SMALL,
                              pady=ModernStyle.PADDING_SMALL)

        # Connection count
        connection_count_label = ttk.Label(
            status_frame,
            text="No connections",
            font=(ModernStyle.FONT_FAMILY, ModernStyle.FONT_SIZE_SMALL),
            foreground=ModernStyle.SECONDARY
        )
        connection_count_label.pack(side="right", padx=ModernStyle.PADDING_SMALL,
                                   pady=ModernStyle.PADDING_SMALL)
        self.connection_count_label = connection_count_label

    def _setup_layout(self):
        """Setup the main layout."""
        # Layout is already handled in _create_widgets
        pass

    def _schedule_ui_updates(self):
        """Schedule periodic UI updates."""
        self._update_ui()
        self.root.after(1000, self._schedule_ui_updates)  # Update every second

    def _update_ui(self):
        """Update UI elements."""
        try:
            # Update local IP
            if self.app.device_discovery:
                local_ip = self.app.device_discovery.get_local_ip()
                if local_ip:
                    self.local_ip_label.config(text=f"Local IP: {local_ip}")
                    self.status_label.config(text="Ready")
                else:
                    self.local_ip_label.config(text="Network not available")
                    self.status_label.config(text="Network unavailable")

            # Update connection status
            if self.app.sync_server and self.app.sync_client:
                server_clients = self.app.sync_server.get_connected_clients_count()
                client_connections = len(self.app.sync_client.get_connected_devices())
                total_connections = server_clients + client_connections

                self.connection_count_label.config(text=f"{total_connections} connection(s)")

                if total_connections > 0:
                    self.connection_status.set_status("Connected", ModernStyle.SUCCESS)
                else:
                    self.connection_status.set_status("Disconnected", "#9e9e9e")

            # Update devices list
            self._update_devices_list()

        except Exception as e:
            self.logger.error(f"Error updating UI: {e}")

    def _update_devices_list(self):
        """Update the devices list."""
        try:
            if not self.app.device_discovery:
                return

            # Clear current list
            self.devices_listbox.delete(0, tk.END)

            # Get discovered devices
            devices = self.app.device_discovery.get_discovered_devices()

            for device_ip, device_info in devices.items():
                device_name = device_info.get('device_name', 'Unknown Device')
                device_type = device_info.get('device_type', 'unknown')
                last_seen = device_info.get('last_seen', 'Never')

                # Create list item
                item_text = f"{device_name} ({device_ip}) - {device_type.title()} • Last seen: {last_seen}"
                self.devices_listbox.insert(tk.END, item_text)

        except Exception as e:
            self.logger.error(f"Error updating devices list: {e}")

    def _switch_page(self, page_key: str):
        """Switch to a different page."""
        page_index = {"devices": 0, "history": 1, "settings": 2}.get(page_key, 0)
        self.notebook.select(page_index)
        self.current_page = page_key

        # Update button styles (visual feedback)
        for key, btn in self.nav_buttons.items():
            if key == page_key:
                btn.configure(style="Primary.TButton")
            else:
                btn.configure(style="Sidebar.TButton")

    # Action methods
    def _refresh_devices(self):
        """Refresh the devices list."""
        try:
            if self.app.device_discovery:
                # Trigger device discovery refresh
                asyncio.run_coroutine_threadsafe(
                    self.app.device_discovery.refresh_discovery(),
                    self.app.loop
                )
            self.status_label.config(text="Refreshing devices...")
        except Exception as e:
            self.logger.error(f"Error refreshing devices: {e}")
            messagebox.showerror("Error", f"Failed to refresh devices: {e}")

    def _add_manual_device(self):
        """Add a device manually."""
        try:
            ip = simpledialog.askstring("Add Device", "Enter device IP address:")
            if ip:
                # Add to manual IPs in config
                manual_ips = self.config.get("network", {}).get("manual_ips", [])
                if ip not in manual_ips:
                    manual_ips.append(ip)
                    self.config["network"]["manual_ips"] = manual_ips
                    self.status_label.config(text=f"Added device: {ip}")
                else:
                    messagebox.showinfo("Info", "Device already in manual list")
        except Exception as e:
            self.logger.error(f"Error adding manual device: {e}")
            messagebox.showerror("Error", f"Failed to add device: {e}")

    def _connect_to_selected_device(self):
        """Connect to the selected device."""
        try:
            selection = self.devices_listbox.curselection()
            if selection:
                # Extract IP from selected item
                item_text = self.devices_listbox.get(selection[0])
                # Parse IP from format "Name (IP) - Type • Last seen: Time"
                ip_start = item_text.find("(") + 1
                ip_end = item_text.find(")")
                if ip_start > 0 and ip_end > ip_start:
                    device_ip = item_text[ip_start:ip_end]
                    self.status_label.config(text=f"Connecting to {device_ip}...")
                    # Implement connection logic here
            else:
                messagebox.showwarning("Warning", "Please select a device to connect to")
        except Exception as e:
            self.logger.error(f"Error connecting to device: {e}")
            messagebox.showerror("Error", f"Failed to connect to device: {e}")

    def _disconnect_from_selected_device(self):
        """Disconnect from the selected device."""
        try:
            selection = self.devices_listbox.curselection()
            if selection:
                item_text = self.devices_listbox.get(selection[0])
                ip_start = item_text.find("(") + 1
                ip_end = item_text.find(")")
                if ip_start > 0 and ip_end > ip_start:
                    device_ip = item_text[ip_start:ip_end]
                    self.status_label.config(text=f"Disconnecting from {device_ip}...")
                    # Implement disconnection logic here
            else:
                messagebox.showwarning("Warning", "Please select a device to disconnect from")
        except Exception as e:
            self.logger.error(f"Error disconnecting from device: {e}")
            messagebox.showerror("Error", f"Failed to disconnect from device: {e}")

    def _clear_history(self):
        """Clear clipboard history."""
        try:
            result = messagebox.askyesno(
                "Clear History",
                "Are you sure you want to clear all clipboard history?"
            )
            if result:
                # Clear history (would integrate with clipboard history system)
                self.history_tree.delete(*self.history_tree.get_children())
                self.status_label.config(text="History cleared")
        except Exception as e:
            self.logger.error(f"Error clearing history: {e}")
            messagebox.showerror("Error", f"Failed to clear history: {e}")

    def _refresh_history(self):
        """Refresh clipboard history."""
        try:
            # This would be implemented based on the clipboard history system
            self.status_label.config(text="History refreshed")
        except Exception as e:
            self.logger.error(f"Error refreshing history: {e}")
            messagebox.showerror("Error", f"Failed to refresh history: {e}")

    def _export_history(self):
        """Export clipboard history."""
        try:
            filename = filedialog.asksaveasfilename(
                title="Export History",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if filename:
                # Export history (would integrate with clipboard history system)
                self.status_label.config(text=f"History exported to {filename}")
        except Exception as e:
            self.logger.error(f"Error exporting history: {e}")
            messagebox.showerror("Error", f"Failed to export history: {e}")

    def _apply_settings(self):
        """Apply settings changes."""
        try:
            # Update config with new values
            self.config["network"]["discovery_port"] = int(self.settings_vars["discovery_port"].get())
            self.config["network"]["websocket_port"] = int(self.settings_vars["websocket_port"].get())
            self.config["network"]["auto_discovery"] = self.settings_vars["auto_discovery"].get()

            self.config["clipboard"]["auto_sync"] = self.settings_vars["auto_sync"].get()
            self.config["clipboard"]["history_enabled"] = self.settings_vars["history_enabled"].get()
            self.config["clipboard"]["history_limit"] = int(self.settings_vars["history_limit"].get())

            self.config["ui"]["theme"] = self.settings_vars["theme"].get().lower()
            self.config["ui"]["minimize_to_tray"] = self.settings_vars["minimize_to_tray"].get()
            self.config["ui"]["show_notifications"] = self.settings_vars["show_notifications"].get()

            # Save config to file
            import os
            config_path = os.path.join(os.path.dirname(__file__), "..", "..", "config.json")
            with open(config_path, 'w') as f:
                json.dump(self.config, f, indent=2)

            self.status_label.config(text="Settings applied successfully")
            messagebox.showinfo("Settings", "Settings have been applied successfully!")

        except Exception as e:
            self.logger.error(f"Error applying settings: {e}")
            messagebox.showerror("Error", f"Failed to apply settings: {e}")

    def _on_window_close(self):
        """Handle window close event."""
        try:
            if self.config.get("ui", {}).get("minimize_to_tray", True):
                # Would minimize to tray here
                self.root.withdraw()
            else:
                self.root.quit()
        except Exception as e:
            self.logger.error(f"Error handling close event: {e}")
            self.root.quit()
