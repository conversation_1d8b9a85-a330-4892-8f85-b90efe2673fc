// Generated by view binder compiler. Do not edit!
package com.clipsy.android.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.clipsy.android.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDeviceBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final Button buttonConnect;

  @NonNull
  public final Button buttonPair;

  @NonNull
  public final Button buttonRemove;

  @NonNull
  public final Button buttonUnpair;

  @NonNull
  public final ImageView iconDeviceType;

  @NonNull
  public final ImageView indicatorOnline;

  @NonNull
  public final TextView textConnectionStatus;

  @NonNull
  public final TextView textDeviceIp;

  @NonNull
  public final TextView textDeviceName;

  @NonNull
  public final TextView textDeviceType;

  @NonNull
  public final TextView textLastSeen;

  private ItemDeviceBinding(@NonNull MaterialCardView rootView, @NonNull Button buttonConnect,
      @NonNull Button buttonPair, @NonNull Button buttonRemove, @NonNull Button buttonUnpair,
      @NonNull ImageView iconDeviceType, @NonNull ImageView indicatorOnline,
      @NonNull TextView textConnectionStatus, @NonNull TextView textDeviceIp,
      @NonNull TextView textDeviceName, @NonNull TextView textDeviceType,
      @NonNull TextView textLastSeen) {
    this.rootView = rootView;
    this.buttonConnect = buttonConnect;
    this.buttonPair = buttonPair;
    this.buttonRemove = buttonRemove;
    this.buttonUnpair = buttonUnpair;
    this.iconDeviceType = iconDeviceType;
    this.indicatorOnline = indicatorOnline;
    this.textConnectionStatus = textConnectionStatus;
    this.textDeviceIp = textDeviceIp;
    this.textDeviceName = textDeviceName;
    this.textDeviceType = textDeviceType;
    this.textLastSeen = textLastSeen;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDeviceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDeviceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_device, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDeviceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_connect;
      Button buttonConnect = ViewBindings.findChildViewById(rootView, id);
      if (buttonConnect == null) {
        break missingId;
      }

      id = R.id.button_pair;
      Button buttonPair = ViewBindings.findChildViewById(rootView, id);
      if (buttonPair == null) {
        break missingId;
      }

      id = R.id.button_remove;
      Button buttonRemove = ViewBindings.findChildViewById(rootView, id);
      if (buttonRemove == null) {
        break missingId;
      }

      id = R.id.button_unpair;
      Button buttonUnpair = ViewBindings.findChildViewById(rootView, id);
      if (buttonUnpair == null) {
        break missingId;
      }

      id = R.id.icon_device_type;
      ImageView iconDeviceType = ViewBindings.findChildViewById(rootView, id);
      if (iconDeviceType == null) {
        break missingId;
      }

      id = R.id.indicator_online;
      ImageView indicatorOnline = ViewBindings.findChildViewById(rootView, id);
      if (indicatorOnline == null) {
        break missingId;
      }

      id = R.id.text_connection_status;
      TextView textConnectionStatus = ViewBindings.findChildViewById(rootView, id);
      if (textConnectionStatus == null) {
        break missingId;
      }

      id = R.id.text_device_ip;
      TextView textDeviceIp = ViewBindings.findChildViewById(rootView, id);
      if (textDeviceIp == null) {
        break missingId;
      }

      id = R.id.text_device_name;
      TextView textDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (textDeviceName == null) {
        break missingId;
      }

      id = R.id.text_device_type;
      TextView textDeviceType = ViewBindings.findChildViewById(rootView, id);
      if (textDeviceType == null) {
        break missingId;
      }

      id = R.id.text_last_seen;
      TextView textLastSeen = ViewBindings.findChildViewById(rootView, id);
      if (textLastSeen == null) {
        break missingId;
      }

      return new ItemDeviceBinding((MaterialCardView) rootView, buttonConnect, buttonPair,
          buttonRemove, buttonUnpair, iconDeviceType, indicatorOnline, textConnectionStatus,
          textDeviceIp, textDeviceName, textDeviceType, textLastSeen);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
