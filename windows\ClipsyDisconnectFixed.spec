# -*- mode: python ; coding: utf-8 -*-
# PyInstaller spec file for Clipsy with disconnect functionality

import os

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(SPEC))

a = Analysis(
    ['src\\main.py'],
    pathex=[current_dir],
    binaries=[],
    datas=[
        # Include the app icon
        ('app_icon.ico', '.'),
        # Include config files
        ('config.json', '.'),
        # Include any other assets if they exist
    ],
    hiddenimports=[
        # Ensure all required modules are included
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tkinter.simpledialog',
        'asyncio',
        'threading',
        'websockets',
        'pyperclip',
        'requests',
        'psutil',
        'netifaces',
        'json',
        'logging',
        'socket',
        'uuid',
        'time',
        'datetime',
        'pathlib',
        'subprocess',
        'sys',
        'os',
        'platform',
        'queue',
        'concurrent.futures',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # Exclude unnecessary modules to reduce size
        'PyQt6',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL.ImageQt',
    ],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ClipsyDisconnectFixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to True for debugging, False for release
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico',  # Include the icon in the executable
)
