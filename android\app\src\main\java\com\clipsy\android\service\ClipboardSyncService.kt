package com.clipsy.android.service

import android.app.*
import android.content.BroadcastReceiver
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Binder
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.PowerManager
import android.util.Log
import androidx.core.app.NotificationCompat
import com.clipsy.android.ClipsyApplication
import com.clipsy.android.R
import com.clipsy.android.data.repository.ClipboardRepository
import com.clipsy.android.data.repository.DeviceRepository
import com.clipsy.android.network.DeviceDiscovery
import com.clipsy.android.network.WebSocketClient
import com.clipsy.android.network.WebSocketServer
import com.clipsy.android.ui.MainActivity
import kotlinx.coroutines.*
import java.security.MessageDigest
/**
 * Background service for clipboard synchronization.
 */
class ClipboardSyncService : Service() {

    companion object {
        private const val TAG = "ClipboardSyncService"
        private const val NOTIFICATION_ID = 1
        private const val CLIPBOARD_CHECK_INTERVAL = 1000L // 1 second
        private const val DEVICE_CLEANUP_INTERVAL = 60000L // 1 minute
        private const val NOTIFICATION_CHANNEL_ID = "clipboard_sync_channel"
        private const val ACTION_QUICK_SYNC = "com.clipsy.android.QUICK_SYNC"
    }

    private lateinit var deviceRepository: DeviceRepository
    private lateinit var clipboardRepository: ClipboardRepository
    private lateinit var deviceDiscovery: DeviceDiscovery
    private lateinit var webSocketClient: WebSocketClient
    private lateinit var webSocketServer: WebSocketServer
    private var wakeLock: PowerManager.WakeLock? = null
    private var connectionManager: com.clipsy.android.pairing.SimpleConnectionManager? = null
    
    private val binder = LocalBinder()
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    private var clipboardManager: ClipboardManager? = null
    private var lastClipboardContent: String = ""
    private var isMonitoring = false

    private var clipboardMonitorJob: Job? = null

    // Sync state management to prevent infinite loops
    private var isSettingClipboardFromRemote = false
    private var lastSyncedContentHash = ""
    private var syncDebounceTime = 1000L // 1 second debounce
    private var lastSyncTimestamp = 0L

    // Service readiness state
    private var isServiceFullyReady = false
    private var uiReadyTimestamp = 0L
    private val UI_READY_DELAY = 2000L // 2 seconds after service start

    // Broadcast receiver for quick sync action
    private val broadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                ACTION_QUICK_SYNC -> {
                    scope.launch {
                        checkClipboard()
                    }
                }
                "com.clipsy.android.MANUAL_SYNC" -> {
                    handleManualSync()
                }
            }
        }
    }
    private var deviceCleanupJob: Job? = null

    // Note: Manual clipboard sync only - no automatic detection


    
    inner class LocalBinder : Binder() {
        fun getService(): ClipboardSyncService = this@ClipboardSyncService
    }

    /**
     * Set the connection manager for the service.
     * This should be called after binding to the service.
     */
    fun setConnectionManager(manager: com.clipsy.android.pairing.SimpleConnectionManager) {
        this.connectionManager = manager
    }

    override fun onCreate() {
        super.onCreate()

        // Acquire wake lock to prevent service from being killed
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "Clipsy::ClipboardSyncService"
        )
        wakeLock?.acquire()

        // Initialize dependencies
        deviceRepository = DeviceRepository(this, connectionManager)
        clipboardRepository = ClipboardRepository.getInstance()
        deviceDiscovery = DeviceDiscovery(this, deviceRepository)

        // Clean up any existing duplicates in clipboard history
        scope.launch {
            clipboardRepository.removeDuplicates()
        }

        // Get WebSocketClient from DeviceRepository to ensure same instance
        webSocketClient = deviceRepository.getWebSocketClient()
        webSocketServer = WebSocketServer(this, deviceRepository, clipboardRepository, connectionManager)

        clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager

        // Register broadcast receiver for manual sync
        val filter = IntentFilter().apply {
            addAction(ACTION_QUICK_SYNC)
            addAction("com.clipsy.android.MANUAL_SYNC")
        }

        // Use RECEIVER_NOT_EXPORTED for Android 13+ compatibility
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(broadcastReceiver, filter, Context.RECEIVER_NOT_EXPORTED)
        } else {
            registerReceiver(broadcastReceiver, filter)
        }

        // Start components
        startNetworkComponents()
        startClipboardMonitoring()
        startPeriodicClipboardCheck()
        startDeviceCleanup()

        // Mark service as ready after a delay to ensure UI is ready
        Handler(Looper.getMainLooper()).postDelayed({
            isServiceFullyReady = true
            uiReadyTimestamp = System.currentTimeMillis()
            Log.d(TAG, "Service marked as fully ready for clipboard updates")
        }, UI_READY_DELAY)
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        try {
            createNotificationChannel()
            val notification = createNotification()
            startForeground(NOTIFICATION_ID, notification)
        } catch (e: Exception) {
            // Try to continue without foreground notification
            try {
                val basicNotification = createBasicNotification()
                startForeground(NOTIFICATION_ID, basicNotification)
            } catch (basicError: Exception) {
                // Continue as regular service
            }
        }

        return START_STICKY // Restart if killed by system
    }
    
    override fun onBind(intent: Intent): IBinder {
        return binder
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)

        // Restart the service to ensure it continues running
        val restartServiceIntent = Intent(applicationContext, ClipboardSyncService::class.java)
        applicationContext.startForegroundService(restartServiceIntent)
    }
    
    override fun onDestroy() {
        super.onDestroy()

        // Unregister broadcast receiver
        try {
            unregisterReceiver(broadcastReceiver)
        } catch (e: Exception) {
            // Ignore unregistration errors
        }

        stopNetworkComponents()
        stopClipboardMonitoring()
        scope.cancel()

        // Release wake lock
        wakeLock?.let {
            if (it.isHeld) {
                it.release()
            }
        }
    }
    
    /**
     * Start network components.
     */
    private fun startNetworkComponents() {
        scope.launch {
            try {
                // Clear cached devices with old port configuration
                deviceRepository.clearDevicesAndRediscover()

                // Set clipboard update callbacks
                webSocketClient.setClipboardUpdateCallback { content ->
                    updateLocalClipboard(content)
                }
                webSocketServer.setClipboardUpdateCallback { content ->
                    updateLocalClipboard(content)
                }

                // Start WebSocket server
                webSocketServer.startServer()

                // Start connection health monitoring
                webSocketClient.startConnectionHealthCheck()

            } catch (e: Exception) {
                Log.e(TAG, "Error starting network components", e)
            }
        }
    }
    
    /**
     * Stop network components.
     */
    private fun stopNetworkComponents() {
        scope.launch {
            try {
                deviceDiscovery.stopDiscovery()
                webSocketClient.disconnectAll()
                webSocketServer.stopServer()
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping network components", e)
            }
        }
    }
    
    /**
     * Start clipboard monitoring - ENABLED for full sync functionality.
     */
    private fun startClipboardMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Clipboard monitoring already started")
            return
        }

        try {
            // Set up clipboard change listener
            clipboardManager?.addPrimaryClipChangedListener {
                scope.launch {
                    try {
                        // Skip if we're setting clipboard from remote to prevent loops
                        if (isSettingClipboardFromRemote) {
                            Log.d(TAG, "Skipping clipboard change - setting from remote")
                            return@launch
                        }

                        val clipData = clipboardManager?.primaryClip
                        if (clipData != null && clipData.itemCount > 0) {
                            val content = clipData.getItemAt(0).text?.toString() ?: ""
                            if (content.isNotEmpty() && content != lastClipboardContent) {
                                Log.d(TAG, "Clipboard changed locally: ${content.take(50)}...")
                                handleClipboardChange(content)
                                lastClipboardContent = content
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error handling clipboard change", e)
                    }
                }
            }

            isMonitoring = true
            Log.d(TAG, "Clipboard monitoring started successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to start clipboard monitoring", e)
            isMonitoring = false
        }
    }

    /**
     * Start periodic clipboard checking as backup monitoring.
     */
    private fun startPeriodicClipboardCheck() {
        scope.launch {
            while (isMonitoring) {
                try {
                    delay(2000) // Check every 2 seconds as backup

                    // Skip if we're setting clipboard from remote
                    if (isSettingClipboardFromRemote) {
                        continue
                    }

                    val clipData = clipboardManager?.primaryClip
                    if (clipData != null && clipData.itemCount > 0) {
                        val content = clipData.getItemAt(0).text?.toString() ?: ""
                        if (content.isNotEmpty() && content != lastClipboardContent) {
                            Log.d(TAG, "Periodic check found clipboard change: ${content.take(50)}...")
                            handleClipboardChange(content)
                            lastClipboardContent = content
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in periodic clipboard check", e)
                }
            }
        }
    }

    /**
     * Stop clipboard monitoring.
     */
    private fun stopClipboardMonitoring() {
        isMonitoring = false
        try {
            // Note: ClipboardManager doesn't have removePrimaryClipChangedListener
            // The listener will be automatically cleaned up when the service is destroyed
            Log.d(TAG, "Clipboard monitoring stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping clipboard monitoring", e)
        }
        clipboardMonitorJob?.cancel()
    }

    /**
     * Generate content hash for duplicate detection.
     */
    private fun generateContentHash(content: String): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(content.toByteArray())
            hashBytes.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to generate content hash", e)
            content.hashCode().toString()
        }
    }

    /**
     * Check if content should be synced (avoid loops and duplicates).
     */
    private fun shouldSyncContent(content: String): Boolean {
        val currentTime = System.currentTimeMillis()
        val contentHash = generateContentHash(content)

        // Check if we're setting clipboard from remote (avoid loop)
        if (isSettingClipboardFromRemote) {
            return false
        }

        // Check if content is the same as last synced (avoid duplicates)
        if (contentHash == lastSyncedContentHash) {
            return false
        }

        // Check debounce time (avoid rapid-fire syncs)
        if (currentTime - lastSyncTimestamp < syncDebounceTime) {
            return false
        }

        return true
    }

    /**
     * Update sync tracking after successful sync.
     */
    private fun updateSyncTracking(content: String) {
        lastSyncedContentHash = generateContentHash(content)
        lastSyncTimestamp = System.currentTimeMillis()
    }

    /**
     * Check clipboard for changes.
     */
    private suspend fun checkClipboard() {
        try {
            if (!canAccessClipboard()) {
                return
            }

            val clipData = clipboardManager?.primaryClip

            if (clipData != null && clipData.itemCount > 0) {
                val item = clipData.getItemAt(0)
                val content = item.text?.toString() ?: ""

                if (content.isNotEmpty() && content != lastClipboardContent) {
                    // Check if we should sync this content (prevent loops/duplicates)
                    if (shouldSyncContent(content)) {
                        handleClipboardChange(content)
                        updateSyncTracking(content)
                    }

                    lastClipboardContent = content
                }
            }
        } catch (e: SecurityException) {
            // Clipboard access denied - Android security restriction
        } catch (e: Exception) {
            Log.e(TAG, "Error checking clipboard", e)
        }
    }

    /**
     * Check if we can access clipboard.
     */
    private fun canAccessClipboard(): Boolean {
        return try {
            clipboardManager?.primaryClip
            true
        } catch (e: SecurityException) {
            false
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Handle clipboard content change.
     */
    private suspend fun handleClipboardChange(content: String) {
        try {
            // Add to local clipboard history (only if different from last content)
            if (content != lastClipboardContent) {
                clipboardRepository.addLocalItem(content)
            }

            // Broadcast to connected devices
            webSocketClient.broadcastClipboard(content)
            webSocketServer.broadcastClipboard(content)

            // Update notification
            updateNotification("Synced clipboard content")

        } catch (e: Exception) {
            Log.e(TAG, "Error handling clipboard change", e)
        }
    }

    /**
     * Update local clipboard with content from remote device.
     */
    fun updateLocalClipboard(content: String) {
        Log.d(TAG, "updateLocalClipboard called with content length: ${content.length}")

        try {
            if (content != lastClipboardContent) {
                // Set sync lock to prevent infinite loop
                isSettingClipboardFromRemote = true

                // Always add to history immediately
                addToHistoryAndNotify(content)

                // Check if service is ready for clipboard updates
                if (!isServiceFullyReady) {
                    Log.d(TAG, "Service not fully ready, delaying clipboard update")
                    // Schedule clipboard update after service is ready
                    scheduleDelayedClipboardUpdate(content)
                } else {
                    // Service is ready, update clipboard immediately
                    performClipboardUpdate(content)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in updateLocalClipboard", e)
            // Ensure sync lock is released even on error
            isSettingClipboardFromRemote = false
        }
    }

    /**
     * Schedule a delayed clipboard update for when the service is ready.
     */
    private fun scheduleDelayedClipboardUpdate(content: String) {
        val delayUntilReady = maxOf(0, UI_READY_DELAY - (System.currentTimeMillis() - uiReadyTimestamp))
        val totalDelay = delayUntilReady + 500L // Extra 500ms buffer

        Log.d(TAG, "Scheduling clipboard update in ${totalDelay}ms")

        Handler(Looper.getMainLooper()).postDelayed({
            if (content == lastClipboardContent) {
                // Content hasn't changed, skip update
                isSettingClipboardFromRemote = false
                return@postDelayed
            }
            performClipboardUpdate(content)
        }, totalDelay)
    }

    /**
     * Perform the actual clipboard update on the main thread.
     */
    private fun performClipboardUpdate(content: String) {
        Handler(Looper.getMainLooper()).post {
            try {
                Log.d(TAG, "Performing clipboard update - Service ready: $isServiceFullyReady")

                // Check if we can safely access clipboard
                val canAccess = canAccessClipboardSafely()
                Log.d(TAG, "Clipboard access check: $canAccess")

                // Check if we can access clipboard
                val clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
                if (clipboardManager == null) {
                    Log.e(TAG, "ClipboardManager is null")
                    return@post
                }

                if (!canAccess) {
                    Log.w(TAG, "🔒 Cannot access clipboard - app not in foreground or Android restrictions")

                    // Still update our internal tracking and history
                    lastClipboardContent = content

                    // Show informative notification
                    updateNotification("Content received - Open Clipsy to access clipboard")
                    return@post
                }

                // Try to set clipboard with better error handling
                val clipData = ClipData.newPlainText("Clipsy", content)

                try {
                    Log.d(TAG, "Attempting to set clipboard content...")
                    clipboardManager.setPrimaryClip(clipData)

                    // Small delay to allow clipboard to be set
                    Handler(Looper.getMainLooper()).postDelayed({
                        try {
                            // Verify the clipboard was actually set by reading it back
                            val verifyClip = clipboardManager.primaryClip
                            val actualContent = verifyClip?.getItemAt(0)?.text?.toString()

                            if (actualContent == content) {
                                Log.d(TAG, "✅ Clipboard updated and verified successfully with content: ${content.take(50)}...")
                                lastClipboardContent = content

                                // Show success notification
                                updateNotification("✅ Clipboard synced successfully")
                            } else {
                                Log.w(TAG, "⚠️ Clipboard update failed - content verification mismatch")
                                Log.w(TAG, "Expected: ${content.take(50)}...")
                                Log.w(TAG, "Actual: ${actualContent?.take(50)}...")

                                // Show failure notification
                                updateNotification("⚠️ Clipboard sync failed - verification failed")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error verifying clipboard content", e)
                        }
                    }, 100) // 100ms delay for verification

                } catch (e: SecurityException) {
                    Log.w(TAG, "🔒 SecurityException when setting clipboard - Android blocked clipboard access")
                    Log.w(TAG, "This is normal Android behavior for background services")

                    // Still update our internal tracking and history
                    lastClipboardContent = content

                    // Show informative notification
                    updateNotification("Content received - Android blocked clipboard access")

                } catch (e: Exception) {
                    Log.e(TAG, "❌ Unexpected error setting clipboard", e)
                    updateNotification("❌ Clipboard sync error")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error in performClipboardUpdate", e)
            } finally {
                // Release sync lock after a short delay
                Handler(Looper.getMainLooper()).postDelayed({
                    isSettingClipboardFromRemote = false
                    Log.d(TAG, "Clipboard sync lock released")
                }, 500)
            }
        }
    }

    /**
     * Check if we can safely access clipboard.
     */
    private fun canAccessClipboardSafely(): Boolean {
        return try {
            // Try to access clipboard manager
            val cm = clipboardManager ?: return false

            // On Android 10+, check if app is in foreground for clipboard access
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                val runningAppProcesses = activityManager.runningAppProcesses

                if (runningAppProcesses != null) {
                    for (processInfo in runningAppProcesses) {
                        if (processInfo.processName == packageName) {
                            return processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
                        }
                    }
                }

                // If we can't determine foreground state, try anyway
                return true
            }

            // For Android 9 and below, clipboard access is generally allowed
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error checking clipboard access", e)
            false
        }
    }

    /**
     * Add content to history and update notification.
     */
    private fun addToHistoryAndNotify(content: String) {
        // Add to clipboard history as remote item
        scope.launch {
            try {
                clipboardRepository.addRemoteItem(
                    content = content,
                    deviceName = "PC",
                    deviceType = "windows",
                    deviceId = "pc-device"
                )
            } catch (e: Exception) {
                Log.e(TAG, "Failed to add remote item to clipboard history", e)
            }
        }

        // Update notification
        updateNotification("Received clipboard from remote device")
    }



    /**
     * Start device cleanup task - DISABLED for lightweight version.
     */
    private fun startDeviceCleanup() {
        // Disabled for lightweight mode
    }
    
    /**
     * Create notification channel.
     */
    private fun createNotificationChannel() {
        try {
            val channel = NotificationChannel(
                ClipsyApplication.NOTIFICATION_CHANNEL_ID,
                ClipsyApplication.NOTIFICATION_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH // Changed to HIGH for better background persistence
            ).apply {
                description = "Notifications for Clipsy background service - keeps clipboard sync active"
                setShowBadge(false)
                // Enable lock screen visibility for better persistence
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                // Prevent user from disabling this channel
                setBypassDnd(true)
                enableVibration(false)
                enableLights(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        } catch (e: Exception) {
            Log.e(TAG, "Error creating notification channel", e)
        }
    }
    
    /**
     * Create service notification.
     */
    private fun createNotification(message: String = "Clipsy ready - Manual sync mode"): Notification {
        try {
            val intent = Intent(this, MainActivity::class.java)
            val pendingIntent = PendingIntent.getActivity(
                this, 0, intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Quick sync action
            val quickSyncIntent = Intent(ACTION_QUICK_SYNC)
            val quickSyncPendingIntent = PendingIntent.getBroadcast(
                this, 1, quickSyncIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            return NotificationCompat.Builder(this, ClipsyApplication.NOTIFICATION_CHANNEL_ID)
                .setContentTitle("Clipsy - Background Active")
                .setContentText(message)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentIntent(pendingIntent)
                .addAction(R.drawable.ic_notification, "Sync Now", quickSyncPendingIntent)
                .setOngoing(true) // Prevents user from swiping away
                .setSilent(true)
                .setPriority(NotificationCompat.PRIORITY_HIGH) // High priority for better persistence
                .setCategory(NotificationCompat.CATEGORY_SERVICE) // Mark as service notification
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC) // Show on lock screen
                .setAutoCancel(false) // Prevent auto-cancellation
                .setLocalOnly(true) // Don't sync to wearables
                .setShowWhen(false) // Don't show timestamp
                .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE) // Immediate foreground
                .build()
        } catch (e: Exception) {
            Log.e(TAG, "Error creating notification", e)
            // Return basic notification as fallback
            return createBasicNotification()
        }
    }

    /**
     * Create basic notification without actions (fallback).
     */
    private fun createBasicNotification(): Notification {
        return try {
            NotificationCompat.Builder(this, ClipsyApplication.NOTIFICATION_CHANNEL_ID)
                .setContentTitle("Clipsy")
                .setContentText("Clipboard sync service running")
                .setSmallIcon(android.R.drawable.ic_dialog_info) // Use system icon as fallback
                .setOngoing(true)
                .setSilent(true)
                .build()
        } catch (e: Exception) {
            Log.e(TAG, "Error creating basic notification", e)
            // Absolute minimal notification
            NotificationCompat.Builder(this, "default")
                .setContentTitle("Clipsy")
                .setContentText("Service running")
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .build()
        }
    }
    
    /**
     * Update notification with new message.
     */
    private fun updateNotification(message: String) {
        try {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.notify(NOTIFICATION_ID, createNotification(message))
        } catch (e: Exception) {
            Log.e(TAG, "Error updating notification", e)
            // Don't crash the service if notification update fails
        }
    }


    
    /**
     * Get service status.
     */
    fun getServiceStatus(): String {
        val connectedDevices = deviceRepository.getConnectedDeviceCount()
        val discoveredDevices = deviceRepository.getDeviceCount()
        
        return when {
            connectedDevices > 0 -> "$connectedDevices device(s) connected"
            discoveredDevices > 0 -> "$discoveredDevices device(s) found"
            else -> "Searching for devices..."
        }
    }

    /**
     * Force reconnect all devices (useful for clearing stuck connections).
     */
    fun forceReconnectAllDevices() {
        Log.d(TAG, "Force reconnecting all devices...")
        webSocketClient.forceReconnectAll()
    }

    /**
     * Get local IP address.
     */
    fun getLocalIpAddress(): String? {
        return if (::deviceDiscovery.isInitialized) {
            deviceDiscovery.getLocalIpAddress()
        } else {
            null
        }
    }
    
    /**
     * Refresh device discovery.
     */
    fun refreshDevices() {
        scope.launch {
            deviceDiscovery.stopDiscovery()
            delay(1000) // Brief pause
            deviceDiscovery.startDiscovery()
        }
    }
    
    /**
     * Connect to a device.
     */
    fun connectToDevice(device: com.clipsy.android.data.model.Device) {
        webSocketClient.connectToDevice(device)
    }
    
    /**
     * Disconnect from a device.
     */
    fun disconnectFromDevice(device: com.clipsy.android.data.model.Device) {
        webSocketClient.disconnectFromDevice(device)
    }

    /**
     * Handle manual sync action - sync current clipboard to PC.
     */
    fun handleManualSync() {
        scope.launch {
            try {
                // Get current clipboard content
                val content = clipboardManager?.primaryClip?.let { clip ->
                    if (clip.itemCount > 0) {
                        clip.getItemAt(0).text?.toString() ?: ""
                    } else ""
                } ?: ""

                if (content.isBlank()) {
                    updateNotification("No clipboard content to sync")
                    return@launch
                }

                // Add to local clipboard history
                clipboardRepository.addLocalItem(content)
                lastClipboardContent = content

                // Sync to connected devices via WebSocket
                val connectedDevices = deviceRepository.getConnectedDevicesSync()

                if (connectedDevices.isEmpty()) {
                    updateNotification("No PC connected for sync")
                    return@launch
                }

                for (device in connectedDevices) {
                    try {
                        webSocketClient.sendClipboardContent(content)
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to send clipboard to ${device.deviceName}: ${e.message}")
                    }
                }

                // Update notification
                updateNotification("Manually synced to PC (${content.length} chars)")

            } catch (e: Exception) {
                Log.e(TAG, "Error handling manual sync", e)
            }
        }
    }


}
