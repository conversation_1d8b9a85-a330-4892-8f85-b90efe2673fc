// Generated by view binder compiler. Do not edit!
package com.clipsy.android.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.clipsy.android.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddManualDeviceBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextInputEditText editTextIp;

  @NonNull
  public final TextInputEditText editTextName;

  private DialogAddManualDeviceBinding(@NonNull LinearLayout rootView,
      @NonNull TextInputEditText editTextIp, @NonNull TextInputEditText editTextName) {
    this.rootView = rootView;
    this.editTextIp = editTextIp;
    this.editTextName = editTextName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddManualDeviceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddManualDeviceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_manual_device, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddManualDeviceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.edit_text_ip;
      TextInputEditText editTextIp = ViewBindings.findChildViewById(rootView, id);
      if (editTextIp == null) {
        break missingId;
      }

      id = R.id.edit_text_name;
      TextInputEditText editTextName = ViewBindings.findChildViewById(rootView, id);
      if (editTextName == null) {
        break missingId;
      }

      return new DialogAddManualDeviceBinding((LinearLayout) rootView, editTextIp, editTextName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
