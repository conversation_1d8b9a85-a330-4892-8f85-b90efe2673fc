package com.clipsy.android.ui.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.clipsy.android.R
import com.clipsy.android.data.model.Device
import com.clipsy.android.data.model.ConnectionState
import com.clipsy.android.data.model.ConnectionStatus
import com.clipsy.android.databinding.ItemDeviceBluetoothStyleBinding

/**
 * RecyclerView adapter for displaying devices in Bluetooth settings style.
 */
class BluetoothStyleDeviceAdapter(
    private val onDeviceClick: (Device) -> Unit,
    private val onInfoClick: (Device) -> Unit
) : ListAdapter<Device, BluetoothStyleDeviceAdapter.DeviceViewHolder>(DeviceDiffCallback()) {
    
    private var connectionStates: Map<String, ConnectionState> = emptyMap()
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        val binding = ItemDeviceBluetoothStyleBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return DeviceViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: DeviceViewHolder, position: Int) {
        val device = getItem(position)
        val connectionState = connectionStates[device.getUniqueId()]
        holder.bind(device, connectionState)
    }
    
    fun updateConnectionStates(states: Map<String, ConnectionState>) {
        connectionStates = states
        notifyDataSetChanged()
    }
    
    inner class DeviceViewHolder(
        private val binding: ItemDeviceBluetoothStyleBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(device: Device, connectionState: ConnectionState?) {
            binding.apply {
                // Device name
                textDeviceName.text = device.deviceName
                
                // Device type icon
                val iconRes = when (device.deviceType.lowercase()) {
                    "windows", "pc", "desktop", "laptop" -> R.drawable.ic_computer
                    "android", "mobile", "phone" -> R.drawable.ic_phone
                    else -> R.drawable.ic_computer
                }
                iconDeviceType.setImageResource(iconRes)
                
                // Connection status
                val status = connectionState?.status ?: ConnectionStatus.DISCONNECTED
                updateConnectionStatus(status)
                
                // Click listeners
                root.setOnClickListener {
                    onDeviceClick(device)
                }
                
                buttonInfo.setOnClickListener {
                    onInfoClick(device)
                }
            }
        }
        
        private fun updateConnectionStatus(status: ConnectionStatus) {
            binding.apply {
                when (status) {
                    ConnectionStatus.CONNECTED -> {
                        textConnectionStatus.text = "Connected"
                        textConnectionStatus.setTextColor(
                            root.context.getColor(R.color.status_connected)
                        )
                        textConnectionStatus.visibility = android.view.View.VISIBLE
                    }
                    ConnectionStatus.CONNECTING -> {
                        textConnectionStatus.text = "Connecting..."
                        textConnectionStatus.setTextColor(
                            root.context.getColor(R.color.status_connecting)
                        )
                        textConnectionStatus.visibility = android.view.View.VISIBLE
                    }
                    ConnectionStatus.SYNCING -> {
                        textConnectionStatus.text = "Syncing..."
                        textConnectionStatus.setTextColor(
                            root.context.getColor(R.color.status_connecting)
                        )
                        textConnectionStatus.visibility = android.view.View.VISIBLE
                    }
                    ConnectionStatus.ERROR -> {
                        textConnectionStatus.text = "Error"
                        textConnectionStatus.setTextColor(
                            root.context.getColor(R.color.status_disconnected)
                        )
                        textConnectionStatus.visibility = android.view.View.VISIBLE
                    }
                    ConnectionStatus.TIMEOUT -> {
                        textConnectionStatus.text = "Timeout"
                        textConnectionStatus.setTextColor(
                            root.context.getColor(R.color.status_disconnected)
                        )
                        textConnectionStatus.visibility = android.view.View.VISIBLE
                    }
                    else -> {
                        // For disconnected devices, hide status text
                        textConnectionStatus.visibility = android.view.View.GONE
                    }
                }
            }
        }
    }
    
    private class DeviceDiffCallback : DiffUtil.ItemCallback<Device>() {
        override fun areItemsTheSame(oldItem: Device, newItem: Device): Boolean {
            return oldItem.getUniqueId() == newItem.getUniqueId()
        }
        
        override fun areContentsTheSame(oldItem: Device, newItem: Device): Boolean {
            return oldItem == newItem
        }
    }
}
