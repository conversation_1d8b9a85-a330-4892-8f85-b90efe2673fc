<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_history" modulePackage="com.clipsy.android" filePath="app\src\main\res\layout\fragment_history.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_history_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="130" endOffset="14"/></Target><Target id="@+id/edit_text_message" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="37" startOffset="12" endLine="48" endOffset="68"/></Target><Target id="@+id/button_send_message" view="ImageButton"><Expressions/><location startLine="59" startOffset="16" endLine="68" endOffset="43"/></Target><Target id="@+id/recycler_view_history" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="81" startOffset="8" endLine="87" endOffset="61"/></Target><Target id="@+id/layout_empty" view="LinearLayout"><Expressions/><location startLine="90" startOffset="8" endLine="126" endOffset="22"/></Target></Targets></Layout>