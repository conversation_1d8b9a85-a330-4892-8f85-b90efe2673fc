#!/usr/bin/env python3
"""
Test script to check if mobile device is discoverable on port 8766.
"""

import asyncio
import websockets
import json
from datetime import datetime

async def test_mobile_device(ip: str, port: int = 8766):
    """Test if a mobile device is running Clipsy on the given IP and port."""
    try:
        uri = f"ws://{ip}:{port}"
        print(f"Testing {uri}...")
        
        # Try to connect with a timeout
        websocket = await asyncio.wait_for(
            websockets.connect(uri, ping_interval=None),
            timeout=5.0
        )
        
        print(f"[+] Connected to {ip}:{port}")

        # First, receive welcome message
        welcome = await asyncio.wait_for(websocket.recv(), timeout=3.0)
        welcome_data = json.loads(welcome)
        print(f"[<] Received welcome: {welcome_data}")

        if welcome_data.get("type") == "welcome":
            device_name = welcome_data.get("device_name", "Unknown")
            print(f"[INFO] Device name: {device_name}")

            # Now send a ping message
            ping_message = {
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(ping_message))
            print(f"[>] Sent ping to {ip}")

            # Wait for pong response
            pong = await asyncio.wait_for(websocket.recv(), timeout=3.0)
            await websocket.close()

            # Parse pong response
            pong_data = json.loads(pong)
            print(f"[<] Received pong: {pong_data}")

            if pong_data.get("type") == "pong":
                print(f"[SUCCESS] Found Clipsy device '{device_name}' at {ip}:{port}")
                return True
            else:
                print(f"[ERROR] Expected pong, got: {pong_data.get('type')}")
                return False
        else:
            await websocket.close()
            print(f"[ERROR] Expected welcome, got: {welcome_data.get('type')}")
            return False

    except asyncio.TimeoutError:
        print(f"[TIMEOUT] Timeout connecting to {ip}:{port}")
        return False
    except ConnectionRefusedError:
        print(f"[REFUSED] Connection refused by {ip}:{port}")
        return False
    except Exception as e:
        print(f"[ERROR] Error testing {ip}:{port}: {e}")
        return False

async def scan_common_ips():
    """Scan common IP addresses in the 192.168.1.x range."""
    print("Scanning for mobile devices on port 8766...")
    print("=" * 50)
    
    # Common mobile device IPs
    test_ips = [
        "************",  # Previously seen mobile IP
        "************",
        "************", 
        "************",
        "************",
        "************",
        "************",
        "************",
        "************",
        "************",
        "************",
    ]
    
    found_devices = []
    
    for ip in test_ips:
        if await test_mobile_device(ip):
            found_devices.append(ip)
        await asyncio.sleep(0.1)  # Small delay between tests
    
    print("=" * 50)
    if found_devices:
        print(f"[SUCCESS] Found {len(found_devices)} Clipsy device(s):")
        for ip in found_devices:
            print(f"   Device: {ip}:8766")
    else:
        print("[NO DEVICES] No Clipsy devices found on port 8766")
        print("\nTroubleshooting tips:")
        print("   1. Make sure Clipsy is running on your mobile device")
        print("   2. Check that mobile device is on the same WiFi network")
        print("   3. Verify mobile device IP address")
        print("   4. Check if mobile device firewall is blocking connections")

if __name__ == "__main__":
    asyncio.run(scan_common_ips())
