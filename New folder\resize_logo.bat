@echo off
echo Clipsy Logo Resizer
echo ==================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Check if PIL/Pillow is installed
python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo Installing Pillow (PIL) for image processing...
    pip install Pillow
)

REM Check if logo file exists
if not exist "logo_original.png" (
    echo Error: logo_original.png not found
    echo Please save your logo image as "logo_original.png" in this directory
    pause
    exit /b 1
)

echo Creating icons for all platforms...
python resize_logo.py logo_original.png --all

echo.
echo Done! Icons have been created in the following directories:
echo - android/app/src/main/res/ (Android app icons)
echo - windows/ (Windows app icons)
echo - web/ (Web/PWA icons)
echo.
pause
