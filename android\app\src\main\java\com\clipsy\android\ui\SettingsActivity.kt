package com.clipsy.android.ui

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.preference.PreferenceFragmentCompat
import com.clipsy.android.R
/**
 * Settings activity for Clipsy app.
 */
class SettingsActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        // Apply settings theme before calling super
        setTheme(R.style.Theme_Clipsy_Settings)
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings)

        // Apply dark theme colors to action bar
        supportActionBar?.setBackgroundDrawable(
            androidx.core.content.ContextCompat.getDrawable(this, android.R.color.black)
        )

        if (savedInstanceState == null) {
            supportFragmentManager
                .beginTransaction()
                .replace(R.id.settings_container, SettingsFragment())
                .commit()
        }

        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "Settings"
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    /**
     * Settings fragment.
     */
    class SettingsFragment : PreferenceFragmentCompat() {
        override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
            setPreferencesFromResource(R.xml.preferences, rootKey)
        }

        override fun onViewCreated(view: android.view.View, savedInstanceState: Bundle?) {
            super.onViewCreated(view, savedInstanceState)
            // Apply dark theme to preference list
            listView.setBackgroundColor(androidx.core.content.ContextCompat.getColor(requireContext(), R.color.clipsy_background))
        }
    }
}
