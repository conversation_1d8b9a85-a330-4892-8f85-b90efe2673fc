<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_device_bluetooth_style" modulePackage="com.clipsy.android" filePath="app\src\main\res\layout\item_device_bluetooth_style.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_device_bluetooth_style_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="72" endOffset="51"/></Target><Target id="@+id/icon_device_type" view="ImageView"><Expressions/><location startLine="21" startOffset="8" endLine="28" endOffset="47"/></Target><Target id="@+id/text_device_name" view="TextView"><Expressions/><location startLine="37" startOffset="12" endLine="44" endOffset="41"/></Target><Target id="@+id/text_connection_status" view="TextView"><Expressions/><location startLine="46" startOffset="12" endLine="55" endOffset="44"/></Target><Target id="@+id/button_info" view="ImageButton"><Expressions/><location startLine="60" startOffset="8" endLine="68" endOffset="47"/></Target></Targets></Layout>