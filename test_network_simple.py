#!/usr/bin/env python3
"""
Simple network accessibility test
"""

import socket
import subprocess

def test_network_access():
    """Test network accessibility."""
    print("Network Accessibility Test")
    print("=" * 30)
    
    # Test local access
    print("1. Testing local access (127.0.0.1:8766)...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(("127.0.0.1", 8766))
        sock.close()
        
        if result == 0:
            print("   SUCCESS: Local access OK")
        else:
            print("   FAILED: Local access failed")
            print("   Make sure Windows Clipsy app is running")
            return False
    except Exception as e:
        print(f"   ERROR: {e}")
        return False
    
    # Test Wi-Fi interface
    print("2. Testing Wi-Fi interface (*************:8766)...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(("*************", 8766))
        sock.close()
        
        if result == 0:
            print("   SUCCESS: Wi-Fi interface accessible")
        else:
            print("   FAILED: Wi-Fi interface not accessible")
            print("   This suggests firewall blocking external access")
            return False
    except Exception as e:
        print(f"   ERROR: {e}")
        return False
    
    # Check listening ports
    print("3. Checking listening ports...")
    try:
        result = subprocess.run([
            "netstat", "-an"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            listening_8766 = [line for line in lines if ":8766" in line and "LISTENING" in line]
            
            if listening_8766:
                print("   Port 8766 is listening:")
                for line in listening_8766:
                    print(f"      {line.strip()}")
            else:
                print("   Port 8766 is NOT listening")
                return False
    except Exception as e:
        print(f"   Port check error: {e}")
    
    return True

def check_firewall():
    """Check Windows Firewall status."""
    print("4. Checking Windows Firewall...")
    try:
        result = subprocess.run([
            "netsh", "advfirewall", "show", "allprofiles", "state"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            if "ON" in result.stdout:
                print("   Windows Firewall is ON - might be blocking connections")
                return True
            else:
                print("   Windows Firewall is OFF")
                return False
    except Exception as e:
        print(f"   Could not check firewall: {e}")
    
    return None

if __name__ == "__main__":
    print("Testing Windows PC network accessibility...")
    print()
    
    access_ok = test_network_access()
    firewall_on = check_firewall()
    
    print()
    print("RESULTS:")
    print("=" * 20)
    
    if access_ok:
        print("SUCCESS: Windows PC is accessible on the network")
        print("If Android still can't connect, check:")
        print("- Android app permissions")
        print("- Router settings (AP isolation)")
        print("- Android device network configuration")
    else:
        print("PROBLEM: Windows PC is not accessible from network")
        if firewall_on:
            print("SOLUTION: Configure Windows Firewall to allow port 8766")
            print()
            print("Quick fix:")
            print("1. Open Windows Security")
            print("2. Firewall & network protection")
            print("3. Allow an app through firewall")
            print("4. Add Clipsy.exe and allow Private/Public networks")
        else:
            print("Check if Windows Clipsy app is running correctly")
