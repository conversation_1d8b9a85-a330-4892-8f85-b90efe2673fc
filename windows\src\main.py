#!/usr/bin/env python3
"""
Clipsy - Cross-Platform Clipboard Manager
Windows Application Entry Point
"""

import sys
import os
import json
import logging
import asyncio
import threading
from pathlib import Path

# Add src directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.main_window import ClipsyMainWindow
from clipboard.monitor import ClipboardMonitor
from network.discovery import DeviceDiscovery
from network.sync_server import SyncServer
from network.sync_client import SyncClient
from pairing.pairing_manager import SimpleConnectionManager


class ClipsyApp:
    """Main application class for Clipsy Windows client."""
    
    def __init__(self):
        self.config = self.load_config()
        self.setup_logging()
        
        # Core components
        self.connection_manager = None
        self.clipboard_monitor = None
        self.device_discovery = None
        self.sync_server = None
        self.sync_client = None
        self.main_window = None
        
        # Event loop for async operations
        self.loop = None
        self.loop_thread = None

        # Initialization synchronization
        self.initialization_ready = threading.Event()
        
    def load_config(self):
        """Load configuration from config.json."""
        config_path = Path(__file__).parent.parent / "config.json"
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return self.get_default_config()
    
    def get_default_config(self):
        """Return default configuration."""
        return {
            "app": {"name": "Clipsy", "version": "1.0.0", "device_name": "Windows-PC"},
            "network": {"discovery_port": 8765, "websocket_port": 8766},
            "clipboard": {"sync_enabled": True, "history_enabled": True, "history_limit": 50},
            "ui": {"window_width": 600, "window_height": 400}
        }
    
    def setup_logging(self):
        """Setup logging configuration."""
        log_level = logging.INFO
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('clipsy.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def start_async_loop(self):
        """Start the asyncio event loop in a separate thread."""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()
    
    def stop_async_loop(self):
        """Stop the asyncio event loop."""
        if self.loop and not self.loop.is_closed():
            self.loop.call_soon_threadsafe(self.loop.stop)
            if self.loop_thread and self.loop_thread.is_alive():
                self.loop_thread.join(timeout=5)
    
    async def initialize_components(self):
        """Initialize all application components."""
        try:
            self.logger.info("Step 1: Initializing connection manager...")
            # Initialize simple connection manager
            self.connection_manager = SimpleConnectionManager()

            self.logger.info("Step 2: Initializing clipboard monitor...")
            # Initialize clipboard monitor
            self.clipboard_monitor = ClipboardMonitor(self.config["clipboard"])

            self.logger.info("Step 3: Initializing network components...")
            # Initialize network components
            self.device_discovery = DeviceDiscovery(self.config["network"], self.connection_manager)
            self.sync_server = SyncServer(self.config["network"], self.connection_manager)
            self.sync_client = SyncClient(self.config["network"], self.connection_manager)

            self.logger.info("Step 4: Starting sync server...")
            # Start services
            await self.sync_server.start()

            self.logger.info("Step 5: Updating discovery with server port...")
            # Update discovery with actual server port
            self.device_discovery.update_server_port(self.sync_server.port)

            self.logger.info("Step 6: Starting device discovery...")
            await self.device_discovery.start()

            self.logger.info("Step 7: Component initialization complete!")

            # Signal that initialization is ready
            self.initialization_ready.set()
            self.logger.info("Step 8: Initialization ready signal sent!")

            # Keep the async loop running for background tasks
            # This coroutine will never complete, which is intentional
            while True:
                await asyncio.sleep(1)

        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            # Signal failure
            self.initialization_ready.set()
            raise
    
    def run(self):
        """Run the main application."""
        try:
            # Start async event loop in background thread
            self.loop_thread = threading.Thread(target=self.start_async_loop, daemon=True)
            self.loop_thread.start()

            # Wait for loop to be ready
            import time
            time.sleep(0.5)

            # Initialize async components
            if self.loop:
                self.logger.info("🚀 Starting async component initialization...")
                # Start the initialization coroutine (but don't wait for it to complete)
                asyncio.run_coroutine_threadsafe(
                    self.initialize_components(), self.loop
                )

                # Wait for initialization to be ready
                self.logger.info("⏳ Waiting for initialization to complete...")
                if self.initialization_ready.wait(timeout=30):
                    self.logger.info("✅ Async components initialized successfully")
                else:
                    self.logger.error("❌ Async initialization timeout after 30 seconds")
                    raise TimeoutError("Async initialization timeout")
            else:
                raise RuntimeError("Failed to start async event loop")

            # Create and run main window (blocking)
            self.main_window = ClipsyMainWindow(self.config, self)
            self.main_window.run()
            
        except KeyboardInterrupt:
            pass
        except Exception as e:
            import traceback
            self.logger.error(f"Application error: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            raise
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Cleanup resources before exit."""
        if self.clipboard_monitor:
            self.clipboard_monitor.stop()

        if self.loop:
            if self.sync_server:
                asyncio.run_coroutine_threadsafe(
                    self.sync_server.stop(), self.loop
                )
            if self.device_discovery:
                asyncio.run_coroutine_threadsafe(
                    self.device_discovery.stop(), self.loop
                )

        self.stop_async_loop()


def main():
    """Main entry point."""
    try:
        app = ClipsyApp()
        app.run()
    except Exception as e:
        import traceback
        print(f"Fatal error: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
