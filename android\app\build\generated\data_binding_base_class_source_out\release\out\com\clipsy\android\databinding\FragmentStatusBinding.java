// Generated by view binder compiler. Do not edit!
package com.clipsy.android.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.clipsy.android.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentStatusBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button buttonRefreshDevices;

  @NonNull
  public final MaterialCardView cardNetworkInfo;

  @NonNull
  public final MaterialCardView cardServiceStatus;

  @NonNull
  public final ProgressBar progressDiscovery;

  @NonNull
  public final TextView textConnectedDevices;

  @NonNull
  public final TextView textConnectionStatus;

  @NonNull
  public final TextView textDiscoveredDevices;

  @NonNull
  public final TextView textDiscoveryStatus;

  @NonNull
  public final TextView textLocalIp;

  @NonNull
  public final TextView textServiceStatus;

  private FragmentStatusBinding(@NonNull ScrollView rootView, @NonNull Button buttonRefreshDevices,
      @NonNull MaterialCardView cardNetworkInfo, @NonNull MaterialCardView cardServiceStatus,
      @NonNull ProgressBar progressDiscovery, @NonNull TextView textConnectedDevices,
      @NonNull TextView textConnectionStatus, @NonNull TextView textDiscoveredDevices,
      @NonNull TextView textDiscoveryStatus, @NonNull TextView textLocalIp,
      @NonNull TextView textServiceStatus) {
    this.rootView = rootView;
    this.buttonRefreshDevices = buttonRefreshDevices;
    this.cardNetworkInfo = cardNetworkInfo;
    this.cardServiceStatus = cardServiceStatus;
    this.progressDiscovery = progressDiscovery;
    this.textConnectedDevices = textConnectedDevices;
    this.textConnectionStatus = textConnectionStatus;
    this.textDiscoveredDevices = textDiscoveredDevices;
    this.textDiscoveryStatus = textDiscoveryStatus;
    this.textLocalIp = textLocalIp;
    this.textServiceStatus = textServiceStatus;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentStatusBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentStatusBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_status, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentStatusBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_refresh_devices;
      Button buttonRefreshDevices = ViewBindings.findChildViewById(rootView, id);
      if (buttonRefreshDevices == null) {
        break missingId;
      }

      id = R.id.card_network_info;
      MaterialCardView cardNetworkInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardNetworkInfo == null) {
        break missingId;
      }

      id = R.id.card_service_status;
      MaterialCardView cardServiceStatus = ViewBindings.findChildViewById(rootView, id);
      if (cardServiceStatus == null) {
        break missingId;
      }

      id = R.id.progress_discovery;
      ProgressBar progressDiscovery = ViewBindings.findChildViewById(rootView, id);
      if (progressDiscovery == null) {
        break missingId;
      }

      id = R.id.text_connected_devices;
      TextView textConnectedDevices = ViewBindings.findChildViewById(rootView, id);
      if (textConnectedDevices == null) {
        break missingId;
      }

      id = R.id.text_connection_status;
      TextView textConnectionStatus = ViewBindings.findChildViewById(rootView, id);
      if (textConnectionStatus == null) {
        break missingId;
      }

      id = R.id.text_discovered_devices;
      TextView textDiscoveredDevices = ViewBindings.findChildViewById(rootView, id);
      if (textDiscoveredDevices == null) {
        break missingId;
      }

      id = R.id.text_discovery_status;
      TextView textDiscoveryStatus = ViewBindings.findChildViewById(rootView, id);
      if (textDiscoveryStatus == null) {
        break missingId;
      }

      id = R.id.text_local_ip;
      TextView textLocalIp = ViewBindings.findChildViewById(rootView, id);
      if (textLocalIp == null) {
        break missingId;
      }

      id = R.id.text_service_status;
      TextView textServiceStatus = ViewBindings.findChildViewById(rootView, id);
      if (textServiceStatus == null) {
        break missingId;
      }

      return new FragmentStatusBinding((ScrollView) rootView, buttonRefreshDevices, cardNetworkInfo,
          cardServiceStatus, progressDiscovery, textConnectedDevices, textConnectionStatus,
          textDiscoveredDevices, textDiscoveryStatus, textLocalIp, textServiceStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
