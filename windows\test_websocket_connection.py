#!/usr/bin/env python3
"""
WebSocket Connection Test Script
Tests direct WebSocket communication to debug sync issues
"""

import asyncio
import websockets
import json
import logging
import sys
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WebSocketTester:
    def __init__(self):
        self.android_ip = "*************"
        self.android_port = 8765
        self.pc_ip = "*************" 
        self.pc_port = 8766
        
    async def test_connection_to_android(self):
        """Test connecting from PC to Android WebSocket server"""
        android_url = f"ws://{self.android_ip}:{self.android_port}"
        logger.info(f"🔄 Testing connection to Android at {android_url}")
        
        try:
            async with websockets.connect(android_url, timeout=10) as websocket:
                logger.info("✅ Successfully connected to Android WebSocket server!")
                
                # Send test message
                test_message = {
                    "type": "clipboard_sync",
                    "content": "Test message from PC",
                    "timestamp": datetime.now().isoformat(),
                    "device_id": "Windows-PC-Test"
                }
                
                logger.info(f"📤 Sending test message: {test_message}")
                await websocket.send(json.dumps(test_message))
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    logger.info(f"📥 Received response: {response}")
                    return True
                except asyncio.TimeoutError:
                    logger.warning("⚠️ No response received within 5 seconds")
                    return True  # Connection worked, just no response
                    
        except Exception as e:
            logger.error(f"❌ Failed to connect to Android: {e}")
            return False
    
    async def test_pc_server_listening(self):
        """Test if PC WebSocket server is accepting connections"""
        pc_url = f"ws://{self.pc_ip}:{self.pc_port}"
        logger.info(f"🔄 Testing PC server at {pc_url}")
        
        try:
            async with websockets.connect(pc_url, timeout=10) as websocket:
                logger.info("✅ Successfully connected to PC WebSocket server!")
                
                # Send test message to self
                test_message = {
                    "type": "test_echo",
                    "content": "Echo test from PC to PC",
                    "timestamp": datetime.now().isoformat()
                }
                
                logger.info(f"📤 Sending echo test: {test_message}")
                await websocket.send(json.dumps(test_message))
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    logger.info(f"📥 Received echo response: {response}")
                    return True
                except asyncio.TimeoutError:
                    logger.warning("⚠️ No echo response received")
                    return True  # Connection worked
                    
        except Exception as e:
            logger.error(f"❌ Failed to connect to PC server: {type(e).__name__}: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return False
    
    async def run_tests(self):
        """Run all connection tests"""
        logger.info("🚀 Starting WebSocket connection tests...")
        
        # Test 1: PC server listening
        logger.info("\n" + "="*50)
        logger.info("TEST 1: PC WebSocket Server")
        logger.info("="*50)
        pc_server_ok = await self.test_pc_server_listening()
        
        # Test 2: Android connection
        logger.info("\n" + "="*50)
        logger.info("TEST 2: Android WebSocket Connection")
        logger.info("="*50)
        android_connection_ok = await self.test_connection_to_android()
        
        # Summary
        logger.info("\n" + "="*50)
        logger.info("TEST SUMMARY")
        logger.info("="*50)
        logger.info(f"PC Server Listening: {'✅ PASS' if pc_server_ok else '❌ FAIL'}")
        logger.info(f"Android Connection: {'✅ PASS' if android_connection_ok else '❌ FAIL'}")
        
        if pc_server_ok and android_connection_ok:
            logger.info("🎉 All tests passed! WebSocket communication should work.")
        else:
            logger.error("💥 Some tests failed. Check network configuration.")
            
        return pc_server_ok and android_connection_ok

async def main():
    """Main test function"""
    tester = WebSocketTester()
    success = await tester.run_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
