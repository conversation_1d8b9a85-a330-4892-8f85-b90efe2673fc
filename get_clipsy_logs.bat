@echo off
echo Getting Clipsy Android logs...
echo ================================
echo.
echo Make sure your Android device is connected via USB
echo and USB debugging is enabled.
echo.
echo Press any key to start monitoring logs, then try connecting in the Android app...
pause

echo.
echo Clearing previous logs...
adb logcat -c

echo.
echo Monitoring Clipsy logs (press Ctrl+C to stop)...
echo ================================================
adb logcat | findstr /i "clipsy websocket connection"
