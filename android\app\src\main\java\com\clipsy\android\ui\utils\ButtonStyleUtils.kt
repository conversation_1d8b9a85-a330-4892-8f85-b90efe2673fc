package com.clipsy.android.ui.utils

import android.content.Context
import android.widget.Button
import androidx.core.content.ContextCompat
import com.clipsy.android.R
import com.google.android.material.button.MaterialButton

/**
 * Utility class for applying consistent button styling across the app.
 * Ensures proper text visibility in dark mode.
 */
object ButtonStyleUtils {
    
    /**
     * Apply primary button style - filled with accent color
     */
    fun applyPrimaryStyle(button: MaterialButton, context: Context) {
        button.apply {
            setTextColor(ContextCompat.getColor(context, R.color.button_text_primary))
            backgroundTintList = ContextCompat.getColorStateList(context, R.color.button_background_primary_selector)
            strokeWidth = 0
            cornerRadius = context.resources.getDimensionPixelSize(R.dimen.button_corner_radius)
            elevation = context.resources.getDimension(R.dimen.button_elevation)
            isAllCaps = false
        }
    }
    
    /**
     * Apply secondary button style - outlined with accent color
     */
    fun applySecondaryStyle(button: MaterialButton, context: Context) {
        button.apply {
            setTextColor(ContextCompat.getColor(context, R.color.button_text_secondary))
            backgroundTintList = ContextCompat.getColorStateList(context, android.R.color.transparent)
            strokeColor = ContextCompat.getColorStateList(context, R.color.button_stroke_primary)
            strokeWidth = context.resources.getDimensionPixelSize(R.dimen.button_stroke_width)
            cornerRadius = context.resources.getDimensionPixelSize(R.dimen.button_corner_radius)
            elevation = 0f
            isAllCaps = false
        }
    }
    
    /**
     * Apply tertiary button style - text only with accent color
     */
    fun applyTertiaryStyle(button: MaterialButton, context: Context) {
        button.apply {
            setTextColor(ContextCompat.getColor(context, R.color.button_text_secondary))
            backgroundTintList = ContextCompat.getColorStateList(context, android.R.color.transparent)
            strokeWidth = 0
            cornerRadius = context.resources.getDimensionPixelSize(R.dimen.button_corner_radius)
            elevation = 0f
            isAllCaps = false
        }
    }
    
    /**
     * Apply small button style - compact version
     */
    fun applySmallStyle(button: MaterialButton, context: Context) {
        button.apply {
            setTextColor(ContextCompat.getColor(context, R.color.high_contrast_text))
            backgroundTintList = ContextCompat.getColorStateList(context, R.color.button_background_secondary)
            strokeColor = ContextCompat.getColorStateList(context, R.color.button_stroke_primary)
            strokeWidth = context.resources.getDimensionPixelSize(R.dimen.button_stroke_width_small)
            cornerRadius = context.resources.getDimensionPixelSize(R.dimen.button_corner_radius_small)
            elevation = 0f
            isAllCaps = false
            textSize = 12f
        }
    }
    
    /**
     * Apply danger button style - for destructive actions
     */
    fun applyDangerStyle(button: MaterialButton, context: Context) {
        button.apply {
            setTextColor(ContextCompat.getColor(context, R.color.high_contrast_text))
            backgroundTintList = ContextCompat.getColorStateList(context, R.color.clipsy_error)
            strokeWidth = 0
            cornerRadius = context.resources.getDimensionPixelSize(R.dimen.button_corner_radius)
            elevation = context.resources.getDimension(R.dimen.button_elevation)
            isAllCaps = false
        }
    }
    
    /**
     * Apply success button style - for positive actions
     */
    fun applySuccessStyle(button: MaterialButton, context: Context) {
        button.apply {
            setTextColor(ContextCompat.getColor(context, R.color.high_contrast_text))
            backgroundTintList = ContextCompat.getColorStateList(context, R.color.status_connected)
            strokeWidth = 0
            cornerRadius = context.resources.getDimensionPixelSize(R.dimen.button_corner_radius)
            elevation = context.resources.getDimension(R.dimen.button_elevation)
            isAllCaps = false
        }
    }
    
    /**
     * Ensure button text is visible by applying high contrast colors
     */
    fun ensureTextVisibility(button: Button, context: Context) {
        button.setTextColor(ContextCompat.getColor(context, R.color.high_contrast_text))
    }
    
    /**
     * Apply style based on button type enum
     */
    fun applyStyle(button: MaterialButton, context: Context, style: ButtonStyle) {
        when (style) {
            ButtonStyle.PRIMARY -> applyPrimaryStyle(button, context)
            ButtonStyle.SECONDARY -> applySecondaryStyle(button, context)
            ButtonStyle.TERTIARY -> applyTertiaryStyle(button, context)
            ButtonStyle.SMALL -> applySmallStyle(button, context)
            ButtonStyle.DANGER -> applyDangerStyle(button, context)
            ButtonStyle.SUCCESS -> applySuccessStyle(button, context)
        }
    }
    
    enum class ButtonStyle {
        PRIMARY,
        SECONDARY,
        TERTIARY,
        SMALL,
        DANGER,
        SUCCESS
    }
}
