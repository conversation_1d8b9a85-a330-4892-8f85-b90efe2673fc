package com.clipsy.android.network

import android.content.Context
import android.util.Log
import com.clipsy.android.data.repository.ClipboardRepository
import com.clipsy.android.data.repository.DeviceRepository
import com.clipsy.android.data.model.ConnectionStatus
import kotlinx.coroutines.*
import org.java_websocket.WebSocket
import org.java_websocket.handshake.ClientHandshake
import org.java_websocket.server.WebSocketServer as JavaWebSocketServer
import org.json.JSONObject
import java.net.InetSocketAddress
import java.util.concurrent.ConcurrentHashMap
/**
 * WebSocket server for accepting connections from other Clipsy devices.
 */
class WebSocketServer(
    private val context: Context,
    private val deviceRepository: DeviceRepository,
    private val clipboardRepository: ClipboardRepository,
    private val connectionManager: com.clipsy.android.pairing.SimpleConnectionManager? = null
) {
    companion object {
        private const val TAG = "WebSocketServer"
        private const val SERVER_PORT = 8765  // Android server port (Windows connects here) - Fixed to match discovery advertisement
    }
    
    private var server: ClipsyWebSocketServer? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val connectedClients = ConcurrentHashMap<WebSocket, String>() // WebSocket to IP mapping
    private var clipboardUpdateCallback: ((String) -> Unit)? = null

    /**
     * Set callback for clipboard updates from remote devices.
     */
    fun setClipboardUpdateCallback(callback: (String) -> Unit) {
        clipboardUpdateCallback = callback
    }

    /**
     * Start the WebSocket server.
     */
    fun startServer() {
        scope.launch {
            try {
                Log.d(TAG, "Starting WebSocket server on port $SERVER_PORT")
                
                server = ClipsyWebSocketServer(InetSocketAddress(SERVER_PORT))
                server?.start()
                
                Log.d(TAG, "WebSocket server started successfully")
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start WebSocket server", e)
            }
        }
    }
    
    /**
     * Stop the WebSocket server.
     */
    fun stopServer() {
        scope.launch {
            try {
                Log.d(TAG, "Stopping WebSocket server")
                
                server?.stop()
                server = null
                connectedClients.clear()
                
                Log.d(TAG, "WebSocket server stopped")
                
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping WebSocket server", e)
            }
        }
    }
    
    /**
     * Send clipboard content to all connected clients.
     */
    fun broadcastClipboard(content: String) {
        scope.launch {
            try {
                val message = createClipboardMessage(content)
                val clients = connectedClients.keys.toList()
                
                Log.d(TAG, "Broadcasting clipboard to ${clients.size} clients")
                
                clients.forEach { client ->
                    if (client.isOpen) {
                        client.send(message)
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to broadcast clipboard", e)
            }
        }
    }
    
    /**
     * Send clipboard content to a specific client.
     */
    fun sendClipboardToClient(clientIp: String, content: String) {
        scope.launch {
            try {
                val message = createClipboardMessage(content)
                val client = connectedClients.entries.find { it.value == clientIp }?.key
                
                if (client != null && client.isOpen) {
                    client.send(message)
                    Log.d(TAG, "Sent clipboard content to client $clientIp")
                } else {
                    Log.w(TAG, "No active connection to client $clientIp")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to send clipboard to client $clientIp", e)
            }
        }
    }
    
    /**
     * Create clipboard message JSON.
     */
    private fun createClipboardMessage(content: String): String {
        return JSONObject().apply {
            put("type", "clipboard_sync")
            put("content", content)
            put("timestamp", System.currentTimeMillis())
            put("device_name", getDeviceName())
            put("device_id", getDeviceId())
        }.toString()
    }
    
    /**
     * Handle received clipboard message.
     */
    private suspend fun handleClipboardMessage(json: JSONObject, clientIp: String) {
        try {
            val content = json.getString("content")
            val timestamp = json.getLong("timestamp")
            val senderName = json.optString("device_name", "Unknown Device")
            val senderId = json.optString("device_id", "unknown")
            
            // Add to clipboard repository as remote item
            clipboardRepository.addRemoteItem(
                content = content,
                deviceName = senderName,
                deviceType = "Android", // Default device type
                deviceId = senderId
            )

            // Update local clipboard
            clipboardUpdateCallback?.invoke(content)

            Log.d(TAG, "Received clipboard content from $clientIp")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling clipboard message from $clientIp", e)
        }
    }
    
    /**
     * Get device name.
     */
    private fun getDeviceName(): String {
        return android.os.Build.MODEL ?: "Android Device"
    }
    
    /**
     * Get device ID.
     */
    private fun getDeviceId(): String {
        return android.provider.Settings.Secure.getString(
            context.contentResolver,
            android.provider.Settings.Secure.ANDROID_ID
        ) ?: "unknown"
    }
    
    /**
     * Check if server is running.
     */
    fun isRunning(): Boolean {
        return server != null
    }
    
    /**
     * Get connected client count.
     */
    fun getConnectedClientCount(): Int {
        return connectedClients.size
    }
    
    /**
     * Clean up resources.
     */
    fun cleanup() {
        stopServer()
        scope.cancel()
    }
    
    /**
     * Custom WebSocket server implementation.
     */
    private inner class ClipsyWebSocketServer(address: InetSocketAddress) : JavaWebSocketServer(address) {
        
        override fun onOpen(conn: WebSocket, handshake: ClientHandshake) {
            val clientIp = conn.remoteSocketAddress.address.hostAddress
            connectedClients[conn] = clientIp ?: "unknown"

            Log.d(TAG, "Client connected: $clientIp")

            // Log connection but don't update status - let WebSocketClient manage its own status
            // This prevents competing status updates that cause flickering
            scope.launch {
                try {
                    val devices = deviceRepository.getDevicesByIp(clientIp ?: "")
                    if (devices.isNotEmpty()) {
                        Log.i(TAG, "WebSocket connection established with $clientIp - found ${devices.size} device(s)")
                        Log.d(TAG, "Server connection established, but letting client manage status to prevent flickering")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error checking devices for $clientIp", e)
                }
            }

            // Send welcome message
            scope.launch {
                try {
                    val welcomeMessage = JSONObject().apply {
                        put("type", "welcome")
                        put("device_name", getDeviceName())
                        put("device_id", getDeviceId())
                        put("timestamp", System.currentTimeMillis())
                    }.toString()

                    conn.send(welcomeMessage)
                } catch (e: Exception) {
                    Log.e(TAG, "Error sending welcome message to $clientIp", e)
                }
            }
        }
        
        override fun onClose(conn: WebSocket, code: Int, reason: String, remote: Boolean) {
            val clientIp = connectedClients.remove(conn)
            Log.d(TAG, "Client disconnected: $clientIp (code: $code, reason: $reason)")

            // Update device connection status for all devices with this IP
            scope.launch {
                try {
                    val devices = deviceRepository.getDevicesByIp(clientIp ?: "")
                    if (devices.isNotEmpty()) {
                        Log.d(TAG, "Updating connection status to DISCONNECTED for ${devices.size} device(s) at $clientIp")

                        devices.forEach { device ->
                            Log.d(TAG, "Updating connection status to DISCONNECTED for ${device.deviceName} via server")
                            deviceRepository.updateConnectionStatus(device, ConnectionStatus.DISCONNECTED)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error updating device disconnection status for $clientIp", e)
                }
            }
        }
        
        override fun onMessage(conn: WebSocket, message: String) {
            val clientIp = connectedClients[conn] ?: "unknown"
            
            scope.launch {
                try {
                    val json = JSONObject(message)
                    val type = json.getString("type")
                    
                    when (type) {
                        "clipboard_sync" -> {
                            handleClipboardMessage(json, clientIp)
                        }
                        "ping" -> {
                            // Respond to ping
                            val pongMessage = JSONObject().apply {
                                put("type", "pong")
                                put("timestamp", System.currentTimeMillis())
                            }.toString()
                            conn.send(pongMessage)
                        }
                        "pong" -> {
                            // Handle pong response
                            Log.d(TAG, "Received pong from $clientIp")
                        }
                        "pairing_request" -> {
                            Log.d(TAG, "Pairing request ignored - using simple connections")
                        }
                        "pairing_response" -> {
                            Log.d(TAG, "Pairing response ignored - using simple connections")
                        }
                    }
                    
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing message from $clientIp", e)
                }
            }
        }
        
        override fun onError(conn: WebSocket?, ex: Exception) {
            val clientIp = if (conn != null) connectedClients[conn] else "unknown"
            Log.e(TAG, "WebSocket error for client $clientIp", ex)
            
            if (conn != null) {
                connectedClients.remove(conn)
            }
        }
        
        override fun onStart() {
            Log.d(TAG, "WebSocket server started on port $SERVER_PORT")
        }
    }

    /**
     * Broadcast clipboard content to all connected clients.
     */
    fun broadcastClipboardContent(content: String) {
        val clipboardMessage = JSONObject().apply {
            put("type", "clipboard_sync")
            put("content", content)
            put("timestamp", System.currentTimeMillis())
            put("source", "android")
        }

        val messageText = clipboardMessage.toString()
        val clientCount = connectedClients.size

        connectedClients.keys.forEach { conn ->
            try {
                conn.send(messageText)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to send clipboard content to client", e)
            }
        }

        Log.d(TAG, "Broadcasted clipboard content to $clientCount clients (${content.length} chars)")
    }


}
