package com.clipsy.android.data.repository

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.preference.PreferenceManager
import com.clipsy.android.data.model.ClipboardItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.security.MessageDigest
import java.util.concurrent.CopyOnWriteArrayList
/**
 * Repository for managing clipboard data.
 */
class ClipboardRepository private constructor() {

    private val _items = MutableLiveData<List<ClipboardItem>>(emptyList())
    // Thread-safe in-memory storage for clipboard items
    private val itemsList = CopyOnWriteArrayList<ClipboardItem>()
    private var context: Context? = null
    private var sharedPreferences: SharedPreferences? = null

    companion object {
        @Volatile
        private var INSTANCE: ClipboardRepository? = null

        fun getInstance(): ClipboardRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ClipboardRepository().also { INSTANCE = it }
            }
        }
    }

    /**
     * Initialize the repository with context for accessing preferences.
     */
    fun initialize(context: Context) {
        this.context = context.applicationContext
        this.sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context.applicationContext)
    }

    /**
     * Get the current history limit from preferences.
     */
    private fun getHistoryLimit(): Int {
        return sharedPreferences?.getString("history_limit", "50")?.toIntOrNull() ?: 50
    }

    /**
     * Get all clipboard items as LiveData.
     */
    fun getAllItems(): LiveData<List<ClipboardItem>> {
        return _items
    }

    /**
     * Get clipboard items with limit as LiveData.
     */
    fun getItemsWithLimit(limit: Int): LiveData<List<ClipboardItem>> {
        return _items
    }

    /**
     * Get all clipboard items synchronously.
     */
    suspend fun getAllItemsSync(): List<ClipboardItem> {
        return withContext(Dispatchers.IO) {
            itemsList.toList()
        }
    }

    /**
     * Get the latest clipboard item.
     */
    suspend fun getLatestItem(): ClipboardItem? {
        return withContext(Dispatchers.IO) {
            itemsList.maxByOrNull { it.timestamp }
        }
    }
    
    /**
     * Add a new clipboard item.
     */
    suspend fun addItem(
        content: String,
        source: String,
        deviceName: String? = null,
        deviceType: String? = null,
        deviceId: String? = null
    ): Long {
        return withContext(Dispatchers.IO) {
            // Simple duplicate check - if content already exists, skip it
            val existingItem = itemsList.find { it.content == content }
            if (existingItem != null) {
                // Content already exists, don't add duplicate
                return@withContext -1L
            }

            val item = ClipboardItem(
                content = content,
                timestamp = System.currentTimeMillis(),
                source = source,
                deviceName = deviceName,
                deviceType = deviceType,
                deviceId = deviceId,
                contentHash = generateContentHash(content)
            )

            itemsList.add(0, item) // Add to beginning

            // Apply history limit automatically
            val historyLimit = getHistoryLimit()
            if (historyLimit > 0 && itemsList.size > historyLimit) {
                // Remove oldest items beyond the limit
                while (itemsList.size > historyLimit) {
                    itemsList.removeAt(itemsList.size - 1)
                }
            }

            _items.postValue(itemsList.toList())
            item.id
        }
    }
    
    /**
     * Add a clipboard item from remote device.
     */
    suspend fun addRemoteItem(
        content: String,
        deviceName: String,
        deviceType: String,
        deviceId: String
    ): Long {
        return addItem(
            content = content,
            source = ClipboardItem.SOURCE_REMOTE,
            deviceName = deviceName,
            deviceType = deviceType,
            deviceId = deviceId
        )
    }
    
    /**
     * Add a local clipboard item.
     */
    suspend fun addLocalItem(content: String): Long {
        return addItem(
            content = content,
            source = ClipboardItem.SOURCE_LOCAL
        )
    }
    
    /**
     * Delete a specific item.
     */
    suspend fun deleteItem(item: ClipboardItem) {
        withContext(Dispatchers.IO) {
            itemsList.removeAll { it.id == item.id }
            _items.postValue(itemsList.toList())
        }
    }

    /**
     * Delete item by ID.
     */
    suspend fun deleteItemById(id: Long) {
        withContext(Dispatchers.IO) {
            itemsList.removeAll { it.id == id }
            _items.postValue(itemsList.toList())
        }
    }

    /**
     * Clear all clipboard history.
     */
    suspend fun clearAllItems() {
        withContext(Dispatchers.IO) {
            itemsList.clear()
            _items.postValue(emptyList())
        }
    }

    /**
     * Maintain history limit by deleting old items.
     */
    suspend fun maintainHistoryLimit(limit: Int) {
        withContext(Dispatchers.IO) {
            if (itemsList.size > limit) {
                val sortedItems = itemsList.sortedByDescending { it.timestamp }
                itemsList.clear()
                itemsList.addAll(sortedItems.take(limit))
                _items.postValue(itemsList.toList())
            }
        }
    }
    
    /**
     * Get item count.
     */
    suspend fun getItemCount(): Int {
        return withContext(Dispatchers.IO) {
            itemsList.size
        }
    }

    /**
     * Search items by content.
     */
    fun searchItems(query: String): LiveData<List<ClipboardItem>> {
        return _items
    }

    /**
     * Get items by source.
     */
    fun getItemsBySource(source: String): LiveData<List<ClipboardItem>> {
        return _items
    }

    /**
     * Get items by device.
     */
    fun getItemsByDevice(deviceId: String): LiveData<List<ClipboardItem>> {
        return _items
    }

    /**
     * Check if content is duplicate.
     */
    suspend fun isContentDuplicate(content: String): Boolean {
        return withContext(Dispatchers.IO) {
            itemsList.any { it.content == content }
        }
    }

    /**
     * Remove duplicate items, keeping only the most recent one for each content.
     */
    suspend fun removeDuplicates() {
        withContext(Dispatchers.IO) {
            val uniqueItems = mutableMapOf<String, ClipboardItem>()

            // Keep only the most recent item for each content
            for (item in itemsList.sortedByDescending { it.timestamp }) {
                if (!uniqueItems.containsKey(item.content)) {
                    uniqueItems[item.content] = item
                }
            }

            // Update the list with unique items, sorted by timestamp (newest first)
            itemsList.clear()
            itemsList.addAll(uniqueItems.values.sortedByDescending { it.timestamp })
            _items.postValue(itemsList.toList())
        }
    }
    
    /**
     * Generate SHA-256 hash for content.
     */
    private fun generateContentHash(content: String): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(content.toByteArray())
            hashBytes.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            // Fallback to simple hash
            content.hashCode().toString()
        }
    }
}
