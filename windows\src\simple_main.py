#!/usr/bin/env python3
"""
Simple Clipsy Windows Application
Minimal version to test basic functionality
"""

import sys
import os
import logging
import tkinter as tk
from tkinter import messagebox, scrolledtext, ttk
import threading
import time

# Add src directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class SimpleClipsyApp:
    """Simple version of Clipsy for testing."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.root = None
        self.running = False
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('clipsy_simple.log')
            ]
        )
        
    def create_ui(self):
        """Create the main UI."""
        self.root = tk.Tk()
        self.root.title("Clipsy - Simple Version")
        self.root.geometry("600x400")

        # Set window icon
        self._set_window_icon()
        
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Clipsy Clipboard Manager", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Status
        self.status_label = ttk.Label(main_frame, text="Starting...", 
                                     font=("Arial", 10))
        self.status_label.grid(row=1, column=0, columnspan=2, pady=(0, 10))
        
        # Clipboard content display
        ttk.Label(main_frame, text="Current Clipboard:").grid(row=2, column=0, sticky=tk.W)
        self.clipboard_text = scrolledtext.ScrolledText(main_frame, height=10, width=60)
        self.clipboard_text.grid(row=3, column=0, columnspan=2, pady=(5, 10), sticky=(tk.W, tk.E))
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="Refresh Clipboard", 
                  command=self.refresh_clipboard).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Test Clipboard", 
                  command=self.test_clipboard).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Exit", 
                  command=self.exit_app).pack(side=tk.LEFT, padx=5)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # Setup window close handler
        self.root.protocol("WM_DELETE_WINDOW", self.exit_app)
        
        # Start clipboard monitoring
        self.start_clipboard_monitor()

        # Update status
        self.status_label.config(text="Ready - Simple clipboard manager running")

    def _set_window_icon(self):
        """Set the window icon using the custom logo."""
        try:
            import os
            from pathlib import Path

            icon_paths = [
                Path(__file__).parent.parent.parent / "windows" / "app_icon.ico",
                Path(__file__).parent.parent / "windows" / "app_icon.ico",
                "../../windows/app_icon.ico",
                "../windows/app_icon.ico",
                "windows/app_icon.ico"
            ]

            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    self.root.iconbitmap(str(icon_path))
                    return

        except Exception:
            pass

    def start_clipboard_monitor(self):
        """Start monitoring clipboard changes."""
        def monitor():
            try:
                import pyperclip
                last_content = ""
                
                while self.running:
                    try:
                        current = pyperclip.paste()
                        if current != last_content:
                            last_content = current
                            # Update UI in main thread
                            self.root.after(0, lambda: self.update_clipboard_display(current))
                    except Exception:
                        pass

                    time.sleep(1)
                    
            except ImportError:
                self.root.after(0, lambda: self.status_label.config(
                    text="Error: pyperclip not available"))
        
        self.running = True
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def update_clipboard_display(self, content):
        """Update the clipboard display."""
        self.clipboard_text.delete(1.0, tk.END)
        self.clipboard_text.insert(1.0, content)
        self.status_label.config(text=f"Clipboard updated - {len(content)} characters")
    
    def refresh_clipboard(self):
        """Manually refresh clipboard content."""
        try:
            import pyperclip
            content = pyperclip.paste()
            self.update_clipboard_display(content)
        except ImportError:
            messagebox.showerror("Error", "pyperclip module not available")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to read clipboard: {e}")
    
    def test_clipboard(self):
        """Test clipboard functionality."""
        try:
            import pyperclip
            test_text = f"Clipsy test - {int(time.time())}"
            pyperclip.copy(test_text)
            messagebox.showinfo("Success", f"Test text copied to clipboard:\n{test_text}")
            self.refresh_clipboard()
        except ImportError:
            messagebox.showerror("Error", "pyperclip module not available")
        except Exception as e:
            messagebox.showerror("Error", f"Clipboard test failed: {e}")
    
    def exit_app(self):
        """Exit the application."""
        self.running = False
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """Run the application."""
        try:
            self.create_ui()
            self.start_clipboard_monitor()
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("Error", f"Application error: {e}")

def main():
    """Main entry point."""
    try:
        app = SimpleClipsyApp()
        app.run()
    except Exception as e:
        print(f"Fatal error: {e}")

if __name__ == "__main__":
    main()
