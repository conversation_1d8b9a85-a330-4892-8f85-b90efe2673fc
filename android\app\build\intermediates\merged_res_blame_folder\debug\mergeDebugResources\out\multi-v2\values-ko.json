{"logs": [{"outputFile": "com.clipsy.android.app-mergeDebugResources-55:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\983aed2efb51dcef9549ad462abafc61\\transformed\\preference-1.2.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,326,458,627,709", "endColumns": "65,80,73,131,168,81,75", "endOffsets": "166,247,321,453,622,704,780"}, "to": {"startLines": "48,50,111,113,116,117,118", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4171,4299,8669,8815,9127,9296,9378", "endColumns": "65,80,73,131,168,81,75", "endOffsets": "4232,4375,8738,8942,9291,9373,9449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bed6289f91d7b29bf75bbeb68ea27ae0\\transformed\\navigation-ui-2.7.5\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "8467,8567", "endColumns": "99,101", "endOffsets": "8562,8664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c7cbaeefdc37cd24560b8fc7fa3163c1\\transformed\\appcompat-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,396,490,591,673,771,877,957,1032,1123,1216,1311,1405,1505,1598,1693,1787,1878,1969,2049,2147,2241,2336,2436,2533,2633,2785,8947", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "391,485,586,668,766,872,952,1027,1118,1211,1306,1400,1500,1593,1688,1782,1873,1964,2044,2142,2236,2331,2431,2528,2628,2780,2874,9021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d1724a1e2b468b75777ff8300e82e0e6\\transformed\\material-1.10.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,249,314,378,447,521,600,683,789,864,926,1007,1069,1126,1213,1273,1331,1389,1448,1505,1559,1654,1710,1767,1821,1887,1991,2066,2143,2264,2329,2394,2494,2573,2648,2698,2749,2815,2879,2949,3026,3097,3165,3236,3303,3373,3466,3546,3620,3700,3782,3854,3919,3991,4039,4112,4176,4251,4328,4390,4454,4517,4584,4668,4746,4826,4904,4958,5013", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,64,63,68,73,78,82,105,74,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,76,120,64,64,99,78,74,49,50,65,63,69,76,70,67,70,66,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71", "endOffsets": "244,309,373,442,516,595,678,784,859,921,1002,1064,1121,1208,1268,1326,1384,1443,1500,1554,1649,1705,1762,1816,1882,1986,2061,2138,2259,2324,2389,2489,2568,2643,2693,2744,2810,2874,2944,3021,3092,3160,3231,3298,3368,3461,3541,3615,3695,3777,3849,3914,3986,4034,4107,4171,4246,4323,4385,4449,4512,4579,4663,4741,4821,4899,4953,5008,5080"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2879,2944,3008,3077,3151,3907,3990,4096,4237,4380,4461,4523,4580,4667,4727,4785,4843,4902,4959,5013,5108,5164,5221,5275,5341,5445,5520,5597,5718,5783,5848,5948,6027,6102,6152,6203,6269,6333,6403,6480,6551,6619,6690,6757,6827,6920,7000,7074,7154,7236,7308,7373,7445,7493,7566,7630,7705,7782,7844,7908,7971,8038,8122,8200,8280,8358,8412,8743", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "endColumns": "12,64,63,68,73,78,82,105,74,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,76,120,64,64,99,78,74,49,50,65,63,69,76,70,67,70,66,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71", "endOffsets": "294,2939,3003,3072,3146,3225,3985,4091,4166,4294,4456,4518,4575,4662,4722,4780,4838,4897,4954,5008,5103,5159,5216,5270,5336,5440,5515,5592,5713,5778,5843,5943,6022,6097,6147,6198,6264,6328,6398,6475,6546,6614,6685,6752,6822,6915,6995,7069,7149,7231,7303,7368,7440,7488,7561,7625,7700,7777,7839,7903,7966,8033,8117,8195,8275,8353,8407,8462,8810"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0d22882521834a86e542cf7cc54c4b22\\transformed\\core-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "38,39,40,41,42,43,44,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3230,3322,3422,3516,3613,3709,3807,9026", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3317,3417,3511,3608,3704,3802,3902,9122"}}]}]}