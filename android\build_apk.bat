@echo off
echo === Clipsy Android Build Script ===
echo Building Android APK...

REM Check if Java is available
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java not found. Please install Java JDK 11 or higher.
    exit /b 1
)

REM Create gradle wrapper directory if it doesn't exist
if not exist "gradle\wrapper" mkdir gradle\wrapper

REM Download gradle wrapper jar if it doesn't exist
if not exist "gradle\wrapper\gradle-wrapper.jar" (
    echo Downloading Gradle wrapper...
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v8.0.0/gradle/wrapper/gradle-wrapper.jar' -OutFile 'gradle\wrapper\gradle-wrapper.jar'"
)

REM Try to build the APK
echo Building debug APK...
gradlew.bat assembleDebug

if %errorlevel% equ 0 (
    echo.
    echo === Build Successful ===
    echo APK location: app\build\outputs\apk\debug\app-debug.apk
    if exist "app\build\outputs\apk\debug\app-debug.apk" (
        for %%A in ("app\build\outputs\apk\debug\app-debug.apk") do echo APK size: %%~zA bytes
    )
) else (
    echo.
    echo === Build Failed ===
    echo Please check the error messages above.
)

pause
