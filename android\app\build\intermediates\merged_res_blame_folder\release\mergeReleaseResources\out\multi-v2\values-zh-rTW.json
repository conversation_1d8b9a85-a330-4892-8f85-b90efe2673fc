{"logs": [{"outputFile": "com.clipsy.android.app-mergeReleaseResources-55:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0d22882521834a86e542cf7cc54c4b22\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "38,39,40,41,42,43,44,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3203,3295,3394,3488,3582,3675,3768,8901", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3290,3389,3483,3577,3670,3763,3859,8997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d1724a1e2b468b75777ff8300e82e0e6\\transformed\\material-1.10.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2452,2529,2603,2653,2704,2770,2833,2901,2979,3050,3111,3182,3249,3311,3398,3477,3542,3625,3710,3784,3848,3924,3972,4045,4109,4185,4263,4325,4389,4452,4518,4598,4676,4752,4831,4885,4940", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2447,2524,2598,2648,2699,2765,2828,2896,2974,3045,3106,3177,3244,3306,3393,3472,3537,3620,3705,3779,3843,3919,3967,4040,4104,4180,4258,4320,4384,4447,4513,4593,4671,4747,4826,4880,4935,5004"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2863,2927,2989,3056,3126,3864,3958,4065,4204,4347,4425,4485,4545,4623,4684,4742,4798,4858,4916,4970,5055,5111,5169,5223,5288,5380,5454,5531,5651,5714,5777,5876,5953,6027,6077,6128,6194,6257,6325,6403,6474,6535,6606,6673,6735,6822,6901,6966,7049,7134,7208,7272,7348,7396,7469,7533,7609,7687,7749,7813,7876,7942,8022,8100,8176,8255,8309,8633", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,2922,2984,3051,3121,3198,3953,4060,4133,4261,4420,4480,4540,4618,4679,4737,4793,4853,4911,4965,5050,5106,5164,5218,5283,5375,5449,5526,5646,5709,5772,5871,5948,6022,6072,6123,6189,6252,6320,6398,6469,6530,6601,6668,6730,6817,6896,6961,7044,7129,7203,7267,7343,7391,7464,7528,7604,7682,7744,7808,7871,7937,8017,8095,8171,8250,8304,8359,8697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bed6289f91d7b29bf75bbeb68ea27ae0\\transformed\\navigation-ui-2.7.5\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,99", "endOffsets": "149,249"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "8364,8463", "endColumns": "98,99", "endOffsets": "8458,8558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c7cbaeefdc37cd24560b8fc7fa3163c1\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,8822", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,8896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\983aed2efb51dcef9549ad462abafc61\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,689", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "166,247,317,437,605,684,760"}, "to": {"startLines": "48,50,111,113,116,117,118", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4138,4266,8563,8702,9002,9170,9249", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "4199,4342,8628,8817,9165,9244,9320"}}]}]}