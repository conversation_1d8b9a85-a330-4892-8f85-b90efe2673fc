#!/usr/bin/env python3
"""
Test multi-network scanning to find Windows Clipsy app on **************
"""

import socket
from concurrent.futures import ThreadPoolExecutor, as_completed

def scan_device_at(ip_address, timeout=3):
    """Scan a specific IP address for Clipsy service on port 8766."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((ip_address, 8766))
        sock.close()
        
        if result == 0:
            return ip_address
        else:
            return None
    except Exception:
        return None

def scan_network_range(network_prefix, max_workers=30):
    """Scan a network range for Clipsy devices."""
    print(f"Scanning {network_prefix}.x...")
    found_devices = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit scan tasks for all IPs in the network
        future_to_ip = {}
        for i in range(1, 255):
            target_ip = f"{network_prefix}.{i}"
            future = executor.submit(scan_device_at, target_ip, 3)
            future_to_ip[future] = target_ip
        
        # Collect results
        completed = 0
        for future in as_completed(future_to_ip):
            completed += 1
            ip = future_to_ip[future]
            result = future.result()
            
            if result:
                found_devices.append(result)
                print(f"   ✅ Found Clipsy device at {result}")
            
            # Show progress every 50 scans
            if completed % 50 == 0:
                print(f"   Scanned {completed}/254 addresses...")
    
    return found_devices

def test_multi_network_scan():
    """Test scanning multiple network ranges like the Android app will do."""
    print("Multi-Network Scanning Test")
    print("===========================")
    print("Testing the same approach the Android app will use")
    print()
    
    # Network ranges to scan (same as Android app)
    network_ranges = [
        "192.168.1",    # Common home Wi-Fi
        "192.168.0",    # Common home Wi-Fi
        "169.254",      # Auto-configuration (APIPA) - where Windows PC is!
        "10.0.0",       # Common private network
        "172.16.0"      # Private network range
    ]
    
    all_found_devices = []
    
    for network_prefix in network_ranges:
        found_devices = scan_network_range(network_prefix)
        all_found_devices.extend(found_devices)
        print()
    
    print("=" * 50)
    print("SCAN RESULTS:")
    
    if all_found_devices:
        print(f"Found {len(all_found_devices)} Clipsy device(s):")
        for device_ip in all_found_devices:
            # Test WebSocket connection
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect((device_ip, 8766))
                sock.close()
                print(f"   🎉 {device_ip}:8766 - WebSocket OK")
            except Exception as e:
                print(f"   ⚠️  {device_ip}:8766 - WebSocket FAILED: {e}")
        
        print()
        print("✅ SUCCESS: Multi-network scanning works!")
        print("   Android app should discover these devices.")
        
        # Check if we found the expected Windows PC
        if "**************" in all_found_devices:
            print("🎯 Found the Windows PC at **************!")
        else:
            print("❓ Windows PC at ************** not found")
            print("   Make sure Windows Clipsy app is running")
    else:
        print("❌ No Clipsy devices found")
        print("   Make sure Windows Clipsy app is running")
        print("   and listening on port 8766")

if __name__ == "__main__":
    test_multi_network_scan()
