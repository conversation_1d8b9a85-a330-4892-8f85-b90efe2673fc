{"logs": [{"outputFile": "com.clipsy.android.app-mergeReleaseResources-55:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0d22882521834a86e542cf7cc54c4b22\\transformed\\core-1.12.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "38,39,40,41,42,43,44,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3530,3632,3740,3842,3943,4049,4156,10149", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3627,3735,3837,3938,4044,4151,4275,10245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c7cbaeefdc37cd24560b8fc7fa3163c1\\transformed\\appcompat-1.6.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,443,555,668,758,863,982,1060,1136,1227,1320,1415,1509,1609,1702,1797,1892,1983,2074,2163,2277,2381,2480,2595,2700,2815,2977,10066", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "438,550,663,753,858,977,1055,1131,1222,1315,1410,1504,1604,1697,1792,1887,1978,2069,2158,2272,2376,2475,2590,2695,2810,2972,3075,10144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\983aed2efb51dcef9549ad462abafc61\\transformed\\preference-1.2.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,277,356,502,671,758", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "173,272,351,497,666,753,837"}, "to": {"startLines": "48,50,111,113,116,117,118", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4591,4730,9758,9920,10250,10419,10506", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "4659,4824,9832,10061,10414,10501,10585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bed6289f91d7b29bf75bbeb68ea27ae0\\transformed\\navigation-ui-2.7.5\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,122", "endOffsets": "163,286"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "9522,9635", "endColumns": "112,122", "endOffsets": "9630,9753"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d1724a1e2b468b75777ff8300e82e0e6\\transformed\\material-1.10.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,276,365,455,541,639,726,830,946,1037,1103,1197,1264,1326,1419,1483,1551,1614,1688,1753,1807,1928,1985,2047,2101,2180,2308,2396,2488,2633,2713,2795,2920,3008,3090,3150,3202,3268,3343,3421,3511,3590,3663,3739,3820,3889,4009,4114,4191,4282,4375,4449,4526,4618,4675,4756,4822,4906,4992,5055,5120,5184,5253,5363,5471,5570,5676,5740,5796", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,88,89,85,97,86,103,115,90,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,91,144,79,81,124,87,81,59,51,65,74,77,89,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,107,98,105,63,55,82", "endOffsets": "271,360,450,536,634,721,825,941,1032,1098,1192,1259,1321,1414,1478,1546,1609,1683,1748,1802,1923,1980,2042,2096,2175,2303,2391,2483,2628,2708,2790,2915,3003,3085,3145,3197,3263,3338,3416,3506,3585,3658,3734,3815,3884,4004,4109,4186,4277,4370,4444,4521,4613,4670,4751,4817,4901,4987,5050,5115,5179,5248,5358,5466,5565,5671,5735,5791,5874"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3080,3169,3259,3345,3443,4280,4384,4500,4664,4829,4923,4990,5052,5145,5209,5277,5340,5414,5479,5533,5654,5711,5773,5827,5906,6034,6122,6214,6359,6439,6521,6646,6734,6816,6876,6928,6994,7069,7147,7237,7316,7389,7465,7546,7615,7735,7840,7917,8008,8101,8175,8252,8344,8401,8482,8548,8632,8718,8781,8846,8910,8979,9089,9197,9296,9402,9466,9837", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "endColumns": "12,88,89,85,97,86,103,115,90,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,91,144,79,81,124,87,81,59,51,65,74,77,89,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,107,98,105,63,55,82", "endOffsets": "321,3164,3254,3340,3438,3525,4379,4495,4586,4725,4918,4985,5047,5140,5204,5272,5335,5409,5474,5528,5649,5706,5768,5822,5901,6029,6117,6209,6354,6434,6516,6641,6729,6811,6871,6923,6989,7064,7142,7232,7311,7384,7460,7541,7610,7730,7835,7912,8003,8096,8170,8247,8339,8396,8477,8543,8627,8713,8776,8841,8905,8974,9084,9192,9291,9397,9461,9517,9915"}}]}]}