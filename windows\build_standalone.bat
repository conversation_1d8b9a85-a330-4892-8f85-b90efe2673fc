@echo off
REM Build script for Clipsy Standalone Windows Application
REM This script creates a single executable with all dependencies bundled

echo ========================================
echo Building Clipsy Standalone Application
echo ========================================

REM Change to the source directory
cd /d "%~dp0\src"

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or later
    pause
    exit /b 1
)

echo Python found. Checking dependencies...

REM Install/upgrade required packages
echo Installing required packages...
pip install --upgrade pip
pip install pyinstaller
pip install tkinter
pip install websockets
pip install pywin32
pip install pillow
pip install psutil
pip install aiohttp
pip install aiofiles
pip install requests
pip install certifi

if errorlevel 1 (
    echo ERROR: Failed to install required packages
    pause
    exit /b 1
)

echo Dependencies installed successfully.

REM Clean previous builds
echo Cleaning previous builds...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

REM Create logs directory
if not exist "logs" mkdir "logs"

echo Building standalone executable...

REM Build the executable using PyInstaller
pyinstaller --clean ClipsyStandalone.spec

if errorlevel 1 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

REM Check if the executable was created
if exist "dist\ClipsyStandalone.exe" (
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo Executable created: dist\ClipsyStandalone.exe
    echo.
    echo Features included:
    echo - Automatic firewall configuration
    echo - All dependencies bundled
    echo - No external files required
    echo - Administrator privilege handling
    echo - Comprehensive error handling
    echo.
    echo You can now distribute the single ClipsyStandalone.exe file
    echo.
    
    REM Copy to parent directory for easy access
    copy "dist\ClipsyStandalone.exe" "..\ClipsyStandalone.exe"
    if exist "..\ClipsyStandalone.exe" (
        echo Executable also copied to: windows\ClipsyStandalone.exe
    )
    
    echo.
    echo Build completed successfully!
) else (
    echo ERROR: Executable not found after build
    echo Check the build output above for errors
    pause
    exit /b 1
)

echo.
echo Press any key to exit...
pause >nul
