#!/usr/bin/env python3
"""
Test UDP broadcast discovery between Windows and Android
"""

import socket
import json
import threading
import time
from datetime import datetime

def listen_for_broadcasts():
    """Listen for UDP broadcasts on port 8765."""
    print("Starting UDP broadcast listener on port 8765...")
    
    try:
        # Create UDP socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        sock.bind(('', 8765))
        sock.settimeout(2)
        
        print("✅ UDP listener started successfully")
        
        while True:
            try:
                data, addr = sock.recvfrom(1024)
                message = data.decode('utf-8')
                print(f"📡 Received from {addr[0]}:{addr[1]}")
                print(f"   Message: {message}")
                
                # Try to parse as JSON
                try:
                    json_data = json.loads(message)
                    print(f"   Type: {json_data.get('type', 'unknown')}")
                    print(f"   Device: {json_data.get('device_name', 'unknown')}")
                    print(f"   Device Type: {json_data.get('device_type', 'unknown')}")
                except:
                    print("   (Not valid JSON)")
                print()
                
            except socket.timeout:
                continue
            except Exception as e:
                print(f"❌ Error receiving: {e}")
                break
                
    except Exception as e:
        print(f"❌ Failed to start UDP listener: {e}")

def send_test_broadcast():
    """Send test broadcast message."""
    print("Sending test broadcast...")
    
    try:
        # Create broadcast socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        
        # Create test message
        message = {
            "type": "discovery",
            "device_name": "Test-PC",
            "device_type": "windows",
            "ip": "*************",
            "websocket_port": 8766,
            "timestamp": datetime.now().isoformat()
        }
        
        data = json.dumps(message).encode('utf-8')
        
        # Send to broadcast addresses
        broadcast_addresses = [
            '***************',
            '*************'
        ]
        
        for addr in broadcast_addresses:
            try:
                sock.sendto(data, (addr, 8765))
                print(f"📤 Sent broadcast to {addr}")
            except Exception as e:
                print(f"❌ Failed to send to {addr}: {e}")
        
        sock.close()
        print("✅ Test broadcast sent")
        
    except Exception as e:
        print(f"❌ Failed to send broadcast: {e}")

def test_websocket_from_android_perspective():
    """Test WebSocket connection like Android would."""
    print("Testing WebSocket connection (Android perspective)...")
    
    try:
        import websockets
        import asyncio
        
        async def test_connection():
            try:
                uri = "ws://*************:8766"
                print(f"Connecting to {uri}...")
                
                async with websockets.connect(uri, timeout=10) as websocket:
                    print("✅ WebSocket connected!")
                    
                    # Wait for welcome message
                    welcome = await asyncio.wait_for(websocket.recv(), timeout=5)
                    print(f"📨 Welcome: {welcome}")
                    
                    # Send ping
                    ping_msg = json.dumps({
                        "type": "ping",
                        "timestamp": datetime.now().isoformat()
                    })
                    await websocket.send(ping_msg)
                    print("📤 Sent ping")
                    
                    # Wait for pong
                    pong = await asyncio.wait_for(websocket.recv(), timeout=5)
                    print(f"📨 Pong: {pong}")
                    
                    return True
                    
            except Exception as e:
                print(f"❌ WebSocket connection failed: {e}")
                return False
        
        return asyncio.run(test_connection())
        
    except ImportError:
        print("⚠️  websockets module not available, skipping WebSocket test")
        return None

def main():
    """Main test function."""
    print("UDP Discovery and WebSocket Test")
    print("=" * 40)
    print()
    
    # Test 1: WebSocket connection
    print("TEST 1: WebSocket Connection")
    print("-" * 30)
    ws_result = test_websocket_from_android_perspective()
    print()
    
    # Test 2: Send broadcast
    print("TEST 2: Send UDP Broadcast")
    print("-" * 30)
    send_test_broadcast()
    print()
    
    # Test 3: Listen for broadcasts
    print("TEST 3: Listen for UDP Broadcasts")
    print("-" * 30)
    print("Listening for 10 seconds...")
    
    # Start listener in background
    listener_thread = threading.Thread(target=listen_for_broadcasts, daemon=True)
    listener_thread.start()
    
    # Wait and send another broadcast
    time.sleep(3)
    print("\nSending another test broadcast...")
    send_test_broadcast()
    
    # Wait for more messages
    time.sleep(7)
    
    print("\n" + "=" * 40)
    print("SUMMARY:")
    if ws_result:
        print("✅ WebSocket: Working")
    elif ws_result is False:
        print("❌ WebSocket: Failed")
    else:
        print("⚠️  WebSocket: Not tested")
    
    print("📡 UDP Discovery: Check output above")
    print("\nIf you see broadcasts from Android devices, discovery is working!")

if __name__ == "__main__":
    main()
