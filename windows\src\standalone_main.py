#!/usr/bin/env python3
"""
Clipsy - Cross-Platform Clipboard Manager
Standalone Windows Application with Firewall Management
"""

import sys
import os
import json
import logging
import asyncio
import threading

from pathlib import Path
import tkinter as tk
from tkinter import messagebox


# Add src directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import modules with fallback handling
# For standalone mode, we'll use fallback UI only
ClipsyMainWindow = None

# Import required components
ClipboardMonitor = None
DeviceDiscovery = None
SyncServer = None
SyncClient = None
SimpleConnectionManager = None
ClipsyMainWindow = None

# Try to import core components
try:
    from clipboard.monitor import ClipboardMonitor
except Exception:
    ClipboardMonitor = None

try:
    from network.discovery import DeviceDiscovery
    from network.sync_server import SyncServer
    from network.sync_client import SyncClient
except Exception:
    DeviceDiscovery = SyncServer = SyncClient = None

try:
    from pairing.pairing_manager import SimpleConnectionManager
except Exception:
    SimpleConnectionManager = None

try:
    from ui.main_window import Clip<PERSON><PERSON>ainWindow
except Exception:
    try:
        from ui.modern_tkinter_window import ModernTkinterWindow as ClipsyMainWindow
    except Exception:
        ClipsyMainWindow = None


class SimpleClipsyUI:
    """Simple fallback UI for standalone Clipsy application."""

    def __init__(self, config, app):
        self.config = config
        self.app = app
        self.root = None

    def run(self):
        """Run the simple UI."""
        try:
            self.root = tk.Tk()
            self.root.title("Clipsy - Clipboard Manager")

            # Set window size and center it on screen
            window_width = 500
            window_height = 400
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            center_x = int(screen_width/2 - window_width/2)
            center_y = int(screen_height/2 - window_height/2)
            self.root.geometry(f'{window_width}x{window_height}+{center_x}+{center_y}')

            # Make window visible and focused
            self.root.deiconify()  # Ensure window is not minimized
            self.root.lift()       # Bring to front
            self.root.attributes('-topmost', True)  # Temporarily on top
            self.root.focus_force()  # Force focus
            self.root.after(100, lambda: self.root.attributes('-topmost', False))  # Remove topmost after 100ms

            # Prevent window from being resized too small
            self.root.minsize(400, 300)

            # Set window icon if available
            try:
                icon_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'icon.ico')
                if os.path.exists(icon_path):
                    self.root.iconbitmap(icon_path)
            except:
                pass

            # Main frame with better styling
            main_frame = tk.Frame(self.root, padx=30, pady=20, bg='white')
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Title with better styling
            title_label = tk.Label(
                main_frame,
                text="🔗 Clipsy Clipboard Manager",
                font=("Segoe UI", 18, "bold"),
                bg='white',
                fg='#2c3e50'
            )
            title_label.pack(pady=(0, 20))

            # Status with better styling
            status_label = tk.Label(
                main_frame,
                text="✅ Network server started successfully!\n✅ Application running and ready",
                font=("Segoe UI", 11),
                bg='white',
                fg='#27ae60',
                justify=tk.LEFT
            )
            status_label.pack(pady=(0, 20))

            # Info text with better styling
            info_frame = tk.Frame(main_frame, bg='white')
            info_frame.pack(pady=(0, 20), fill=tk.BOTH, expand=True)

            info_text = tk.Text(
                info_frame,
                height=10,
                width=60,
                wrap=tk.WORD,
                font=("Segoe UI", 10),
                bg='#f8f9fa',
                fg='#2c3e50',
                relief=tk.FLAT,
                padx=15,
                pady=15
            )
            info_text.pack(fill=tk.BOTH, expand=True)

            info_content = """Clipsy is now running and ready to sync clipboards between devices.

🔧 Features:
• Automatic device discovery on your network
• Real-time clipboard synchronization
• Secure local network communication
• Lightweight and efficient

📱 How to connect:
1. Install the Clipsy Android app on your phone
2. Ensure both devices are on the same WiFi network
3. Configure Windows Firewall if needed (allow port 8767)
4. The devices will automatically discover each other
5. Start copying and pasting between devices!

💡 The application runs in the background and will continue syncing even after closing this window."""

            info_text.insert(tk.END, info_content)
            info_text.config(state=tk.DISABLED)

            # Button frame
            button_frame = tk.Frame(main_frame, bg='white')
            button_frame.pack(pady=(20, 0), fill=tk.X)

            # Minimize to tray button
            minimize_button = tk.Button(
                button_frame,
                text="Minimize to Background",
                command=self.minimize_to_background,
                font=("Segoe UI", 10),
                bg='#3498db',
                fg='white',
                relief=tk.FLAT,
                padx=20,
                pady=8
            )
            minimize_button.pack(side=tk.LEFT, padx=(0, 10))

            # Close button
            close_button = tk.Button(
                button_frame,
                text="Exit Application",
                command=self.close_application,
                font=("Segoe UI", 10),
                bg='#e74c3c',
                fg='white',
                relief=tk.FLAT,
                padx=20,
                pady=8
            )
            close_button.pack(side=tk.RIGHT)

            # Handle window close event
            self.root.protocol("WM_DELETE_WINDOW", self.minimize_to_background)

            # Make sure window is visible
            self.root.update()
            self.root.deiconify()

            # Start the GUI (this blocks until window is closed)
            self.root.mainloop()

        except Exception:
            print("Clipsy is running in background mode.")

    def minimize_to_background(self):
        """Minimize window to background but keep app running."""
        if self.root:
            self.root.withdraw()  # Hide window
            # No notification - just minimize silently

    def close_application(self):
        """Close the application completely."""
        if self.root:
            self.root.quit()
            self.root.destroy()
        # Signal the app to shutdown
        if self.app:
            self.app.cleanup()






class StandaloneClipsyApp:
    """Standalone Clipsy application for clipboard synchronization."""
    
    def __init__(self):
        self.config = self.load_config()
        self.setup_logging()
        
        # Core components
        self.connection_manager = None
        self.clipboard_monitor = None
        self.device_discovery = None
        self.sync_server = None
        self.sync_client = None
        self.main_window = None
        
        # Event loop for async operations
        self.loop = None
        self.loop_thread = None
        
    def load_config(self):
        """Load configuration with embedded defaults."""
        # Try to load from external config file first
        config_path = Path(__file__).parent / "config.json"
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    return json.load(f)
            except:
                pass
        
        # Use embedded default configuration
        return {
            "app": {
                "name": "Clipsy",
                "version": "1.0.0",
                "device_name": "Windows-PC"
            },
            "network": {
                "discovery_port": 8765,
                "websocket_port": 8766
            },
            "clipboard": {
                "sync_enabled": True,
                "history_enabled": True,
                "history_limit": 50
            },
            "ui": {
                "window_width": 600,
                "window_height": 400
            },
            "firewall": {
                "auto_configure": True,
                "check_on_startup": True
            }
        }
    
    def setup_logging(self):
        """Setup logging configuration."""
        log_level = logging.INFO

        log_dir = Path(__file__).parent / "logs"
        log_dir.mkdir(exist_ok=True)

        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(log_dir / 'clipsy_standalone.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
    

    
    def start_async_loop(self):
        """Start the asyncio event loop in a separate thread."""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()
    
    def stop_async_loop(self):
        """Stop the asyncio event loop."""
        if self.loop and not self.loop.is_closed():
            self.loop.call_soon_threadsafe(self.loop.stop)
            if self.loop_thread and self.loop_thread.is_alive():
                self.loop_thread.join(timeout=5)
    
    async def initialize_components(self):
        """Initialize all application components."""
        try:
            # Initialize components only if available
            if SimpleConnectionManager:
                self.connection_manager = SimpleConnectionManager()
            else:
                self.logger.warning("SimpleConnectionManager not available")

            if ClipboardMonitor:
                self.clipboard_monitor = ClipboardMonitor(self.config["clipboard"])
            else:
                self.logger.warning("ClipboardMonitor not available")

            # Initialize network components if available
            if DeviceDiscovery and SyncServer and SyncClient and self.connection_manager:
                try:
                    self.device_discovery = DeviceDiscovery(self.config["network"], self.connection_manager)
                    self.sync_server = SyncServer(self.config["network"], self.connection_manager)
                    self.sync_client = SyncClient(self.config["network"], self.connection_manager)

                    # Start services
                    await self.sync_server.start()

                    # Update discovery with actual server port
                    self.device_discovery.update_server_port(self.sync_server.port)

                    await self.device_discovery.start()

                    self.logger.info("Network components initialized successfully")
                except Exception as net_error:
                    self.logger.warning(f"Network components failed to start: {net_error}")
                    # Continue without network components
                    self.device_discovery = None
                    self.sync_server = None
                    self.sync_client = None
            else:
                self.logger.warning("Network components not available - running in basic mode")

            self.logger.info("Component initialization completed")

        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            # Don't raise - continue with basic functionality
    
    def run(self):
        """Run the main application."""
        try:
            # Start async event loop in background thread
            self.loop_thread = threading.Thread(target=self.start_async_loop, daemon=True)
            self.loop_thread.start()

            import time
            time.sleep(0.5)

            # Initialize async components
            if self.loop:
                future = asyncio.run_coroutine_threadsafe(
                    self.initialize_components(), self.loop
                )
                future.result(timeout=10)
            else:
                raise RuntimeError("Failed to start async event loop")

            # Create and run main window (blocking)
            try:
                if ClipsyMainWindow:
                    self.main_window = ClipsyMainWindow(self.config, self)
                    self.main_window.run()
                else:
                    self.main_window = SimpleClipsyUI(self.config, self)
                    self.main_window.run()
            except Exception:
                print("Clipsy is running in background mode.")
                print("Network server started successfully.")
            
        except KeyboardInterrupt:
            pass
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            # Show error dialog
            try:
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("Clipsy Error", f"Application error: {e}")
                root.destroy()
            except:
                pass
            raise
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Cleanup resources before exit."""
        self.logger.info("Cleaning up application resources...")
        
        if self.clipboard_monitor:
            self.clipboard_monitor.stop()
        
        if self.loop:
            # Stop async services
            if self.sync_server:
                asyncio.run_coroutine_threadsafe(
                    self.sync_server.stop(), self.loop
                )
            if self.device_discovery:
                asyncio.run_coroutine_threadsafe(
                    self.device_discovery.stop(), self.loop
                )
        
        self.stop_async_loop()
        self.logger.info("Application cleanup complete")


def check_single_instance():
    """Check if another instance is already running."""
    import tempfile

    lock_file_path = os.path.join(tempfile.gettempdir(), "clipsy_standalone.lock")

    try:
        # Try to create/open lock file
        lock_file = open(lock_file_path, 'w')

        # Try to acquire exclusive lock (non-blocking) - Windows only
        if os.name == 'nt':  # Windows
            import msvcrt
            try:
                msvcrt.locking(lock_file.fileno(), msvcrt.LK_NBLCK, 1)
                return lock_file  # Return file handle to keep lock
            except (IOError, OSError):
                lock_file.close()
                return None
        else:  # Unix-like (fallback, though this is Windows-specific app)
            try:
                import fcntl
                fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                return lock_file  # Return file handle to keep lock
            except (IOError, ImportError):
                lock_file.close()
                return None

    except Exception:
        return None


def main():
    """Main entry point for standalone application."""
    # Check for single instance
    lock_file = check_single_instance()
    if lock_file is None:
        print("Another instance of Clipsy is already running.")
        sys.exit(1)

    try:
        app = StandaloneClipsyApp()
        app.run()
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
    finally:
        # Release lock
        if lock_file:
            try:
                lock_file.close()
            except:
                pass


if __name__ == "__main__":
    main()
