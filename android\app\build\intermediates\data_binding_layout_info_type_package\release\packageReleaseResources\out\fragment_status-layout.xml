<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_status" modulePackage="com.clipsy.android" filePath="app\src\main\res\layout\fragment_status.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_status_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="260" endOffset="12"/></Target><Target id="@+id/card_service_status" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="15" startOffset="8" endLine="67" endOffset="59"/></Target><Target id="@+id/text_service_status" view="TextView"><Expressions/><location startLine="53" startOffset="20" endLine="61" endOffset="45"/></Target><Target id="@+id/text_connection_status" view="TextView"><Expressions/><location startLine="96" startOffset="16" endLine="103" endOffset="45"/></Target><Target id="@+id/text_connected_devices" view="TextView"><Expressions/><location startLine="105" startOffset="16" endLine="112" endOffset="45"/></Target><Target id="@+id/card_network_info" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="121" startOffset="8" endLine="172" endOffset="59"/></Target><Target id="@+id/text_local_ip" view="TextView"><Expressions/><location startLine="159" startOffset="20" endLine="166" endOffset="50"/></Target><Target id="@+id/text_discovery_status" view="TextView"><Expressions/><location startLine="205" startOffset="20" endLine="212" endOffset="49"/></Target><Target id="@+id/progress_discovery" view="ProgressBar"><Expressions/><location startLine="214" startOffset="20" endLine="219" endOffset="51"/></Target><Target id="@+id/text_discovered_devices" view="TextView"><Expressions/><location startLine="223" startOffset="16" endLine="230" endOffset="45"/></Target><Target id="@+id/button_refresh_devices" view="Button"><Expressions/><location startLine="232" startOffset="16" endLine="249" endOffset="81"/></Target></Targets></Layout>