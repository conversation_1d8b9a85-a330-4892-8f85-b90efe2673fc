package com.clipsy.android.ui.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.clipsy.android.R
import com.clipsy.android.data.model.Device
import com.clipsy.android.data.model.ConnectionState
import com.clipsy.android.data.model.ConnectionStatus
import com.clipsy.android.databinding.ItemDeviceBinding

/**
 * RecyclerView adapter for displaying discovered devices.
 */
class DeviceAdapter(
    private val onConnectClick: (Device) -> Unit,
    private val onRemoveClick: (Device) -> Unit
) : ListAdapter<Device, DeviceAdapter.DeviceViewHolder>(DeviceDiffCallback()) {
    
    private var connectionStates: Map<String, ConnectionState> = emptyMap()
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        val binding = ItemDeviceBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return DeviceViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: DeviceViewHolder, position: Int) {
        val device = getItem(position)
        val connectionState = connectionStates[device.getUniqueId()]
        holder.bind(device, connectionState)
    }
    
    fun updateConnectionStates(states: Map<String, ConnectionState>) {
        connectionStates = states
        notifyDataSetChanged()
    }
    
    inner class DeviceViewHolder(
        private val binding: ItemDeviceBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(device: Device, connectionState: ConnectionState?) {
            binding.apply {
                // Device info
                textDeviceName.text = device.deviceName
                textDeviceIp.text = device.ip
                textDeviceType.text = device.deviceType.replaceFirstChar { it.uppercase() }
                textLastSeen.text = if (device.manual) "Manual" else device.getLastSeenFormatted()
                
                // Connection status
                val status = connectionState?.status ?: ConnectionStatus.DISCONNECTED
                updateConnectionStatus(status)
                
                // Online indicator
                val isOnline = device.isOnline() || device.manual
                indicatorOnline.setImageResource(
                    if (isOnline) R.color.status_connected else R.color.status_disconnected
                )
                
                // Device type icon - use custom logo for all device types
                iconDeviceType.setImageResource(R.drawable.ic_clipsy_devices)
                
                // Click listeners
                buttonConnect.setOnClickListener {
                    onConnectClick(device)
                }
                
                buttonRemove.setOnClickListener {
                    onRemoveClick(device)
                }

                // Hide pairing buttons - using simple connections
                buttonPair.visibility = android.view.View.GONE
                buttonUnpair.visibility = android.view.View.GONE

                // Long click for additional options
                root.setOnLongClickListener {
                    // Could show context menu
                    true
                }
            }
        }
        
        private fun updateConnectionStatus(status: ConnectionStatus) {
            binding.apply {
                when (status) {
                    ConnectionStatus.CONNECTED -> {
                        textConnectionStatus.text = "Connected"
                        textConnectionStatus.setTextColor(
                            root.context.getColor(R.color.status_connected)
                        )
                        buttonConnect.text = "Disconnect"
                        buttonConnect.isEnabled = true
                    }
                    ConnectionStatus.CONNECTING -> {
                        textConnectionStatus.text = "Connecting..."
                        textConnectionStatus.setTextColor(
                            root.context.getColor(R.color.status_connecting)
                        )
                        buttonConnect.text = "Cancel"
                        buttonConnect.isEnabled = true
                    }
                    ConnectionStatus.SYNCING -> {
                        textConnectionStatus.text = "Syncing..."
                        textConnectionStatus.setTextColor(
                            root.context.getColor(R.color.status_connecting)
                        )
                        buttonConnect.text = "Disconnect"
                        buttonConnect.isEnabled = true
                    }
                    ConnectionStatus.ERROR -> {
                        textConnectionStatus.text = "Error"
                        textConnectionStatus.setTextColor(
                            root.context.getColor(R.color.status_disconnected)
                        )
                        buttonConnect.text = "Retry"
                        buttonConnect.isEnabled = true
                    }
                    ConnectionStatus.TIMEOUT -> {
                        textConnectionStatus.text = "Timeout"
                        textConnectionStatus.setTextColor(
                            root.context.getColor(R.color.status_disconnected)
                        )
                        buttonConnect.text = "Retry"
                        buttonConnect.isEnabled = true
                    }
                    else -> {
                        textConnectionStatus.text = "Disconnected"
                        textConnectionStatus.setTextColor(
                            root.context.getColor(R.color.status_disconnected)
                        )
                        buttonConnect.text = "Connect"
                        buttonConnect.isEnabled = true
                    }
                }
            }
        }
    }
    
    private class DeviceDiffCallback : DiffUtil.ItemCallback<Device>() {
        override fun areItemsTheSame(oldItem: Device, newItem: Device): Boolean {
            return oldItem.getUniqueId() == newItem.getUniqueId()
        }
        
        override fun areContentsTheSame(oldItem: Device, newItem: Device): Boolean {
            return oldItem == newItem
        }
    }
}
