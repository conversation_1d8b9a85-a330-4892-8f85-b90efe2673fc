// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '8.7.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.22' apply false
}

ext {
    compileSdkVersion = 34
    targetSdkVersion = 34
    minSdkVersion = 23
    
    // Dependency versions
    kotlinVersion = '1.9.22'
    coroutinesVersion = '1.7.3'
    lifecycleVersion = '2.7.0'
    workManagerVersion = '2.8.1'
    roomVersion = '2.5.0'
    retrofitVersion = '2.9.0'
    okhttpVersion = '4.12.0'
    materialVersion = '1.10.0'
    appCompatVersion = '1.6.1'
    constraintLayoutVersion = '2.1.4'
    fragmentVersion = '1.6.2'
    navigationVersion = '2.7.5'
    preferenceVersion = '1.2.1'
}
