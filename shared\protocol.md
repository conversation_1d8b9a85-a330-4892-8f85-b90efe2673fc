# Clipsy Network Protocol Specification

## Overview

Clipsy uses a two-layer network protocol for device discovery and clipboard synchronization:

1. **Discovery Layer**: UDP broadcast for automatic device discovery
2. **Sync Layer**: WebSocket for real-time clipboard synchronization

## Discovery Protocol (UDP)

### Port
- **Default Port**: 8765
- **Protocol**: UDP
- **Broadcast**: Local network broadcast addresses

### Message Format

All discovery messages are JSON objects with the following structure:

```json
{
  "type": "discovery",
  "device_name": "string",
  "device_type": "windows|android",
  "websocket_port": 8766,
  "timestamp": "ISO8601_timestamp",
  "device_id": "unique_device_identifier"
}
```

### Discovery Flow

1. **Broadcast**: Each device periodically broadcasts its presence
2. **Listen**: Devices listen for broadcasts from other devices
3. **Response**: No explicit response required (passive discovery)
4. **Timeout**: Devices are considered offline after 30 seconds of no broadcasts

### Example Discovery Message

```json
{
  "type": "discovery",
  "device_name": "<PERSON>'s Windows PC",
  "device_type": "windows",
  "websocket_port": 8766,
  "timestamp": "2024-01-15T10:30:00.000Z",
  "device_id": "win-pc-12345"
}
```

## Synchronization Protocol (WebSocket)

### Port
- **Default Port**: 8766
- **Protocol**: WebSocket (ws://)
- **Connection**: Direct IP connection

### Message Types

#### 1. Welcome Message
Sent by server upon client connection.

```json
{
  "type": "welcome",
  "server_info": {
    "device_name": "string",
    "device_type": "windows|android",
    "timestamp": "ISO8601_timestamp",
    "device_id": "string"
  }
}
```

#### 2. Clipboard Update
Sent when clipboard content changes.

```json
{
  "type": "clipboard_update",
  "content": "clipboard_text_content",
  "device_info": {
    "device_name": "string",
    "device_type": "windows|android",
    "device_id": "string"
  },
  "timestamp": "ISO8601_timestamp",
  "content_hash": "optional_sha256_hash"
}
```

#### 3. History Request
Request clipboard history from a device.

```json
{
  "type": "history_request",
  "timestamp": "ISO8601_timestamp",
  "limit": 50
}
```

#### 4. History Response
Response containing clipboard history.

```json
{
  "type": "history_response",
  "history": [
    {
      "content": "clipboard_text",
      "timestamp": "ISO8601_timestamp",
      "source": "local|remote",
      "device_info": {
        "device_name": "string",
        "device_type": "string",
        "device_id": "string"
      }
    }
  ],
  "timestamp": "ISO8601_timestamp"
}
```

#### 5. Ping/Pong
Connection health check.

```json
{
  "type": "ping",
  "timestamp": "ISO8601_timestamp"
}
```

```json
{
  "type": "pong",
  "timestamp": "ISO8601_timestamp"
}
```

#### 6. Error Message
Error notification.

```json
{
  "type": "error",
  "error_code": "string",
  "error_message": "string",
  "timestamp": "ISO8601_timestamp"
}
```

## Connection Flow

### 1. Device Discovery
1. Device A starts broadcasting its presence via UDP
2. Device B receives broadcast and adds Device A to discovered devices list
3. User initiates connection from Device B to Device A

### 2. WebSocket Connection
1. Device B connects to Device A's WebSocket server
2. Device A sends welcome message
3. Both devices can now send clipboard updates and requests

### 3. Clipboard Synchronization
1. User copies text on Device A
2. Device A detects clipboard change
3. Device A broadcasts clipboard_update to all connected devices
4. Device B receives update and sets its clipboard content
5. Device B stores the content in its history

## Security Considerations

### Network Security
- **Local Network Only**: Protocol designed for local network use only
- **No Encryption**: Currently no encryption (future enhancement)
- **IP Whitelisting**: Optional device whitelisting support

### Content Security
- **Size Limits**: Maximum clipboard content size (default: 1MB)
- **Content Filtering**: Optional content type filtering
- **History Limits**: Configurable history retention limits

## Error Handling

### Connection Errors
- **Timeout**: 10-second connection timeout
- **Retry Logic**: Automatic reconnection with exponential backoff
- **Graceful Degradation**: Continue operation with available connections

### Protocol Errors
- **Invalid JSON**: Log error and ignore message
- **Unknown Message Type**: Log warning and ignore
- **Malformed Messages**: Log error and ignore

## Configuration

### Default Ports
- Discovery: 8765 (UDP)
- Sync: 8766 (WebSocket)

### Timeouts
- Connection: 10 seconds
- Ping Interval: 20 seconds
- Ping Timeout: 10 seconds
- Device Timeout: 30 seconds

### Limits
- Max Content Size: 1MB
- Max History Items: 100 (configurable)
- Max Connections: 10 devices

## Implementation Notes

### Windows (Python)
- Use `socket` module for UDP discovery
- Use `websockets` library for WebSocket communication
- Use `pyperclip` for clipboard access

### Android (Kotlin)
- Use `DatagramSocket` for UDP discovery
- Use Java-WebSocket library for WebSocket communication
- Use `ClipboardManager` for clipboard access
- Implement as foreground service for background operation

## Future Enhancements

1. **Encryption**: TLS/SSL support for secure communication
2. **Authentication**: Device pairing and authentication
3. **File Transfer**: Support for file clipboard content
4. **Compression**: Content compression for large clipboard data
5. **Conflict Resolution**: Handle simultaneous clipboard changes
