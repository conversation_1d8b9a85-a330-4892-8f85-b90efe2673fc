#!/usr/bin/env python3
"""
Test script to verify Windows Clipsy networking functionality.
This script tests if the Windows app is properly listening on the required ports.
"""

import socket
import json
import time
import threading
from datetime import datetime

def test_udp_discovery():
    """Test UDP discovery broadcast reception."""
    print("Testing UDP Discovery (Port 8765)...")

    try:
        # Create UDP socket for listening
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.bind(('', 8765))
        sock.settimeout(10)  # 10 second timeout

        print("   UDP socket bound to port 8765")
        print("   Listening for discovery broadcasts...")
        
        start_time = time.time()
        while time.time() - start_time < 10:
            try:
                data, addr = sock.recvfrom(1024)
                message = json.loads(data.decode('utf-8'))
                print(f"   Received discovery from {addr[0]}:")
                print(f"      Device: {message.get('device_name', 'Unknown')}")
                print(f"      Type: {message.get('device_type', 'Unknown')}")
                print(f"      WebSocket Port: {message.get('websocket_port', 'Unknown')}")
                return True
            except socket.timeout:
                continue
            except Exception as e:
                print(f"   Error receiving data: {e}")

        print("   No discovery broadcasts received in 10 seconds")
        return False

    except Exception as e:
        print(f"   UDP Discovery test failed: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

def test_websocket_connection():
    """Test WebSocket server connection."""
    print("\nTesting WebSocket Connection (Port 8766)...")

    try:
        # Try to connect to WebSocket port
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)

        # Try to connect to localhost
        result = sock.connect_ex(('127.0.0.1', 8766))

        if result == 0:
            print("   WebSocket server is listening on port 8766")
            sock.close()
            return True
        else:
            print("   Cannot connect to WebSocket server on port 8766")
            return False

    except Exception as e:
        print(f"   WebSocket connection test failed: {e}")
        return False

def send_test_discovery():
    """Send a test discovery broadcast."""
    print("\nSending Test Discovery Broadcast...")
    
    try:
        # Create UDP socket for sending
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        
        # Create test discovery message
        message = {
            "type": "discovery",
            "device_name": "Test Android Device",
            "device_type": "android",
            "websocket_port": 8766,
            "timestamp": datetime.now().isoformat()
        }
        
        data = json.dumps(message).encode('utf-8')
        
        # Send to broadcast address
        sock.sendto(data, ('***************', 8765))
        print("   Test discovery broadcast sent")

        sock.close()
        return True

    except Exception as e:
        print(f"   Failed to send discovery broadcast: {e}")
        return False

def get_local_ip():
    """Get local IP address."""
    try:
        # Connect to a remote address to determine local IP
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.connect(("*******", 80))
        local_ip = sock.getsockname()[0]
        sock.close()
        return local_ip
    except:
        return "127.0.0.1"

def main():
    """Main test function."""
    print("Clipsy Windows Networking Test")
    print("=" * 50)

    local_ip = get_local_ip()
    print(f"Local IP Address: {local_ip}")
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    print("\nTesting Requirements:")
    print("   1. Windows Clipsy app should be running")
    print("   2. Both devices should be on same Wi-Fi network")
    print("   3. Windows Firewall should allow ports 8765 and 8766")
    
    # Run tests
    udp_ok = test_udp_discovery()
    websocket_ok = test_websocket_connection()
    broadcast_ok = send_test_discovery()
    
    print("\nTest Results:")
    print("=" * 30)
    print(f"   UDP Discovery Reception: {'PASS' if udp_ok else 'FAIL'}")
    print(f"   WebSocket Server:        {'PASS' if websocket_ok else 'FAIL'}")
    print(f"   Discovery Broadcast:     {'PASS' if broadcast_ok else 'FAIL'}")

    if udp_ok and websocket_ok:
        print("\nWindows app networking is working correctly!")
        print("   Android app should be able to discover and connect.")
    else:
        print("\nIssues detected:")
        if not udp_ok:
            print("   - No discovery broadcasts received (Windows app may not be running)")
        if not websocket_ok:
            print("   - WebSocket server not accessible (check firewall/app status)")

        print("\nTroubleshooting:")
        print("   1. Make sure ClipsyFull.exe is running")
        print("   2. Check Windows Firewall settings")
        print("   3. Verify both devices are on same Wi-Fi network")
        print("   4. Try running ClipsyFull.exe as administrator")

if __name__ == "__main__":
    main()
