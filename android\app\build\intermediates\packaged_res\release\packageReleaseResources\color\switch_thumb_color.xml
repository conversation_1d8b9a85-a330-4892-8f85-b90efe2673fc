<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- When switch is checked (ON) - White -->
    <item android:state_checked="true" android:color="@android:color/white" />
    <!-- When switch is unchecked (OFF) - Light Gray -->
    <item android:state_checked="false" android:color="#FFCCCCCC" />
    <!-- Default state - Light Gray -->
    <item android:color="#FFCCCCCC" />
</selector>
