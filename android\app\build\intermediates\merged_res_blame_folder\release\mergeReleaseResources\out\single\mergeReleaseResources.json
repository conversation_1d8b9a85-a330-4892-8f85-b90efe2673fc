[{"merged": "com.clipsy.android.app-release-57:/drawable_ic_scan_animation.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_scan_animation.xml"}, {"merged": "com.clipsy.android.app-release-57:/layout_activity_main.xml.flat", "source": "com.clipsy.android.app-main-58:/layout/activity_main.xml"}, {"merged": "com.clipsy.android.app-release-57:/color_switch_thumb_color.xml.flat", "source": "com.clipsy.android.app-main-58:/color/switch_thumb_color.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xxxhdpi_ic_clipsy_history.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xxxhdpi/ic_clipsy_history.png"}, {"merged": "com.clipsy.android.app-release-57:/layout_item_clipboard_history.xml.flat", "source": "com.clipsy.android.app-main-58:/layout/item_clipboard_history.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable_code_background.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/code_background.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_info.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_info.xml"}, {"merged": "com.clipsy.android.app-release-57:/color_button_background_primary_selector.xml.flat", "source": "com.clipsy.android.app-main-58:/color/button_background_primary_selector.xml"}, {"merged": "com.clipsy.android.app-release-57:/menu_main_menu.xml.flat", "source": "com.clipsy.android.app-main-58:/menu/main_menu.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-mdpi_ic_clipsy_status.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-mdpi/ic_clipsy_status.png"}, {"merged": "com.clipsy.android.app-release-57:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.clipsy.android.app-main-58:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_sync.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_sync.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xxhdpi_ic_clipsy_status.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xxhdpi/ic_clipsy_status.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-mdpi_ic_notification.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-mdpi/ic_notification.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xxxhdpi_ic_clipsy_devices.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xxxhdpi/ic_clipsy_devices.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_search.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_search.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xxhdpi_ic_clipsy_refresh.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xxhdpi/ic_clipsy_refresh.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_refresh.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_refresh.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-mdpi_ic_clipsy_devices.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-mdpi/ic_clipsy_devices.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xxxhdpi_ic_notification.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xxxhdpi/ic_notification.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-mdpi_ic_clipsy_settings.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-mdpi/ic_clipsy_settings.png"}, {"merged": "com.clipsy.android.app-release-57:/layout_fragment_history.xml.flat", "source": "com.clipsy.android.app-main-58:/layout/fragment_history.xml"}, {"merged": "com.clipsy.android.app-release-57:/layout_activity_settings.xml.flat", "source": "com.clipsy.android.app-main-58:/layout/activity_settings.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xxhdpi_ic_clipsy_devices.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xxhdpi/ic_clipsy_devices.png"}, {"merged": "com.clipsy.android.app-release-57:/mipmap-xxxhdpi_ic_launcher_foreground.png.flat", "source": "com.clipsy.android.app-main-58:/mipmap-xxxhdpi/ic_launcher_foreground.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable_circle_background.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/circle_background.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_history_vector.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_history_vector.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable_button_secondary.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/button_secondary.xml"}, {"merged": "com.clipsy.android.app-release-57:/menu_menu_history.xml.flat", "source": "com.clipsy.android.app-main-58:/menu/menu_history.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xxxhdpi_ic_clipsy_refresh.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xxxhdpi/ic_clipsy_refresh.png"}, {"merged": "com.clipsy.android.app-release-57:/layout_dialog_add_manual_device.xml.flat", "source": "com.clipsy.android.app-main-58:/layout/dialog_add_manual_device.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_arrow_right.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_arrow_right.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xxxhdpi_ic_clipsy_status.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xxxhdpi/ic_clipsy_status.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable_badge_background.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/badge_background.xml"}, {"merged": "com.clipsy.android.app-release-57:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.clipsy.android.app-main-58:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.clipsy.android.app-release-57:/layout_fragment_status.xml.flat", "source": "com.clipsy.android.app-main-58:/layout/fragment_status.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable_circle_indicator.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/circle_indicator.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xxxhdpi_ic_clipsy_settings.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xxxhdpi/ic_clipsy_settings.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xhdpi_ic_clipsy_devices.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xhdpi/ic_clipsy_devices.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_settings.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_settings.xml"}, {"merged": "com.clipsy.android.app-release-57:/color_button_text_secondary_selector.xml.flat", "source": "com.clipsy.android.app-main-58:/color/button_text_secondary_selector.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_status_vector.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_status_vector.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_send_arrow.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_send_arrow.xml"}, {"merged": "com.clipsy.android.app-release-57:/xml_backup_rules.xml.flat", "source": "com.clipsy.android.app-main-58:/xml/backup_rules.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xxhdpi_ic_notification.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xxhdpi/ic_notification.png"}, {"merged": "com.clipsy.android.app-release-57:/mipmap-mdpi_ic_launcher_foreground.png.flat", "source": "com.clipsy.android.app-main-58:/mipmap-mdpi/ic_launcher_foreground.png"}, {"merged": "com.clipsy.android.app-release-57:/mipmap-xhdpi_ic_launcher_foreground.png.flat", "source": "com.clipsy.android.app-main-58:/mipmap-xhdpi/ic_launcher_foreground.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_notification.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_notification.xml"}, {"merged": "com.clipsy.android.app-release-57:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.clipsy.android.app-main-58:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xhdpi_ic_clipsy_settings.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xhdpi/ic_clipsy_settings.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-hdpi_ic_clipsy_history.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-hdpi/ic_clipsy_history.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_phone.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_phone.xml"}, {"merged": "com.clipsy.android.app-release-57:/xml_data_extraction_rules.xml.flat", "source": "com.clipsy.android.app-main-58:/xml/data_extraction_rules.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-hdpi_ic_clipsy_devices.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-hdpi/ic_clipsy_devices.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-hdpi_ic_notification.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-hdpi/ic_notification.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-mdpi_ic_clipsy_refresh.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-mdpi/ic_clipsy_refresh.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xhdpi_ic_notification.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xhdpi/ic_notification.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_sync_to_pc.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_sync_to_pc.xml"}, {"merged": "com.clipsy.android.app-release-57:/menu_bottom_navigation_menu.xml.flat", "source": "com.clipsy.android.app-main-58:/menu/bottom_navigation_menu.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-hdpi_ic_clipsy_settings.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-hdpi/ic_clipsy_settings.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xhdpi_ic_clipsy_history.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xhdpi/ic_clipsy_history.png"}, {"merged": "com.clipsy.android.app-release-57:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.clipsy.android.app-main-58:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-hdpi_ic_clipsy_status.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-hdpi/ic_clipsy_status.png"}, {"merged": "com.clipsy.android.app-release-57:/layout_item_device.xml.flat", "source": "com.clipsy.android.app-main-58:/layout/item_device.xml"}, {"merged": "com.clipsy.android.app-release-57:/xml_network_security_config.xml.flat", "source": "com.clipsy.android.app-main-58:/xml/network_security_config.xml"}, {"merged": "com.clipsy.android.app-release-57:/layout_item_device_bluetooth_style.xml.flat", "source": "com.clipsy.android.app-main-58:/layout/item_device_bluetooth_style.xml"}, {"merged": "com.clipsy.android.app-release-57:/mipmap-hdpi_ic_launcher_foreground.png.flat", "source": "com.clipsy.android.app-main-58:/mipmap-hdpi/ic_launcher_foreground.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-hdpi_ic_clipsy_refresh.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-hdpi/ic_clipsy_refresh.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable_dialog_background.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/dialog_background.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_devices_vector.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_devices_vector.xml"}, {"merged": "com.clipsy.android.app-release-57:/layout_dialog_pairing.xml.flat", "source": "com.clipsy.android.app-main-58:/layout/dialog_pairing.xml"}, {"merged": "com.clipsy.android.app-release-57:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.clipsy.android.app-main-58:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.clipsy.android.app-release-57:/xml_preferences.xml.flat", "source": "com.clipsy.android.app-main-58:/xml/preferences.xml"}, {"merged": "com.clipsy.android.app-release-57:/mipmap-xxhdpi_ic_launcher_foreground.png.flat", "source": "com.clipsy.android.app-main-58:/mipmap-xxhdpi/ic_launcher_foreground.png"}, {"merged": "com.clipsy.android.app-release-57:/color_bottom_nav_color_selector.xml.flat", "source": "com.clipsy.android.app-main-58:/color/bottom_nav_color_selector.xml"}, {"merged": "com.clipsy.android.app-release-57:/layout_fragment_devices.xml.flat", "source": "com.clipsy.android.app-main-58:/layout/fragment_devices.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xxhdpi_ic_clipsy_settings.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xxhdpi/ic_clipsy_settings.png"}, {"merged": "com.clipsy.android.app-release-57:/color_switch_track_color.xml.flat", "source": "com.clipsy.android.app-main-58:/color/switch_track_color.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable_ic_computer.xml.flat", "source": "com.clipsy.android.app-main-58:/drawable/ic_computer.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xhdpi_ic_clipsy_status.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xhdpi/ic_clipsy_status.png"}, {"merged": "com.clipsy.android.app-release-57:/color_button_text_primary_selector.xml.flat", "source": "com.clipsy.android.app-main-58:/color/button_text_primary_selector.xml"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xxhdpi_ic_clipsy_history.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xxhdpi/ic_clipsy_history.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-xhdpi_ic_clipsy_refresh.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-xhdpi/ic_clipsy_refresh.png"}, {"merged": "com.clipsy.android.app-release-57:/drawable-mdpi_ic_clipsy_history.png.flat", "source": "com.clipsy.android.app-main-58:/drawable-mdpi/ic_clipsy_history.png"}]