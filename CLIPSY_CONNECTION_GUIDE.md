# 📱💻 Clipsy Connection Guide

## How to Connect Android App to Windows PC

### 🖥️ **Step 1: Start Windows App**
1. Run `ClipsyFull.exe` on your Windows PC
2. The app will show your local IP address (e.g., `*************`)
3. Make sure "Service Running" shows as `True`

### 📱 **Step 2: Use Android App**
1. Install and open the Clipsy Android app
2. Go to the **Status** tab
3. Tap the **"Refresh Devices"** button to scan for nearby devices
4. Go to the **Devices** tab to see discovered devices
5. Tap **"Connect"** next to your PC to establish connection

### 🔧 **Step 3: Manual Connection (if auto-discovery fails)**
1. In the **Devices** tab, tap the **"+"** button (Add Device)
2. Enter your PC's IP address (shown in Windows app)
3. Enter port `8766` (default WebSocket port)
4. Tap **"Add"** to connect manually

### ✅ **Step 4: Test Connection**
1. Copy text on your PC - it should appear in Android app's **History** tab
2. Copy text on your Android - it should appear in PC app's history
3. Both devices should show as "Connected" in their respective status displays

### 🔍 **Troubleshooting**
- **Both devices must be on the same Wi-Fi network**
- **Check Windows Firewall** - allow ClipsyFull.exe through firewall
- **Check Android permissions** - ensure app has network access
- **Restart both apps** if connection fails
- **Try manual connection** with PC's IP address if auto-discovery doesn't work

### 📋 **Connection Status Indicators**
- **Green/Connected**: Devices are syncing clipboard
- **Yellow/Connecting**: Attempting to establish connection  
- **Red/Disconnected**: No connection established

### 🚀 **Features Once Connected**
- ✅ Real-time clipboard sync between devices
- ✅ Clipboard history on both devices
- ✅ Automatic reconnection when devices come back online
- ✅ Secure local network communication
