"""
Main window UI for Clipsy Windows application.
Provides the primary user interface for clipboard management and device connections.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, simpledialog
import asyncio
import threading

import logging
import os
from pathlib import Path
import pystray
from PIL import Image
from .simple_device_manager import SimpleDeviceManager


class ClipsyMainWindow:
    """Main application window for Clipsy."""
    
    def __init__(self, config: dict, app):
        self.config = config
        self.app = app
        self.logger = logging.getLogger(__name__)
        
        # UI state
        self.root = None
        self.is_running = False
        
        # UI components
        self.notebook = None
        self.devices_frame = None
        self.history_frame = None
        self.settings_frame = None

        # Modern device manager
        self.device_manager = None

        # Legacy device list (for backward compatibility)
        self.devices_listbox = None
        self.discovered_devices = {}
        
        # History list
        self.history_listbox = None
        self.history_text = None
        
        # Status
        self.status_label = None
        self.connection_status = None

        # System tray
        self.tray_icon = None
        self.tray_thread = None
        self.is_minimized_to_tray = False

        # Connection manager (from app)
        self.connection_manager = self.app.connection_manager

        # Setup callbacks
        self._setup_callbacks()
    
    def _setup_callbacks(self):
        """Setup callbacks with the application components."""
        if self.app.clipboard_monitor:
            self.app.clipboard_monitor.set_clipboard_change_callback(self._on_clipboard_change)
            self.app.clipboard_monitor.set_history_update_callback(self._on_history_update)
        
        if self.app.device_discovery:
            self.app.device_discovery.add_device_callback(self._on_device_event)
        
        if self.app.sync_server:
            self.app.sync_server.set_clipboard_callback(self._on_remote_clipboard_update)
            self.app.sync_server.set_history_callback(self._get_clipboard_history)
            self.app.sync_server.set_device_event_callback(self._on_connection_event)
        
        if self.app.sync_client:
            self.app.sync_client.set_clipboard_callback(self._on_remote_clipboard_update)
            self.app.sync_client.set_connection_callback(self._on_connection_event)
    
    def run(self):
        """Run the main window."""
        self.root = tk.Tk()
        self.root.title("Clipsy - Clipboard Manager")

        # Get UI config with defaults
        ui_config = self.config.get('ui', {})
        window_width = ui_config.get('window_width', 600)
        window_height = ui_config.get('window_height', 400)
        self.root.geometry(f"{window_width}x{window_height}")

        # Set window icon
        self._set_window_icon()

        # Setup UI
        self._create_widgets()
        self._setup_layout()

        # Initialize system tray
        self._setup_system_tray()

        # Start clipboard monitor
        if self.app.clipboard_monitor:
            self.app.clipboard_monitor.start()

        # Update UI periodically
        self._schedule_ui_updates()

        self.is_running = True

        # Handle window close and minimize
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_close)
        self.root.bind("<Unmap>", self._on_window_minimize)
        
        # Start main loop
        self.root.mainloop()

    def _set_window_icon(self):
        """Set the window icon using the custom logo."""
        try:
            # Try to load the icon file
            import os
            from pathlib import Path

            # Look for icon files in the windows directory
            icon_paths = [
                Path(__file__).parent.parent.parent.parent / "windows" / "app_icon.ico",
                Path(__file__).parent.parent.parent / "windows" / "app_icon.ico",
                Path(__file__).parent.parent / "windows" / "app_icon.ico",
                "../../windows/app_icon.ico",
                "../windows/app_icon.ico",
                "windows/app_icon.ico"
            ]

            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    self.root.iconbitmap(str(icon_path))
                    return

            png_paths = [
                Path(__file__).parent.parent.parent.parent / "windows" / "icon_64x64.png",
                Path(__file__).parent.parent.parent / "windows" / "icon_64x64.png",
                "../../windows/icon_64x64.png",
                "../windows/icon_64x64.png"
            ]

            for png_path in png_paths:
                if os.path.exists(png_path):
                    try:
                        from PIL import Image, ImageTk
                        img = Image.open(png_path)
                        img = img.resize((32, 32), Image.Resampling.LANCZOS)
                        photo = ImageTk.PhotoImage(img)
                        self.root.iconphoto(True, photo)
                        return
                    except ImportError:
                        pass

        except Exception:
            pass

    def _setup_system_tray(self):
        """Setup system tray icon and menu."""
        try:
            # Load tray icon
            tray_image = self._load_tray_icon()
            if not tray_image:
                self.logger.warning("Could not load tray icon, system tray disabled")
                return

            # Create tray menu
            menu = pystray.Menu(
                pystray.MenuItem("Open Clipsy", self._restore_window, default=True),
                pystray.MenuItem("Reconnect", self._reconnect_devices),
                pystray.Menu.SEPARATOR,
                pystray.MenuItem("Exit", self._exit_application)
            )

            # Create tray icon
            self.tray_icon = pystray.Icon(
                "Clipsy",
                tray_image,
                "Clipsy - Clipboard Manager",
                menu
            )

            self.logger.info("System tray initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to setup system tray: {e}")
            self.tray_icon = None

    def _load_tray_icon(self):
        """Load the system tray icon image."""
        try:
            # Try to find icon files
            icon_paths = [
                Path(__file__).parent.parent.parent / "app_icon.ico",
                Path(__file__).parent.parent.parent / "icon_32x32.png",
                Path(__file__).parent.parent.parent / "icon_64x64.png",
                "../../app_icon.ico",
                "../app_icon.ico",
                "app_icon.ico"
            ]

            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    try:
                        # Load and resize image for tray
                        image = Image.open(icon_path)
                        # Resize to standard tray icon size
                        image = image.resize((32, 32), Image.Resampling.LANCZOS)
                        self.logger.info(f"Loaded tray icon from: {icon_path}")
                        return image
                    except Exception as e:
                        self.logger.warning(f"Failed to load icon {icon_path}: {e}")
                        continue

            # Create a simple default icon if no file found
            image = Image.new('RGBA', (32, 32), (0, 120, 215, 255))  # Blue square
            self.logger.info("Using default blue square tray icon")
            return image

        except Exception as e:
            self.logger.error(f"Failed to load tray icon: {e}")
            return None

    def _create_widgets(self):
        """Create all UI widgets."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        
        # Devices tab
        self.devices_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.devices_frame, text="Devices")
        self._create_devices_tab()
        
        # History tab
        self.history_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.history_frame, text="History")
        self._create_history_tab()
        
        # Settings tab
        self.settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_frame, text="Settings")
        self._create_settings_tab()
        
        # Status bar
        self.status_frame = ttk.Frame(self.root)
        self.status_label = ttk.Label(self.status_frame, text="Ready")
        self.connection_status = ttk.Label(self.status_frame, text="No connections")
    
    def _create_devices_tab(self):
        """Create the devices tab content with modern device manager."""
        # Create simple device manager
        self.device_manager = SimpleDeviceManager(self.devices_frame, self.app)

        # Set up device manager callbacks
        self.device_manager.set_callbacks(
            on_connect=self._device_manager_connect,
            on_disconnect=self._device_manager_disconnect,
            on_sync_toggle=self._device_manager_sync_toggle
        )

        # Initial device list update
        self._update_device_manager()

    def _device_manager_connect(self, device_id: str):
        """Handle device connection from device manager."""
        # Find device info by ID
        device_info = None
        if self.app.device_discovery:
            devices = self.app.device_discovery.get_discovered_devices()
            for ip, info in devices.items():
                if info.get("device_id", ip) == device_id:
                    device_info = info
                    break

        if device_info and self.app.sync_client:
            try:
                self._update_status(f"Connecting to {device_info['device_name']}...")
                asyncio.run_coroutine_threadsafe(
                    self.app.sync_client.connect_to_device(device_info["ip"], device_info),
                    self.app.loop
                )
            except Exception as e:
                self._update_status(f"Connection failed: {e}")

    def _device_manager_disconnect(self, device_id: str):
        """Handle device disconnection from device manager."""
        # Find device info by ID - check both connected and discovered devices
        device_info = None
        device_ip = None

        # First check connected devices
        if self.app.sync_server:
            connected_devices = self.app.sync_server.get_connected_devices()
            for ip, info in connected_devices.items():
                if info.get("device_id", ip) == device_id:
                    device_info = info
                    device_ip = ip
                    break

        # If not found in connected, check discovered devices
        if not device_info and self.app.discovery:
            discovered_devices = self.app.discovery.get_discovered_devices()
            for ip, info in discovered_devices.items():
                if info.get("device_id", ip) == device_id:
                    device_info = info
                    device_ip = ip
                    break

        if device_info and device_ip:
            try:
                self._update_status(f"Disconnecting from {device_info.get('device_name', 'Unknown')}...")

                # Disconnect from both client and server sides
                if self.app.sync_client:
                    asyncio.run_coroutine_threadsafe(
                        self.app.sync_client.disconnect_from_device(device_ip),
                        self.app.loop
                    )

                if self.app.sync_server:
                    self.app.sync_server.disconnect_device(device_ip)

                self._update_status(f"Disconnected from {device_info.get('device_name', 'Unknown')}")
            except Exception as e:
                self._update_status(f"Disconnection failed: {e}")
        else:
            self._update_status(f"Could not find device to disconnect: {device_id}")

    def _device_manager_sync_toggle(self, device_id: str, enabled: bool):
        """Handle sync toggle from device manager."""
        # This could be used to enable/disable sync for specific devices
        # For now, just log the action
        action = "enabled" if enabled else "disabled"
        self._update_status(f"Sync {action} for device {device_id}")

    def _update_device_manager(self):
        """Update the device manager with current device data."""
        if self.device_manager:
            discovered = {}
            connected = {}

            if self.app.device_discovery:
                discovered = self.app.device_discovery.get_discovered_devices()

            if self.app.sync_server:
                connected = self.app.sync_server.get_connected_devices()

            self.device_manager.update_devices(discovered, connected)

    def _create_history_tab(self):
        """Create the history tab content."""
        # History controls
        controls_frame = ttk.Frame(self.history_frame)
        controls_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Button(controls_frame, text="Clear History", command=self._clear_history).pack(side="left", padx=2)
        ttk.Button(controls_frame, text="Refresh", command=self._refresh_history).pack(side="left", padx=2)
        
        # History list
        history_list_frame = ttk.LabelFrame(self.history_frame, text="Clipboard History")
        history_list_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Create treeview for history
        columns = ("Time", "Source", "Preview")
        self.history_tree = ttk.Treeview(history_list_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=100)
        
        history_tree_scrollbar = ttk.Scrollbar(history_list_frame, orient="vertical", command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_tree_scrollbar.set)
        
        self.history_tree.pack(side="left", fill="both", expand=True)
        history_tree_scrollbar.pack(side="right", fill="y")
        
        # History content display
        content_frame = ttk.LabelFrame(self.history_frame, text="Content")
        content_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        self.history_text = scrolledtext.ScrolledText(content_frame, height=6, wrap=tk.WORD)
        self.history_text.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Bind selection event
        self.history_tree.bind("<<TreeviewSelect>>", self._on_history_select)
    
    def _create_settings_tab(self):
        """Create the settings tab content."""
        # Clipboard settings
        clipboard_frame = ttk.LabelFrame(self.settings_frame, text="Clipboard Settings")
        clipboard_frame.pack(fill="x", padx=5, pady=5)
        
        self.sync_enabled_var = tk.BooleanVar(value=self.config["clipboard"]["sync_enabled"])
        ttk.Checkbutton(clipboard_frame, text="Enable clipboard sync", variable=self.sync_enabled_var).pack(anchor="w", padx=5, pady=2)
        
        self.history_enabled_var = tk.BooleanVar(value=self.config["clipboard"]["history_enabled"])
        ttk.Checkbutton(clipboard_frame, text="Enable clipboard history", variable=self.history_enabled_var).pack(anchor="w", padx=5, pady=2)
        
        # History limit
        limit_frame = ttk.Frame(clipboard_frame)
        limit_frame.pack(fill="x", padx=5, pady=2)
        
        ttk.Label(limit_frame, text="History limit:").pack(side="left")
        self.history_limit_var = tk.StringVar(value=str(self.config["clipboard"]["history_limit"]))
        history_limit_combo = ttk.Combobox(limit_frame, textvariable=self.history_limit_var, values=["10", "20", "50", "100"], width=10)
        history_limit_combo.pack(side="left", padx=5)
        
        # Network settings
        network_frame = ttk.LabelFrame(self.settings_frame, text="Network Settings")
        network_frame.pack(fill="x", padx=5, pady=5)
        
        # Ports display
        ttk.Label(network_frame, text=f"Discovery Port: {self.config['network']['discovery_port']}").pack(anchor="w", padx=5, pady=2)
        ttk.Label(network_frame, text=f"WebSocket Port: {self.config['network']['websocket_port']}").pack(anchor="w", padx=5, pady=2)
        
        # Apply button
        ttk.Button(self.settings_frame, text="Apply Settings", command=self._apply_settings).pack(pady=10)
    
    def _setup_layout(self):
        """Setup the main layout."""
        self.notebook.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Status bar
        self.status_frame.pack(fill="x", side="bottom")
        self.status_label.pack(side="left", padx=5)
        self.connection_status.pack(side="right", padx=5)
    
    def _schedule_ui_updates(self):
        """Schedule periodic UI updates."""
        self._update_ui()
        self.root.after(1000, self._schedule_ui_updates)  # Update every second
    
    def _update_ui(self):
        """Update UI elements."""
        if not self.is_running:
            return
        
        # Local IP display removed - now handled by modern device manager
        
        # Update connection status
        if self.app.sync_server and self.app.sync_client:
            server_clients = self.app.sync_server.get_connected_clients_count()
            client_connections = len(self.app.sync_client.get_connected_devices())
            self.connection_status.config(text=f"Connections: {server_clients + client_connections}")
    
    def _on_clipboard_change(self, content: str):
        """Handle clipboard change event."""
        self.root.after(0, lambda: self._update_status(f"Clipboard updated: {len(content)} chars"))
        
        # Send to connected devices
        if self.app.sync_server:
            asyncio.run_coroutine_threadsafe(
                self.app.sync_server.broadcast_clipboard_sync(content),
                self.app.loop
            )
    
    def _on_remote_clipboard_update(self, content: str, device_info: dict):
        """Handle remote clipboard update."""
        device_name = device_info.get("device_name", "Unknown")
        self.root.after(0, lambda: self._update_status(f"Clipboard from {device_name}: {len(content)} chars"))

        # Update local clipboard
        if self.app.clipboard_monitor:
            self.app.clipboard_monitor.set_clipboard_content(content, "remote")

        # Automatically refresh history to show the new content
        self.root.after(0, self._refresh_history)

    def _on_history_update(self, new_item: dict):
        """Handle new history item added.

        This is called when a new item is added to clipboard history.
        Instead of refreshing the entire list, we add just the new item.

        Args:
            new_item (dict): The new history item with 'timestamp', 'source', and 'content'
        """
        # Use root.after to ensure UI updates happen in the main thread
        self.root.after(0, lambda: self._add_history_item_to_ui(new_item))

    def _on_device_event(self, event_type: str, device_info: dict):
        """Handle device discovery events."""
        # Legacy device list update disabled - using modern device manager
        # self.root.after(0, lambda: self._update_devices_list())
        self.root.after(0, lambda: self._update_device_manager())

        # Auto-connect disabled - manual control only
        # if event_type == "discovered" and device_info.get("device_type") == "android":
        #     self._auto_connect_to_device(device_info)

    def _auto_connect_to_device(self, device_info: dict):
        """Automatically connect to a discovered device."""
        if not self.app.sync_client:
            return

        device_ip = device_info.get("ip")
        device_name = device_info.get("device_name", "Unknown")

        # Check if already connected
        if self.app.sync_client.is_connected_to(device_ip):
            return

        try:
            self._update_status(f"Auto-connecting to {device_name} ({device_ip})...")
            asyncio.run_coroutine_threadsafe(
                self.app.sync_client.connect_to_device(device_ip, device_info),
                self.app.loop
            )
        except Exception as e:
            self._update_status(f"Auto-connect failed to {device_name}: {e}")

    def _on_connection_event(self, event_type: str, device_ip: str, info: dict):
        """Handle connection events."""
        if event_type == "connection_request":
            # Handle device connection request through device manager
            if self.device_manager:
                self.root.after(0, lambda: self.device_manager._handle_connection_event("connected", {"device_name": info.get("device_name", "Unknown"), "device_ip": device_ip}))
        elif event_type == "pairing_required":
            # Handle pairing required event through device manager
            if self.device_manager:
                self.root.after(0, lambda: self.device_manager.handle_pairing_required(device_ip, info))
        else:
            self.root.after(0, lambda: self._update_status(f"Connection {event_type}: {device_ip}"))
            self.root.after(0, lambda: self._update_device_manager())
    
    def _get_clipboard_history(self) -> list:
        """Get clipboard history for sharing."""
        if self.app.clipboard_monitor:
            return self.app.clipboard_monitor.get_history()
        return []
    
    def _update_devices_list(self):
        """Update the devices list."""
        # Clear current list
        self.devices_listbox.delete(0, tk.END)
        self.discovered_devices.clear()

        # Add discovered devices (from UDP discovery)
        if self.app.device_discovery:
            devices = self.app.device_discovery.get_discovered_devices()
            for ip, device_info in devices.items():
                # Check pairing status
                device_id = device_info.get('device_id')
                paired_status = ""
                if device_id and self.connection_manager.is_device_connected(device_id):
                    paired_status = " [PAIRED]"

                display_text = f"{device_info['device_name']} ({ip}) - {device_info['device_type']} [Discovered]{paired_status}"
                self.devices_listbox.insert(tk.END, display_text)
                self.discovered_devices[display_text] = device_info

        # Add connected devices (from WebSocket connections)
        if self.app.sync_server:
            connected_devices = self.app.sync_server.get_connected_devices()
            for ip, device_info in connected_devices.items():
                # Check pairing status
                device_id = device_info.get('device_id')
                paired_status = ""
                if device_id and self.connection_manager.is_device_connected(device_id):
                    paired_status = " [PAIRED]"

                display_text = f"{device_info['device_name']} ({ip}) - {device_info['device_type']} [Connected]{paired_status}"
                self.devices_listbox.insert(tk.END, display_text)
                self.discovered_devices[display_text] = device_info
    
    def _refresh_history(self, scroll_to_top=True):
        """Refresh the history display.

        Args:
            scroll_to_top (bool): Whether to automatically scroll to the top after refresh
        """
        if not self.app.clipboard_monitor:
            return

        history = self.app.clipboard_monitor.get_history()

        # Store current selection to potentially restore it
        current_selection = self.history_tree.selection()

        # Clear current items
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        # Add history items (newest first, since history is already sorted)
        for item in history:
            timestamp = item.get("timestamp", "")[:19]  # Remove microseconds
            source = item.get("source", "unknown")
            content = item.get("content", "")
            preview = content[:50] + "..." if len(content) > 50 else content

            self.history_tree.insert("", "end", values=(timestamp, source, preview))

        # Auto-scroll to top to show newest items
        if scroll_to_top and history:
            self._scroll_history_to_top()

    def _scroll_history_to_top(self, smooth=True):
        """Scroll the history TreeView to the top to show newest items.

        Args:
            smooth (bool): Whether to use smooth scrolling animation
        """
        try:
            children = self.history_tree.get_children()
            if children:
                first_item = children[0]

                if smooth:
                    # Smooth scroll animation
                    self._smooth_scroll_to_item(first_item)
                else:
                    # Instant scroll
                    self.history_tree.see(first_item)
                    self.history_tree.selection_set(first_item)


        except Exception as e:
            self.logger.error(f"Failed to scroll history to top: {e}")

    def _smooth_scroll_to_item(self, target_item):
        """Smoothly scroll to a specific item in the TreeView.

        Args:
            target_item: The item ID to scroll to
        """
        try:
            # Get current visible range
            children = self.history_tree.get_children()
            if not children:
                return

            # Find the index of the target item
            target_index = children.index(target_item)

            # Get the currently visible items
            bbox = self.history_tree.bbox(children[0])
            if bbox:
                # Calculate scroll steps for smooth animation
                steps = 5  # Number of animation steps
                current_top = self.history_tree.winfo_rooty()

                # Animate scroll in steps
                for step in range(steps):
                    progress = (step + 1) / steps
                    # Use easing function for smoother animation
                    eased_progress = 1 - (1 - progress) ** 2  # Ease-out quadratic

                    # Schedule each animation frame
                    self.root.after(step * 20, lambda p=eased_progress: self._scroll_step(target_item, p))

                # Final step: ensure we're at the exact position and select the item
                self.root.after(steps * 20, lambda: self._finalize_scroll(target_item))
            else:
                # Fallback to instant scroll if bbox calculation fails
                self.history_tree.see(target_item)
                self.history_tree.selection_set(target_item)

        except Exception as e:
            self.logger.error(f"Failed to smooth scroll: {e}")
            # Fallback to instant scroll
            self.history_tree.see(target_item)
            self.history_tree.selection_set(target_item)

    def _scroll_step(self, target_item, progress):
        """Perform one step of the smooth scroll animation."""
        try:
            # For TreeView, we'll use the see() method with some interpolation
            # This is a simplified smooth scroll - TreeView doesn't have native smooth scrolling
            if progress >= 0.8:  # Near the end of animation
                self.history_tree.see(target_item)
        except Exception as e:
            pass

    def _finalize_scroll(self, target_item):
        """Finalize the smooth scroll by ensuring exact positioning and selection."""
        try:
            self.history_tree.see(target_item)
            self.history_tree.selection_set(target_item)

        except Exception as e:
            self.logger.error(f"Failed to finalize scroll: {e}")

    def _add_history_item_to_ui(self, item: dict):
        """Add a single new history item to the UI at the top.

        This is more efficient than refreshing the entire history list.

        Args:
            item (dict): History item with 'timestamp', 'source', and 'content' keys
        """
        try:
            timestamp = item.get("timestamp", "")[:19]  # Remove microseconds
            source = item.get("source", "unknown")
            content = item.get("content", "")
            preview = content[:50] + "..." if len(content) > 50 else content

            # Insert at the beginning (index 0) since newest items go to top
            new_item_id = self.history_tree.insert("", 0, values=(timestamp, source, preview))

            # Smooth scroll to show the new item and select it
            self._smooth_scroll_to_item(new_item_id)

            # Remove excess items if history limit is exceeded
            children = self.history_tree.get_children()
            if len(children) > self.app.clipboard_monitor.history_limit:
                # Remove items from the end (oldest items)
                for i in range(self.app.clipboard_monitor.history_limit, len(children)):
                    self.history_tree.delete(children[i])



        except Exception as e:
            self.logger.error(f"Failed to add history item to UI: {e}")

    def _on_history_select(self, event):
        """Handle history item selection."""
        selection = self.history_tree.selection()
        if not selection:
            return
        
        item = self.history_tree.item(selection[0])
        values = item["values"]
        
        if len(values) >= 3:
            # Find full content from history
            if self.app.clipboard_monitor:
                history = self.app.clipboard_monitor.get_history()
                for hist_item in history:
                    if hist_item.get("timestamp", "")[:19] == values[0]:
                        self.history_text.delete(1.0, tk.END)
                        self.history_text.insert(1.0, hist_item.get("content", ""))
                        break
    
    def _connect_to_selected_device(self):
        """Connect to the selected device."""
        selection = self.devices_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a device to connect to.")
            return

        device_text = self.devices_listbox.get(selection[0])
        device_info = self.discovered_devices.get(device_text)

        if device_info and self.app.sync_client:
            try:
                self._update_status(f"Connecting to {device_info['device_name']}...")
                asyncio.run_coroutine_threadsafe(
                    self.app.sync_client.connect_to_device(device_info["ip"], device_info),
                    self.app.loop
                )
            except Exception as e:
                messagebox.showerror("Connection Error", f"Failed to connect to device: {e}")
                self._update_status("Connection failed")
    
    def _disconnect_from_selected_device(self):
        """Disconnect from the selected device."""
        selection = self.devices_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a device to disconnect from.")
            return

        device_text = self.devices_listbox.get(selection[0])
        device_info = self.discovered_devices.get(device_text)

        if device_info:
            try:
                self._update_status(f"Disconnecting from {device_info['device_name']}...")

                # Disconnect from both client and server sides
                if self.app.sync_client:
                    asyncio.run_coroutine_threadsafe(
                        self.app.sync_client.disconnect_from_device(device_info["ip"]),
                        self.app.loop
                    )

                if self.app.sync_server:
                    self.app.sync_server.disconnect_device(device_info["ip"])

            except Exception as e:
                messagebox.showerror("Disconnection Error", f"Failed to disconnect from device: {e}")
                self._update_status("Disconnection failed")
    
    def _add_manual_device(self):
        """Add a device manually by IP address."""
        ip = simpledialog.askstring("Manual Device", "Enter device IP address:")
        if ip and self.app.device_discovery:
            try:
                # Validate IP format
                import ipaddress
                ipaddress.ip_address(ip)

                self.app.device_discovery.add_manual_device(ip)
                self._update_status(f"Added manual device: {ip}")
                self._refresh_devices()
            except ValueError:
                messagebox.showerror("Invalid IP", "Please enter a valid IP address.")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to add device: {e}")
    
    def _refresh_devices(self):
        """Refresh the devices list."""
        self._update_devices_list()

    def _show_device_info(self):
        """Show detailed information about the selected device."""
        selection = self.devices_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a device to view information.")
            return

        device_text = self.devices_listbox.get(selection[0])
        device_info = self.discovered_devices.get(device_text)
        if device_info:
            info_text = f"""Device Information:

Name: {device_info.get('device_name', 'Unknown')}
IP Address: {device_info.get('ip', 'Unknown')}
Type: {device_info.get('device_type', 'Unknown')}
WebSocket Port: {device_info.get('websocket_port', 'Unknown')}
Last Seen: {device_info.get('last_seen', 'Unknown')}
Device ID: {device_info.get('device_id', 'Unknown')}"""

            messagebox.showinfo("Device Information", info_text)

    def _pair_with_selected_device(self):
        """Start pairing process with the selected device."""
        selection = self.devices_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a device to pair with.")
            return

        device_text = self.devices_listbox.get(selection[0])
        device_info = self.discovered_devices.get(device_text)

        if not device_info:
            messagebox.showerror("Error", "Device information not found.")
            return

        # Check if already paired
        device_id = device_info.get('device_id')
        if device_id and self.connection_manager.is_device_connected(device_id):
            messagebox.showinfo("Already Connected", f"Device '{device_info['device_name']}' is already connected.")
            return

        # Start connection process
        try:
            success = self.connection_manager.connect_to_device(device_info.get('ip', ''), device_info.get('device_name', 'Unknown'))
            if success:
                messagebox.showinfo("Connected", f"Successfully connected to '{device_info['device_name']}'")
                self._refresh_devices()
            else:
                messagebox.showerror("Connection Failed", f"Failed to connect to '{device_info['device_name']}'")
        except Exception as e:
            messagebox.showerror("Connection Error", f"Failed to connect: {e}")

    def _unpair_selected_device(self):
        """Unpair the selected device."""
        selection = self.devices_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a device to unpair.")
            return

        device_text = self.devices_listbox.get(selection[0])
        device_info = self.discovered_devices.get(device_text)

        if not device_info:
            messagebox.showerror("Error", "Device information not found.")
            return

        device_id = device_info.get('device_id')
        if not device_id or not self.connection_manager.is_device_connected(device_id):
            messagebox.showinfo("Not Connected", f"Device '{device_info['device_name']}' is not connected.")
            return

        # Confirm disconnect
        if messagebox.askyesno("Confirm Disconnect", f"Are you sure you want to disconnect '{device_info['device_name']}'?"):
            try:
                self.connection_manager.disconnect_from_device(device_id)
                self._update_status(f"Disconnected device: {device_info['device_name']}")
                self._refresh_devices()

                # Disconnect if currently connected
                if self.app.sync_client:
                    asyncio.run_coroutine_threadsafe(
                        self.app.sync_client.disconnect_from_device(device_info["ip"]),
                        self.app.loop
                    )

                if self.app.sync_server:
                    self.app.sync_server.disconnect_device(device_info["ip"])
            except Exception as e:
                messagebox.showerror("Unpair Error", f"Failed to unpair device: {e}")

    def _show_pairing_dialog(self, pairing_code: str, device_info: dict):
        """Show the pairing code dialog."""
        dialog = tk.Toplevel(self.root)
        dialog.title("Device Pairing")
        dialog.geometry("300x200")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # Pairing instructions
        ttk.Label(dialog, text=f"Pairing with: {device_info['device_name']}", font=("Arial", 12, "bold")).pack(pady=10)
        ttk.Label(dialog, text="Enter this code on the other device:", font=("Arial", 10)).pack(pady=5)

        # Pairing code display
        code_frame = ttk.Frame(dialog)
        code_frame.pack(pady=10)
        ttk.Label(code_frame, text=pairing_code, font=("Arial", 24, "bold"), foreground="blue").pack()

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="Cancel", command=lambda: self._cancel_pairing(dialog)).pack(side="left", padx=10)

        # Auto-close after 60 seconds
        dialog.after(60000, lambda: self._cancel_pairing(dialog))

    def _cancel_pairing(self, dialog):
        """Cancel the connection process."""
        # No need to cancel anything for simple connections
        dialog.destroy()
        self._update_status("Connection cancelled")

    def _on_pairing_result(self, result: str, data: dict):
        """Handle pairing result callback."""
        if result == "success":
            self._update_status(f"Successfully paired with {data['device_name']}")
            self._refresh_devices()
        elif result == "failed":
            self._update_status(f"Pairing failed: {data.get('reason', 'Unknown error')}")
        elif result == "cancelled":
            self._update_status("Pairing cancelled")

    def _clear_history(self):
        """Clear clipboard history."""
        if messagebox.askyesno("Clear History", "Are you sure you want to clear the clipboard history?"):
            if self.app.clipboard_monitor:
                self.app.clipboard_monitor.clear_history()
            self._refresh_history()
    
    def _apply_settings(self):
        """Apply settings changes."""
        try:
            # Update clipboard settings
            if self.app.clipboard_monitor:
                self.app.clipboard_monitor.set_enabled(self.sync_enabled_var.get())
                self.app.clipboard_monitor.set_history_enabled(self.history_enabled_var.get())
                self.app.clipboard_monitor.set_history_limit(int(self.history_limit_var.get()))

            # Update config
            self.config["clipboard"]["sync_enabled"] = self.sync_enabled_var.get()
            self.config["clipboard"]["history_enabled"] = self.history_enabled_var.get()
            self.config["clipboard"]["history_limit"] = int(self.history_limit_var.get())

            # Save config to file
            self._save_config()

            messagebox.showinfo("Settings", "Settings applied and saved successfully!")
            self._update_status("Settings applied")

        except ValueError as e:
            messagebox.showerror("Invalid Value", f"Please enter valid numeric values: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply settings: {e}")

    def _save_config(self):
        """Save current configuration to file."""
        try:
            import json
            from pathlib import Path

            config_path = Path(__file__).parent.parent.parent / "config.json"
            with open(config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")
            raise
    
    def _update_status(self, message: str):
        """Update status message."""
        if self.status_label:
            self.status_label.config(text=message)

    def _on_window_minimize(self, event):
        """Handle window minimize event."""
        # Check if window is being minimized (iconified)
        if event.widget == self.root and self.root.state() == 'iconic':
            self._minimize_to_tray()

    def _minimize_to_tray(self):
        """Minimize window to system tray."""
        try:
            if self.tray_icon:
                self.root.withdraw()  # Hide window
                self.is_minimized_to_tray = True

                # Start tray icon in a separate thread
                if not self.tray_thread or not self.tray_thread.is_alive():
                    self.tray_thread = threading.Thread(target=self.tray_icon.run, daemon=True)
                    self.tray_thread.start()

                self.logger.info("Application minimized to system tray")
            else:
                self.logger.warning("System tray not available, minimizing normally")
        except Exception as e:
            self.logger.error(f"Failed to minimize to tray: {e}")

    def _restore_window(self, icon=None, item=None):
        """Restore window from system tray."""
        try:
            if self.is_minimized_to_tray:
                self.root.deiconify()  # Show window
                self.root.lift()       # Bring to front
                self.root.focus_force()  # Give focus
                self.is_minimized_to_tray = False
                self.logger.info("Application restored from system tray")
        except Exception as e:
            self.logger.error(f"Failed to restore window: {e}")

    def _reconnect_devices(self, icon=None, item=None):
        """Reconnect to devices from tray menu."""
        try:
            if self.device_manager:
                # Trigger device refresh/reconnection
                self.device_manager.refresh_devices()
                self.logger.info("Device reconnection triggered from tray")
        except Exception as e:
            self.logger.error(f"Failed to reconnect devices: {e}")

    def _exit_application(self, icon=None, item=None):
        """Exit application from tray menu."""
        try:
            self.logger.info("Exit requested from system tray")
            self._cleanup_tray()
            self.is_running = False
            if self.root:
                self.root.quit()
                self.root.destroy()
        except Exception as e:
            self.logger.error(f"Failed to exit application: {e}")

    def _cleanup_tray(self):
        """Cleanup system tray resources."""
        try:
            if self.tray_icon:
                self.tray_icon.stop()
                self.tray_icon = None
            if self.tray_thread and self.tray_thread.is_alive():
                self.tray_thread.join(timeout=1)
        except Exception as e:
            self.logger.error(f"Failed to cleanup tray: {e}")

    def _on_window_close(self):
        """Handle window close event - minimize to tray instead of closing."""
        try:
            # Minimize to tray instead of closing
            self._minimize_to_tray()
        except Exception as e:
            self.logger.error(f"Failed to handle window close: {e}")
            # Fallback to normal close
            self.is_running = False
            self.root.quit()
            self.root.destroy()
