@echo off
echo ========================================
echo Fixing Gradle Issues and Building APK
echo ========================================

echo.
echo Step 1: Stopping all Gradle daemons...
call gradlew --stop

echo.
echo Step 2: Cleaning project...
call gradlew clean

echo.
echo Step 3: Clearing Gradle cache...
rmdir /s /q "%USERPROFILE%\.gradle\caches" 2>nul
echo Gradle cache cleared (if it existed)

echo.
echo Step 4: Re-downloading dependencies...
call gradlew --refresh-dependencies

echo.
echo Step 5: Building debug APK...
call gradlew assembleDebug

echo.
echo Step 6: Building release APK...
call gradlew assembleRelease

echo.
echo ========================================
echo Build Complete!
echo ========================================
echo.
echo APK files should be available in:
echo - Debug APK: app\build\outputs\apk\debug\app-debug.apk
echo - Release APK: app\build\outputs\apk\release\app-release.apk
echo.
echo If you still get errors, try:
echo 1. Close Android Studio completely
echo 2. Kill all Java processes in Task Manager
echo 3. Run this script again
echo.
pause
