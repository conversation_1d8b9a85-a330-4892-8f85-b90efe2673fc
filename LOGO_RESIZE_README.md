# Clipsy Logo Resizer

This tool helps you resize your logo for use in both Android and Windows applications, creating all the necessary icon sizes automatically.

## Quick Start

### Step 1: Save Your Logo
Save your logo image as `logo_original.png` in this directory. The image should be:
- High resolution (at least 512x512 pixels recommended)
- Square aspect ratio (1:1)
- PNG format with transparent background (if desired)

### Step 2: Run the Resizer

#### On Windows:
```bash
resize_logo.bat
```

#### On Linux/Mac:
```bash
./resize_logo.sh
```

#### Manual Python Command:
```bash
python resize_logo.py logo_original.png --all
```

## What Gets Created

### Android Icons
Located in: `android/app/src/main/res/`

- **App Icons** (mipmap folders):
  - `mipmap-mdpi/ic_launcher.png` (48x48)
  - `mipmap-hdpi/ic_launcher.png` (72x72)
  - `mipmap-xhdpi/ic_launcher.png` (96x96)
  - `mipmap-xxhdpi/ic_launcher.png` (144x144)
  - `mipmap-xxxhdpi/ic_launcher.png` (192x192)

- **Foreground Icons** (for adaptive icons):
  - Same sizes as above, named `ic_launcher_foreground.png`

- **Notification Icons** (drawable folders):
  - `drawable-mdpi/ic_notification.png` (24x24)
  - `drawable-hdpi/ic_notification.png` (36x36)
  - `drawable-xhdpi/ic_notification.png` (48x48)
  - `drawable-xxhdpi/ic_notification.png` (72x72)
  - `drawable-xxxhdpi/ic_notification.png` (96x96)

### Windows Icons
Located in: `windows/`

- Individual PNG files: `icon_16x16.png` through `icon_256x256.png`
- Multi-size ICO file: `app_icon.ico` (for use in Electron apps)

### Web Icons
Located in: `web/`

- Various sizes from 16x16 to 512x512
- `favicon.ico` for browser tabs

## Using the Icons

### For Android App
1. Copy the generated files from `android/app/src/main/res/` to your Android project
2. Update your `AndroidManifest.xml` if needed:
   ```xml
   <application
       android:icon="@mipmap/ic_launcher"
       android:label="@string/app_name">
   ```

### For Windows/Electron App
1. Use `windows/app_icon.ico` for your Electron app
2. Update your `package.json`:
   ```json
   {
     "build": {
       "win": {
         "icon": "windows/app_icon.ico"
       }
     }
   }
   ```

### For Web/PWA
1. Copy files from `web/` to your web app's public directory
2. Update your HTML head:
   ```html
   <link rel="icon" type="image/x-icon" href="/favicon.ico">
   <link rel="icon" type="image/png" sizes="32x32" href="/icon_32x32.png">
   <link rel="icon" type="image/png" sizes="16x16" href="/icon_16x16.png">
   ```

## Advanced Usage

### Create Icons for Specific Platforms
```bash
# Android only
python resize_logo.py logo_original.png --android

# Windows only
python resize_logo.py logo_original.png --windows

# Web only
python resize_logo.py logo_original.png --web
```

### Requirements
- Python 3.x
- Pillow (PIL) library (automatically installed by the batch/shell scripts)

## Troubleshooting

### "Python not found"
Install Python from https://python.org and make sure it's added to your PATH.

### "PIL/Pillow not found"
Run: `pip install Pillow`

### "Image file not found"
Make sure your logo is saved as `logo_original.png` in the same directory as these scripts.

### Poor Quality Icons
- Use a higher resolution source image (1024x1024 or larger)
- Ensure your logo is designed to work at small sizes
- Consider simplifying complex details for smaller icon sizes
