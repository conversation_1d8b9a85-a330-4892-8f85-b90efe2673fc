#!/usr/bin/env python3
"""
Simple UDP broadcast test to verify Android can receive broadcasts.
This sends UDP broadcasts that the Android app should be able to receive.
"""

import socket
import json
import time
import threading
from datetime import datetime

def get_broadcast_addresses():
    """Get broadcast addresses for all network interfaces."""
    import netifaces
    broadcast_addresses = []
    
    try:
        for interface in netifaces.interfaces():
            try:
                addrs = netifaces.ifaddresses(interface)
                if netifaces.AF_INET in addrs:
                    for addr_info in addrs[netifaces.AF_INET]:
                        broadcast = addr_info.get('broadcast')
                        if broadcast and broadcast != '***************':
                            broadcast_addresses.append(broadcast)
            except Exception:
                continue
    except Exception as e:
        print(f"Error getting broadcast addresses: {e}")
        # Fallback to common broadcast addresses
        broadcast_addresses = ['***************', '*************', '*************']
    
    return list(set(broadcast_addresses))

def send_test_broadcasts():
    """Send test UDP broadcasts on port 8765."""
    try:
        # Create UDP socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        # Test message similar to Windows Clipsy format
        message = {
            "type": "discovery",
            "device_name": "Test-PC",
            "device_type": "windows", 
            "websocket_port": 8766,
            "timestamp": datetime.now().isoformat()
        }
        
        data = json.dumps(message).encode('utf-8')
        broadcast_addresses = get_broadcast_addresses()
        
        print("UDP Broadcast Test")
        print("==================")
        print(f"Message: {message}")
        print(f"Broadcast addresses: {broadcast_addresses}")
        print(f"Port: 8765")
        print()
        
        for i in range(10):  # Send 10 broadcasts
            print(f"Sending broadcast #{i+1}...")
            
            for broadcast_addr in broadcast_addresses:
                try:
                    sock.sendto(data, (broadcast_addr, 8765))
                    print(f"  -> Sent to {broadcast_addr}:8765")
                except Exception as e:
                    print(f"  -> Failed to send to {broadcast_addr}: {e}")
            
            time.sleep(2)  # Wait 2 seconds between broadcasts
        
        sock.close()
        print("\nBroadcast test completed!")
        print("If Android app is running and working, it should discover 'Test-PC'")
        
    except Exception as e:
        print(f"Error in broadcast test: {e}")

def listen_for_responses():
    """Listen for any responses from Android devices."""
    try:
        # Create listening socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.bind(('', 8765))
        sock.settimeout(1.0)
        
        print("Listening for responses on port 8765...")
        
        start_time = time.time()
        while time.time() - start_time < 25:  # Listen for 25 seconds
            try:
                data, addr = sock.recvfrom(1024)
                message = data.decode('utf-8')
                print(f"Received from {addr}: {message}")
            except socket.timeout:
                continue
            except Exception as e:
                print(f"Error receiving: {e}")
        
        sock.close()
        print("Stopped listening for responses.")
        
    except Exception as e:
        print(f"Error in listener: {e}")

if __name__ == "__main__":
    print("Starting UDP broadcast test...")
    print("Make sure Android Clipsy app is running and try 'Refresh Devices'")
    print()
    
    # Start listener in background
    listener_thread = threading.Thread(target=listen_for_responses, daemon=True)
    listener_thread.start()
    
    # Send broadcasts
    send_test_broadcasts()
    
    # Wait for listener to finish
    listener_thread.join()
