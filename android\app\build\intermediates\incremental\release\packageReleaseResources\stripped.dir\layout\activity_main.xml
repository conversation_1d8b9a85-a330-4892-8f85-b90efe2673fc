<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.MainActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Dark" />

    </com.google.android.material.appbar.AppBarLayout>

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="?attr/actionBarSize"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/clipsy_surface"
        android:elevation="12dp"
        app:itemIconTint="@color/bottom_nav_color_selector"
        app:itemTextColor="@color/bottom_nav_color_selector"
        app:itemActiveIndicatorStyle="@style/BottomNavActiveIndicator"
        app:labelVisibilityMode="selected"
        app:menu="@menu/bottom_navigation_menu" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_manual_sync"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="160dp"
        android:contentDescription="Manual Sync to PC"
        android:stateListAnimator="@null"
        app:backgroundTint="@color/clipsy_accent"
        app:srcCompat="@drawable/ic_sync"
        app:tint="@android:color/white"
        app:elevation="6dp"
        app:pressedTranslationZ="6dp"
        app:hoveredFocusedTranslationZ="6dp"
        app:layout_dodgeInsetEdges="none"
        app:fabSize="normal"
        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Clipsy.FloatingActionButton"
        style="@style/Widget.Material3.FloatingActionButton.Primary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
