#!/usr/bin/env python3
"""
Clipsy Windows Build Script
This script builds the Windows application into an executable
"""

import sys
import os
import subprocess
import shutil
from pathlib import Path

def run_command(command, cwd=None):
    """Run a command and return the result."""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {command}")
        print(f"Error output: {e.stderr}")
        raise

def check_dependencies():
    """Check if required dependencies are installed."""
    print("Checking build dependencies...")
    
    # Check if PyInstaller is available
    try:
        import PyInstaller
        print("✓ PyInstaller found")
        return True
    except ImportError:
        print("✗ PyInstaller not found")
        print("Install with: pip install pyinstaller")
        return False

def install_dependencies():
    """Install Python dependencies."""
    print("Installing Python dependencies...")
    
    windows_dir = Path(__file__).parent.parent / "windows"
    requirements_file = windows_dir / "requirements.txt"
    
    if requirements_file.exists():
        run_command(f"pip install -r {requirements_file}")
    else:
        # Install basic dependencies
        dependencies = [
            "websockets",
            "pyperclip",
            "pyinstaller"
        ]
        for dep in dependencies:
            print(f"Installing {dep}...")
            run_command(f"pip install {dep}")

def build_executable():
    """Build the Windows executable using PyInstaller."""
    print("Building Windows executable...")
    
    windows_dir = Path(__file__).parent.parent / "windows"
    src_dir = windows_dir / "src"
    main_file = src_dir / "main.py"
    
    if not main_file.exists():
        raise FileNotFoundError(f"Main file not found: {main_file}")
    
    # Create build directory
    build_dir = windows_dir / "build"
    dist_dir = windows_dir / "dist"
    
    # PyInstaller command
    pyinstaller_args = [
        "pyinstaller",
        "--onefile",  # Create a single executable file
        "--windowed",  # Don't show console window
        "--name=Clipsy",
        f"--distpath={dist_dir}",
        f"--workpath={build_dir}",
        "--add-data=../../config.json;.",  # Include config file
        "--hidden-import=tkinter",
        "--hidden-import=websockets",
        "--hidden-import=pyperclip",
        "main.py"  # Use relative path
    ]

    # Run PyInstaller
    command = " ".join(pyinstaller_args)
    print(f"Running: {command}")
    run_command(command, cwd=src_dir)
    
    # Check if executable was created
    exe_file = dist_dir / "Clipsy.exe"
    if exe_file.exists():
        print(f"✓ Executable created: {exe_file}")
        
        # Get file size
        size = exe_file.stat().st_size
        size_mb = size / (1024 * 1024)
        print(f"Executable size: {size_mb:.1f} MB")
        
        return exe_file
    else:
        raise FileNotFoundError("Executable not created")

def create_installer():
    """Create an installer using NSIS (if available)."""
    print("Checking for NSIS installer...")
    
    try:
        # Check if NSIS is available
        run_command("makensis /VERSION")
        print("✓ NSIS found, creating installer...")
        
        windows_dir = Path(__file__).parent.parent / "windows"
        nsis_script = windows_dir / "installer.nsi"
        
        if nsis_script.exists():
            run_command(f"makensis {nsis_script}", cwd=windows_dir)
            print("✓ Installer created")
        else:
            print("Warning: NSIS script not found, skipping installer creation")
            
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Warning: NSIS not found, skipping installer creation")
        print("To create an installer, install NSIS from https://nsis.sourceforge.io/")

def create_portable_package():
    """Create a portable package with all necessary files."""
    print("Creating portable package...")
    
    windows_dir = Path(__file__).parent.parent / "windows"
    dist_dir = windows_dir / "dist"
    portable_dir = dist_dir / "Clipsy-Portable"
    
    # Create portable directory
    portable_dir.mkdir(exist_ok=True)
    
    # Copy executable
    exe_file = dist_dir / "Clipsy.exe"
    if exe_file.exists():
        shutil.copy2(exe_file, portable_dir)
    
    # Copy config file
    config_file = windows_dir.parent / "config.json"
    if config_file.exists():
        shutil.copy2(config_file, portable_dir)
    
    # Copy documentation
    readme_file = windows_dir.parent / "README.md"
    if readme_file.exists():
        shutil.copy2(readme_file, portable_dir)
    
    # Create batch file for easy launching
    batch_content = '''@echo off
echo Starting Clipsy...
Clipsy.exe
pause
'''
    with open(portable_dir / "run.bat", 'w') as f:
        f.write(batch_content)
    
    print(f"✓ Portable package created: {portable_dir}")
    return portable_dir

def main():
    """Main build function."""
    print("=== Clipsy Windows Build Script ===")
    print("Building Windows application...")
    
    # Check dependencies
    if not check_dependencies():
        print("Installing missing dependencies...")
        install_dependencies()
    
    try:
        # Build executable
        exe_file = build_executable()
        
        # Create portable package
        portable_dir = create_portable_package()
        
        # Try to create installer
        create_installer()
        
        print("\n=== Build Summary ===")
        print("Build completed successfully!")
        print(f"Executable: {exe_file}")
        print(f"Portable package: {portable_dir}")
        
        print("\nTo run the application:")
        print(f"1. Double-click: {exe_file}")
        print(f"2. Or run from portable package: {portable_dir / 'run.bat'}")
        
    except Exception as e:
        print(f"\nBuild failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
