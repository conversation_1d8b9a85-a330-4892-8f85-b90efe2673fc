package com.clipsy.android.network

import android.content.Context
import android.util.Log
import com.clipsy.android.data.model.Device
import com.clipsy.android.data.model.ConnectionStatus
import com.clipsy.android.data.repository.DeviceRepository
import com.clipsy.android.data.repository.ClipboardRepository
import kotlinx.coroutines.*
import okhttp3.*
import okio.ByteString
import org.json.JSONObject
import java.util.concurrent.TimeUnit
/**
 * WebSocket client for connecting to other Clipsy devices.
 */
class WebSocketClient(
    private val context: Context,
    private val deviceRepository: DeviceRepository,
    private val clipboardRepository: ClipboardRepository,
    private val connectionManager: com.clipsy.android.pairing.SimpleConnectionManager? = null
) {
    companion object {
        private const val TAG = "WebSocketClient"
        private const val CONNECTION_TIMEOUT = 10000L // 10 seconds - more reliable connection
        private const val PING_INTERVAL = 15000L // 15 seconds - more frequent keepalive
        private const val RECONNECT_DELAY = 1000L // 1 second - much faster reconnection
        private const val MAX_RECONNECT_ATTEMPTS = 5
        private const val CONNECTION_HEALTH_CHECK_INTERVAL = 30000L // 30 seconds - more frequent health checks
        private const val STATUS_DEBOUNCE_DELAY = 1000L // 1 second - much faster status updates
        private const val ACTIVITY_TIMEOUT = 60000L // 1 minute - faster timeout detection
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val activeConnections = mutableMapOf<String, WebSocket>()
    private val reconnectAttempts = mutableMapOf<String, Int>()
    private val reconnectJobs = mutableMapOf<String, Job>()
    private val pingJobs = mutableMapOf<String, Job>()
    private var clipboardUpdateCallback: ((String) -> Unit)? = null
    private var healthCheckJob: Job? = null

    // Socket-state based status system (no ping dependency)
    private val statusUpdateJobs = mutableMapOf<String, Job>()
    private val pendingStatusUpdates = mutableMapOf<String, Pair<ConnectionStatus, String?>>()
    private val currentStatus = mutableMapOf<String, ConnectionStatus>()
    private val lastActivityTime = mutableMapOf<String, Long>() // Track clipboard activity
    private val socketConnected = mutableMapOf<String, Boolean>() // Track actual socket state

    // Track manually disconnected devices to prevent automatic reconnection
    private val manuallyDisconnectedDevices = mutableSetOf<String>()
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(CONNECTION_TIMEOUT, TimeUnit.MILLISECONDS)
        .readTimeout(0, TimeUnit.MILLISECONDS) // No read timeout for WebSocket
        .writeTimeout(CONNECTION_TIMEOUT, TimeUnit.MILLISECONDS)
        // Remove automatic ping - we'll handle it manually for better control
        .build()

    /**
     * Set callback for clipboard updates from remote devices.
     */
    fun setClipboardUpdateCallback(callback: (String) -> Unit) {
        clipboardUpdateCallback = callback
    }

    /**
     * Connect to a device.
     */
    fun connectToDevice(device: Device) {
        scope.launch {
            try {
                Log.d(TAG, "Connecting to device: ${device.deviceName} (${device.ip})")
                Log.d(TAG, "Device WebSocket URL: ${device.getWebSocketUrl()}")
                Log.d(TAG, "Device WebSocket Port: ${device.websocketPort}")

                // Ensure device is in repository before updating connection status
                deviceRepository.addOrUpdateDevice(device)

                // Update connection status to connecting (debounced)
                updateConnectionStatusDebounced(device, ConnectionStatus.CONNECTING)

                val url = device.getWebSocketUrl()
                Log.d(TAG, "Constructed URL: $url")

                val request = Request.Builder()
                    .url(url as String)
                    .build()

                Log.d(TAG, "Creating WebSocket connection...")
                val webSocketListener = DeviceWebSocketListener(device)
                val webSocket = okHttpClient.newWebSocket(request, webSocketListener)

                val deviceKey = device.getUniqueId()
                activeConnections[deviceKey] = webSocket
                Log.d(TAG, "🟢 ADDED to activeConnections: $deviceKey (${device.deviceName})")
                Log.d(TAG, "🟢 activeConnections size: ${activeConnections.size}")
                Log.d(TAG, "WebSocket connection initiated for ${device.deviceName}")

            } catch (e: Exception) {
                Log.e(TAG, "Failed to connect to device: ${device.deviceName}", e)
                Log.e(TAG, "Exception type: ${e.javaClass.simpleName}")
                Log.e(TAG, "Exception message: ${e.message}")
                updateConnectionStatusDebounced(
                    device,
                    ConnectionStatus.ERROR,
                    "${e.javaClass.simpleName}: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Disconnect from a device and stop all background sync operations.
     */
    fun disconnectFromDevice(device: Device) {
        scope.launch {
            try {
                Log.d(TAG, "🔴 MANUAL DISCONNECT from device: ${device.deviceName}")

                val deviceKey = device.getUniqueId()

                // Mark as manually disconnected to prevent automatic reconnection
                manuallyDisconnectedDevices.add(deviceKey)
                Log.d(TAG, "🔴 Added ${device.deviceName} to manually disconnected devices")

                // Cancel all background jobs for this device
                cancelAllJobsForDevice(deviceKey, device.deviceName)

                // Close WebSocket connection
                val webSocket = activeConnections[deviceKey]
                if (webSocket != null) {
                    webSocket.close(1000, "User disconnected")
                    activeConnections.remove(deviceKey)
                    Log.d(TAG, "🔴 Closed WebSocket for ${device.deviceName}")
                }

                // Clear all state for this device
                clearDeviceState(deviceKey)

                // Update connection status
                deviceRepository.updateConnectionStatus(device, ConnectionStatus.DISCONNECTED)

                Log.d(TAG, "🔴 DISCONNECT COMPLETE for ${device.deviceName}")

            } catch (e: Exception) {
                Log.e(TAG, "Error disconnecting from device: ${device.deviceName}", e)
            }
        }
    }

    /**
     * Allow reconnection to a manually disconnected device.
     */
    fun allowReconnection(device: Device) {
        val deviceKey = device.getUniqueId()
        manuallyDisconnectedDevices.remove(deviceKey)
        Log.d(TAG, "🟢 Removed ${device.deviceName} from manually disconnected devices - reconnection allowed")
    }

    /**
     * Cancel all background jobs for a specific device.
     */
    private fun cancelAllJobsForDevice(deviceKey: String, deviceName: String) {
        // Cancel reconnection job
        reconnectJobs[deviceKey]?.cancel()
        reconnectJobs.remove(deviceKey)

        // Cancel ping job
        pingJobs[deviceKey]?.cancel()
        pingJobs.remove(deviceKey)

        // Cancel status update job
        statusUpdateJobs[deviceKey]?.cancel()
        statusUpdateJobs.remove(deviceKey)

        Log.d(TAG, "🔴 Cancelled all background jobs for $deviceName")
    }

    /**
     * Clear all state maps for a specific device.
     */
    private fun clearDeviceState(deviceKey: String) {
        reconnectAttempts.remove(deviceKey)
        pendingStatusUpdates.remove(deviceKey)
        currentStatus.remove(deviceKey)
        lastActivityTime.remove(deviceKey)
        socketConnected.remove(deviceKey)

        Log.d(TAG, "🔴 Cleared all state for device: $deviceKey")
    }
    
    /**
     * Send clipboard content to a specific device.
     */
    fun sendClipboardToDevice(device: Device, content: String) {
        scope.launch {
            try {
                val deviceKey = device.getUniqueId()
                val webSocket = activeConnections[deviceKey]
                
                if (webSocket != null) {
                    val message = createClipboardMessage(content)
                    webSocket.send(message)
                    Log.d(TAG, "Sent clipboard content to ${device.deviceName}")
                } else {
                    Log.w(TAG, "No active connection to ${device.deviceName}")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to send clipboard to ${device.deviceName}", e)
            }
        }
    }
    
    /**
     * Send clipboard content to all connected devices.
     */
    fun broadcastClipboard(content: String) {
        scope.launch {
            try {
                val message = createClipboardMessage(content)
                val connectedDevices = deviceRepository.getConnectedDevicesSync()
                
                Log.d(TAG, "Broadcasting clipboard to ${connectedDevices.size} devices")
                
                connectedDevices.forEach { device ->
                    val deviceKey = device.getUniqueId()
                    val webSocket = activeConnections[deviceKey]
                    
                    if (webSocket != null) {
                        webSocket.send(message)
                        Log.d(TAG, "Broadcast sent to ${device.deviceName}")
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to broadcast clipboard", e)
            }
        }
    }
    
    /**
     * Create clipboard message JSON.
     */
    private fun createClipboardMessage(content: String): String {
        return JSONObject().apply {
            put("type", "clipboard_sync")
            put("content", content)
            put("timestamp", System.currentTimeMillis())
            put("device_name", getDeviceName())
            put("device_id", getDeviceId())
        }.toString()
    }
    
    /**
     * Handle received clipboard message.
     */
    private suspend fun handleClipboardMessage(json: JSONObject, device: Device) {
        try {
            val content = json.getString("content")
            // Handle timestamp - could be long or string
            val timestamp = try {
                json.getLong("timestamp")
            } catch (e: Exception) {
                // If it's a string timestamp, try to parse it or use current time
                try {
                    val timestampStr = json.getString("timestamp")
                    // Try to parse ISO timestamp or use current time as fallback
                    System.currentTimeMillis()
                } catch (e2: Exception) {
                    System.currentTimeMillis()
                }
            }
            val senderName = json.optString("device_name", device.deviceName)
            val senderId = json.optString("device_id", device.deviceId ?: "unknown")
            
            // Add to clipboard repository as remote item
            clipboardRepository.addRemoteItem(
                content = content,
                deviceName = senderName,
                deviceType = "Android", // Default device type
                deviceId = senderId
            )

            // Update local clipboard
            clipboardUpdateCallback?.invoke(content)

            Log.d(TAG, "Received clipboard content from ${device.deviceName}")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling clipboard message from ${device.deviceName}", e)
        }
    }
    
    /**
     * Get device name.
     */
    private fun getDeviceName(): String {
        return android.os.Build.MODEL ?: "Android Device"
    }
    
    /**
     * Get device ID.
     */
    private fun getDeviceId(): String {
        return android.provider.Settings.Secure.getString(
            context.contentResolver,
            android.provider.Settings.Secure.ANDROID_ID
        ) ?: "unknown"
    }
    
    /**
     * Disconnect all devices.
     */
    fun disconnectAll() {
        scope.launch {
            Log.d(TAG, "Disconnecting all devices")
            
            activeConnections.values.forEach { webSocket ->
                webSocket.close(1000, "Service stopping")
            }
            
            activeConnections.clear()
            Log.d(TAG, "🔴 CLEARED all activeConnections (disconnectAll)")
        }
    }
    
    /**
     * Clean up resources.
     */
    fun cleanup() {
        disconnectAll()
        scope.cancel()
        okHttpClient.dispatcher.executorService.shutdown()
    }
    
    /**
     * WebSocket listener for device connections.
     */
    private inner class DeviceWebSocketListener(
        private val device: Device
    ) : WebSocketListener() {
        
        override fun onOpen(webSocket: WebSocket, response: Response) {
            Log.d(TAG, "🟢 WebSocket opened to ${device.deviceName}")

            val deviceKey = device.getUniqueId()
            activeConnections[deviceKey] = webSocket
            socketConnected[deviceKey] = true
            lastActivityTime[deviceKey] = System.currentTimeMillis()

            Log.d(TAG, "🟢 ADDED to activeConnections: $deviceKey (${device.deviceName})")
            Log.d(TAG, "🟢 activeConnections size: ${activeConnections.size}")

            // Send device info to register with the server
            val deviceInfo = JSONObject().apply {
                put("type", "device_info")
                put("device_name", "Android-Device")
                put("device_type", "android")
                put("websocket_port", Device.ANDROID_SERVER_PORT)  // Android's server port
                put("device_id", getDeviceId())  // CRITICAL: Add device_id for pairing validation
                put("timestamp", System.currentTimeMillis())
            }

            webSocket.send(deviceInfo.toString())
            Log.d(TAG, "Sent device info to ${device.deviceName}")

            scope.launch {
                Log.d(TAG, "WebSocket connected to ${device.deviceName}")
                resetReconnectionAttempts(device)

                // Set status to CONNECTED based on successful socket connection
                updateConnectionStatusDebounced(device, ConnectionStatus.CONNECTED)

                // Start optional ping for keepalive only (not for status determination)
                startKeepalivePing(device, webSocket)
            }
        }
        
        override fun onMessage(webSocket: WebSocket, text: String) {
            scope.launch {
                try {
                    val json = JSONObject(text)
                    val type = json.getString("type")
                    
                    when (type) {
                        "welcome" -> {
                            // Handle welcome message from server
                            Log.d(TAG, "Received welcome from ${device.deviceName}")
                            val serverInfo = json.optJSONObject("server_info")
                            if (serverInfo != null) {
                                val deviceName = serverInfo.optString("device_name", "Unknown")
                                val deviceType = serverInfo.optString("device_type", "unknown")
                                Log.d(TAG, "Connected to $deviceName ($deviceType)")
                            }
                        }
                        "clipboard_sync" -> {
                            // Handle clipboard sync and mark as recent activity
                            lastActivityTime[device.getUniqueId()] = System.currentTimeMillis()
                            handleClipboardMessage(json, device)

                            // Ensure status is CONNECTED when data is actively syncing
                            if (socketConnected[device.getUniqueId()] == true) {
                                updateConnectionStatusDebounced(device, ConnectionStatus.CONNECTED)
                            }
                        }
                        "ping" -> {
                            // Respond to ping
                            val pongMessage = JSONObject().apply {
                                put("type", "pong")
                                put("timestamp", System.currentTimeMillis())
                            }.toString()
                            webSocket.send(pongMessage)
                        }
                        "pong" -> {
                            // Handle pong response - just log, don't change status
                            lastActivityTime[device.getUniqueId()] = System.currentTimeMillis()
                            Log.d(TAG, "📥 Received pong from ${device.deviceName}")
                        }
                        "pairing_request" -> {
                            Log.d(TAG, "Pairing request ignored - using simple connections")
                        }
                        "pairing_response" -> {
                            Log.d(TAG, "Pairing response ignored - using simple connections")
                        }
                        else -> {
                            Log.d(TAG, "Unknown message type: $type from ${device.deviceName}")
                        }
                    }
                    
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing message from ${device.deviceName}", e)
                }
            }
        }
        
        override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
            // Handle binary messages if needed
            Log.d(TAG, "Received binary message from ${device.deviceName}")
        }
        
        override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
            Log.d(TAG, "Connection closing to ${device.deviceName}: $reason")
            webSocket.close(1000, null)
        }
        
        override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
            Log.w(TAG, "🔴 CONNECTION CLOSED to ${device.deviceName}: code=$code, reason=$reason")

            scope.launch {
                val deviceKey = device.getUniqueId()
                activeConnections.remove(deviceKey)
                socketConnected[deviceKey] = false

                // Clean up ping monitoring
                stopKeepalivePing(device)

                Log.d(TAG, "WebSocket disconnected from ${device.deviceName}")
                updateConnectionStatusDebounced(device, ConnectionStatus.DISCONNECTED)

                // Attempt automatic reconnection unless it was a normal close
                if (code != 1000) { // 1000 = normal closure
                    scheduleReconnection(device)
                }
            }
        }
        
        override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
            Log.e(TAG, "🔴 CONNECTION FAILED to ${device.deviceName}", t)
            Log.e(TAG, "Response code: ${response?.code}")
            Log.e(TAG, "Response message: ${response?.message}")
            Log.e(TAG, "Exception type: ${t.javaClass.simpleName}")
            Log.e(TAG, "Exception message: ${t.message}")
            Log.e(TAG, "Device URL: ${device.getWebSocketUrl()}")

            scope.launch {
                val deviceKey = device.getUniqueId()
                activeConnections.remove(deviceKey)
                socketConnected[deviceKey] = false

                // Clean up ping monitoring
                stopKeepalivePing(device)

                Log.e(TAG, "WebSocket error for ${device.deviceName}: ${t.message}")
                updateConnectionStatusDebounced(
                    device,
                    ConnectionStatus.ERROR,
                    "${t.javaClass.simpleName}: ${t.message}"
                )

                // Attempt automatic reconnection
                scheduleReconnection(device)
            }
        }
    }

    /**
     * Send clipboard content to connected devices.
     */
    fun sendClipboardContent(content: String) {
        val connectionCount = activeConnections.size
        Log.d(TAG, "Sending clipboard content to $connectionCount connected devices (${content.length} chars)")

        if (connectionCount == 0) {
            Log.w(TAG, "No active connections to send clipboard content to")
            return
        }

        activeConnections.forEach { (deviceKey, webSocket) ->
            try {
                val clipboardMessage = JSONObject().apply {
                    put("type", "clipboard_sync")
                    put("content", content)
                    put("timestamp", System.currentTimeMillis())
                    put("source", "android")
                }

                webSocket.send(clipboardMessage.toString())

                // Mark as recent activity for connection health
                lastActivityTime[deviceKey] = System.currentTimeMillis()

                Log.d(TAG, "Sent clipboard content to device (${content.length} chars)")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to send clipboard content", e)
            }
        }
    }

    /**
     * Get active connection count.
     */
    fun getActiveConnectionCount(): Int {
        return activeConnections.size
    }

    fun getActiveConnectionsDebug(): String {
        return "size=${activeConnections.size}, keys=[${activeConnections.keys.joinToString(", ")}]"
    }

    /**
     * Schedule automatic reconnection to a device.
     */
    private fun scheduleReconnection(device: Device) {
        val deviceKey = device.getUniqueId()

        // Skip reconnection if device was manually disconnected
        if (manuallyDisconnectedDevices.contains(deviceKey)) {
            Log.d(TAG, "🔴 Skipping reconnection to ${device.deviceName} - manually disconnected")
            return
        }

        val currentAttempts = reconnectAttempts[deviceKey] ?: 0

        if (currentAttempts >= MAX_RECONNECT_ATTEMPTS) {
            Log.w(TAG, "Max reconnection attempts reached for ${device.deviceName}, giving up")
            reconnectAttempts.remove(deviceKey)
            return
        }

        // Cancel any existing reconnection job for this device
        reconnectJobs[deviceKey]?.cancel()

        val delay = RECONNECT_DELAY * (currentAttempts + 1) // Exponential backoff
        Log.d(TAG, "Scheduling reconnection to ${device.deviceName} in ${delay}ms (attempt ${currentAttempts + 1}/$MAX_RECONNECT_ATTEMPTS)")

        val reconnectJob = scope.launch {
            try {
                delay(delay)

                // Double-check if device is still not manually disconnected
                if (manuallyDisconnectedDevices.contains(deviceKey)) {
                    Log.d(TAG, "🔴 Cancelling scheduled reconnection to ${device.deviceName} - manually disconnected")
                    return@launch
                }

                Log.d(TAG, "Attempting reconnection to ${device.deviceName}")
                reconnectAttempts[deviceKey] = currentAttempts + 1
                connectToDevice(device)
            } catch (e: Exception) {
                Log.e(TAG, "Error during scheduled reconnection to ${device.deviceName}", e)
            }
        }

        reconnectJobs[deviceKey] = reconnectJob
    }

    /**
     * Reset reconnection attempts for a device (call on successful connection).
     */
    private fun resetReconnectionAttempts(device: Device) {
        val deviceKey = device.getUniqueId()
        reconnectAttempts.remove(deviceKey)
        reconnectJobs[deviceKey]?.cancel()
        reconnectJobs.remove(deviceKey)
        Log.d(TAG, "Reset reconnection attempts for ${device.deviceName}")
    }

    /**
     * Start connection health monitoring.
     */
    fun startConnectionHealthCheck() {
        healthCheckJob?.cancel()
        healthCheckJob = scope.launch {
            while (isActive) {
                try {
                    delay(CONNECTION_HEALTH_CHECK_INTERVAL)
                    checkConnectionHealth()
                } catch (e: Exception) {
                    Log.e(TAG, "Error in connection health check", e)
                }
            }
        }
        Log.d(TAG, "Started connection health monitoring")
    }

    /**
     * Check health of all connections and reconnect if needed.
     */
    private suspend fun checkConnectionHealth() {
        Log.d(TAG, "Performing connection health check...")

        // Get all known devices that should be connected
        val knownDevices = deviceRepository.getDiscoveredDevicesSync()
        val connectedDeviceKeys = activeConnections.keys.toSet()

        for (device in knownDevices) {
            val deviceKey = device.getUniqueId()

            // Skip manually disconnected devices
            if (manuallyDisconnectedDevices.contains(deviceKey)) {
                Log.d(TAG, "🔴 Skipping health check for ${device.deviceName} - manually disconnected")
                continue
            }

            if (!connectedDeviceKeys.contains(deviceKey)) {
                Log.d(TAG, "Device ${device.deviceName} should be connected but isn't - attempting reconnection")
                connectToDevice(device)
            }
        }

        Log.d(TAG, "Connection health check completed. Active connections: ${activeConnections.size}")
    }

    /**
     * Force reconnect all devices (useful for clearing stuck connections).
     */
    fun forceReconnectAll() {
        Log.d(TAG, "Force reconnecting all devices...")

        // Close all existing connections
        activeConnections.values.forEach { webSocket ->
            try {
                webSocket.close(1000, "Force reconnect")
            } catch (e: Exception) {
                Log.w(TAG, "Error closing connection during force reconnect", e)
            }
        }
        activeConnections.clear()

        // Clear reconnection state
        reconnectAttempts.clear()
        reconnectJobs.values.forEach { it.cancel() }
        reconnectJobs.clear()

        // Reconnect to all known devices
        scope.launch {
            delay(200) // Brief delay to ensure cleanup - faster reconnection
            val devices = deviceRepository.getDiscoveredDevicesSync()
            devices.forEach { device ->
                Log.d(TAG, "Force reconnecting to ${device.deviceName}")
                connectToDevice(device)
            }
        }
    }

    /**
     * Start keepalive ping for a device connection (for keepalive only, not status).
     */
    private fun startKeepalivePing(device: Device, webSocket: WebSocket) {
        val deviceKey = device.getUniqueId()

        // Cancel any existing ping job for this device
        pingJobs[deviceKey]?.cancel()

        // Start periodic ping for keepalive only
        pingJobs[deviceKey] = scope.launch {
            while (isActive && activeConnections[deviceKey] == webSocket) {
                try {
                    delay(PING_INTERVAL)
                    sendPing(device, webSocket)
                } catch (e: Exception) {
                    Log.e(TAG, "Error in keepalive ping for ${device.deviceName}", e)
                    break
                }
            }
        }

        Log.d(TAG, "Started keepalive ping for ${device.deviceName}")
    }

    /**
     * Stop keepalive ping for a device.
     */
    private fun stopKeepalivePing(device: Device) {
        val deviceKey = device.getUniqueId()
        pingJobs[deviceKey]?.cancel()
        pingJobs.remove(deviceKey)
        Log.d(TAG, "Stopped keepalive ping for ${device.deviceName}")
    }

    /**
     * Send ping message to verify connection.
     */
    private fun sendPing(device: Device, webSocket: WebSocket) {
        try {
            val pingMessage = JSONObject().apply {
                put("type", "ping")
                put("timestamp", System.currentTimeMillis())
            }.toString()

            webSocket.send(pingMessage)
            Log.d(TAG, "📤 Sent ping to ${device.deviceName}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send ping to ${device.deviceName}", e)
        }
    }



    /**
     * Update connection status with debouncing to prevent UI flickering.
     * Uses socket state and activity to determine actual connection health.
     */
    private fun updateConnectionStatusDebounced(device: Device, status: ConnectionStatus, errorMessage: String? = null) {
        val deviceKey = device.getUniqueId()

        // Check if status actually changed
        if (currentStatus[deviceKey] == status) {
            Log.d(TAG, "Status unchanged for ${device.deviceName}: $status - skipping update")
            return
        }

        // Validate status based on actual socket state and activity
        val validatedStatus = validateConnectionStatus(device, status)
        if (currentStatus[deviceKey] == validatedStatus) {
            Log.d(TAG, "Validated status unchanged for ${device.deviceName}: $validatedStatus - skipping update")
            return
        }

        Log.d(TAG, "Debounced status update for ${device.deviceName}: ${currentStatus[deviceKey]} -> $validatedStatus")

        // Cancel any pending status update for this device
        statusUpdateJobs[deviceKey]?.cancel()

        // Store the pending update
        pendingStatusUpdates[deviceKey] = Pair(validatedStatus, errorMessage)

        // Schedule the debounced update
        statusUpdateJobs[deviceKey] = scope.launch {
            try {
                // Wait for debounce period (4 seconds for stability)
                delay(STATUS_DEBOUNCE_DELAY)

                // Check if this update is still the latest pending update
                val pendingUpdate = pendingStatusUpdates[deviceKey]
                if (pendingUpdate != null && pendingUpdate.first == validatedStatus) {
                    // Apply the status update
                    currentStatus[deviceKey] = validatedStatus
                    deviceRepository.updateConnectionStatus(device, validatedStatus, pendingUpdate.second)
                    pendingStatusUpdates.remove(deviceKey)
                    Log.d(TAG, "✅ Applied debounced status update for ${device.deviceName}: $validatedStatus")
                } else {
                    Log.d(TAG, "⏭️ Skipped outdated status update for ${device.deviceName}: $validatedStatus")
                }
            } catch (e: Exception) {
                if (e !is kotlinx.coroutines.CancellationException) {
                    Log.e(TAG, "Error in debounced status update for ${device.deviceName}", e)
                }
            } finally {
                statusUpdateJobs.remove(deviceKey)
            }
        }
    }

    /**
     * Validate connection status based on actual socket state and recent activity.
     */
    private fun validateConnectionStatus(device: Device, requestedStatus: ConnectionStatus): ConnectionStatus {
        val deviceKey = device.getUniqueId()
        val isSocketConnected = socketConnected[deviceKey] == true
        val lastActivity = lastActivityTime[deviceKey] ?: 0
        val timeSinceActivity = System.currentTimeMillis() - lastActivity

        return when (requestedStatus) {
            ConnectionStatus.CONNECTED -> {
                if (isSocketConnected) {
                    ConnectionStatus.CONNECTED
                } else {
                    Log.d(TAG, "Cannot set CONNECTED for ${device.deviceName} - socket not connected")
                    ConnectionStatus.DISCONNECTED
                }
            }
            ConnectionStatus.ERROR -> {
                if (isSocketConnected && timeSinceActivity < ACTIVITY_TIMEOUT) {
                    Log.d(TAG, "Ignoring ERROR for ${device.deviceName} - socket connected with recent activity")
                    ConnectionStatus.CONNECTED
                } else {
                    ConnectionStatus.ERROR
                }
            }
            else -> requestedStatus // CONNECTING, DISCONNECTED, SYNCING pass through
        }
    }

    /**
     * Force immediate status update (bypasses debouncing) - use sparingly.
     */
    private fun updateConnectionStatusImmediate(device: Device, status: ConnectionStatus, errorMessage: String? = null) {
        val deviceKey = device.getUniqueId()

        // Cancel any pending debounced update
        statusUpdateJobs[deviceKey]?.cancel()
        statusUpdateJobs.remove(deviceKey)
        pendingStatusUpdates.remove(deviceKey)

        // Apply immediately
        currentStatus[deviceKey] = status
        scope.launch {
            deviceRepository.updateConnectionStatus(device, status, errorMessage)
            Log.d(TAG, "⚡ Applied immediate status update for ${device.deviceName}: $status")
        }
    }


}
