<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Network permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    
    <!-- Foreground service permission (Android 9+) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <!-- Wake lock for background operations -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Battery optimization bypass -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    
    <!-- Boot receiver permission -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    
    <!-- Notification permission (Android 13+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- Note: Android restricts clipboard access for background services.
         Clipboard updates will only work when app is in foreground or recently used. -->

    <application
        android:name=".ClipsyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.Clipsy"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="31">
        
        <!-- Main Activity -->
        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.Clipsy">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Simple Test Activity (backup) -->
        <activity
            android:name=".ui.SimpleMainActivity"
            android:exported="false"
            android:theme="@style/Theme.Clipsy" />

        <!-- Settings Activity -->
        <activity
            android:name=".ui.SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.Clipsy"
            android:parentActivityName=".ui.MainActivity" />
        
        <!-- Clipboard Sync Service -->
        <service
            android:name=".service.ClipboardSyncService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />



    </application>

</manifest>
