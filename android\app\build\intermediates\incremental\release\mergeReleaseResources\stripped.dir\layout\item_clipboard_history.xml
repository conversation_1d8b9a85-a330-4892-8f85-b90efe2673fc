<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardBackgroundColor="@color/clipsy_card_dark"
    app:cardCornerRadius="16dp"
    app:cardElevation="0dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Content Type Icon -->
        <ImageView
            android:id="@+id/icon_content_type"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="12dp"
            android:layout_marginTop="4dp"
            android:src="@android:drawable/ic_menu_edit"
            android:alpha="0.6"
            tools:ignore="ContentDescription" />

        <!-- Content Information -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Content Preview -->
            <TextView
                android:id="@+id/text_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:maxLines="3"
                android:ellipsize="end"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="@color/clipsy_text_primary"
                tools:text="This is a sample clipboard content that might be quite long and needs to be truncated" />

            <!-- Metadata Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="2dp">

                <!-- Source Badge -->
                <TextView
                    android:id="@+id/text_source"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/badge_background"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                    android:textColor="@android:color/white"
                    android:textStyle="bold"
                    tools:text="Local" />

                <!-- Timestamp -->
                <TextView
                    android:id="@+id/text_timestamp"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="@color/clipsy_text_secondary"
                    tools:text="2 minutes ago" />

            </LinearLayout>

            <!-- Device Info (for remote items) -->
            <TextView
                android:id="@+id/text_device_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="@color/clipsy_text_primary"
                android:textStyle="italic"
                android:visibility="gone"
                tools:text="from Windows PC"
                tools:visibility="visible" />

        </LinearLayout>

        <!-- Delete Button -->
        <ImageButton
            android:id="@+id/button_delete"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginStart="8dp"
            android:layout_gravity="center_vertical"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/action_delete"
            android:src="@android:drawable/ic_menu_delete"
            android:alpha="0.6" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
