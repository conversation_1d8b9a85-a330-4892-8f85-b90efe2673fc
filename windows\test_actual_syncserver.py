#!/usr/bin/env python3
"""
Test actual SyncServer class in isolation
"""

import asyncio
import threading
import logging
import time
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from network.sync_server import SyncServer
from pairing.pairing_manager import SimpleConnectionManager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ActualSyncServerTest:
    def __init__(self):
        self.loop = None
        self.loop_thread = None
        self.sync_server = None
        self.pairing_manager = None
        self.initialization_ready = threading.Event()
        self.config = {
            "device_name": "Test-PC-Actual",
            "websocket_port": 8775,
            "discovery_port": 8776
        }
    
    def start_async_loop(self):
        """Start the asyncio event loop in a separate thread (same as our app)."""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        logger.info(f"🔄 Event loop started in thread: {threading.current_thread().name}")
        self.loop.run_forever()
    
    async def start_actual_syncserver(self):
        """Start actual SyncServer class (same as full app)"""
        logger.info(f"🚀 Starting actual SyncServer...")
        
        try:
            # Create SimpleConnectionManager (SAME AS FULL APP)
            logger.info("📋 Creating SimpleConnectionManager...")
            self.pairing_manager = SimpleConnectionManager()

            # Create actual SyncServer instance (SAME AS FULL APP)
            logger.info("📋 Creating actual SyncServer instance...")
            self.sync_server = SyncServer(self.config, self.pairing_manager)

            logger.info(f"✅ SyncServer created - host: {self.sync_server.host}, port: {self.sync_server.port}")
            
            # Start SyncServer (SAME AS FULL APP)
            logger.info("📋 Starting SyncServer...")
            await self.sync_server.start()
            
            logger.info(f"✅ SyncServer started successfully")
            logger.info(f"Server object: {self.sync_server.server}")
            logger.info(f"Server running: {self.sync_server.is_running}")
            
            if self.sync_server.server and self.sync_server.server.sockets:
                logger.info(f"Server sockets: {self.sync_server.server.sockets}")
            else:
                logger.warning("⚠️ No server sockets found!")
            
            # Signal that initialization is complete
            self.initialization_ready.set()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start actual SyncServer: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.initialization_ready.set()  # Signal even on failure
            return False
    
    def run_test(self):
        """Run the actual SyncServer test"""
        logger.info("🚀 Starting actual SyncServer test...")
        
        try:
            # Start async event loop in background thread (same as our app)
            self.loop_thread = threading.Thread(target=self.start_async_loop, daemon=True)
            self.loop_thread.start()
            
            # Wait for loop to be ready
            time.sleep(0.5)
            
            # Start actual SyncServer using run_coroutine_threadsafe (same as our app)
            if self.loop:
                logger.info("🔄 Scheduling actual SyncServer start...")
                future = asyncio.run_coroutine_threadsafe(
                    self.start_actual_syncserver(), self.loop
                )
                
                # Wait for initialization
                logger.info("⏳ Waiting for SyncServer initialization...")
                if self.initialization_ready.wait(timeout=15):
                    logger.info("✅ SyncServer initialization completed")
                    
                    # Keep server running for testing
                    logger.info("🔄 SyncServer is running - waiting for connections...")
                    logger.info(f"Test with: python test_minimal_websocket_client.py (port {self.config['websocket_port']})")
                    logger.info("Press Ctrl+C to stop")
                    
                    try:
                        while True:
                            time.sleep(1)
                    except KeyboardInterrupt:
                        logger.info("Stopping SyncServer...")
                        
                else:
                    logger.error("❌ SyncServer initialization timeout")
                    return False
            else:
                logger.error("❌ Failed to start event loop")
                return False
                
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            return False
        finally:
            # Cleanup
            if self.sync_server:
                try:
                    asyncio.run_coroutine_threadsafe(
                        self.sync_server.stop(), self.loop
                    )
                except:
                    pass
            
            if self.loop and not self.loop.is_closed():
                self.loop.call_soon_threadsafe(self.loop.stop)
            if self.loop_thread and self.loop_thread.is_alive():
                self.loop_thread.join(timeout=5)
        
        return True

if __name__ == "__main__":
    test = ActualSyncServerTest()
    success = test.run_test()
    sys.exit(0 if success else 1)
