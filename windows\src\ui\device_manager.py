"""
Modern Windows 11-style device manager for Clipsy.
Provides a clean, modern interface for managing device connections and sync settings.
"""

import tkinter as tk
from tkinter import ttk, messagebox


import logging


class ModernDeviceManager:
    """Windows 11-style device manager interface."""
    
    def __init__(self, parent_frame, app):
        self.parent_frame = parent_frame
        self.app = app
        self.logger = logging.getLogger(__name__)

        # Set up connection callback for simple connection manager
        self.app.connection_manager.set_connection_callback(self._handle_connection_event)

        # Device state tracking
        self.devices = {}  # device_id -> device_info
        self.device_widgets = {}  # device_id -> widget_frame
        self.sync_states = {}  # device_id -> sync_enabled
        
        # UI components
        self.main_frame = None
        self.devices_container = None
        self.no_devices_label = None
        
        # Callbacks
        self.on_device_connect = None
        self.on_device_disconnect = None
        self.on_sync_toggle = None
        
        self._create_ui()
        
    def _create_ui(self):
        """Create the modern device manager UI."""
        # Main container
        self.main_frame = ttk.Frame(self.parent_frame)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Header
        header_frame = ttk.Frame(self.main_frame)
        header_frame.pack(fill="x", pady=(0, 20))
        
        title_label = ttk.Label(header_frame, text="Devices", font=("Segoe UI", 16, "bold"))
        title_label.pack(side="left")
        
        # Refresh button
        refresh_btn = ttk.Button(header_frame, text="🔄 Refresh", command=self._refresh_devices)
        refresh_btn.pack(side="right")
        
        # Devices container with scrollbar
        container_frame = ttk.Frame(self.main_frame)
        container_frame.pack(fill="both", expand=True)
        
        # Create scrollable frame
        canvas = tk.Canvas(container_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(container_frame, orient="vertical", command=canvas.yview)
        self.devices_container = ttk.Frame(canvas)
        
        self.devices_container.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.devices_container, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # No devices message
        self.no_devices_label = ttk.Label(
            self.devices_container, 
            text="No devices found\n\nMake sure other Clipsy devices are running on your network.",
            font=("Segoe UI", 10),
            foreground="gray",
            justify="center"
        )
        self.no_devices_label.pack(expand=True, pady=50)
        
    def _create_device_card(self, device_id: str, device_info: dict) -> ttk.Frame:
        """Create a Windows 11-style device card."""
        # Main card frame with border
        card_frame = ttk.Frame(self.devices_container, relief="solid", borderwidth=1)
        card_frame.pack(fill="x", pady=5, padx=5)
        
        # Configure card styling
        style = ttk.Style()
        style.configure("DeviceCard.TFrame", background="#f8f9fa", relief="solid", borderwidth=1)
        card_frame.configure(style="DeviceCard.TFrame")
        
        # Inner content frame
        content_frame = ttk.Frame(card_frame)
        content_frame.pack(fill="x", padx=15, pady=15)
        
        # Top row: Device icon, name, and connection status
        top_row = ttk.Frame(content_frame)
        top_row.pack(fill="x", pady=(0, 10))
        
        # Device icon
        device_type = device_info.get("device_type", "unknown")
        icon = self._get_device_icon(device_type)
        icon_label = ttk.Label(top_row, text=icon, font=("Segoe UI", 20))
        icon_label.pack(side="left", padx=(0, 15))
        
        # Device info
        info_frame = ttk.Frame(top_row)
        info_frame.pack(side="left", fill="x", expand=True)
        
        # Device name
        device_name = device_info.get("device_name", "Unknown Device")
        name_label = ttk.Label(info_frame, text=device_name, font=("Segoe UI", 12, "bold"))
        name_label.pack(anchor="w")
        
        # Device details
        device_ip = device_info.get("ip", "Unknown IP")
        is_connected = self._is_device_connected(device_id)
        status_text = "Connected" if is_connected else "Available"
        status_color = "#0078d4" if is_connected else "#6b7280"
        
        details_text = f"{device_ip} • {status_text}"
        details_label = ttk.Label(info_frame, text=details_text, font=("Segoe UI", 9), foreground=status_color)
        details_label.pack(anchor="w")
        
        # Connection status indicator
        status_frame = ttk.Frame(top_row)
        status_frame.pack(side="right")
        
        status_indicator = "🟢" if is_connected else "⚪"
        status_label = ttk.Label(status_frame, text=status_indicator, font=("Segoe UI", 16))
        status_label.pack()
        
        # Middle row: Sync toggle and status
        middle_row = ttk.Frame(content_frame)
        middle_row.pack(fill="x", pady=(0, 10))
        
        # Sync label
        sync_label = ttk.Label(middle_row, text="Clipboard sync", font=("Segoe UI", 10))
        sync_label.pack(side="left")
        
        # Sync toggle switch (using checkbutton styled as switch)
        sync_enabled = self.sync_states.get(device_id, False)
        sync_var = tk.BooleanVar(value=sync_enabled)
        
        sync_toggle = ttk.Checkbutton(
            middle_row,
            variable=sync_var,
            command=lambda: self._toggle_sync(device_id, sync_var.get())
        )
        sync_toggle.pack(side="right")
        
        # Bottom row: Action buttons
        bottom_row = ttk.Frame(content_frame)
        bottom_row.pack(fill="x")
        
        if is_connected:
            # Disconnect button
            disconnect_btn = ttk.Button(
                bottom_row,
                text="Disconnect",
                command=lambda: self._disconnect_device(device_id)
            )
            disconnect_btn.pack(side="left", padx=(0, 10))
        else:
            # Connect button
            connect_btn = ttk.Button(
                bottom_row,
                text="Connect",
                command=lambda: self._connect_device(device_id)
            )
            connect_btn.pack(side="left", padx=(0, 10))
        
        # Pair/Unpair button
        is_paired = self._is_device_paired(device_id)
        if is_paired:
            pair_btn = ttk.Button(
                bottom_row,
                text="Unpair",
                command=lambda: self._unpair_device(device_id)
            )
        else:
            pair_btn = ttk.Button(
                bottom_row,
                text="Pair",
                command=lambda: self._pair_device(device_id)
            )
        pair_btn.pack(side="left", padx=(0, 10))
        
        # Device info button
        info_btn = ttk.Button(
            bottom_row,
            text="ℹ️ Info",
            command=lambda: self._show_device_info(device_id)
        )
        info_btn.pack(side="right")
        
        return card_frame
        
    def _get_device_icon(self, device_type: str) -> str:
        """Get appropriate icon for device type."""
        icons = {
            "android": "📱",
            "ios": "📱", 
            "windows": "💻",
            "mac": "💻",
            "linux": "💻",
            "unknown": "📟"
        }
        return icons.get(device_type.lower(), "📟")
        
    def _is_device_connected(self, device_id: str) -> bool:
        """Check if device is currently connected."""
        if not self.app.sync_server:
            return False
        connected_devices = self.app.sync_server.get_connected_devices()
        return any(d.get("device_id") == device_id for d in connected_devices.values())
        
    def _is_device_connected_simple(self, device_id: str) -> bool:
        """Check if device is connected via simple connection manager."""
        if not self.app.connection_manager:
            return False
        return self.app.connection_manager.is_device_connected(device_id)
        
    def _toggle_sync(self, device_id: str, enabled: bool):
        """Toggle sync for a device."""
        self.sync_states[device_id] = enabled
        if self.on_sync_toggle:
            self.on_sync_toggle(device_id, enabled)
        self.logger.info(f"Sync {'enabled' if enabled else 'disabled'} for device {device_id}")
        
    def _connect_device(self, device_id: str):
        """Connect to a device directly."""
        device_info = self.devices.get(device_id)
        if not device_info:
            return

        device_ip = device_info.get("ip") or device_info.get("ip_address")
        device_name = device_info.get("name") or device_info.get("device_name", "Unknown Device")

        if not device_ip:
            messagebox.showerror("Error", "Device IP not available")
            return

        try:
            success = self.app.connection_manager.connect_to_device(device_ip, device_name)
            if success:
                messagebox.showinfo("Success", f"Connected to {device_name}")
                self._refresh_devices()
            else:
                messagebox.showerror("Error", f"Failed to connect to {device_name}")
        except Exception as e:
            messagebox.showerror("Connection Error", f"Failed to connect: {e}")

    def _disconnect_device(self, device_id: str):
        """Disconnect from a device."""
        try:
            device_info = self.devices.get(device_id, {})
            device_ip = device_info.get("ip") or device_info.get("ip_address")
            device_name = device_info.get("name") or device_info.get("device_name", "Unknown Device")

            # Disconnect from both client and server sides
            if device_ip:
                if self.app.sync_client:
                    import asyncio
                    if hasattr(self.app, 'loop') and self.app.loop:
                        asyncio.run_coroutine_threadsafe(
                            self.app.sync_client.disconnect_from_device(device_ip),
                            self.app.loop
                        )

                if self.app.sync_server:
                    self.app.sync_server.disconnect_device(device_ip)

            success = self.app.connection_manager.disconnect_from_device(device_id)
            if success:
                messagebox.showinfo("Success", f"Disconnected from {device_name}")
                self._refresh_devices()
            else:
                messagebox.showerror("Error", "Failed to disconnect from device")
        except Exception as e:
            messagebox.showerror("Disconnection Error", f"Failed to disconnect: {e}")
        
    def _remove_device(self, device_id: str):
        """Remove a device from known devices."""
        if messagebox.askyesno("Remove Device", "Are you sure you want to remove this device?"):
            try:
                success = self.app.connection_manager.remove_known_device(device_id)
                if success:
                    self._refresh_devices()
                    messagebox.showinfo("Success", "Device removed successfully")
                else:
                    messagebox.showerror("Error", "Failed to remove device")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to remove device: {e}")

    def _handle_connection_event(self, event_type: str, data: dict):
        """Handle connection events from connection manager."""
        if event_type == "connected":
            device_name = data.get("device_name", "Unknown Device")
            self.logger.info(f"Device connected: {device_name}")
            self._refresh_devices()
        elif event_type == "disconnected":
            device_name = data.get("device_name", "Unknown Device")
            self.logger.info(f"Device disconnected: {device_name}")
            self._refresh_devices()
                
    def _show_device_info(self, device_id: str):
        """Show detailed device information."""
        device_info = self.devices.get(device_id)
        if not device_info:
            return
            
        info_text = f"""Device Information:

Name: {device_info.get('device_name', 'Unknown')}
Type: {device_info.get('device_type', 'Unknown')}
IP Address: {device_info.get('ip', 'Unknown')}
WebSocket Port: {device_info.get('websocket_port', 'Unknown')}
Last Seen: {device_info.get('last_seen', 'Unknown')}
Device ID: {device_info.get('device_id', 'Unknown')}
Connected: {'Yes' if self._is_device_connected_simple(device_id) else 'No'}"""

        messagebox.showinfo("Device Information", info_text)
        
    def _show_pairing_dialog(self, pairing_code: str, device_info: dict, is_incoming: bool = False):
        """Show the pairing code dialog."""
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("Device Pairing")
        dialog.geometry("400x300")
        dialog.transient(self.parent_frame)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Content
        content_frame = ttk.Frame(dialog)
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title_text = "Incoming Pairing Request" if is_incoming else "Pairing Code"
        title_label = ttk.Label(content_frame, text=title_text, font=("Segoe UI", 14, "bold"))
        title_label.pack(pady=(0, 10))

        # Instructions
        device_name = device_info.get("device_name", "Unknown Device")
        if is_incoming:
            instructions = f"{device_name} wants to pair with this PC.\nDoes this code match what you see on {device_name}?"
        else:
            instructions = f"Enter this code on {device_name}:"
        instructions_label = ttk.Label(content_frame, text=instructions, font=("Segoe UI", 10))
        instructions_label.pack(pady=(0, 20))

        # Pairing code (large, centered)
        code_frame = ttk.Frame(content_frame)
        code_frame.pack(pady=(0, 20))

        code_label = ttk.Label(code_frame, text=pairing_code, font=("Segoe UI", 24, "bold"), foreground="#0078d4")
        code_label.pack()

        # Status
        if is_incoming:
            status_text = "Confirm if the codes match"
        else:
            status_text = "Waiting for device response..."
        status_label = ttk.Label(content_frame, text=status_text, font=("Segoe UI", 9))
        status_label.pack(pady=(0, 20))

        # Buttons
        button_frame = ttk.Frame(content_frame)
        button_frame.pack()

        if is_incoming:
            # For incoming requests, show Yes/No buttons
            def confirm_pairing():
                self.app.pairing_manager.confirm_pairing()
                dialog.destroy()

            def reject_pairing():
                # TODO: Implement pairing rejection
                dialog.destroy()

            ttk.Button(button_frame, text="Yes", command=confirm_pairing).pack(side="left", padx=(0, 10))
            ttk.Button(button_frame, text="No", command=reject_pairing).pack(side="left")
        else:
            # For outgoing requests, show Cancel button
            ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side="right", padx=(10, 0))

    async def _send_bluetooth_pairing_start(self, device_ip: str, pairing_code: str, device_id: str, device_name: str):
        """Send Bluetooth-style pairing start message to device."""
        try:
            # Send pairing_start message to Android device
            await self.app.pairing_manager.send_bluetooth_pairing_start(
                device_ip,
                8768,  # Android TCP pairing port
                pairing_code,
                device_id,
                device_name,
                self._on_bluetooth_pairing_response
            )
        except Exception as e:
            self.logger.error(f"Error sending Bluetooth pairing start to {device_ip}: {e}")
            messagebox.showerror("Pairing Error", f"Failed to send pairing request: {e}")

    async def _send_tcp_pairing_request(self, device_ip: str, pairing_code: str):
        """Send TCP pairing request to device."""
        try:
            await self.app.pairing_manager.send_tcp_pairing_request(
                device_ip,
                8768,  # Android TCP pairing port
                self._on_tcp_pairing_response
            )
        except Exception as e:
            self.logger.error(f"Error sending TCP pairing request to {device_ip}: {e}")

    def _on_bluetooth_pairing_response(self, success: bool, device_id: str, device_name: str, message: str):
        """Handle Bluetooth-style pairing response."""
        if success:
            self.logger.info(f"Bluetooth pairing successful with {device_name}")
            # Pairing dialog should remain open for user confirmation
        else:
            messagebox.showerror("Pairing Failed", f"Failed to pair with {device_name}: {message}")

    def _on_tcp_pairing_response(self, success: bool, device_id: str, device_name: str, message: str):
        """Handle TCP pairing response."""
        if success:
            messagebox.showinfo("Pairing Successful", f"Successfully paired with {device_name}")
            self._refresh_devices()
        else:
            messagebox.showerror("Pairing Failed", f"Failed to pair with {device_name}: {message}")

    def _on_pairing_result(self, success: bool, device_id: str, device_name: str):
        """Handle pairing result."""
        if success:
            messagebox.showinfo("Pairing Successful", f"Successfully paired with {device_name}")
        else:
            messagebox.showerror("Pairing Failed", f"Failed to pair with {device_name}")

    def _handle_pairing_notification(self, notification_type: str, data: dict):
        """Handle pairing notifications from pairing manager."""
        if notification_type == 'show_pairing_dialog':
            code = data.get('code')
            device_name = data.get('device_name')
            is_initiator = data.get('is_initiator', False)

            # Create device info for the dialog
            device_info = {
                'device_name': device_name,
                'ip_address': 'incoming'  # We don't have the IP in this context
            }

            # Show pairing dialog with the received code
            self._show_pairing_dialog(code, device_info, is_incoming=True)

        elif notification_type == 'pairing_result':
            success = data.get('success', False)
            device_name = data.get('device_name', 'Unknown Device')
            message = data.get('message', '')

            if success:
                messagebox.showinfo("Pairing Successful", f"Successfully paired with {device_name}")
                # Refresh device list to show the newly paired device
                self.refresh_devices()
            else:
                messagebox.showerror("Pairing Failed", f"Failed to pair with {device_name}: {message}")
        self._refresh_devices()
        
    def update_devices(self, discovered_devices: dict, connected_devices: dict):
        """Update the device list with new data."""
        # Combine discovered and connected devices
        all_devices = {}
        
        # Add discovered devices
        for ip, device_info in discovered_devices.items():
            device_id = device_info.get("device_id", ip)
            all_devices[device_id] = device_info
            
        # Add connected devices (may override discovered ones with more info)
        for ip, device_info in connected_devices.items():
            device_id = device_info.get("device_id", ip)
            all_devices[device_id] = device_info
            
        self.devices = all_devices
        self._rebuild_device_list()
        
    def _rebuild_device_list(self):
        """Rebuild the entire device list UI."""
        # Clear existing widgets
        for widget in self.device_widgets.values():
            widget.destroy()
        self.device_widgets.clear()
        
        # Hide/show no devices message
        if self.devices:
            self.no_devices_label.pack_forget()
            
            # Create device cards
            for device_id, device_info in self.devices.items():
                card = self._create_device_card(device_id, device_info)
                self.device_widgets[device_id] = card
        else:
            self.no_devices_label.pack(expand=True, pady=50)
            
    def _refresh_devices(self):
        """Refresh the device list from the app."""
        discovered = {}
        connected = {}
        
        if self.app.device_discovery:
            discovered = self.app.device_discovery.get_discovered_devices()
            
        if self.app.sync_server:
            connected = self.app.sync_server.get_connected_devices()
            
        self.update_devices(discovered, connected)
        
    def set_callbacks(self, on_connect=None, on_disconnect=None, on_sync_toggle=None):
        """Set callback functions for device actions."""
        self.on_device_connect = on_connect
        self.on_device_disconnect = on_disconnect
        self.on_sync_toggle = on_sync_toggle

    def handle_connection_request(self, device_ip: str, device_info: dict):
        """Handle a device connection request with user approval dialog."""
        device_name = device_info.get('device_name', 'Unknown Device')
        device_type = device_info.get('device_type', 'unknown')

        # Show approval dialog
        result = messagebox.askyesno(
            "Device Connection Request",
            f"Device '{device_name}' ({device_type}) at {device_ip} wants to connect.\n\n"
            f"Do you want to allow this connection?",
            icon='question'
        )

        if result:
            # User approved - tell sync server to approve the connection
            if self.app.sync_server:
                success = self.app.sync_server.approve_device_connection(device_ip)
                if success:
                    self.logger.info(f"Approved connection for {device_name} ({device_ip})")
                    # Refresh the device list to show the connected device
                    self._refresh_devices()
                else:
                    messagebox.showerror("Error", f"Failed to approve connection for {device_name}")
        else:
            # User rejected - tell sync server to reject the connection
            if self.app.sync_server:
                success = self.app.sync_server.reject_device_connection(device_ip)
                if success:
                    self.logger.info(f"Rejected connection for {device_name} ({device_ip})")
                else:
                    messagebox.showerror("Error", f"Failed to reject connection for {device_name}")

    def handle_pairing_required(self, device_ip: str, device_info: dict):
        """Handle when a device needs pairing before connection."""
        device_name = device_info.get('device_name', 'Unknown Device')

        # Show pairing required dialog
        result = messagebox.askyesno(
            "Pairing Required",
            f"Device '{device_name}' at {device_ip} is not paired.\n\n"
            f"You must pair with this device before it can connect.\n"
            f"Do you want to start the pairing process?",
            icon='warning'
        )

        if result:
            # Start pairing process
            self._pair_device(device_info.get("device_id", device_ip))
        else:
            # Reject the connection since no pairing
            if self.app.sync_server:
                self.app.sync_server.reject_device_connection(device_ip)
