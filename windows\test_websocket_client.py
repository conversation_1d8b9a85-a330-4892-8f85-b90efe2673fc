#!/usr/bin/env python3
"""
Simple WebSocket client test to check if PC WebSocket server accepts connections.
"""

import asyncio
import websockets
import json
import sys

async def test_websocket_connection():
    """Test WebSocket connection to PC server."""
    uri = "ws://192.168.1.161:8766"
    
    try:
        print(f"Connecting to {uri}...")
        
        async with websockets.connect(uri, ping_interval=None) as websocket:
            print("Connected successfully!")

            # Send a test message
            test_message = {
                "type": "device_info",
                "device_name": "Test-Client",
                "device_type": "test",
                "timestamp": "2025-07-08T04:00:00"
            }

            print(f"Sending test message: {test_message}")
            await websocket.send(json.dumps(test_message))

            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"Received response: {response}")
            except asyncio.TimeoutError:
                print("No response received within 5 seconds")

            print("Test completed successfully")
            
    except Exception as e:
        print(f"Connection failed: {e}")
        return False
    
    return True

async def main():
    """Main test function."""
    print("Testing WebSocket connection to PC server...")
    success = await test_websocket_connection()

    if success:
        print("WebSocket server is working correctly!")
        sys.exit(0)
    else:
        print("WebSocket server test failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
