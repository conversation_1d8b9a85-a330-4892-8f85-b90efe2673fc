# Clipsy Windows Executable

## Quick Start

### Option 1: Direct Run
1. Navigate to the `dist` folder
2. Double-click `Clipsy.exe` to start the application

### Option 2: Batch File
1. Double-click `Run_Clipsy.bat` in the main windows folder
2. This will automatically start the application

## What the Application Does

Clipsy is a cross-platform clipboard synchronization tool that allows you to:

- **Sync clipboard content** between Windows PC and Android devices
- **Auto-discover devices** on the same Wi-Fi network
- **Secure pairing** with manual 2-digit codes
- **Real-time sync** when devices are connected
- **Manual sync** option for on-demand clipboard sharing

## First Time Setup

1. **Start the Windows app** using one of the methods above
2. **Install the Android app** on your mobile device
3. **Connect both devices** to the same Wi-Fi network
4. **Pair the devices**:
   - The Windows app will show discovered Android devices
   - Click "Pair" next to your Android device
   - Enter the 2-digit code shown on your Android device
   - Devices are now paired and will auto-connect

## Usage

### Windows PC:
- The app runs in the system tray
- Copy any text to clipboard - it will automatically sync to paired Android devices
- Use the "Manual Sync" button to force sync current clipboard content

### Android Device:
- Use the "Sync to PC" button to send current clipboard to Windows
- View clipboard history in the History tab
- Check connection status in the Devices tab

## Network Requirements

- Both devices must be on the same Wi-Fi network
- Windows Firewall may need to allow the application (it will prompt you)
- Uses ports 8765 (discovery) and 8767 (sync server)

## Troubleshooting

### If devices don't discover each other:
1. Check both devices are on the same Wi-Fi network
2. Restart both applications
3. Check Windows Firewall settings
4. Try manual sync buttons

### If sync doesn't work:
1. Ensure devices are paired
2. Check connection status in the Devices tab
3. Try manual sync first
4. Restart both applications if needed

## Files Included

- `Clipsy.exe` - Main executable (22.6 MB)
- `config.json` - Configuration file
- `app_icon.ico` - Application icon
- Various support files bundled in the executable

## Technical Details

- **Built with**: Python 3.12 + PyInstaller
- **UI Framework**: Tkinter (built-in)
- **Network**: WebSockets for real-time communication
- **Discovery**: UDP broadcast on local network
- **Security**: Manual pairing with random codes

## Version Information

- **Build Date**: July 6, 2025
- **PyInstaller**: 6.14.1
- **Python**: 3.12.7
- **Platform**: Windows 11

The executable is self-contained and doesn't require Python installation on the target machine.
