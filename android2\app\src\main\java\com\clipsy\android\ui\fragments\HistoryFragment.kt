package com.clipsy.android.ui.fragments

import android.app.AlertDialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import com.clipsy.android.R
import com.clipsy.android.databinding.FragmentHistoryBinding
import com.clipsy.android.ui.adapters.ClipboardHistoryAdapter
import com.clipsy.android.viewmodel.MainViewModel
/**
 * Fragment for displaying clipboard history.
 */
class HistoryFragment : Fragment() {
    
    private var _binding: FragmentHistoryBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: MainViewModel by activityViewModels()
    private lateinit var historyAdapter: ClipboardHistoryAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHistoryBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupMenu()
        setupRecyclerView()
        setupObservers()
        setupSendMessage()
    }
    
    private fun setupMenu() {
        val menuHost: MenuHost = requireActivity()
        menuHost.addMenuProvider(object : MenuProvider {
            override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                menuInflater.inflate(R.menu.menu_history, menu)
            }
            
            override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                return when (menuItem.itemId) {
                    R.id.action_clear_history -> {
                        showClearHistoryDialog()
                        true
                    }
                    else -> false
                }
            }
        }, viewLifecycleOwner, Lifecycle.State.RESUMED)
    }
    
    private fun setupRecyclerView() {
        historyAdapter = ClipboardHistoryAdapter(
            onItemClick = { item ->
                copyToClipboard(item.content)
            },
            onDeleteClick = { item ->
                showDeleteItemDialog(item)
            }
        )
        
        binding.recyclerViewHistory.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = historyAdapter
        }
    }
    
    private fun setupObservers() {
        // Get clipboard history with a reasonable limit
        viewModel.getClipboardHistory(100).observe(viewLifecycleOwner) { items ->
            historyAdapter.submitList(items)
            updateEmptyState(items.isEmpty())
        }
    }
    
    private fun updateEmptyState(isEmpty: Boolean) {
        if (isEmpty) {
            binding.recyclerViewHistory.visibility = View.GONE
            binding.layoutEmpty.visibility = View.VISIBLE
        } else {
            binding.recyclerViewHistory.visibility = View.VISIBLE
            binding.layoutEmpty.visibility = View.GONE
        }
    }
    
    private fun copyToClipboard(content: String) {
        val clipboardManager = requireContext().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clipData = ClipData.newPlainText("Clipsy", content)
        clipboardManager.setPrimaryClip(clipData)
        
        Toast.makeText(requireContext(), "Copied to clipboard", Toast.LENGTH_SHORT).show()
    }
    
    private fun showDeleteItemDialog(item: com.clipsy.android.data.model.ClipboardItem) {
        AlertDialog.Builder(requireContext())
            .setTitle("Delete Item")
            .setMessage("Are you sure you want to delete this clipboard item?")
            .setPositiveButton("Delete") { _, _ ->
                viewModel.deleteClipboardItem(item)
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    private fun showClearHistoryDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("Clear History")
            .setMessage("Are you sure you want to clear all clipboard history? This action cannot be undone.")
            .setPositiveButton("Clear") { _, _ ->
                viewModel.clearClipboardHistory()
                Toast.makeText(requireContext(), "History cleared", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun setupSendMessage() {
        binding.buttonSendMessage.setOnClickListener {
            val message = binding.editTextMessage.text?.toString()?.trim()
            if (!message.isNullOrEmpty()) {
                sendMessageToPC(message)
                binding.editTextMessage.text?.clear()
            } else {
                Toast.makeText(requireContext(), "Please enter a message", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun sendMessageToPC(message: String) {
        try {
            // Copy message to clipboard first
            copyToClipboard(message)

            // Send manual sync broadcast to service (same as manual sync button)
            val intent = android.content.Intent("com.clipsy.android.MANUAL_SYNC")
            intent.setPackage(requireContext().packageName)
            requireContext().sendBroadcast(intent)

            Toast.makeText(requireContext(), "Sending to PC...", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(requireContext(), "Failed to send: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
