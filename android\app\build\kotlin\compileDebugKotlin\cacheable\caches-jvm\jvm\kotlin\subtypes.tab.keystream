android.os.Parcelable(androidx.appcompat.app.AppCompatActivity(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallbackandroidx.fragment.app.Fragmentkotlin.Enum androidx.viewbinding.ViewBindingandroid.app.Applicationokhttp3.WebSocketListener)org.java_websocket.server.WebSocketServerandroid.app.Serviceandroid.os.Binder,androidx.lifecycle.ViewModelProvider.Factory,androidx.preference.PreferenceFragmentCompatandroidx.lifecycle.ViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           