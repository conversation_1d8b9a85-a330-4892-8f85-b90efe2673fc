@echo off
REM Clipsy PC Application Launcher
REM This script checks firewall permissions and launches Clipsy

echo ========================================
echo        Clipsy PC Application
echo ========================================
echo.

REM Define Clipsy executable paths
set "CLIPSY_EXE=%~dp0src\dist\Clipsy.exe"
set "CLIPSY_CLEANED_EXE=%~dp0src\dist\ClipsyCleanedCode.exe"

REM Check which executable exists and prefer the cleaned version
if exist "%CLIPSY_CLEANED_EXE%" (
    set "CLIPSY_APP=%CLIPSY_CLEANED_EXE%"
    set "APP_NAME=ClipsyCleanedCode.exe"
) else if exist "%CLIPSY_EXE%" (
    set "CLIPSY_APP=%CLIPSY_EXE%"
    set "APP_NAME=Clipsy.exe"
) else (
    echo [ERROR] Clipsy executable not found!
    echo.
    echo Please ensure one of these files exists:
    echo - %CLIPSY_CLEANED_EXE%
    echo - %CLIPSY_EXE%
    echo.
    echo You may need to build the executable first.
    pause
    exit /b 1
)

echo [INFO] Found Clipsy application: %APP_NAME%
echo.

REM Check if firewall rules exist
echo [INFO] Checking Windows Firewall configuration...
netsh advfirewall firewall show rule name=all | findstr /i "clipsy" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Clipsy firewall rules are configured.
) else (
    echo [WARNING] No Clipsy firewall rules found.
    echo.
    echo Clipsy needs Windows Firewall permissions to communicate with mobile devices.
    echo.
    choice /C YN /M "Do you want to configure firewall permissions now? (Requires Administrator)"
    if errorlevel 2 (
        echo [INFO] Skipping firewall setup. You may experience connection issues.
        echo.
    ) else (
        echo [INFO] Launching firewall setup...
        echo.

        REM Check if running as administrator
        net session >nul 2>&1
        if %errorLevel% == 0 (
            REM Already admin, run setup directly
            call "%~dp0setup-firewall.bat"
        ) else (
            REM Not admin, request elevation
            echo [INFO] Requesting Administrator privileges for firewall setup...
            powershell -Command "Start-Process '%~dp0setup-firewall.bat' -Verb RunAs -Wait"
        )
        echo.
    )
)

REM Check if autostart is configured
echo [INFO] Checking Windows startup configuration...
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" >nul 2>&1
set "USER_STARTUP=%errorLevel%"
reg query "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v "ClipsyPC" >nul 2>&1
set "SYSTEM_STARTUP=%errorLevel%"

if %USER_STARTUP% == 0 (
    echo [OK] Clipsy is configured to start with Windows (Current User).
) else if %SYSTEM_STARTUP% == 0 (
    echo [OK] Clipsy is configured to start with Windows (All Users).
) else (
    echo [INFO] Clipsy is not configured to start with Windows.
    echo.
    choice /C YN /M "Do you want to configure Clipsy to start with Windows?"
    if errorlevel 1 if not errorlevel 2 (
        echo [INFO] Launching autostart setup...
        call "%~dp0setup-autostart.bat"
        echo.
    )
)

REM Launch Clipsy application
echo [INFO] Launching %APP_NAME%...
echo.
echo ========================================
echo   Clipsy is starting...
echo ========================================
echo.
echo - The application will appear in the system tray
echo - Right-click the tray icon for options
echo - Make sure your mobile device is on the same network
echo.

REM Start Clipsy in the background
start "" "%CLIPSY_APP%"

if %errorLevel% == 0 (
    echo [SUCCESS] Clipsy has been launched successfully!
    echo.
    echo The application is now running in the system tray.
    echo You can close this window.
) else (
    echo [ERROR] Failed to launch Clipsy application.
    echo.
    echo Please check:
    echo - The executable file exists and is not corrupted
    echo - You have necessary permissions
    echo - No antivirus software is blocking the application
    pause
)

echo.
timeout /t 3 /nobreak >nul
