@echo off
REM Clipsy Windows Firewall Removal Script
REM This script removes all Clipsy-related Windows Firewall rules
REM Run as Administrator for proper permissions

echo ========================================
echo   Clipsy Firewall Rules Removal
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Running with Administrator privileges...
    echo.
) else (
    echo [ERROR] This script must be run as Administrator!
    echo [INFO] Right-click on this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [INFO] Removing all Clipsy-related firewall rules...
echo.

REM Remove all Clipsy firewall rules
echo [STEP 1] Removing Clipsy application rules...

netsh advfirewall firewall delete rule name="Clipsy PC App - Inbound"
if %errorLevel% == 0 (
    echo [OK] Removed: Clipsy PC App - Inbound
) else (
    echo [INFO] Rule not found: Clipsy PC App - Inbound
)

netsh advfirewall firewall delete rule name="Clipsy PC App - Outbound"
if %errorLevel% == 0 (
    echo [OK] Removed: Clipsy PC App - Outbound
) else (
    echo [INFO] Rule not found: Clipsy PC App - Outbound
)

netsh advfirewall firewall delete rule name="Clipsy Cleaned - Inbound"
if %errorLevel% == 0 (
    echo [OK] Removed: Clipsy Cleaned - Inbound
) else (
    echo [INFO] Rule not found: Clipsy Cleaned - Inbound
)

netsh advfirewall firewall delete rule name="Clipsy Cleaned - Outbound"
if %errorLevel% == 0 (
    echo [OK] Removed: Clipsy Cleaned - Outbound
) else (
    echo [INFO] Rule not found: Clipsy Cleaned - Outbound
)

echo.
echo [STEP 2] Removing Python rules...

netsh advfirewall firewall delete rule name="Clipsy Python - Inbound"
if %errorLevel% == 0 (
    echo [OK] Removed: Clipsy Python - Inbound
) else (
    echo [INFO] Rule not found: Clipsy Python - Inbound
)

netsh advfirewall firewall delete rule name="Clipsy Python - Outbound"
if %errorLevel% == 0 (
    echo [OK] Removed: Clipsy Python - Outbound
) else (
    echo [INFO] Rule not found: Clipsy Python - Outbound
)

echo.
echo [STEP 3] Removing port-specific rules...

netsh advfirewall firewall delete rule name="Clipsy WebSocket Port - Inbound"
if %errorLevel% == 0 (
    echo [OK] Removed: Clipsy WebSocket Port - Inbound
) else (
    echo [INFO] Rule not found: Clipsy WebSocket Port - Inbound
)

netsh advfirewall firewall delete rule name="Clipsy WebSocket Port - Outbound"
if %errorLevel% == 0 (
    echo [OK] Removed: Clipsy WebSocket Port - Outbound
) else (
    echo [INFO] Rule not found: Clipsy WebSocket Port - Outbound
)

netsh advfirewall firewall delete rule name="Clipsy Discovery Port - Inbound"
if %errorLevel% == 0 (
    echo [OK] Removed: Clipsy Discovery Port - Inbound
) else (
    echo [INFO] Rule not found: Clipsy Discovery Port - Inbound
)

netsh advfirewall firewall delete rule name="Clipsy Discovery Port - Outbound"
if %errorLevel% == 0 (
    echo [OK] Removed: Clipsy Discovery Port - Outbound
) else (
    echo [INFO] Rule not found: Clipsy Discovery Port - Outbound
)

REM Remove any legacy rules
echo.
echo [STEP 4] Removing any legacy rules...

netsh advfirewall firewall delete rule name="Clipsy PC App" >nul 2>&1
netsh advfirewall firewall delete rule name="Clipsy Executable" >nul 2>&1
netsh advfirewall firewall delete rule name="Clipsy Python Script" >nul 2>&1

echo [OK] Legacy rules cleaned up.

echo.
echo [STEP 5] Verifying removal...
echo.

REM Check if any Clipsy rules remain
set "REMAINING_RULES="
for /f %%i in ('netsh advfirewall firewall show rule name^=all ^| findstr /i "clipsy" 2^>nul') do set "REMAINING_RULES=found"

if defined REMAINING_RULES (
    echo [WARNING] Some Clipsy rules may still exist:
    netsh advfirewall firewall show rule name=all | findstr /i "clipsy"
    echo.
    echo You may need to remove them manually through Windows Firewall settings.
) else (
    echo [SUCCESS] All Clipsy firewall rules have been removed.
)

echo.
echo ========================================
echo     Firewall Removal Complete!
echo ========================================
echo.
echo All Clipsy-related Windows Firewall rules have been removed.
echo.
echo If you want to re-enable firewall permissions for Clipsy,
echo run: setup-firewall.bat
echo.
pause
