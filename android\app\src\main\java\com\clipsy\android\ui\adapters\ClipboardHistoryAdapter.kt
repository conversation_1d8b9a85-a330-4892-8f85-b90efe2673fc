package com.clipsy.android.ui.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.clipsy.android.R
import com.clipsy.android.data.model.ClipboardItem
import com.clipsy.android.databinding.ItemClipboardHistoryBinding

/**
 * RecyclerView adapter for displaying clipboard history.
 */
class ClipboardHistoryAdapter(
    private val onItemClick: (ClipboardItem) -> Unit,
    private val onDeleteClick: (ClipboardItem) -> Unit,
    private val onNewItemAdded: (() -> Unit)? = null
) : ListAdapter<ClipboardItem, ClipboardHistoryAdapter.HistoryViewHolder>(HistoryDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HistoryViewHolder {
        val binding = ItemClipboardHistoryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return HistoryViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: HistoryViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class HistoryViewHolder(
        private val binding: ItemClipboardHistoryBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(item: ClipboardItem) {
            binding.apply {
                // Content preview
                textContent.text = item.getPreview()
                
                // Timestamp
                textTimestamp.text = item.getFormattedTimestamp()
                
                // Source indicator
                val sourceText = if (item.isLocal()) "Local" else "Remote"
                textSource.text = sourceText
                // Always use white text for source badge for visibility
                textSource.setTextColor(root.context.getColor(android.R.color.white))

                // Device info (for remote items)
                if (!item.isLocal() && !item.deviceName.isNullOrEmpty()) {
                    textDeviceInfo.text = "from ${item.deviceName}"
                    textDeviceInfo.visibility = android.view.View.VISIBLE
                } else {
                    textDeviceInfo.visibility = android.view.View.GONE
                }
                
                // Content type icon
                iconContentType.setImageResource(
                    when {
                        item.content.contains("http://") || item.content.contains("https://") -> 
                            android.R.drawable.ic_menu_mapmode
                        item.content.contains("@") && item.content.contains(".") -> 
                            android.R.drawable.ic_dialog_email
                        item.content.matches(Regex("\\d+")) -> 
                            android.R.drawable.ic_dialog_dialer
                        else -> android.R.drawable.ic_menu_edit
                    }
                )
                
                // Click listeners
                root.setOnClickListener {
                    onItemClick(item)
                }
                
                buttonDelete.setOnClickListener {
                    onDeleteClick(item)
                }

                // Long click for additional options
                root.setOnLongClickListener {
                    // Could show context menu with more options
                    true
                }
            }
        }
    }

    private var lastTopItemId: Long = -1L

    override fun submitList(list: List<ClipboardItem>?) {
        val shouldNotifyNewItem = list?.let { newList ->
            if (newList.isNotEmpty()) {
                val newTopItemId = newList.first().id
                val isNewItem = lastTopItemId != -1L && newTopItemId != lastTopItemId
                lastTopItemId = newTopItemId
                isNewItem
            } else {
                lastTopItemId = -1L
                false
            }
        } ?: false

        super.submitList(list) {
            if (shouldNotifyNewItem) {
                onNewItemAdded?.invoke()
            }
        }
    }

    override fun submitList(list: List<ClipboardItem>?, commitCallback: Runnable?) {
        val shouldNotifyNewItem = list?.let { newList ->
            if (newList.isNotEmpty()) {
                val newTopItemId = newList.first().id
                val isNewItem = lastTopItemId != -1L && newTopItemId != lastTopItemId
                lastTopItemId = newTopItemId
                isNewItem
            } else {
                lastTopItemId = -1L
                false
            }
        } ?: false

        super.submitList(list) {
            commitCallback?.run()
            if (shouldNotifyNewItem) {
                onNewItemAdded?.invoke()
            }
        }
    }

    private class HistoryDiffCallback : DiffUtil.ItemCallback<ClipboardItem>() {
        override fun areItemsTheSame(oldItem: ClipboardItem, newItem: ClipboardItem): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: ClipboardItem, newItem: ClipboardItem): Boolean {
            return oldItem == newItem
        }
    }
}
