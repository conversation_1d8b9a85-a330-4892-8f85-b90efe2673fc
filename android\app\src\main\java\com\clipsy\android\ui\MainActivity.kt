package com.clipsy.android.ui

import android.Manifest
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.IBinder
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.google.android.material.snackbar.Snackbar
import com.clipsy.android.R
import com.clipsy.android.data.repository.ClipboardRepository
import com.clipsy.android.databinding.ActivityMainBinding
import com.clipsy.android.pairing.SimpleConnectionManager
import com.clipsy.android.service.ClipboardSyncService
import com.clipsy.android.ui.fragments.DevicesFragment
import com.clipsy.android.ui.fragments.HistoryFragment
import com.clipsy.android.ui.fragments.StatusFragment
import com.clipsy.android.viewmodel.MainViewModel
/**
 * Main activity for Clipsy Android app.
 * Hosts the main navigation and fragments.
 */
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var viewModel: MainViewModel
    private lateinit var connectionManager: SimpleConnectionManager

    // Service binding
    private var clipboardService: ClipboardSyncService? = null
    private var serviceBound = false

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {
            val binder = service as ClipboardSyncService.LocalBinder
            clipboardService = binder.getService()
            serviceBound = true
            clipboardService?.setConnectionManager(connectionManager)
        }

        override fun onServiceDisconnected(className: ComponentName) {
            clipboardService = null
            serviceBound = false
        }
    }

    // Permission request launcher
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        try {
            if (isGranted) {
                startClipboardService()
            } else {
                showPermissionDeniedMessage()
                startClipboardService()
            }
        } catch (e: Exception) {
            // Try to start service anyway
            startClipboardService()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityMainBinding.inflate(layoutInflater)
            setContentView(binding.root)

            // Setup toolbar
            setSupportActionBar(binding.toolbar)
            supportActionBar?.title = "Clipsy"

            // Initialize simple connection manager
            connectionManager = SimpleConnectionManager(this)

            // Initialize ViewModel with dependencies
            initializeViewModel()

            // Setup bottom navigation
            setupBottomNavigation()

            // Load default fragment
            if (savedInstanceState == null) {
                loadFragment(StatusFragment())
            }

            // Initialize networking safely after UI is ready
            binding.root.post {
                viewModel.initializeWithContext(this@MainActivity)
            }

            // Check permissions and start service (with delay to ensure UI is ready)
            binding.root.post {
                checkPermissionsAndStartService()
            }

            // Observe ViewModel
            observeViewModel()

        } catch (e: Exception) {
            // Try to continue with minimal functionality
            startClipboardService()
        }
    }

    private fun initializeViewModel() {
        val clipboardRepository = ClipboardRepository.getInstance()
        val factory = MainViewModelFactory(clipboardRepository, connectionManager)
        viewModel = ViewModelProvider(this, factory)[MainViewModel::class.java]
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_settings -> {
                startActivity(Intent(this, SettingsActivity::class.java))
                true
            }
            R.id.action_refresh -> {
                viewModel.refreshDevices()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun setupBottomNavigation() {
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.nav_status -> {
                    loadFragment(StatusFragment())
                    true
                }
                R.id.nav_devices -> {
                    loadFragment(DevicesFragment())
                    true
                }
                R.id.nav_history -> {
                    loadFragment(HistoryFragment())
                    true
                }
                else -> false
            }
        }

        // Setup manual sync FAB
        binding.fabManualSync.setOnClickListener {
            handleManualSync()
        }
    }
    
    private fun loadFragment(fragment: Fragment) {
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .commit()
    }
    
    private fun checkPermissionsAndStartService() {
        try {
            when {
                // Check notification permission (Android 13+)
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                    try {
                        when {
                            ContextCompat.checkSelfPermission(
                                this,
                                Manifest.permission.POST_NOTIFICATIONS
                            ) == PackageManager.PERMISSION_GRANTED -> {
                                startClipboardService()
                            }
                            shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS) -> {
                                showPermissionRationale()
                            }
                            else -> {
                                requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                            }
                        }
                    } catch (e: Exception) {
                        // Start service anyway on older Android or if permission check fails
                        startClipboardService()
                    }
                }
                else -> {
                    startClipboardService()
                }
            }
        } catch (e: Exception) {
            // Fallback: try to start service anyway
            startClipboardService()
        }
    }
    
    private fun startClipboardService() {
        try {
            checkBackgroundPermissions()

            val serviceIntent = Intent(this, ClipboardSyncService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent)
            } else {
                startService(serviceIntent)
            }

            bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE)
            viewModel.setServiceRunning(true)

        } catch (e: Exception) {
            // Don't crash the app, just continue
        }
    }

    /**
     * Check if battery optimization is needed (but don't automatically open settings).
     */
    private fun isBatteryOptimizationNeeded(): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = getSystemService(POWER_SERVICE) as PowerManager
            return !powerManager.isIgnoringBatteryOptimizations(packageName)
        }
        return false
    }

    /**
     * Request battery optimization bypass (call this only when user explicitly requests it).
     */
    fun requestBatteryOptimizationBypass() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = getSystemService(POWER_SERVICE) as PowerManager
            if (!powerManager.isIgnoringBatteryOptimizations(packageName)) {
                try {
                    val intent = Intent().apply {
                        action = android.provider.Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
                        data = android.net.Uri.parse("package:$packageName")
                    }
                    startActivity(intent)
                } catch (e: Exception) {
                    // If the specific intent fails, open general battery optimization settings
                    try {
                        val intent = Intent(android.provider.Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
                        startActivity(intent)
                    } catch (e2: Exception) {
                        // Ignore if both fail
                    }
                }
            }
        }
    }

    /**
     * Request auto-start permission (call this only when user explicitly requests it).
     */
    fun requestAutoStartPermission() {
        try {
            // OnePlus auto-start permission
            val manufacturer = Build.MANUFACTURER.lowercase()
            when {
                manufacturer.contains("oneplus") -> {
                    try {
                        val intent = Intent().apply {
                            component = android.content.ComponentName(
                                "com.oneplus.security",
                                "com.oneplus.security.chainlaunch.view.ChainLaunchAppListActivity"
                            )
                        }
                        startActivity(intent)
                    } catch (e: Exception) {
                        openGeneralAutoStartSettings()
                    }
                }
                manufacturer.contains("xiaomi") || manufacturer.contains("redmi") -> {
                    try {
                        val intent = Intent().apply {
                            component = android.content.ComponentName(
                                "com.miui.securitycenter",
                                "com.miui.permcenter.autostart.AutoStartManagementActivity"
                            )
                        }
                        startActivity(intent)
                    } catch (e: Exception) {
                        openGeneralAutoStartSettings()
                    }
                }
                manufacturer.contains("huawei") -> {
                    try {
                        val intent = Intent().apply {
                            component = android.content.ComponentName(
                                "com.huawei.systemmanager",
                                "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity"
                            )
                        }
                        startActivity(intent)
                    } catch (e: Exception) {
                        openGeneralAutoStartSettings()
                    }
                }
                else -> {
                    // For other manufacturers, try general settings
                    openGeneralAutoStartSettings()
                }
            }
        } catch (e: Exception) {
            // Ignore if auto-start settings cannot be opened
        }
    }

    private fun openGeneralAutoStartSettings() {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:$packageName")
            }
            startActivity(intent)
        } catch (e: Exception) {
            // Ignore if app settings cannot be opened
        }
    }

    private fun showPermissionRationale() {
        Snackbar.make(
            binding.root,
            "Notification permission is required for clipboard sync notifications",
            Snackbar.LENGTH_LONG
        ).setAction("Grant") {
            requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
        }.show()
    }
    
    private fun showPermissionDeniedMessage() {
        Snackbar.make(
            binding.root,
            "Notification permission denied. Some features may not work properly.",
            Snackbar.LENGTH_LONG
        ).show()
    }
    
    private fun observeViewModel() {
        viewModel.connectionStatus.observe(this) { status ->
            // Update UI based on connection status
            updateConnectionStatus(status)
        }
        
        viewModel.errorMessage.observe(this) { message ->
            if (message.isNotEmpty()) {
                Snackbar.make(binding.root, message, Snackbar.LENGTH_LONG).show()
                viewModel.clearErrorMessage()
            }
        }
        
        viewModel.isServiceRunning.observe(this) { isRunning ->
            // Update UI based on service status
            updateServiceStatus(isRunning)
        }
    }
    
    private fun updateConnectionStatus(status: String) {
        // This will be handled by individual fragments
        // through shared ViewModel
    }
    
    private fun updateServiceStatus(isRunning: Boolean) {
        // Update toolbar subtitle or status indicator
        supportActionBar?.subtitle = if (isRunning) {
            "Service Running"
        } else {
            "Service Stopped"
        }
    }

    /**
     * Check if background permissions are recommended and automatically request them.
     */
    private fun checkBackgroundPermissions() {
        // Check if battery optimization bypass is needed and request it
        if (isBatteryOptimizationNeeded()) {
            Log.d("MainActivity", "Battery optimization is enabled - requesting bypass for background sync")
            requestBatteryOptimizationBypass()
        }

        // Check if auto-start permission might be needed (for OnePlus devices)
        val manufacturer = Build.MANUFACTURER.lowercase()
        if (manufacturer.contains("oneplus")) {
            Log.d("MainActivity", "OnePlus device detected - may need auto-start permission")
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // Unbind from service
        if (serviceBound) {
            unbindService(serviceConnection)
            serviceBound = false
        }

        // Note: We don't stop the service here as it should continue running
        // in the background for clipboard sync
    }

    /**
     * Handle manual sync button press.
     */
    private fun handleManualSync() {
        try {
            startSyncAnimation()
            Toast.makeText(this, "Syncing clipboard to PC...", Toast.LENGTH_SHORT).show()

            val intent = Intent("com.clipsy.android.MANUAL_SYNC")
            intent.setPackage(packageName)
            sendBroadcast(intent)

            binding.fabManualSync.postDelayed({
                stopSyncAnimation()
            }, 2000)

        } catch (e: Exception) {
            Snackbar.make(binding.root, "Sync failed: ${e.message}", Snackbar.LENGTH_LONG).show()
            stopSyncAnimation()
        }
    }

    /**
     * Start sync button rotation animation.
     */
    private fun startSyncAnimation() {
        val rotateAnimation = android.view.animation.RotateAnimation(
            0f, 360f,
            android.view.animation.Animation.RELATIVE_TO_SELF, 0.5f,
            android.view.animation.Animation.RELATIVE_TO_SELF, 0.5f
        ).apply {
            duration = 1000
            repeatCount = android.view.animation.Animation.INFINITE
            interpolator = android.view.animation.LinearInterpolator()
        }
        binding.fabManualSync.startAnimation(rotateAnimation)
    }

    /**
     * Stop sync button rotation animation.
     */
    private fun stopSyncAnimation() {
        binding.fabManualSync.clearAnimation()
    }

    /**
     * Get the connection manager instance
     */
    fun getConnectionManager(): SimpleConnectionManager {
        return connectionManager
    }
}

/**
 * ViewModelFactory for creating MainViewModel with dependencies.
 */
class MainViewModelFactory(
    private val clipboardRepository: ClipboardRepository,
    private val connectionManager: SimpleConnectionManager
) : ViewModelProvider.Factory {
    override fun <T : androidx.lifecycle.ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(MainViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return MainViewModel(clipboardRepository, null, connectionManager) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
