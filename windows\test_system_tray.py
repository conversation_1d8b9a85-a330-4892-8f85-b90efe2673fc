#!/usr/bin/env python3
"""
Test script to verify system tray functionality in Clipsy.
This script demonstrates the system tray features without running the full application.
"""

import tkinter as tk
import threading
import time
import os
from pathlib import Path
import pystray
from PIL import Image

class SystemTrayTest:
    """Simple test application for system tray functionality."""
    
    def __init__(self):
        self.root = None
        self.tray_icon = None
        self.tray_thread = None
        self.is_minimized_to_tray = False
        
    def create_test_window(self):
        """Create a simple test window."""
        self.root = tk.Tk()
        self.root.title("Clipsy System Tray Test")
        self.root.geometry("400x300")
        
        # Main frame
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(
            main_frame, 
            text="Clipsy System Tray Test", 
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Instructions
        instructions = tk.Text(main_frame, height=8, wrap=tk.WORD)
        instructions.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        instructions.insert(tk.END, """System Tray Test Instructions:

1. Click 'Minimize to Tray' to hide window and show tray icon
2. Look for Clipsy icon in Windows system tray (bottom-right)
3. Right-click the tray icon to see context menu:
   - Open: Restore this window
   - Test Action: Shows a test message
   - Exit: Close the application
4. Double-click tray icon to restore window
5. Click 'Exit' to close application properly

This tests the same system tray functionality used in the main Clipsy application.""")
        
        instructions.config(state=tk.DISABLED)
        
        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        minimize_btn = tk.Button(
            button_frame,
            text="Minimize to Tray",
            command=self.minimize_to_tray,
            bg="#3498db",
            fg="white",
            font=("Arial", 10),
            padx=20,
            pady=5
        )
        minimize_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        exit_btn = tk.Button(
            button_frame,
            text="Exit",
            command=self.exit_application,
            bg="#e74c3c",
            fg="white",
            font=("Arial", 10),
            padx=20,
            pady=5
        )
        exit_btn.pack(side=tk.RIGHT)
        
        # Handle window events
        self.root.protocol("WM_DELETE_WINDOW", self.minimize_to_tray)
        
        # Setup system tray
        self.setup_system_tray()
        
    def setup_system_tray(self):
        """Setup system tray icon and menu."""
        try:
            # Load tray icon
            tray_image = self.load_tray_icon()
            if not tray_image:
                print("Could not load tray icon, using default")
                tray_image = Image.new('RGBA', (32, 32), (0, 120, 215, 255))
            
            # Create tray menu
            menu = pystray.Menu(
                pystray.MenuItem("Open", self.restore_window, default=True),
                pystray.MenuItem("Test Action", self.test_action),
                pystray.Menu.SEPARATOR,
                pystray.MenuItem("Exit", self.exit_application)
            )
            
            # Create tray icon
            self.tray_icon = pystray.Icon(
                "ClipsyTest",
                tray_image,
                "Clipsy System Tray Test",
                menu
            )
            
            print("System tray initialized successfully")
            
        except Exception as e:
            print(f"Failed to setup system tray: {e}")
            self.tray_icon = None
    
    def load_tray_icon(self):
        """Load the system tray icon image."""
        try:
            # Try to find icon files
            icon_paths = [
                "app_icon.ico",
                "../app_icon.ico",
                "icon_32x32.png",
                "../icon_32x32.png"
            ]
            
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    try:
                        image = Image.open(icon_path)
                        image = image.resize((32, 32), Image.Resampling.LANCZOS)
                        print(f"Loaded tray icon from: {icon_path}")
                        return image
                    except Exception as e:
                        print(f"Failed to load icon {icon_path}: {e}")
                        continue
            
            return None
            
        except Exception as e:
            print(f"Failed to load tray icon: {e}")
            return None
    
    def minimize_to_tray(self):
        """Minimize window to system tray."""
        try:
            if self.tray_icon:
                self.root.withdraw()  # Hide window
                self.is_minimized_to_tray = True
                
                # Start tray icon in a separate thread
                if not self.tray_thread or not self.tray_thread.is_alive():
                    self.tray_thread = threading.Thread(target=self.tray_icon.run, daemon=True)
                    self.tray_thread.start()
                
                print("Application minimized to system tray")
            else:
                print("System tray not available")
        except Exception as e:
            print(f"Failed to minimize to tray: {e}")
    
    def restore_window(self, icon=None, item=None):
        """Restore window from system tray."""
        try:
            if self.is_minimized_to_tray:
                self.root.deiconify()  # Show window
                self.root.lift()       # Bring to front
                self.root.focus_force()  # Give focus
                self.is_minimized_to_tray = False
                print("Application restored from system tray")
        except Exception as e:
            print(f"Failed to restore window: {e}")
    
    def test_action(self, icon=None, item=None):
        """Test action from tray menu."""
        print("Test action triggered from system tray!")
        # Show a simple message
        if not self.is_minimized_to_tray:
            tk.messagebox.showinfo("Test", "Test action triggered from system tray!")
    
    def exit_application(self, icon=None, item=None):
        """Exit application from tray menu."""
        try:
            print("Exit requested")
            self.cleanup_tray()
            if self.root:
                self.root.quit()
                self.root.destroy()
        except Exception as e:
            print(f"Failed to exit application: {e}")
    
    def cleanup_tray(self):
        """Cleanup system tray resources."""
        try:
            if self.tray_icon:
                self.tray_icon.stop()
                self.tray_icon = None
            if self.tray_thread and self.tray_thread.is_alive():
                self.tray_thread.join(timeout=1)
        except Exception as e:
            print(f"Failed to cleanup tray: {e}")
    
    def run(self):
        """Run the test application."""
        print("Starting Clipsy System Tray Test...")
        self.create_test_window()
        print("Test window created. Try minimizing to tray!")
        self.root.mainloop()
        print("Test application closed.")

if __name__ == "__main__":
    test_app = SystemTrayTest()
    test_app.run()
