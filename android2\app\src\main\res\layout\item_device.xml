<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardBackgroundColor="@color/clipsy_card_dark"
    app:cardCornerRadius="16dp"
    app:cardElevation="0dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Device Icon and Online Indicator -->
        <FrameLayout
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="16dp">

            <ImageView
                android:id="@+id/icon_device_type"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@android:drawable/ic_menu_myplaces"
                tools:ignore="ContentDescription" />

            <ImageView
                android:id="@+id/indicator_online"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_gravity="bottom|end"
                android:background="@drawable/circle_background"
                android:backgroundTint="@color/status_connected"
                tools:ignore="ContentDescription" />

        </FrameLayout>

        <!-- Device Information -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_device_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                android:textColor="@color/clipsy_text_primary"
                tools:text="Windows PC" />

            <TextView
                android:id="@+id/text_device_ip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="@color/clipsy_text_secondary"
                tools:text="*************" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/text_device_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="@color/clipsy_text_secondary"
                    tools:text="Windows" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="•"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="@color/clipsy_text_secondary" />

                <TextView
                    android:id="@+id/text_last_seen"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="@color/clipsy_text_secondary"
                    tools:text="2 minutes ago" />

            </LinearLayout>

            <TextView
                android:id="@+id/text_connection_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textStyle="bold"
                tools:text="Connected" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <Button
                android:id="@+id/button_connect"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:minWidth="80dp"
                android:text="@string/action_connect"
                android:textColor="@color/clipsy_primary"
                android:backgroundTint="@android:color/transparent"
                app:strokeColor="@color/clipsy_primary"
                app:strokeWidth="1dp"
                app:cornerRadius="16dp"
                android:textSize="12sp" />

            <Button
                android:id="@+id/button_pair"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:minWidth="80dp"
                android:text="@string/action_pair"
                android:textColor="@color/clipsy_accent"
                android:backgroundTint="@android:color/transparent"
                app:strokeColor="@color/clipsy_accent"
                app:strokeWidth="1dp"
                app:cornerRadius="16dp"
                android:textSize="12sp" />

            <Button
                android:id="@+id/button_unpair"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:minWidth="80dp"
                android:text="@string/action_unpair"
                android:textColor="@color/clipsy_error"
                android:textSize="12sp"
                android:visibility="gone" />

            <Button
                android:id="@+id/button_remove"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="80dp"
                android:text="@string/action_remove"
                android:textColor="@color/clipsy_error"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
