package com.clipsy.android.data.model

/**
 * Represents the connection status of a device.
 */
enum class ConnectionStatus {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    SYNCING,
    ERROR,
    TIMEOUT
}

/**
 * Data class for connection state with additional information.
 */
data class ConnectionState(
    val status: ConnectionStatus,
    val device: Device? = null,
    val errorMessage: String? = null,
    val timestamp: Long = System.currentTimeMillis()
) {
    
    /**
     * Check if the connection is active.
     */
    fun isConnected(): Boolean = status == ConnectionStatus.CONNECTED
    
    /**
     * Check if there's an error.
     */
    fun hasError(): Boolean = status == ConnectionStatus.ERROR
    
    /**
     * Get status display text.
     */
    fun getStatusText(): String {
        return when (status) {
            ConnectionStatus.DISCONNECTED -> "Disconnected"
            ConnectionStatus.CONNECTING -> "Connecting..."
            ConnectionStatus.CONNECTED -> "Connected"
            ConnectionStatus.SYNCING -> "Syncing..."
            ConnectionStatus.ERROR -> "Error"
            ConnectionStatus.TIMEOUT -> "Timeout"
        }
    }
    
    /**
     * Get status color resource name.
     */
    fun getStatusColor(): String {
        return when (status) {
            ConnectionStatus.CONNECTED -> "status_connected"
            ConnectionStatus.CONNECTING,
            ConnectionStatus.SYNCING -> "status_connecting"
            ConnectionStatus.DISCONNECTED,
            ConnectionStatus.ERROR,
            ConnectionStatus.TIMEOUT -> "status_disconnected"
        }
    }
}
