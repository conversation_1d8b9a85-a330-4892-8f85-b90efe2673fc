<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_clipboard_history" modulePackage="com.clipsy.android" filePath="app\src\main\res\layout\item_clipboard_history.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_clipboard_history_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="109" endOffset="51"/></Target><Target id="@+id/icon_content_type" view="ImageView"><Expressions/><location startLine="19" startOffset="8" endLine="27" endOffset="47"/></Target><Target id="@+id/text_content" view="TextView"><Expressions/><location startLine="37" startOffset="12" endLine="46" endOffset="116"/></Target><Target id="@+id/text_source" view="TextView"><Expressions/><location startLine="56" startOffset="16" endLine="67" endOffset="40"/></Target><Target id="@+id/text_timestamp" view="TextView"><Expressions/><location startLine="70" startOffset="16" endLine="77" endOffset="48"/></Target><Target id="@+id/text_device_info" view="TextView"><Expressions/><location startLine="82" startOffset="12" endLine="91" endOffset="44"/></Target><Target id="@+id/button_delete" view="ImageButton"><Expressions/><location startLine="96" startOffset="8" endLine="105" endOffset="33"/></Target></Targets></Layout>