package com.clipsy.android.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Data model for clipboard items.
 */
@Parcelize
data class ClipboardItem(
    val id: Long = System.currentTimeMillis(),
    
    val content: String,
    val timestamp: Long,
    val source: String, // "local" or "remote"
    val deviceName: String? = null,
    val deviceType: String? = null,
    val deviceId: String? = null,
    val contentHash: String? = null
) : Parcelable {
    
    companion object {
        const val SOURCE_LOCAL = "local"
        const val SOURCE_REMOTE = "remote"
    }
    
    /**
     * Get a preview of the content (first 100 characters).
     */
    fun getPreview(): String {
        return if (content.length > 100) {
            content.take(100) + "..."
        } else {
            content
        }
    }
    
    /**
     * Get formatted timestamp for display.
     */
    fun getFormattedTime(): String {
        val date = java.util.Date(timestamp)
        val format = java.text.SimpleDateFormat("MMM dd, HH:mm", java.util.Locale.getDefault())
        return format.format(date)
    }
    
    /**
     * Check if this item is from a remote device.
     */
    fun isFromRemote(): Boolean = source == SOURCE_REMOTE

    /**
     * Check if this item is from local device.
     */
    fun isLocal(): Boolean = source == SOURCE_LOCAL

    /**
     * Get formatted timestamp for display (alias for getFormattedTime).
     */
    fun getFormattedTimestamp(): String = getFormattedTime()

    /**
     * Get device info string for display.
     */
    fun getDeviceInfo(): String {
        return when {
            deviceName != null -> deviceName
            deviceId != null -> "Device $deviceId"
            else -> if (isFromRemote()) "Remote Device" else "Local"
        }
    }
}
