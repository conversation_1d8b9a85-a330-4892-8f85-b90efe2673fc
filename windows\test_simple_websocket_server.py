#!/usr/bin/env python3
"""
Simple WebSocket Server Test
Creates a minimal WebSocket server to test if the issue is with our implementation
"""

import asyncio
import websockets
import json
import logging
import sys
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def handle_client(websocket, path):
    """Simple WebSocket client handler"""
    client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
    logger.info(f"✅ Client connected: {client_address}")
    
    try:
        # Send welcome message
        welcome_msg = {
            "type": "welcome",
            "message": "Hello from simple test server!",
            "timestamp": datetime.now().isoformat()
        }
        logger.info(f"📤 Sending welcome: {welcome_msg}")
        await websocket.send(json.dumps(welcome_msg))
        
        # Echo messages back
        async for message in websocket:
            logger.info(f"📥 Received: {message}")
            
            try:
                data = json.loads(message)
                response = {
                    "type": "echo",
                    "original": data,
                    "timestamp": datetime.now().isoformat()
                }
                logger.info(f"📤 Echoing back: {response}")
                await websocket.send(json.dumps(response))
            except json.JSONDecodeError:
                logger.error(f"❌ Invalid JSON: {message}")
                
    except websockets.exceptions.ConnectionClosed:
        logger.info(f"Client disconnected: {client_address}")
    except Exception as e:
        logger.error(f"❌ Error handling client {client_address}: {e}")

async def start_test_server():
    """Start the test WebSocket server"""
    host = "*************"
    port = 8767  # Different port to avoid conflict
    
    logger.info(f"🚀 Starting test WebSocket server on {host}:{port}")
    
    try:
        server = await websockets.serve(
            handle_client,
            host,
            port,
            ping_interval=20,
            ping_timeout=10
        )
        
        logger.info(f"✅ Test server listening on {host}:{port}")
        logger.info("Server is ready to accept connections...")
        
        # Keep the server running
        await server.wait_closed()
        
    except Exception as e:
        logger.error(f"❌ Failed to start test server: {e}")
        raise

async def main():
    """Main function"""
    try:
        await start_test_server()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
