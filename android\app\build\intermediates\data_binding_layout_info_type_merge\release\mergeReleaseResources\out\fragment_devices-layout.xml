<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_devices" modulePackage="com.clipsy.android" filePath="app\src\main\res\layout\fragment_devices.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_devices_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="241" endOffset="53"/></Target><Target id="@+id/progress_scanning" view="ProgressBar"><Expressions/><location startLine="9" startOffset="4" endLine="17" endOffset="35"/></Target><Target id="@+id/swipe_refresh_layout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="19" startOffset="4" endLine="228" endOffset="59"/></Target><Target id="@+id/switch_sync" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="63" startOffset="24" endLine="71" endOffset="71"/></Target><Target id="@+id/card_device_name" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="78" startOffset="16" endLine="130" endOffset="67"/></Target><Target id="@+id/text_current_device_name" view="TextView"><Expressions/><location startLine="110" startOffset="28" endLine="117" endOffset="57"/></Target><Target id="@+id/text_description" view="TextView"><Expressions/><location startLine="133" startOffset="16" endLine="141" endOffset="52"/></Target><Target id="@+id/header_connected_devices" view="TextView"><Expressions/><location startLine="144" startOffset="16" endLine="153" endOffset="47"/></Target><Target id="@+id/recycler_view_connected_devices" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="155" startOffset="16" endLine="161" endOffset="74"/></Target><Target id="@+id/header_available_devices" view="LinearLayout"><Expressions/><location startLine="164" startOffset="16" endLine="191" endOffset="30"/></Target><Target id="@+id/button_refresh" view="ImageButton"><Expressions/><location startLine="181" startOffset="20" endLine="189" endOffset="47"/></Target><Target id="@+id/recycler_view_available_devices" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="193" startOffset="16" endLine="198" endOffset="74"/></Target><Target id="@+id/card_no_available_devices" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="201" startOffset="16" endLine="222" endOffset="67"/></Target><Target id="@+id/fab_add_device" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="230" startOffset="4" endLine="239" endOffset="41"/></Target></Targets></Layout>