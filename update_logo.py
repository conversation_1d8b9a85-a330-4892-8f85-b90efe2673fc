#!/usr/bin/env python3
"""
<PERSON>ript to update <PERSON><PERSON><PERSON> with your custom high-quality logo
"""

import os
import sys
from pathlib import Path

def main():
    print("🎨 Clipsy Logo Update Tool")
    print("=" * 40)
    
    # Check if logo_original.png exists
    logo_path = Path("logo_original.png")
    if not logo_path.exists():
        print("❌ Error: logo_original.png not found!")
        print("\n📝 Instructions:")
        print("1. Save your high-quality logo image as 'logo_original.png' in this directory")
        print("2. Run this script again")
        print("\n💡 Your logo should be:")
        print("   - High resolution (at least 512x512 pixels)")
        print("   - PNG format")
        print("   - Square aspect ratio works best")
        return False
    
    print(f"✅ Found logo: {logo_path}")
    
    # Check if resize_logo.py exists
    resize_script = Path("resize_logo.py")
    if not resize_script.exists():
        print("❌ Error: resize_logo.py not found!")
        return False
    
    print("🔄 Generating icons from your logo...")
    
    # Run the resize script
    import subprocess
    try:
        result = subprocess.run([
            sys.executable, "resize_logo.py", "logo_original.png", "--all"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Icons generated successfully!")
            print(result.stdout)
        else:
            print("❌ Error generating icons:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error running resize script: {e}")
        return False
    
    print("\n🔨 Rebuilding Windows application...")
    
    # Rebuild Windows app
    try:
        os.chdir("windows/src")
        result = subprocess.run([
            "pyinstaller", "clipsy.spec"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Windows app rebuilt successfully!")
        else:
            print("❌ Error rebuilding Windows app:")
            print(result.stderr)
            os.chdir("../..")
            return False
            
        os.chdir("../..")
        
    except Exception as e:
        print(f"❌ Error rebuilding Windows app: {e}")
        return False
    
    # Copy the new executable
    try:
        import shutil
        src = Path("windows/src/dist/Clipsy.exe")
        dst = Path("ClipsyWithYourLogo.exe")
        
        if src.exists():
            shutil.copy2(src, dst)
            print(f"✅ Updated app saved as: {dst}")
        else:
            print("❌ Error: Built executable not found")
            return False
            
    except Exception as e:
        print(f"❌ Error copying executable: {e}")
        return False
    
    print("\n🎉 Logo update complete!")
    print("\n📱 Next steps:")
    print("1. Run 'ClipsyWithYourLogo.exe' to see your custom logo in the window")
    print("2. For Android: Rebuild and install the Android app")
    print("   - cd android")
    print("   - ./gradlew assembleDebug")
    print("   - adb install -r app/build/outputs/apk/debug/app-debug.apk")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nPress Enter to exit...")
