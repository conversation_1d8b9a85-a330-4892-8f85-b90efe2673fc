<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_background">

    <TextView
        android:id="@+id/tv_pairing_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Pairing with Devi<PERSON>"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tv_pairing_instruction"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Enter this code on the other device:"
        android:textSize="14sp"
        android:textColor="@color/text_secondary"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@drawable/code_background"
        android:padding="16dp"
        android:layout_marginBottom="24dp">

        <TextView
            android:id="@+id/tv_pairing_code"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00"
            android:textSize="48sp"
            android:textStyle="bold"
            android:textColor="@color/primary_color"
            android:fontFamily="monospace" />

    </LinearLayout>

    <Button
        android:id="@+id/btn_cancel_pairing"
        style="@style/Button.Clipsy.Secondary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Cancel"
        android:layout_marginTop="8dp" />

</LinearLayout>
