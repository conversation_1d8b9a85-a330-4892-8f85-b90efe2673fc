(['N:\\new project\\windows\\src\\main.py'],
 ['N:\\new project\\windows\\src'],
 [],
 [('C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('config\\device_id.txt',
   'N:\\new project\\windows\\src\\config\\device_id.txt',
   'DATA'),
  ('config\\known_devices.json',
   'N:\\new project\\windows\\src\\config\\known_devices.json',
   'DATA')],
 '3.12.7 | packaged by Anaconda, Inc. | (main, Oct  4 2024, 13:17:27) [MSC '
 'v.1929 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'N:\\new project\\windows\\src\\main.py', 'PYSOURCE')],
 [('pkg_resources',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('subprocess', 'C:\\Users\\<USER>\\miniconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'C:\\Users\\<USER>\\miniconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('contextlib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('signal', 'C:\\Users\\<USER>\\miniconda3\\Lib\\signal.py', 'PYMODULE'),
  ('struct', 'C:\\Users\\<USER>\\miniconda3\\Lib\\struct.py', 'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email', 'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Users\\<USER>\\miniconda3\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Users\\<USER>\\miniconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Users\\<USER>\\miniconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('copy', 'C:\\Users\\<USER>\\miniconda3\\Lib\\copy.py', 'PYMODULE'),
  ('string', 'C:\\Users\\<USER>\\miniconda3\\Lib\\string.py', 'PYMODULE'),
  ('urllib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri', 'C:\\Users\\<USER>\\miniconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.utils', 'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'C:\\Users\\<USER>\\miniconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'C:\\Users\\<USER>\\miniconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('shutil', 'C:\\Users\\<USER>\\miniconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Users\\<USER>\\miniconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_compression.py',
   'PYMODULE'),
  ('lzma', 'C:\\Users\\<USER>\\miniconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Users\\<USER>\\miniconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Users\\<USER>\\miniconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('datetime', 'C:\\Users\\<USER>\\miniconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Users\\<USER>\\miniconda3\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\Users\\<USER>\\miniconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'C:\\Users\\<USER>\\miniconda3\\Lib\\socket.py', 'PYMODULE'),
  ('random', 'C:\\Users\\<USER>\\miniconda3\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\Users\\<USER>\\miniconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\Users\\<USER>\\miniconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Users\\<USER>\\miniconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Users\\<USER>\\miniconda3\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'C:\\Users\\<USER>\\miniconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Users\\<USER>\\miniconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'C:\\Users\\<USER>\\miniconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('dataclasses', 'C:\\Users\\<USER>\\miniconda3\\Lib\\dataclasses.py', 'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ast.py', 'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('sysconfig', 'C:\\Users\\<USER>\\miniconda3\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('pprint', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('platformdirs',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\configparser.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('platformdirs.version',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.api',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('queue', 'C:\\Users\\<USER>\\miniconda3\\Lib\\queue.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'C:\\Users\\<USER>\\miniconda3\\Lib\\runpy.py', 'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('csv', 'C:\\Users\\<USER>\\miniconda3\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('tokenize', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'C:\\Users\\<USER>\\miniconda3\\Lib\\token.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\Users\\<USER>\\miniconda3\\Lib\\secrets.py', 'PYMODULE'),
  ('hmac', 'C:\\Users\\<USER>\\miniconda3\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('pickle', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\Users\\<USER>\\miniconda3\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'C:\\Users\\<USER>\\miniconda3\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Users\\<USER>\\miniconda3\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Users\\<USER>\\miniconda3\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Users\\<USER>\\miniconda3\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'C:\\Users\\<USER>\\miniconda3\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'C:\\Users\\<USER>\\miniconda3\\Lib\\http\\client.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('py_compile', 'C:\\Users\\<USER>\\miniconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('setuptools._distutils.compat.py38',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('site', 'C:\\Users\\<USER>\\miniconda3\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter', 'C:\\Users\\<USER>\\miniconda3\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Users\\<USER>\\miniconda3\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'C:\\Users\\<USER>\\miniconda3\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'C:\\Users\\<USER>\\miniconda3\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\socketserver.py',
   'PYMODULE'),
  ('html', 'C:\\Users\\<USER>\\miniconda3\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel._setuptools_logging',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('wheel.util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.cli',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel._bdist_wheel',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\_bdist_wheel.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.metadata',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tomllib._re', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('glob', 'C:\\Users\\<USER>\\miniconda3\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('dis', 'C:\\Users\\<USER>\\miniconda3\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Users\\<USER>\\miniconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py38',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.functional',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\functional.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future.adapters',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py39',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('typing', 'C:\\Users\\<USER>\\miniconda3\\Lib\\typing.py', 'PYMODULE'),
  ('zipimport', 'C:\\Users\\<USER>\\miniconda3\\Lib\\zipimport.py', 'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('textwrap', 'C:\\Users\\<USER>\\miniconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('tempfile', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('plistlib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\plistlib.py', 'PYMODULE'),
  ('platform', 'C:\\Users\\<USER>\\miniconda3\\Lib\\platform.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pkgutil.py', 'PYMODULE'),
  ('inspect', 'C:\\Users\\<USER>\\miniconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('__future__', 'C:\\Users\\<USER>\\miniconda3\\Lib\\__future__.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Users\\<USER>\\miniconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'C:\\Users\\<USER>\\miniconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('pairing.pairing_manager',
   'N:\\new project\\windows\\src\\pairing\\pairing_manager.py',
   'PYMODULE'),
  ('pairing',
   'N:\\new project\\windows\\src\\pairing\\__init__.py',
   'PYMODULE'),
  ('uuid', 'C:\\Users\\<USER>\\miniconda3\\Lib\\uuid.py', 'PYMODULE'),
  ('network.sync_client',
   'N:\\new project\\windows\\src\\network\\sync_client.py',
   'PYMODULE'),
  ('network',
   'N:\\new project\\windows\\src\\network\\__init__.py',
   'PYMODULE'),
  ('websockets',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.uri',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.streams',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.protocol',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.compatibility',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\compatibility.py',
   'PYMODULE'),
  ('websockets.legacy.async_timeout',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\async_timeout.py',
   'PYMODULE'),
  ('websockets.legacy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.http',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.headers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.connection',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.auth',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.__main__',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.typing',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.server',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.client',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.http11',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.extensions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.frames',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.version',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('websockets.imports',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('network.sync_server',
   'N:\\new project\\windows\\src\\network\\sync_server.py',
   'PYMODULE'),
  ('network.discovery',
   'N:\\new project\\windows\\src\\network\\discovery.py',
   'PYMODULE'),
  ('clipboard.monitor',
   'N:\\new project\\windows\\src\\clipboard\\monitor.py',
   'PYMODULE'),
  ('clipboard',
   'N:\\new project\\windows\\src\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyperclip',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('ui.main_window',
   'N:\\new project\\windows\\src\\ui\\main_window.py',
   'PYMODULE'),
  ('ui', 'N:\\new project\\windows\\src\\ui\\__init__.py', 'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('ui.simple_device_manager',
   'N:\\new project\\windows\\src\\ui\\simple_device_manager.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Users\\<USER>\\miniconda3\\Lib\\colorsys.py', 'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('fileinput', 'C:\\Users\\<USER>\\miniconda3\\Lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._compat',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('doctest', 'C:\\Users\\<USER>\\miniconda3\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pdb.py', 'PYMODULE'),
  ('code', 'C:\\Users\\<USER>\\miniconda3\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Users\\<USER>\\miniconda3\\Lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'C:\\Users\\<USER>\\miniconda3\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'C:\\Users\\<USER>\\miniconda3\\Lib\\cmd.py', 'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('ctypes.util', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('pystray',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\__init__.py',
   'PYMODULE'),
  ('pystray._util.win32',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\_util\\win32.py',
   'PYMODULE'),
  ('pystray._util.notify_dbus',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\_util\\notify_dbus.py',
   'PYMODULE'),
  ('pystray._util.gtk',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\_util\\gtk.py',
   'PYMODULE'),
  ('pystray._util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\_util\\__init__.py',
   'PYMODULE'),
  ('pystray._info',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\_info.py',
   'PYMODULE'),
  ('pystray._base',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\_base.py',
   'PYMODULE'),
  ('six', 'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('pystray._xorg',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\_xorg.py',
   'PYMODULE'),
  ('pystray._win32',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\_win32.py',
   'PYMODULE'),
  ('pystray._gtk',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\_gtk.py',
   'PYMODULE'),
  ('pystray._darwin',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\_darwin.py',
   'PYMODULE'),
  ('pystray._appindicator',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\_appindicator.py',
   'PYMODULE'),
  ('pystray._dummy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pystray\\_dummy.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('threading', 'C:\\Users\\<USER>\\miniconda3\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('json', 'C:\\Users\\<USER>\\miniconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\json\\scanner.py',
   'PYMODULE')],
 [('python312.dll', 'C:\\Users\\<USER>\\miniconda3\\python312.dll', 'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('select.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\miniconda3\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\miniconda3\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('websockets\\speedups.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets\\speedups.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('netifaces.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\netifaces.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\miniconda3\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Users\\<USER>\\miniconda3\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'C:\\Users\\<USER>\\miniconda3\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'C:\\Users\\<USER>\\miniconda3\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\miniconda3\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('ffi.dll', 'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\ffi.dll', 'BINARY'),
  ('libexpat.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\libexpat.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Users\\<USER>\\miniconda3\\python3.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes312.dll',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\pywin32_system32\\pywintypes312.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\tcl86t.dll',
   'BINARY'),
  ('tk86t.dll', 'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\tk86t.dll', 'BINARY'),
  ('ucrtbase.dll', 'C:\\Users\\<USER>\\miniconda3\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-sysinfo-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-1.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-fibers-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('config\\device_id.txt',
   'N:\\new project\\windows\\src\\config\\device_id.txt',
   'DATA'),
  ('config\\known_devices.json',
   'N:\\new project\\windows\\src\\config\\known_devices.json',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tk_data\\console.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tk_data\\tclIndex',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:/Users/<USER>/miniconda3/Library/lib\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:/Users/<USER>/miniconda3/Library/lib\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:/Users/<USER>/miniconda3/Library/lib\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\README',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tk_data\\button.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.7.tm',
   'C:/Users/<USER>/miniconda3/Library/lib\\tcl8\\8.5\\tcltest-2.5.7.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:/Users/<USER>/miniconda3/Library/lib\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tk_data\\text.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:/Users/<USER>/miniconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('numpy-2.3.1.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy-2.3.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('websockets-12.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets-12.0.dist-info\\REQUESTED',
   'DATA'),
  ('websockets-12.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets-12.0.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.3.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy-2.3.1.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-12.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets-12.0.dist-info\\top_level.txt',
   'DATA'),
  ('websockets-12.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets-12.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-12.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets-12.0.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.44.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel-0.44.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.3.1.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy-2.3.1.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.3.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy-2.3.1.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.44.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel-0.44.0.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.44.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel-0.44.0.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.44.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel-0.44.0.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy-2.3.1.dist-info\\METADATA',
   'DATA'),
  ('websockets-12.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets-12.0.dist-info\\METADATA',
   'DATA'),
  ('websockets-12.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\websockets-12.0.dist-info\\LICENSE',
   'DATA'),
  ('numpy-2.3.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy-2.3.1.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.44.0.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\wheel-0.44.0.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.3.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\numpy-2.3.1.dist-info\\entry_points.txt',
   'DATA'),
  ('base_library.zip',
   'N:\\new project\\windows\\src\\build\\ClipsyStartupFixed\\base_library.zip',
   'DATA')],
 [('locale', 'C:\\Users\\<USER>\\miniconda3\\Lib\\locale.py', 'PYMODULE'),
  ('stat', 'C:\\Users\\<USER>\\miniconda3\\Lib\\stat.py', 'PYMODULE'),
  ('io', 'C:\\Users\\<USER>\\miniconda3\\Lib\\io.py', 'PYMODULE'),
  ('copyreg', 'C:\\Users\\<USER>\\miniconda3\\Lib\\copyreg.py', 'PYMODULE'),
  ('sre_parse', 'C:\\Users\\<USER>\\miniconda3\\Lib\\sre_parse.py', 'PYMODULE'),
  ('types', 'C:\\Users\\<USER>\\miniconda3\\Lib\\types.py', 'PYMODULE'),
  ('collections.abc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('genericpath', 'C:\\Users\\<USER>\\miniconda3\\Lib\\genericpath.py', 'PYMODULE'),
  ('_collections_abc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('_weakrefset', 'C:\\Users\\<USER>\\miniconda3\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('weakref', 'C:\\Users\\<USER>\\miniconda3\\Lib\\weakref.py', 'PYMODULE'),
  ('ntpath', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ntpath.py', 'PYMODULE'),
  ('abc', 'C:\\Users\\<USER>\\miniconda3\\Lib\\abc.py', 'PYMODULE'),
  ('functools', 'C:\\Users\\<USER>\\miniconda3\\Lib\\functools.py', 'PYMODULE'),
  ('posixpath', 'C:\\Users\\<USER>\\miniconda3\\Lib\\posixpath.py', 'PYMODULE'),
  ('sre_constants',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('operator', 'C:\\Users\\<USER>\\miniconda3\\Lib\\operator.py', 'PYMODULE'),
  ('reprlib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\reprlib.py', 'PYMODULE'),
  ('keyword', 'C:\\Users\\<USER>\\miniconda3\\Lib\\keyword.py', 'PYMODULE'),
  ('heapq', 'C:\\Users\\<USER>\\miniconda3\\Lib\\heapq.py', 'PYMODULE'),
  ('re._parser', 'C:\\Users\\<USER>\\miniconda3\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix', 'C:\\Users\\<USER>\\miniconda3\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'C:\\Users\\<USER>\\miniconda3\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('sre_compile', 'C:\\Users\\<USER>\\miniconda3\\Lib\\sre_compile.py', 'PYMODULE'),
  ('linecache', 'C:\\Users\\<USER>\\miniconda3\\Lib\\linecache.py', 'PYMODULE'),
  ('enum', 'C:\\Users\\<USER>\\miniconda3\\Lib\\enum.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('traceback', 'C:\\Users\\<USER>\\miniconda3\\Lib\\traceback.py', 'PYMODULE'),
  ('codecs', 'C:\\Users\\<USER>\\miniconda3\\Lib\\codecs.py', 'PYMODULE'),
  ('warnings', 'C:\\Users\\<USER>\\miniconda3\\Lib\\warnings.py', 'PYMODULE'),
  ('os', 'C:\\Users\\<USER>\\miniconda3\\Lib\\os.py', 'PYMODULE')])
