<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Disabled state -->
    <item android:state_enabled="false" android:color="@color/button_text_disabled" />
    <!-- Pressed state -->
    <item android:state_pressed="true" android:color="@color/clipsy_accent" />
    <!-- Default state -->
    <item android:color="@color/button_text_secondary" />
</selector>
