{"logs": [{"outputFile": "com.clipsy.android.app-mergeDebugResources-55:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\724090e78494501a803eec31dba02c36\\transformed\\material-1.10.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1204,1298,1374,1437,1549,1609,1674,1728,1798,1858,1914,2026,2083,2145,2201,2274,2408,2493,2578,2721,2805,2888,3022,3111,3188,3244,3299,3365,3438,3515,3599,3678,3752,3828,3903,3976,4081,4169,4242,4332,4423,4495,4569,4660,4712,4794,4861,4945,5032,5094,5158,5221,5290,5393,5501,5599,5703,5763,5822", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,88,88,87,97,90,105,125,83,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,84,142,83,82,133,88,76,55,54,65,72,76,83,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1199,1293,1369,1432,1544,1604,1669,1723,1793,1853,1909,2021,2078,2140,2196,2269,2403,2488,2573,2716,2800,2883,3017,3106,3183,3239,3294,3360,3433,3510,3594,3673,3747,3823,3898,3971,4076,4164,4237,4327,4418,4490,4564,4655,4707,4789,4856,4940,5027,5089,5153,5216,5285,5388,5496,5594,5698,5758,5817,5894"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,51,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3157,3246,3335,3423,3521,4335,4441,4567,4722,4874,4968,5044,5107,5219,5279,5344,5398,5468,5528,5584,5696,5753,5815,5871,5944,6078,6163,6248,6391,6475,6558,6692,6781,6858,6914,6969,7035,7108,7185,7269,7348,7422,7498,7573,7646,7751,7839,7912,8002,8093,8165,8239,8330,8382,8464,8531,8615,8702,8764,8828,8891,8960,9063,9171,9269,9373,9433,9802", "endLines": "7,35,36,37,38,39,47,48,49,51,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,114", "endColumns": "12,88,88,87,97,90,105,125,83,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,84,142,83,82,133,88,76,55,54,65,72,76,83,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76", "endOffsets": "412,3241,3330,3418,3516,3607,4436,4562,4646,4783,4963,5039,5102,5214,5274,5339,5393,5463,5523,5579,5691,5748,5810,5866,5939,6073,6158,6243,6386,6470,6553,6687,6776,6853,6909,6964,7030,7103,7180,7264,7343,7417,7493,7568,7641,7746,7834,7907,7997,8088,8160,8234,8325,8377,8459,8526,8610,8697,8759,8823,8886,8955,9058,9166,9264,9368,9428,9487,9874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\345ccf188a6f49c4c91ca3120313e197\\transformed\\core-1.12.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "40,41,42,43,44,45,46,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,3709,3811,3909,4013,4116,4218,10104", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3704,3806,3904,4008,4111,4213,4330,10200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\18d5a599756ea47edbaa6b32ae2eb3d5\\transformed\\navigation-ui-2.7.5\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,120", "endOffsets": "158,279"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9492,9600", "endColumns": "107,120", "endOffsets": "9595,9716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\60faa6f29802d848c65e1c1909fcb4a3\\transformed\\appcompat-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "417,529,631,739,826,929,1048,1129,1207,1299,1393,1488,1582,1677,1771,1867,1967,2059,2151,2235,2343,2451,2551,2664,2772,2877,3057,10020", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "524,626,734,821,924,1043,1124,1202,1294,1388,1483,1577,1672,1766,1862,1962,2054,2146,2230,2338,2446,2546,2659,2767,2872,3052,3152,10099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f959e0cc303079f1bb4b6f18f6d78dd7\\transformed\\preference-1.2.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,343,484,653,741", "endColumns": "70,85,80,140,168,87,83", "endOffsets": "171,257,338,479,648,736,820"}, "to": {"startLines": "50,52,113,115,118,119,120", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4651,4788,9721,9879,10205,10374,10462", "endColumns": "70,85,80,140,168,87,83", "endOffsets": "4717,4869,9797,10015,10369,10457,10541"}}]}]}