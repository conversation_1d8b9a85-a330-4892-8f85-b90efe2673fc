<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/clipsy_background"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Service Status Card - Green -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_service_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/clipsy_green_bright"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp"
                android:gravity="center_vertical">

                <View
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/circle_indicator"
                    android:backgroundTint="@color/white" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Service Status"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="normal" />

                    <TextView
                        android:id="@+id/text_service_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Service Running"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:layout_marginTop="2dp"
                        android:alpha="0.9" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>



        <!-- Connection Status Card - Dark -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/clipsy_card_dark"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Connection Status"
                    android:textColor="@color/clipsy_text_primary"
                    android:textSize="16sp"
                    android:textStyle="normal" />

                <TextView
                    android:id="@+id/text_connection_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Network detected"
                    android:textColor="@color/clipsy_text_secondary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/text_connected_devices"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="0 device(s) connected"
                    android:textColor="@color/clipsy_text_secondary"
                    android:textSize="14sp" />



            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Network Information Card - Dark -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_network_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/clipsy_card_dark"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Network Information"
                    android:textColor="@color/clipsy_text_primary"
                    android:textSize="16sp"
                    android:textStyle="normal" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Local IP Address:"
                        android:textColor="@color/clipsy_text_secondary"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/text_local_ip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="*************"
                        android:textColor="@color/clipsy_text_primary"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Device Discovery Card - Dark -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/clipsy_card_dark"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Device Discovery"
                    android:textColor="@color/clipsy_text_primary"
                    android:textSize="16sp"
                    android:textStyle="normal" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:id="@+id/text_discovery_status"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Discovery idle"
                        android:textColor="@color/clipsy_text_secondary"
                        android:textSize="14sp" />

                    <ProgressBar
                        android:id="@+id/progress_discovery"
                        style="?android:attr/progressBarStyleSmall"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone" />

                </LinearLayout>

                <TextView
                    android:id="@+id/text_discovered_devices"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="0 device(s) found"
                    android:textColor="@color/clipsy_text_secondary"
                    android:textSize="14sp" />

                <Button
                    android:id="@+id/button_refresh_devices"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Refresh"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="6dp"
                    android:backgroundTint="@android:color/transparent"
                    android:drawableStart="@android:drawable/ic_menu_search"
                    android:drawablePadding="8dp"
                    android:drawableTint="@color/white"
                    app:strokeColor="@color/white"
                    app:strokeWidth="1dp"
                    app:cornerRadius="16dp"
                    android:foreground="?android:attr/selectableItemBackground" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>




    </LinearLayout>

</ScrollView>
