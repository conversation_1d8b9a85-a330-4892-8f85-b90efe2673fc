#!/usr/bin/env python3
"""
Test if Windows PC is accessible from other devices (firewall test)
"""

import socket
import subprocess
import sys

def test_port_accessibility():
    """Test if port 8766 is accessible from external devices."""
    print("Windows Firewall and Network Accessibility Test")
    print("=" * 50)
    
    # Test local access first
    print("1. Testing local access (127.0.0.1:8766)...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(("127.0.0.1", 8766))
        sock.close()
        
        if result == 0:
            print("   ✅ Local access: OK")
        else:
            print("   ❌ Local access: FAILED")
            print("   Make sure Windows Clipsy app is running")
            return False
    except Exception as e:
        print(f"   ❌ Local access error: {e}")
        return False
    
    # Test Wi-Fi interface access
    print("2. Testing Wi-Fi interface access (*************:8766)...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(("*************", 8766))
        sock.close()
        
        if result == 0:
            print("   ✅ Wi-Fi interface: OK")
        else:
            print("   ❌ Wi-Fi interface: FAILED")
            print("   This suggests a firewall or binding issue")
            return False
    except Exception as e:
        print(f"   ❌ Wi-Fi interface error: {e}")
        return False
    
    # Check Windows Firewall status
    print("3. Checking Windows Firewall status...")
    try:
        result = subprocess.run([
            "netsh", "advfirewall", "show", "allprofiles", "state"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            output = result.stdout
            if "ON" in output:
                print("   ⚠️  Windows Firewall is ON")
                print("   This might be blocking incoming connections")
                return "firewall_on"
            else:
                print("   ✅ Windows Firewall appears to be OFF")
        else:
            print("   ❓ Could not check firewall status")
    except Exception as e:
        print(f"   ❓ Firewall check error: {e}")
    
    # Check if port 8766 is listening
    print("4. Checking if port 8766 is listening...")
    try:
        result = subprocess.run([
            "netstat", "-an"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            listening_8766 = [line for line in lines if ":8766" in line and "LISTENING" in line]
            
            if listening_8766:
                print("   ✅ Port 8766 is listening:")
                for line in listening_8766:
                    print(f"      {line.strip()}")
            else:
                print("   ❌ Port 8766 is NOT listening")
                print("   Make sure Windows Clipsy app is running")
                return False
        else:
            print("   ❓ Could not check listening ports")
    except Exception as e:
        print(f"   ❓ Port check error: {e}")
    
    return True

def show_firewall_instructions():
    """Show instructions for configuring Windows Firewall."""
    print("\n" + "=" * 60)
    print("WINDOWS FIREWALL CONFIGURATION INSTRUCTIONS")
    print("=" * 60)
    print()
    print("If Windows Firewall is blocking connections, you need to:")
    print()
    print("METHOD 1 - Add Firewall Rule (Recommended):")
    print("1. Open Windows Security (Windows Defender)")
    print("2. Go to 'Firewall & network protection'")
    print("3. Click 'Advanced settings'")
    print("4. Click 'Inbound Rules' → 'New Rule'")
    print("5. Select 'Port' → Next")
    print("6. Select 'TCP' → Specific local ports: 8766 → Next")
    print("7. Select 'Allow the connection' → Next")
    print("8. Check all profiles (Domain, Private, Public) → Next")
    print("9. Name: 'Clipsy Port 8766' → Finish")
    print()
    print("METHOD 2 - Allow App Through Firewall:")
    print("1. Open Windows Security")
    print("2. Go to 'Firewall & network protection'")
    print("3. Click 'Allow an app through firewall'")
    print("4. Click 'Change settings' → 'Allow another app'")
    print("5. Browse to your Clipsy.exe file")
    print("6. Check both 'Private' and 'Public' networks")
    print()
    print("METHOD 3 - Temporary Test (NOT RECOMMENDED):")
    print("1. Temporarily disable Windows Firewall")
    print("2. Test if Android can connect")
    print("3. Re-enable firewall and use Method 1 or 2")

if __name__ == "__main__":
    result = test_port_accessibility()
    
    if result == "firewall_on":
        show_firewall_instructions()
    elif result:
        print("\n✅ All tests passed! Network should be accessible.")
        print("If Android still can't connect, the issue might be:")
        print("- Android app network permissions")
        print("- Android device network configuration")
        print("- Router/Wi-Fi settings blocking device-to-device communication")
    else:
        print("\n❌ Network accessibility issues detected.")
        print("Make sure Windows Clipsy app is running and try again.")
