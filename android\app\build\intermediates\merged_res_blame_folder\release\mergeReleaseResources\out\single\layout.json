[{"merged": "com.clipsy.android.app-mergeReleaseResources-56:/layout/item_device_bluetooth_style.xml", "source": "com.clipsy.android.app-main-58:/layout/item_device_bluetooth_style.xml"}, {"merged": "com.clipsy.android.app-mergeReleaseResources-56:/layout/fragment_status.xml", "source": "com.clipsy.android.app-main-58:/layout/fragment_status.xml"}, {"merged": "com.clipsy.android.app-mergeReleaseResources-56:/layout/dialog_pairing.xml", "source": "com.clipsy.android.app-main-58:/layout/dialog_pairing.xml"}, {"merged": "com.clipsy.android.app-mergeReleaseResources-56:/layout/item_clipboard_history.xml", "source": "com.clipsy.android.app-main-58:/layout/item_clipboard_history.xml"}, {"merged": "com.clipsy.android.app-mergeReleaseResources-56:/layout/activity_settings.xml", "source": "com.clipsy.android.app-main-58:/layout/activity_settings.xml"}, {"merged": "com.clipsy.android.app-mergeReleaseResources-56:/layout/fragment_devices.xml", "source": "com.clipsy.android.app-main-58:/layout/fragment_devices.xml"}, {"merged": "com.clipsy.android.app-mergeReleaseResources-56:/layout/activity_main.xml", "source": "com.clipsy.android.app-main-58:/layout/activity_main.xml"}, {"merged": "com.clipsy.android.app-mergeReleaseResources-56:/layout/fragment_history.xml", "source": "com.clipsy.android.app-main-58:/layout/fragment_history.xml"}, {"merged": "com.clipsy.android.app-mergeReleaseResources-56:/layout/dialog_add_manual_device.xml", "source": "com.clipsy.android.app-main-58:/layout/dialog_add_manual_device.xml"}, {"merged": "com.clipsy.android.app-mergeReleaseResources-56:/layout/item_device.xml", "source": "com.clipsy.android.app-main-58:/layout/item_device.xml"}]