R_DEF: Internal format may change without notice
local
array history_limit_entries
array history_limit_values
color background_color
color black
color bluetooth_blue
color border_color
color bottom_nav_color_selector
color button_background_disabled
color button_background_primary
color button_background_primary_selector
color button_background_secondary
color button_stroke_primary
color button_stroke_secondary
color button_text_disabled
color button_text_primary
color button_text_primary_selector
color button_text_secondary
color button_text_secondary_selector
color clipsy_accent
color clipsy_background
color clipsy_card_background
color clipsy_card_dark
color clipsy_error
color clipsy_green_bright
color clipsy_primary
color clipsy_primary_dark
color clipsy_surface
color clipsy_text_primary
color clipsy_text_secondary
color code_background
color high_contrast_background
color high_contrast_text
color medium_contrast_background
color medium_contrast_text
color primary_color
color purple_200
color purple_500
color purple_700
color secondary_color
color source_local
color source_remote
color status_connected
color status_connecting
color status_disconnected
color switch_thumb_color
color switch_track_color
color teal_200
color teal_700
color text_primary
color text_secondary
color white
dimen button_corner_radius
dimen button_corner_radius_small
dimen button_elevation
dimen button_min_height
dimen button_min_height_small
dimen button_min_width
dimen button_padding_horizontal
dimen button_padding_horizontal_small
dimen button_padding_vertical
dimen button_padding_vertical_small
dimen button_stroke_width
dimen button_stroke_width_small
dimen text_size_button
dimen text_size_button_small
drawable badge_background
drawable button_secondary
drawable circle_background
drawable circle_indicator
drawable code_background
drawable dialog_background
drawable ic_arrow_right
drawable ic_clipsy_devices
drawable ic_clipsy_history
drawable ic_clipsy_refresh
drawable ic_clipsy_settings
drawable ic_clipsy_status
drawable ic_computer
drawable ic_devices_vector
drawable ic_history_vector
drawable ic_info
drawable ic_notification
drawable ic_phone
drawable ic_refresh
drawable ic_scan_animation
drawable ic_search
drawable ic_send_arrow
drawable ic_settings
drawable ic_status_vector
drawable ic_sync
drawable ic_sync_to_pc
id action_clear_history
id action_refresh
id action_settings
id bottom_navigation
id btn_cancel_pairing
id button_connect
id button_delete
id button_info
id button_pair
id button_refresh
id button_refresh_devices
id button_remove
id button_send_message
id button_unpair
id card_device_name
id card_network_info
id card_no_available_devices
id card_service_status
id edit_text_ip
id edit_text_message
id edit_text_name
id fab_add_device
id fab_manual_sync
id fragment_container
id header_available_devices
id header_connected_devices
id icon_content_type
id icon_device_type
id indicator_online
id layout_empty
id nav_devices
id nav_history
id nav_status
id progress_discovery
id progress_scanning
id recycler_view_available_devices
id recycler_view_connected_devices
id recycler_view_history
id settings_container
id swipe_refresh_layout
id switch_sync
id text_connected_devices
id text_connection_status
id text_content
id text_current_device_name
id text_description
id text_device_info
id text_device_ip
id text_device_name
id text_device_type
id text_discovered_devices
id text_discovery_status
id text_last_seen
id text_local_ip
id text_service_status
id text_source
id text_timestamp
id toolbar
id tv_pairing_code
id tv_pairing_instruction
id tv_pairing_title
layout activity_main
layout activity_settings
layout dialog_add_manual_device
layout dialog_pairing
layout fragment_devices
layout fragment_history
layout fragment_status
layout item_clipboard_history
layout item_device
layout item_device_bluetooth_style
menu bottom_navigation_menu
menu main_menu
menu menu_history
mipmap ic_launcher
mipmap ic_launcher_foreground
string action_clear_history
string action_connect
string action_copy
string action_delete
string action_pair
string action_refresh
string action_remove
string action_settings
string action_unpair
string add
string app_name
string app_version
string cancel
string copy
string delete
string device_add_manual
string device_connect
string device_connected
string device_disconnect
string device_disconnected
string device_manual_ip_hint
string device_manual_name_hint
string devices_empty
string devices_title
string error_connection_failed
string error_invalid_ip
string error_network_unavailable
string error_permission_denied
string history_clear
string history_clear_confirm
string history_empty
string history_item_copy
string history_item_delete
string history_title
string nav_devices
string nav_history
string nav_status
string no
string notification_clipboard_synced
string notification_service_text
string notification_service_title
string ok
string pref_app_version_title
string pref_auto_cleanup_summary
string pref_auto_cleanup_title
string pref_auto_sync_summary
string pref_auto_sync_title
string pref_category_about
string pref_category_history
string pref_category_network
string pref_category_sync
string pref_discovery_port_summary
string pref_discovery_port_title
string pref_history_limit_summary
string pref_history_limit_title
string pref_sync_interval_summary
string pref_sync_interval_title
string pref_websocket_port_summary
string pref_websocket_port_title
string settings_auto_discovery
string settings_auto_discovery_summary
string settings_auto_start
string settings_auto_start_summary
string settings_clipboard
string settings_device_name
string settings_device_name_summary
string settings_history_enabled
string settings_history_enabled_summary
string settings_history_limit
string settings_history_limit_summary
string settings_network
string settings_startup
string settings_sync_enabled
string settings_sync_enabled_summary
string settings_title
string share
string status_connected
string status_disconnected
string status_discovering
string status_service_running
string status_service_stopped
string yes
style AlertDialog.Clipsy
style AlertDialog.Clipsy.Body
style AlertDialog.Clipsy.Button
style AlertDialog.Clipsy.Title
style BottomNavActiveIndicator
style Button.Clipsy.Danger
style Button.Clipsy.Primary
style Button.Clipsy.Secondary
style Button.Clipsy.Small
style Button.Clipsy.Success
style Button.Clipsy.Tertiary
style PreferenceThemeOverlay.Clipsy
style ShapeAppearanceOverlay.Clipsy.FloatingActionButton
style Theme.Clipsy
style Theme.Clipsy.Settings
xml backup_rules
xml data_extraction_rules
xml network_security_config
xml preferences
