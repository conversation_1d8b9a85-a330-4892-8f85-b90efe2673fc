"""
WebSocket server for clipboard synchronization.
Handles incoming connections from other Clipsy devices.
"""

import json
import asyncio
import logging
import websockets
from typing import Set, Dict, Callable
from datetime import datetime


class SyncServer:
    """WebSocket server for clipboard synchronization."""
    
    def __init__(self, config: dict, pairing_manager=None):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.pairing_manager = pairing_manager

        # Server configuration
        self.host = self._get_preferred_host()  # Use Wi-Fi interface
        self.port = config.get("websocket_port", 8766)  # Use websocket_port for consistency
        
        # Server state
        self.server = None
        self.is_running = False
        self.connected_clients: Set[websockets.WebSocketServerProtocol] = set()
        self.connected_devices: Dict[str, dict] = {}  # Track connected devices by IP
        self.pending_devices: Dict[str, dict] = {}    # Track pending devices waiting for approval

        # Track manually disconnected devices to prevent sync
        self.manually_disconnected_devices = set()

        # Message handlers
        self.message_handlers = {
            "clipboard_sync": self._handle_clipboard_sync,
            "history_request": self._handle_history_request,
            "ping": self._handle_ping,
            "device_info": self._handle_device_info,
            "pairing_request": self._handle_pairing_request,
            "pairing_response": self._handle_pairing_response
        }
        
        # Callbacks
        self.clipboard_callback = None
        self.history_callback = None
        self.device_event_callback = None

    def _get_preferred_host(self) -> str:
        """Get preferred host IP (Wi-Fi interface) or fallback to all interfaces."""
        try:
            # Import here to avoid circular imports
            from .discovery import DeviceDiscovery

            # Create temporary discovery instance to get local IP
            temp_discovery = DeviceDiscovery(self.config)
            local_ip = temp_discovery.get_local_ip()

            if local_ip and local_ip.startswith(('192.168.', '10.', '172.')):
                self.logger.info(f"Binding to Wi-Fi interface: {local_ip}")
                return local_ip
            else:
                self.logger.warning(f"No suitable Wi-Fi IP found, using all interfaces")
                return "0.0.0.0"
        except Exception as e:
            self.logger.error(f"Error getting preferred host: {e}")
            return "0.0.0.0"
    
    def set_clipboard_callback(self, callback: Callable[[str, dict], None]):
        """Set callback for clipboard updates from clients."""
        self.clipboard_callback = callback
    
    def set_history_callback(self, callback: Callable[[], list]):
        """Set callback to get clipboard history."""
        self.history_callback = callback

    def set_device_event_callback(self, callback: Callable[[str, str, dict], None]):
        """Set callback for device connection events."""
        self.device_event_callback = callback
    
    async def start(self):
        """Start the WebSocket server."""
        if self.is_running:
            self.logger.warning("Sync server already running")
            return

        # Try to start on the configured port only - no fallback to avoid confusion
        try:
            self.server = await websockets.serve(
                self._handle_client,
                self.host,
                self.port,
                ping_interval=20,
                ping_timeout=10
            )

            self.is_running = True
            self.logger.info(f"Sync server started on {self.host}:{self.port}")
            return

        except OSError as e:
            if "10048" in str(e) or "Address already in use" in str(e):
                self.logger.error(f"Port {self.port} is already in use. Please close other applications using this port or change the port in config.json")
                raise Exception(f"Port {self.port} is already in use. Cannot start server.")
            else:
                self.logger.error(f"Failed to start sync server on port {self.port}: {e}")
                raise
        except Exception as e:
            self.logger.error(f"Failed to start sync server on port {self.port}: {e}")
            raise
    
    async def stop(self):
        """Stop the WebSocket server."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Close all client connections
        if self.connected_clients:
            await asyncio.gather(
                *[client.close() for client in self.connected_clients],
                return_exceptions=True
            )
            self.connected_clients.clear()
        
        # Stop server
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            self.server = None
        
        self.logger.info("Sync server stopped")
    
    async def _handle_client(self, websocket, path):
        """Handle new client connection."""
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        client_ip = websocket.remote_address[0]
        self.logger.info(f"Client connected: {client_address}")

        self.connected_clients.add(websocket)

        try:
            # Send welcome message
            await self._send_message(websocket, {
                "type": "welcome",
                "server_info": {
                    "device_name": self.config.get("device_name", "Windows-PC"),
                    "device_type": "windows",
                    "timestamp": datetime.now().isoformat()
                }
            })

            # Handle messages from client
            async for message in websocket:
                await self._handle_message(websocket, message)

        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"Client disconnected: {client_address}")
        except Exception as e:
            self.logger.error(f"Error handling client {client_address}: {e}")
        finally:
            self.connected_clients.discard(websocket)
            # Remove from connected devices
            if client_ip in self.connected_devices:
                device_info = self.connected_devices.pop(client_ip)
                if self.device_event_callback:
                    self.device_event_callback("disconnected", client_ip, device_info)
    
    async def _handle_message(self, websocket, message):
        """Handle message from client."""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            if message_type in self.message_handlers:
                await self.message_handlers[message_type](websocket, data)
            else:
                self.logger.warning(f"Unknown message type: {message_type}")
                
        except json.JSONDecodeError:
            self.logger.error("Invalid JSON received from client")
        except Exception as e:
            self.logger.error(f"Error handling message: {e}")
    
    async def _handle_clipboard_sync(self, websocket, data):
        """Handle clipboard sync from client."""
        try:
            content = data.get("content", "")
            device_info = data.get("device_info", {})
            
            if content and self.clipboard_callback:
                self.clipboard_callback(content, device_info)
            
            # Broadcast to other clients
            await self._broadcast_message(data, exclude=websocket)
            
        except Exception as e:
            self.logger.error(f"Error handling clipboard update: {e}")
    
    async def _handle_history_request(self, websocket, data):
        """Handle history request from client."""
        try:
            history = []
            if self.history_callback:
                history = self.history_callback()
            
            response = {
                "type": "history_response",
                "history": history,
                "timestamp": datetime.now().isoformat()
            }
            
            await self._send_message(websocket, response)
            
        except Exception as e:
            self.logger.error(f"Error handling history request: {e}")
    
    async def _handle_ping(self, websocket, data):
        """Handle ping from client."""
        try:
            response = {
                "type": "pong",
                "timestamp": datetime.now().isoformat()
            }
            await self._send_message(websocket, response)
            
        except Exception as e:
            self.logger.error(f"Error handling ping: {e}")

    async def _handle_device_info(self, websocket, data):
        """Handle device info from client."""
        try:
            client_ip = websocket.remote_address[0]
            device_info = {
                "device_name": data.get("device_name", "Unknown Device"),
                "device_type": data.get("device_type", "unknown"),
                "ip": client_ip,
                "websocket_port": data.get("websocket_port", 8766),
                "timestamp": datetime.now().isoformat(),
                "device_id": data.get("device_id", client_ip)
            }

            # Check if device was manually disconnected
            if client_ip in self.manually_disconnected_devices:
                self.allow_reconnection(client_ip)
                self.logger.info(f"🟢 Allowing reconnection for previously disconnected device: {client_ip}")

            # Simple connection - automatically approve all devices
            self.connected_devices[client_ip] = device_info

            # Notify UI about successful connection
            if self.device_event_callback:
                self.device_event_callback("connected", client_ip, device_info)

            self.logger.info(f"Device connected: {device_info['device_name']} ({client_ip}) - auto-approved")

        except Exception as e:
            self.logger.error(f"Error handling device info: {e}")

    def approve_device_connection(self, device_ip: str) -> bool:
        """Approve a pending device connection."""
        if device_ip in self.pending_devices:
            device_info = self.pending_devices.pop(device_ip)

            # Simple connection - no pairing validation required
            # Allow all devices to connect without pairing checks

            self.connected_devices[device_ip] = device_info

            # Notify UI about approved connection
            if self.device_event_callback:
                self.device_event_callback("connected", device_ip, device_info)

            self.logger.info(f"Device connection approved: {device_info['device_name']} ({device_ip})")
            return True
        return False

    def reject_device_connection(self, device_ip: str) -> bool:
        """Reject a pending device connection."""
        if device_ip in self.pending_devices:
            device_info = self.pending_devices.pop(device_ip)

            # Notify UI about rejected connection
            if self.device_event_callback:
                self.device_event_callback("connection_rejected", device_ip, device_info)

            self.logger.info(f"Device connection rejected: {device_info['device_name']} ({device_ip})")
            return True
        return False

    def get_pending_devices(self) -> Dict[str, dict]:
        """Get all pending device connections."""
        return self.pending_devices.copy()

    async def _send_message(self, websocket, message: dict):
        """Send message to a specific client."""
        try:
            data = json.dumps(message)
            await websocket.send(data)
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
    
    async def _broadcast_message(self, message: dict, exclude=None):
        """Broadcast message to all connected clients, excluding manually disconnected devices."""
        if not self.connected_clients:
            return

        clients_to_send = set()
        for client in self.connected_clients:
            try:
                client_ip = client.remote_address[0]
                # Skip manually disconnected devices
                if client_ip in self.manually_disconnected_devices:
                    continue

                # Skip excluded client
                if exclude and client == exclude:
                    continue

                clients_to_send.add(client)
            except Exception:
                # If we can't get the IP, include the client (better safe than sorry)
                if exclude and client != exclude:
                    clients_to_send.add(client)

        if clients_to_send:
            data = json.dumps(message)
            await asyncio.gather(
                *[client.send(data) for client in clients_to_send],
                return_exceptions=True
            )
    
    async def broadcast_clipboard_sync(self, content: str, device_info: dict = None):
        """Broadcast clipboard sync to all clients."""
        message = {
            "type": "clipboard_sync",
            "content": content,
            "device_info": device_info or {
                "device_name": self.config.get("device_name", "Windows-PC"),
                "device_type": "windows"
            },
            "timestamp": int(datetime.now().timestamp() * 1000)
        }
        
        await self._broadcast_message(message)
    
    def get_connected_clients_count(self) -> int:
        """Get number of connected clients."""
        return len(self.connected_clients)
    
    def get_connected_clients_info(self) -> list:
        """Get information about connected clients."""
        clients_info = []
        for client in self.connected_clients:
            try:
                clients_info.append({
                    "address": f"{client.remote_address[0]}:{client.remote_address[1]}",
                    "state": client.state.name if hasattr(client.state, 'name') else str(client.state)
                })
            except Exception:
                continue
        return clients_info

    def get_connected_devices(self) -> dict:
        """Get connected devices."""
        return self.connected_devices.copy()

    def disconnect_device(self, device_ip: str):
        """Manually disconnect a device and stop sync operations."""
        self.logger.info(f"🔴 MANUAL DISCONNECT from device: {device_ip}")

        # Mark as manually disconnected
        self.manually_disconnected_devices.add(device_ip)
        self.logger.info(f"🔴 Added {device_ip} to manually disconnected devices")

        # Find and close the WebSocket connection for this device
        client_to_disconnect = None
        for client in self.connected_clients:
            try:
                if client.remote_address[0] == device_ip:
                    client_to_disconnect = client
                    break
            except Exception:
                continue

        if client_to_disconnect:
            asyncio.create_task(client_to_disconnect.close())
            self.logger.info(f"🔴 Closed WebSocket connection for {device_ip}")

        # Remove from connected devices
        if device_ip in self.connected_devices:
            device_info = self.connected_devices.pop(device_ip)
            if self.device_event_callback:
                self.device_event_callback("disconnected", device_ip, device_info)

        self.logger.info(f"🔴 DISCONNECT COMPLETE for {device_ip}")

    def allow_reconnection(self, device_ip: str):
        """Allow reconnection to a manually disconnected device."""
        self.manually_disconnected_devices.discard(device_ip)
        self.logger.info(f"🟢 Removed {device_ip} from manually disconnected devices - reconnection allowed")

    async def _handle_pairing_request(self, websocket, data):
        """Handle pairing request from client."""
        try:
            if not self.pairing_manager:
                await self._send_error(websocket, "Pairing not supported")
                return

            pairing_code = data.get("pairing_code")
            remote_device_id = data.get("device_id")
            remote_device_name = data.get("device_name")

            if not all([pairing_code, remote_device_id, remote_device_name]):
                await self._send_error(websocket, "Missing pairing information")
                return

            # Verify pairing code
            success = self.pairing_manager.verify_pairing_code(
                pairing_code, remote_device_id, remote_device_name
            )

            response = {
                "type": "pairing_response",
                "success": success,
                "device_id": self.pairing_manager.device_id,
                "device_name": self.pairing_manager.device_name
            }

            await websocket.send(json.dumps(response))

            if success:
                self.logger.info(f"Successfully paired with {remote_device_name}")
            else:
                self.logger.warning(f"Pairing failed with {remote_device_name}")

        except Exception as e:
            self.logger.error(f"Error handling pairing request: {e}")
            await self._send_error(websocket, f"Pairing error: {e}")

    async def _handle_pairing_response(self, websocket, data):
        """Handle pairing response from client."""
        try:
            success = data.get("success", False)
            remote_device_id = data.get("device_id")
            remote_device_name = data.get("device_name")

            if success and remote_device_id and remote_device_name:
                self.logger.info(f"Pairing confirmed with {remote_device_name}")
                # Update pairing manager if needed
                if self.pairing_manager:
                    self.pairing_manager.update_last_connected(remote_device_id)
            else:
                self.logger.warning("Pairing response indicated failure")

        except Exception as e:
            self.logger.error(f"Error handling pairing response: {e}")

    async def _send_error(self, websocket, error_message: str):
        """Send error message to client."""
        try:
            error_response = {
                "type": "error",
                "message": error_message
            }
            await websocket.send(json.dumps(error_response))
        except Exception as e:
            self.logger.error(f"Error sending error message: {e}")
