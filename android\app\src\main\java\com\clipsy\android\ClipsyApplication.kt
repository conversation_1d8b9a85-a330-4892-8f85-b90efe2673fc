package com.clipsy.android

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import com.clipsy.android.data.repository.ClipboardRepository

/**
 * Application class for Clipsy Android app.
 * Initializes core components.
 */
class ClipsyApplication : Application() {

    companion object {
        const val NOTIFICATION_CHANNEL_ID = "clipsy_service_channel"
        const val NOTIFICATION_CHANNEL_NAME = "Clipsy Service"
    }

    override fun onCreate() {
        super.onCreate()

        // Initialize clipboard repository with context
        ClipboardRepository.getInstance().initialize(this)

        // Initialize notification channel
        createNotificationChannel()
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                NOTIFICATION_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Notifications for Clipsy background service"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
}
