<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Clipsy" parent="Theme.Material3.Dark.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/clipsy_primary</item>
        <item name="colorPrimaryVariant">@color/clipsy_primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/clipsy_accent</item>
        <item name="colorSecondaryVariant">@color/clipsy_accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Background colors -->
        <item name="android:colorBackground">@color/clipsy_background</item>
        <item name="colorSurface">@color/clipsy_surface</item>
        <item name="colorOnSurface">@color/white</item>
        <item name="colorOnBackground">@color/white</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Toolbar text colors -->
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/white</item>
        <item name="actionMenuTextColor">@color/white</item>
        <!-- Customize your theme here. -->
    </style>

    <!-- Bottom Navigation Active Indicator Style -->
    <style name="BottomNavActiveIndicator" parent="Widget.Material3.BottomNavigationView.ActiveIndicator">
        <item name="android:color">#FF3A3A3A</item>
        <item name="android:alpha">0.9</item>
    </style>

    <!-- Settings Activity Theme -->
    <style name="Theme.Clipsy.Settings" parent="Theme.Clipsy">
        <item name="preferenceTheme">@style/PreferenceThemeOverlay.Clipsy</item>
        <item name="android:textColorPrimary">@color/clipsy_text_primary</item>
        <item name="android:textColorSecondary">@color/clipsy_text_secondary</item>
    </style>

    <!-- Preference Theme Overlay -->
    <style name="PreferenceThemeOverlay.Clipsy" parent="PreferenceThemeOverlay.v14.Material">
        <item name="android:textColorPrimary">@color/clipsy_text_primary</item>
        <item name="android:textColorSecondary">@color/clipsy_text_primary</item>
        <item name="android:colorBackground">@color/clipsy_background</item>
        <item name="colorSurface">@color/clipsy_card_dark</item>
        <item name="colorOnSurface">@color/clipsy_text_primary</item>
        <item name="colorPrimary">@color/clipsy_text_primary</item>
        <item name="colorOnPrimary">@color/clipsy_text_primary</item>
        <item name="colorAccent">@color/clipsy_text_primary</item>
    </style>

    <!-- Round FAB Shape -->
    <style name="ShapeAppearanceOverlay.Clipsy.FloatingActionButton" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
</resources>
