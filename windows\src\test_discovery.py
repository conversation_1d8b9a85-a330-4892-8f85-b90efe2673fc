#!/usr/bin/env python3
"""
Test script to verify discovery service works without UI.
"""

import asyncio
import logging
import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from network.discovery import DeviceDiscovery
from network.sync_server import SyncServer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_discovery():
    """Test discovery service."""
    config = {
        "device_name": "Windows-PC-Test",
        "discovery_port": 8765,
        "sync_port": 8766
    }
    
    logger = logging.getLogger(__name__)
    logger.info("🧪 Starting discovery test...")
    
    try:
        # Create discovery service
        discovery = DeviceDiscovery(config)
        
        # Create sync server
        sync_server = SyncServer(config)
        
        logger.info("🚀 Starting sync server...")
        await sync_server.start()
        
        logger.info("🔍 Starting discovery service...")
        await discovery.start()
        
        logger.info("✅ Services started successfully!")
        logger.info("🕐 Running for 30 seconds to test discovery...")
        
        # Run for 30 seconds
        await asyncio.sleep(30)
        
        logger.info("🛑 Stopping services...")
        await discovery.stop()
        await sync_server.stop()
        
        logger.info("✅ Test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    asyncio.run(test_discovery())
