<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <!-- Mobile device -->
  <path
      android:fillColor="@android:color/white"
      android:pathData="M17,1H7C5.9,1 5,1.9 5,3v18c0,1.1 0.9,2 2,2h10c1.1,0 2,-0.9 2,-2V3C19,1.9 18.1,1 17,1zM17,19H7V5h10V19z"/>
  <!-- PC monitor -->
  <path
      android:fillColor="@android:color/white"
      android:pathData="M21,2H3C1.9,2 1,2.9 1,4v12c0,1.1 0.9,2 2,2h7l-2,3v1h8v-1l-2,-3h7c1.1,0 2,-0.9 2,-2V4<PERSON>23,2.9 22.1,2 21,2zM21,14H3V4h18V14z"/>
  <!-- Arrow from mobile to PC -->
  <path
      android:fillColor="@android:color/white"
      android:pathData="M14,8l-2,-2v1.5H8v1h4V10l2,-2z"/>
</vector>
