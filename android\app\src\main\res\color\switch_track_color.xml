<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- When switch is checked (ON) - Blue -->
    <item android:state_checked="true" android:color="@color/bluetooth_blue" />
    <!-- When switch is unchecked (OFF) - Gray -->
    <item android:state_checked="false" android:color="#FF666666" />
    <!-- Default state - Gray -->
    <item android:color="#FF666666" />
</selector>
