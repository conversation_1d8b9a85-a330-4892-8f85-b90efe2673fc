$com.clipsy.android.ClipsyApplication+com.clipsy.android.data.model.ClipboardItem.com.clipsy.android.data.model.ConnectionStatus$<EMAIL>/com.clipsy.android.service.ClipboardSyncService;com.clipsy.android.service.ClipboardSyncService.LocalBinder"com.clipsy.android.ui.MainActivity*com.clipsy.android.ui.MainViewModelFactory&com.clipsy.android.ui.SettingsActivity7com.clipsy.android.ui.SettingsActivity.SettingsFragment:com.clipsy.android.ui.adapters.BluetoothStyleDeviceAdapterKcom.clipsy.android.ui.adapters.BluetoothStyleDeviceAdapter.DeviceViewHolderMcom.clipsy.android.ui.adapters.BluetoothStyleDeviceAdapter.DeviceDiffCallback6com.clipsy.android.ui.adapters.ClipboardHistoryAdapterHcom.clipsy.android.ui.adapters.ClipboardHistoryAdapter.HistoryViewHolderJcom.clipsy.android.ui.adapters.ClipboardHistoryAdapter.HistoryDiffCallback,com.clipsy.android.ui.adapters.DeviceAdapter=com.clipsy.android.ui.adapters.DeviceAdapter.DeviceViewHolder?com.clipsy.android.ui.adapters.DeviceAdapter.DeviceDiffCallback/com.clipsy.android.ui.fragments.DevicesFragment/com.clipsy.android.ui.fragments.HistoryFragment.com.clipsy.android.ui.fragments.StatusFragment8com.clipsy.android.ui.utils.ButtonStyleUtils.ButtonStyle*com.clipsy.android.viewmodel.MainViewModel2com.clipsy.android.databinding.ActivityMainBinding4com.clipsy.android.databinding.FragmentStatusBinding0com.clipsy.android.databinding.ItemDeviceBinding5com.clipsy.android.databinding.FragmentHistoryBinding:com.clipsy.android.databinding.ItemClipboardHistoryBinding5com.clipsy.android.databinding.FragmentDevicesBinding>com.clipsy.android.databinding.ItemDeviceBluetoothStyleBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 