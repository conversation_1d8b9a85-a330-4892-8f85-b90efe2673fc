#!/usr/bin/env python3
"""
Test the Windows app's IP selection logic to see which IP it chooses.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'windows', 'src'))

from network.discovery import DeviceDiscovery

def test_ip_selection():
    """Test which IP the Windows app selects."""
    print("Windows App IP Selection Test")
    print("=============================")
    
    # Create discovery instance
    config = {"discovery_port": 8765, "websocket_port": 8766}
    discovery = DeviceDiscovery(config)
    
    # Get all local IPs
    all_ips = discovery._get_local_ips()
    print(f"All available IPs: {all_ips}")
    
    # Get selected primary IP
    primary_ip = discovery.get_local_ip()
    print(f"Selected primary IP: {primary_ip}")
    
    print()
    print("IP Analysis:")
    for ip in all_ips:
        if ip.startswith('192.168.'):
            print(f"  {ip} - Wi-Fi/Home network (PREFERRED)")
        elif ip.startswith('169.254.'):
            print(f"  {ip} - Auto-configuration (AVOID)")
        elif ip.startswith('172.16.'):
            print(f"  {ip} - VPN/Private network")
        elif ip.startswith('10.'):
            print(f"  {ip} - Private network")
        else:
            print(f"  {ip} - Other")
    
    print()
    if primary_ip and primary_ip.startswith('192.168.'):
        print("✅ GOOD: Selected Wi-Fi network IP")
        print("   Android devices should be able to connect")
    elif primary_ip and primary_ip.startswith('169.254.'):
        print("❌ PROBLEM: Selected auto-configuration IP")
        print("   Android devices won't be able to connect")
        print("   Need to fix IP selection logic")
    else:
        print(f"⚠️  UNKNOWN: Selected {primary_ip}")

if __name__ == "__main__":
    test_ip_selection()
