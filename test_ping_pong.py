#!/usr/bin/env python3
"""
Test ping-pong functionality with Windows Clipsy app
"""

import asyncio
import websockets
import json

async def test_ping_pong():
    """Test ping-pong with Windows Clipsy app."""
    try:
        uri = "ws://*************:8766"
        print(f"Connecting to {uri}...")
        
        async with websockets.connect(uri, timeout=10) as websocket:
            print("Connected successfully!")
            
            # First, receive the welcome message
            welcome_msg = await websocket.recv()
            welcome_data = json.loads(welcome_msg)
            print(f"Welcome message: {welcome_data}")
            
            # Now send a ping
            ping_message = {
                "type": "ping",
                "timestamp": "2025-07-04T13:25:00Z"
            }
            
            print("Sending ping...")
            await websocket.send(json.dumps(ping_message))
            
            # Wait for pong response
            pong_msg = await asyncio.wait_for(websocket.recv(), timeout=5)
            pong_data = json.loads(pong_msg)
            print(f"Pong response: {pong_data}")
            
            if pong_data.get("type") == "pong":
                print("SUCCESS: Ping-pong working correctly!")
                print("Windows Clipsy service is fully functional.")
                return True
            else:
                print("FAILED: Expected pong response")
                return False
                
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_ping_pong())
