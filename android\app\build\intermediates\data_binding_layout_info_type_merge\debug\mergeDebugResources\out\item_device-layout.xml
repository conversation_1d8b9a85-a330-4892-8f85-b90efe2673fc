<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_device" modulePackage="com.clipsy.android" filePath="app\src\main\res\layout\item_device.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_device_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="160" endOffset="51"/></Target><Target id="@+id/icon_device_type" view="ImageView"><Expressions/><location startLine="24" startOffset="12" endLine="29" endOffset="51"/></Target><Target id="@+id/indicator_online" view="ImageView"><Expressions/><location startLine="31" startOffset="12" endLine="38" endOffset="51"/></Target><Target id="@+id/text_device_name" view="TextView"><Expressions/><location startLine="49" startOffset="12" endLine="55" endOffset="41"/></Target><Target id="@+id/text_device_ip" view="TextView"><Expressions/><location startLine="57" startOffset="12" endLine="63" endOffset="44"/></Target><Target id="@+id/text_device_type" view="TextView"><Expressions/><location startLine="70" startOffset="16" endLine="77" endOffset="42"/></Target><Target id="@+id/text_last_seen" view="TextView"><Expressions/><location startLine="87" startOffset="16" endLine="93" endOffset="48"/></Target><Target id="@+id/text_connection_status" view="TextView"><Expressions/><location startLine="97" startOffset="12" endLine="104" endOffset="40"/></Target><Target id="@+id/button_connect" view="Button"><Expressions/><location startLine="114" startOffset="12" endLine="122" endOffset="41"/></Target><Target id="@+id/button_pair" view="Button"><Expressions/><location startLine="124" startOffset="12" endLine="132" endOffset="41"/></Target><Target id="@+id/button_unpair" view="Button"><Expressions/><location startLine="134" startOffset="12" endLine="144" endOffset="43"/></Target><Target id="@+id/button_remove" view="Button"><Expressions/><location startLine="146" startOffset="12" endLine="154" endOffset="41"/></Target></Targets></Layout>