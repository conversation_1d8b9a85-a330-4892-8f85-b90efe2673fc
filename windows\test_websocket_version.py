#!/usr/bin/env python3
"""
WebSocket Version and Configuration Test
Tests WebSocket library version and basic functionality
"""

import asyncio
import websockets
import logging
import sys
import socket

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_websockets_version():
    """Check WebSocket library version"""
    logger.info(f"WebSockets library version: {websockets.__version__}")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Asyncio debug mode: {asyncio.get_event_loop().get_debug()}")

def check_socket_binding():
    """Test if we can bind to the socket directly"""
    host = "*************"
    port = 8767
    
    logger.info(f"Testing direct socket binding to {host}:{port}")
    
    try:
        # Test TCP socket binding
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.bind((host, port))
        sock.listen(1)
        logger.info(f"✅ Successfully bound TCP socket to {host}:{port}")
        
        # Test if we can accept a connection
        sock.settimeout(1)  # 1 second timeout
        try:
            conn, addr = sock.accept()
            logger.info(f"✅ Accepted connection from {addr}")
            conn.close()
        except socket.timeout:
            logger.info("⏰ No incoming connections (expected)")
        
        sock.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to bind socket: {e}")
        return False

async def test_minimal_websocket_server():
    """Test minimal WebSocket server"""
    host = "*************"
    port = 8767
    
    logger.info(f"Testing minimal WebSocket server on {host}:{port}")
    
    async def echo_handler(websocket, path):
        logger.info(f"🔗 Connection received from {websocket.remote_address}")
        try:
            await websocket.send("Hello from minimal server!")
            async for message in websocket:
                logger.info(f"📥 Received: {message}")
                await websocket.send(f"Echo: {message}")
        except Exception as e:
            logger.error(f"❌ Handler error: {e}")
    
    try:
        # Create server
        logger.info("Creating WebSocket server...")
        server = await websockets.serve(
            echo_handler,
            host,
            port,
            ping_interval=None,  # Disable ping
            ping_timeout=None    # Disable ping timeout
        )
        
        logger.info(f"✅ WebSocket server created successfully")
        logger.info(f"Server: {server}")
        logger.info(f"Server sockets: {server.sockets}")
        
        # Check if server is actually listening
        for sock in server.sockets:
            logger.info(f"Socket: {sock.getsockname()}")
        
        logger.info("Server is ready - waiting for connections...")
        
        # Wait for a short time to see if connections work
        await asyncio.sleep(5)
        
        # Close server
        server.close()
        await server.wait_closed()
        logger.info("Server closed")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create WebSocket server: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Main test function"""
    logger.info("🚀 Starting WebSocket diagnostics...")
    logger.info("="*60)
    
    # Check versions
    check_websockets_version()
    logger.info("="*60)
    
    # Test socket binding
    socket_ok = check_socket_binding()
    logger.info("="*60)
    
    if socket_ok:
        # Test WebSocket server
        websocket_ok = await test_minimal_websocket_server()
        logger.info("="*60)
        
        if websocket_ok:
            logger.info("✅ All tests passed!")
        else:
            logger.error("❌ WebSocket server test failed")
    else:
        logger.error("❌ Socket binding test failed")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
    except Exception as e:
        logger.error(f"Test error: {e}")
        sys.exit(1)
