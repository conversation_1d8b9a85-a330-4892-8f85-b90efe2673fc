<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <PreferenceCategory
        android:title="@string/pref_category_sync"
        app:iconSpaceReserved="false">

        <Preference
            android:key="sync_info"
            android:title="Manual Sync Only"
            android:summary="Use the sync button to manually sync clipboard to PC"
            android:selectable="false"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <PreferenceCategory
        android:title="@string/pref_category_history"
        app:iconSpaceReserved="false">

        <ListPreference
            android:key="history_limit"
            android:title="@string/pref_history_limit_title"
            android:summary="@string/pref_history_limit_summary"
            android:entries="@array/history_limit_entries"
            android:entryValues="@array/history_limit_values"
            android:defaultValue="50"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <PreferenceCategory
        android:title="@string/pref_category_network"
        app:iconSpaceReserved="false">

        <Preference
            android:key="discovery_port_info"
            android:title="Discovery Port"
            android:summary="8765 (UDP - Device Discovery)"
            android:selectable="false"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="websocket_port_info"
            android:title="WebSocket Port"
            android:summary="8766 (TCP - Data Sync)"
            android:selectable="false"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <PreferenceCategory
        android:title="@string/pref_category_about"
        app:iconSpaceReserved="false">

        <Preference
            android:key="app_version"
            android:title="@string/pref_app_version_title"
            android:summary="@string/app_version"
            android:selectable="false"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

</PreferenceScreen>
