package com.clipsy.android.pairing

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.*

data class KnownDevice(
    val deviceId: String,
    val deviceName: String,
    val deviceIp: String,
    val lastSeen: Long
)

data class ConnectedDevice(
    val deviceId: String,
    val deviceName: String,
    val deviceIp: String,
    val connectedAt: Long
)

class SimpleConnectionManager(private val context: Context) {
    companion object {
        private const val TAG = "SimpleConnectionManager"
        private const val PREFS_NAME = "clipsy_connections"
        private const val KEY_DEVICE_ID = "device_id"
        private const val KEY_KNOWN_DEVICES = "known_devices"
        private const val DEVICE_NAME = "Android-Device"
    }

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    // Device info
    val deviceId: String = getOrCreateDeviceId()
    val deviceName: String = DEVICE_NAME
    
    // Connection state
    private val connectedDevices = mutableMapOf<String, ConnectedDevice>()
    private var connectionCallback: ((String, Map<String, Any>) -> Unit)? = null
    
    init {
        // Connection manager initialized
    }
    
    private fun getOrCreateDeviceId(): String {
        val existingId = prefs.getString(KEY_DEVICE_ID, null)
        if (existingId != null) {
            return existingId
        }
        
        val newId = UUID.randomUUID().toString()
        prefs.edit().putString(KEY_DEVICE_ID, newId).apply()
        return newId
    }
    
    fun setConnectionCallback(callback: (String, Map<String, Any>) -> Unit) {
        this.connectionCallback = callback
    }
    
    private fun loadKnownDevices(): List<KnownDevice> {
        val json = prefs.getString(KEY_KNOWN_DEVICES, null) ?: return emptyList()
        return try {
            val type = object : TypeToken<List<KnownDevice>>() {}.type
            gson.fromJson(json, type) ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    private fun saveKnownDevices(devices: List<KnownDevice>) {
        try {
            val json = gson.toJson(devices)
            prefs.edit().putString(KEY_KNOWN_DEVICES, json).apply()
        } catch (e: Exception) {
            // Ignore save errors
        }
    }
    
    fun addKnownDevice(deviceId: String, deviceName: String, deviceIp: String) {
        val devices = loadKnownDevices().toMutableList()
        
        // Remove existing device with same ID
        devices.removeAll { it.deviceId == deviceId }
        
        // Add new device
        devices.add(KnownDevice(deviceId, deviceName, deviceIp, System.currentTimeMillis()))
        
        saveKnownDevices(devices)
    }
    
    fun removeKnownDevice(deviceId: String): Boolean {
        val devices = loadKnownDevices().toMutableList()
        val removed = devices.removeAll { it.deviceId == deviceId }
        
        if (removed) {
            saveKnownDevices(devices)
        }
        
        return removed
    }
    
    fun getKnownDevices(): List<KnownDevice> {
        return loadKnownDevices()
    }
    
    fun connectToDevice(deviceIp: String, deviceName: String? = null): Boolean {
        return try {
            // Generate device ID from IP if not provided
            val deviceId = "device_${deviceIp.replace(".", "_")}"
            val finalDeviceName = deviceName ?: "Device at $deviceIp"
            
            // Add to known devices
            addKnownDevice(deviceId, finalDeviceName, deviceIp)
            
            // Mark as connected
            val connectedDevice = ConnectedDevice(
                deviceId = deviceId,
                deviceName = finalDeviceName,
                deviceIp = deviceIp,
                connectedAt = System.currentTimeMillis()
            )
            
            connectedDevices[deviceId] = connectedDevice
            
            // Notify callback
            connectionCallback?.invoke("connected", mapOf(
                "device_id" to deviceId,
                "device_name" to finalDeviceName,
                "device_ip" to deviceIp
            ))

            true
        } catch (e: Exception) {
            false
        }
    }
    
    fun disconnectFromDevice(deviceId: String): Boolean {
        return try {
            val deviceInfo = connectedDevices.remove(deviceId)
            
            if (deviceInfo != null) {
                // Notify callback
                connectionCallback?.invoke("disconnected", mapOf(
                    "device_id" to deviceInfo.deviceId,
                    "device_name" to deviceInfo.deviceName,
                    "device_ip" to deviceInfo.deviceIp
                ))

                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    fun getConnectedDevices(): List<ConnectedDevice> {
        return connectedDevices.values.toList()
    }
    
    fun isDeviceConnected(deviceId: String): Boolean {
        return connectedDevices.containsKey(deviceId)
    }
    
    fun getDeviceInfo(): Map<String, String> {
        return mapOf(
            "device_id" to deviceId,
            "device_name" to deviceName,
            "device_type" to "android"
        )
    }
    
    fun disconnectFromAllDevices(): Boolean {
        return try {
            val deviceIds = connectedDevices.keys.toList()
            var allSuccess = true

            deviceIds.forEach { deviceId ->
                if (!disconnectFromDevice(deviceId)) {
                    allSuccess = false
                }
            }

            allSuccess
        } catch (e: Exception) {
            false
        }
    }

    fun startSyncService() {
        // This is a placeholder - sync service management would be handled elsewhere
    }

    fun stopSyncService() {
        // This is a placeholder - sync service management would be handled elsewhere
    }

    fun cleanup() {
        connectedDevices.clear()
    }
}
