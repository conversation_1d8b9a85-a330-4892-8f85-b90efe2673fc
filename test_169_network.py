#!/usr/bin/env python3
"""
Test scanning 169.254.x.x network to find Windows Clipsy app at **************
"""

import socket
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

def scan_device_at(ip_address, timeout=3):
    """Scan a specific IP address for Clipsy service on port 8766."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((ip_address, 8766))
        sock.close()
        
        if result == 0:
            return ip_address
        else:
            return None
    except Exception:
        return None

def test_169_network():
    """Test scanning 169.254.x.x network for Windows PC."""
    print("Testing 169.254.x.x Network Scan")
    print("=================================")
    print("Looking for Windows PC at **************...")
    print()
    
    # First test the specific IP we know
    print("Testing known IP: **************...")
    result = scan_device_at("**************", 5)
    if result:
        print("SUCCESS: Found Clipsy at **************!")
        
        # Test WebSocket connection
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect(("**************", 8766))
            sock.close()
            print("WebSocket connection: OK")
        except Exception as e:
            print(f"WebSocket connection: FAILED - {e}")
    else:
        print("FAILED: No Clipsy service at **************")
        print("Make sure Windows Clipsy app is running")
    
    print()
    print("Scanning 169.254.x.x network range...")
    
    found_devices = []
    
    with ThreadPoolExecutor(max_workers=50) as executor:
        # Submit scan tasks for 169.254.x.x range
        future_to_ip = {}
        for i in range(1, 255):
            target_ip = f"169.254.18.{i}"  # Focus on .18.x subnet first
            future = executor.submit(scan_device_at, target_ip, 2)
            future_to_ip[future] = target_ip
        
        # Collect results
        completed = 0
        for future in as_completed(future_to_ip):
            completed += 1
            ip = future_to_ip[future]
            result = future.result()
            
            if result:
                found_devices.append(result)
                print(f"Found Clipsy device at {result}")
            
            if completed % 50 == 0:
                print(f"Scanned {completed}/254 addresses...")
    
    print(f"\nScan completed! Found {len(found_devices)} devices.")
    
    if found_devices:
        print("Discovered devices:")
        for device in found_devices:
            print(f"  - {device}:8766")
        print("\nAndroid app should be able to find these!")
    else:
        print("No devices found in 169.254.18.x range")

if __name__ == "__main__":
    test_169_network()
