9app/src/main/java/com/clipsy/android/ClipsyApplication.kt@app/src/main/java/com/clipsy/android/data/model/ClipboardItem.ktCapp/src/main/java/com/clipsy/android/data/model/ConnectionStatus.kt9app/src/main/java/com/clipsy/android/data/model/Device.ktKapp/src/main/java/com/clipsy/android/data/repository/ClipboardRepository.ktHapp/src/main/java/com/clipsy/android/data/repository/DeviceRepository.kt?app/src/main/java/com/clipsy/android/network/DeviceDiscovery.kt?app/src/main/java/com/clipsy/android/network/WebSocketClient.kt?app/src/main/java/com/clipsy/android/network/WebSocketServer.ktGapp/src/main/java/com/clipsy/android/pairing/SimpleConnectionManager.ktDapp/src/main/java/com/clipsy/android/service/ClipboardSyncService.kt7app/src/main/java/com/clipsy/android/ui/MainActivity.kt;app/src/main/java/com/clipsy/android/ui/SettingsActivity.ktOapp/src/main/java/com/clipsy/android/ui/adapters/BluetoothStyleDeviceAdapter.ktKapp/src/main/java/com/clipsy/android/ui/adapters/ClipboardHistoryAdapter.ktAapp/src/main/java/com/clipsy/android/ui/adapters/DeviceAdapter.ktDapp/src/main/java/com/clipsy/android/ui/fragments/DevicesFragment.ktDapp/src/main/java/com/clipsy/android/ui/fragments/HistoryFragment.ktCapp/src/main/java/com/clipsy/android/ui/fragments/StatusFragment.ktAapp/src/main/java/com/clipsy/android/ui/utils/ButtonStyleUtils.kt?app/src/main/java/com/clipsy/android/viewmodel/MainViewModel.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   