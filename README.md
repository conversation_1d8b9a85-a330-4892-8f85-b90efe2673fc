# Clipsy - Cross-Platform Clipboard Manager

A cross-platform clipboard manager and sync tool that works over Wi-Fi networks, supporting Windows and Android devices.

## Features

### 📡 Device Discovery & Connection
- Automatic device discovery on the same Wi-Fi network
- Manual IP address entry as fallback
- Display local IP addresses in UI
- Real-time connection status

### 📋 Universal Clipboard Sync
- Automatic real-time clipboard synchronization
- Manual clipboard content sharing
- Low-latency updates between devices
- Cross-platform text sharing

### 📚 Clipboard History
- Configurable history length (10, 20, 50, 100 items)
- UI for browsing and managing history
- Option to disable history feature
- Persistent storage across sessions

### ⚙️ Settings & Configuration
- Customizable clipboard history length
- Enable/disable sync and history independently
- Auto-start on boot/login option
- Device naming and identification

## Architecture

### Windows Application
- **Technology**: Python with Tkinter
- **Features**: System clipboard access, background monitoring, WebSocket communication
- **Requirements**: Python 3.8+

### Android Application
- **Technology**: Kotlin
- **Features**: Background service, clipboard monitoring, notifications
- **Requirements**: Android 6.0+ (API level 23)

### Networking
- **Discovery**: UDP broadcast on port 8765
- **Communication**: WebSocket on port 8766
- **Protocol**: JSON-based message exchange
- **Security**: Local network only

## Project Structure

```
clipsy/
├── windows/              # Windows Python application
│   ├── src/
│   │   ├── main.py      # Main application entry
│   │   ├── clipboard/   # Clipboard monitoring
│   │   ├── network/     # Network communication
│   │   └── ui/          # User interface
│   ├── requirements.txt # Python dependencies
│   └── config.json      # Default configuration
├── android/             # Android Kotlin application
│   ├── app/
│   │   ├── src/main/
│   │   │   ├── java/    # Kotlin source code
│   │   │   └── res/     # Resources
│   │   └── build.gradle # App build configuration
│   ├── build.gradle     # Project build configuration
│   └── settings.gradle  # Project settings
├── shared/              # Shared documentation
│   ├── protocol.md      # Network protocol specification
│   └── api.md           # API documentation
└── docs/                # Additional documentation
    ├── setup.md         # Setup instructions
    └── troubleshooting.md
```

## Quick Start

### Windows
1. Install Python 3.8+
2. Navigate to `windows/` directory
3. Install dependencies: `pip install -r requirements.txt`
4. Run: `python src/main.py`

### Android
1. Open `android/` directory in Android Studio
2. Build and install the APK
3. Grant clipboard access permissions
4. Enable background service

## Building

### Android
```bash
# Debug build
./scripts/build_android.sh

# Or manually with Gradle
cd android
./gradlew assembleDebug
```

### Windows
```bash
# Create executable
python scripts/build_windows.py

# Or run directly
python windows/src/main.py
```

## Testing

### Run All Tests
```bash
python scripts/run_all_tests.py
```

### Platform-Specific Tests
```bash
# Android tests
./scripts/test_android.sh

# Windows tests
python scripts/test_windows.py
```

## Network Protocol

### Device Discovery
```json
{
  "type": "discovery_request",
  "device_name": "My Device",
  "device_type": "android|windows",
  "device_id": "unique_device_id",
  "websocket_port": 8766
}
```

### Clipboard Sync
```json
{
  "type": "clipboard_update",
  "content": "clipboard text content",
  "timestamp": 1234567890,
  "device_info": {
    "device_name": "My Device",
    "device_id": "unique_device_id"
  }
}
```

## Troubleshooting

### Common Issues

1. **Devices not discovering each other**
   - Ensure both devices are on the same Wi-Fi network
   - Check firewall settings
   - Verify UDP port 8765 is not blocked

2. **Connection failures**
   - Check WebSocket port 8766 accessibility
   - Restart both applications
   - Try manual device addition by IP address

3. **Clipboard not syncing**
   - Verify clipboard permissions on Android
   - Check if sync is enabled in settings
   - Ensure stable network connection

## License

MIT License - see LICENSE file for details.

## Testing

1. Connect both devices to the same Wi-Fi network
2. Start Clipsy on both devices
3. Copy text on Device A
4. Verify text appears on Device B
5. Check clipboard history shows items from both devices

## License

MIT License - see LICENSE file for details
