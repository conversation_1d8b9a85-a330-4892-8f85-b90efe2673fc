$com/clipsy/android/ClipsyApplication.com/clipsy/android/ClipsyApplication$Companion+com/clipsy/android/data/model/ClipboardItem3com/clipsy/android/data/model/ClipboardItem$Creator5com/clipsy/android/data/model/ClipboardItem$Companion.com/clipsy/android/data/model/ConnectionStatus-com/clipsy/android/data/model/ConnectionState:com/clipsy/android/data/model/ConnectionState$WhenMappings$com/clipsy/android/data/model/Device,com/clipsy/android/data/model/Device$Creator.com/clipsy/android/data/model/Device$Companion6com/clipsy/android/data/repository/ClipboardRepositoryHcom/clipsy/android/data/repository/ClipboardRepository$getAllItemsSync$2Fcom/clipsy/android/data/repository/ClipboardRepository$getLatestItem$2@com/clipsy/android/data/repository/ClipboardRepository$addItem$2Bcom/clipsy/android/data/repository/ClipboardRepository$addItem$2$1Ccom/clipsy/android/data/repository/ClipboardRepository$deleteItem$2Ecom/clipsy/android/data/repository/ClipboardRepository$deleteItem$2$1Gcom/clipsy/android/data/repository/ClipboardRepository$deleteItemById$2Icom/clipsy/android/data/repository/ClipboardRepository$deleteItemById$2$1Fcom/clipsy/android/data/repository/ClipboardRepository$clearAllItems$2Mcom/clipsy/android/data/repository/ClipboardRepository$maintainHistoryLimit$2ycom/clipsy/android/data/repository/ClipboardRepository$maintainHistoryLimit$2$invokeSuspend$$inlined$sortedByDescending$1Ecom/clipsy/android/data/repository/ClipboardRepository$getItemCount$2Kcom/clipsy/android/data/repository/ClipboardRepository$isContentDuplicate$2Icom/clipsy/android/data/repository/ClipboardRepository$removeDuplicates$2ucom/clipsy/android/data/repository/ClipboardRepository$removeDuplicates$2$invokeSuspend$$inlined$sortedByDescending$1ucom/clipsy/android/data/repository/ClipboardRepository$removeDuplicates$2$invokeSuspend$$inlined$sortedByDescending$2Lcom/clipsy/android/data/repository/ClipboardRepository$generateContentHash$1@com/clipsy/android/data/repository/ClipboardRepository$Companion3com/clipsy/android/data/repository/DeviceRepositoryGcom/clipsy/android/data/repository/DeviceRepository$addOrUpdateDevice$2Bcom/clipsy/android/data/repository/DeviceRepository$removeDevice$2Fcom/clipsy/android/data/repository/DeviceRepository$removeDeviceByIp$2Ecom/clipsy/android/data/repository/DeviceRepository$addManualDevice$1Lcom/clipsy/android/data/repository/DeviceRepository$updateConnectionStatus$2Mcom/clipsy/android/data/repository/DeviceRepository$refreshConnectionStatus$2Ecom/clipsy/android/data/repository/DeviceRepository$clearAllDevices$2Jcom/clipsy/android/data/repository/DeviceRepository$removeOfflineDevices$2ncom/clipsy/android/data/repository/DeviceRepository$updateDiscoveredDevicesList$$inlined$compareByDescending$1kcom/clipsy/android/data/repository/DeviceRepository$updateDiscoveredDevicesList$$inlined$thenByDescending$1acom/clipsy/android/data/repository/DeviceRepository$updateDiscoveredDevicesList$$inlined$thenBy$1bcom/clipsy/android/data/repository/DeviceRepository$updateConnectedDevicesList$$inlined$sortedBy$1Dcom/clipsy/android/data/repository/DeviceRepository$startDiscovery$2Ecom/clipsy/android/data/repository/DeviceRepository$connectToDevice$2Jcom/clipsy/android/data/repository/DeviceRepository$disconnectFromDevice$2Ncom/clipsy/android/data/repository/DeviceRepository$forceReconnectAllDevices$2Ocom/clipsy/android/data/repository/DeviceRepository$clearDevicesAndRediscover$2Ocom/clipsy/android/data/repository/DeviceRepository$clearDevicesAndRediscover$1=com/clipsy/android/data/repository/DeviceRepository$Companion*com/clipsy/android/network/DeviceDiscovery;com/clipsy/android/network/DeviceDiscovery$startDiscovery$1=com/clipsy/android/network/DeviceDiscovery$startDiscovery$1$18com/clipsy/android/network/DeviceDiscovery$setupSocket$2>com/clipsy/android/network/DeviceDiscovery$startBroadcasting$1;com/clipsy/android/network/DeviceDiscovery$startListening$2Ccom/clipsy/android/network/DeviceDiscovery$sendDiscoveryBroadcast$2Ccom/clipsy/android/network/DeviceDiscovery$handleDiscoveryMessage$1Bcom/clipsy/android/network/DeviceDiscovery$sendDiscoveryResponse$2Ecom/clipsy/android/network/DeviceDiscovery$processDiscoveryResponse$1Acom/clipsy/android/network/DeviceDiscovery$startNetworkScanning$2Gcom/clipsy/android/network/DeviceDiscovery$startNetworkScanning$2$job$19com/clipsy/android/network/DeviceDiscovery$scanDeviceAt$24com/clipsy/android/network/DeviceDiscovery$Companion*com/clipsy/android/network/WebSocketClient<com/clipsy/android/network/WebSocketClient$connectToDevice$1Acom/clipsy/android/network/WebSocketClient$disconnectFromDevice$1Bcom/clipsy/android/network/WebSocketClient$sendClipboardToDevice$1?com/clipsy/android/network/WebSocketClient$broadcastClipboard$1Ccom/clipsy/android/network/WebSocketClient$handleClipboardMessage$1:com/clipsy/android/network/WebSocketClient$disconnectAll$1Ncom/clipsy/android/network/WebSocketClient$scheduleReconnection$reconnectJob$1Gcom/clipsy/android/network/WebSocketClient$startConnectionHealthCheck$1>com/clipsy/android/network/WebSocketClient$forceReconnectAll$3?com/clipsy/android/network/WebSocketClient$startKeepalivePing$1Lcom/clipsy/android/network/WebSocketClient$updateConnectionStatusDebounced$1Lcom/clipsy/android/network/WebSocketClient$updateConnectionStatusImmediate$14com/clipsy/android/network/WebSocketClient$CompanionBcom/clipsy/android/network/WebSocketClient$DeviceWebSocketListenerKcom/clipsy/android/network/WebSocketClient$DeviceWebSocketListener$onOpen$1Ncom/clipsy/android/network/WebSocketClient$DeviceWebSocketListener$onMessage$1Mcom/clipsy/android/network/WebSocketClient$DeviceWebSocketListener$onClosed$1Ncom/clipsy/android/network/WebSocketClient$DeviceWebSocketListener$onFailure$17com/clipsy/android/network/WebSocketClient$WhenMappings*com/clipsy/android/network/WebSocketServer8com/clipsy/android/network/WebSocketServer$startServer$17com/clipsy/android/network/WebSocketServer$stopServer$1?com/clipsy/android/network/WebSocketServer$broadcastClipboard$1Bcom/clipsy/android/network/WebSocketServer$sendClipboardToClient$1Ccom/clipsy/android/network/WebSocketServer$handleClipboardMessage$14com/clipsy/android/network/WebSocketServer$Companion@com/clipsy/android/network/WebSocketServer$ClipsyWebSocketServerIcom/clipsy/android/network/WebSocketServer$ClipsyWebSocketServer$onOpen$1Icom/clipsy/android/network/WebSocketServer$ClipsyWebSocketServer$onOpen$2Jcom/clipsy/android/network/WebSocketServer$ClipsyWebSocketServer$onClose$1Lcom/clipsy/android/network/WebSocketServer$ClipsyWebSocketServer$onMessage$1&com/clipsy/android/pairing/KnownDevice*com/clipsy/android/pairing/ConnectedDevice2com/clipsy/android/pairing/SimpleConnectionManagerJcom/clipsy/android/pairing/SimpleConnectionManager$loadKnownDevices$type$1Ccom/clipsy/android/pairing/SimpleConnectionManager$addKnownDevice$1Ncom/clipsy/android/pairing/SimpleConnectionManager$removeKnownDevice$removed$1<com/clipsy/android/pairing/SimpleConnectionManager$Companion/com/clipsy/android/service/ClipboardSyncService:com/clipsy/android/service/ClipboardSyncService$onCreate$1Hcom/clipsy/android/service/ClipboardSyncService$startNetworkComponents$1Jcom/clipsy/android/service/ClipboardSyncService$startNetworkComponents$1$1Jcom/clipsy/android/service/ClipboardSyncService$startNetworkComponents$1$2Gcom/clipsy/android/service/ClipboardSyncService$stopNetworkComponents$1Mcom/clipsy/android/service/ClipboardSyncService$startPeriodicClipboardCheck$1Ecom/clipsy/android/service/ClipboardSyncService$generateContentHash$1@com/clipsy/android/service/ClipboardSyncService$checkClipboard$1Gcom/clipsy/android/service/ClipboardSyncService$handleClipboardChange$1Gcom/clipsy/android/service/ClipboardSyncService$addToHistoryAndNotify$1@com/clipsy/android/service/ClipboardSyncService$refreshDevices$1Bcom/clipsy/android/service/ClipboardSyncService$handleManualSync$1Lcom/clipsy/android/service/ClipboardSyncService$startClipboardMonitoring$1$19com/clipsy/android/service/ClipboardSyncService$Companion;com/clipsy/android/service/ClipboardSyncService$LocalBinderCcom/clipsy/android/service/ClipboardSyncService$broadcastReceiver$1Ocom/clipsy/android/service/ClipboardSyncService$broadcastReceiver$1$onReceive$1"com/clipsy/android/ui/MainActivity5com/clipsy/android/ui/MainActivity$observeViewModel$15com/clipsy/android/ui/MainActivity$observeViewModel$25com/clipsy/android/ui/MainActivity$observeViewModel$3Dcom/clipsy/android/ui/MainActivity$sam$androidx_lifecycle_Observer$06com/clipsy/android/ui/MainActivity$serviceConnection$1*com/clipsy/android/ui/MainViewModelFactory&com/clipsy/android/ui/SettingsActivity7com/clipsy/android/ui/SettingsActivity$SettingsFragment:com/clipsy/android/ui/adapters/BluetoothStyleDeviceAdapterKcom/clipsy/android/ui/adapters/BluetoothStyleDeviceAdapter$DeviceViewHolderXcom/clipsy/android/ui/adapters/BluetoothStyleDeviceAdapter$DeviceViewHolder$WhenMappingsMcom/clipsy/android/ui/adapters/BluetoothStyleDeviceAdapter$DeviceDiffCallback6com/clipsy/android/ui/adapters/ClipboardHistoryAdapterHcom/clipsy/android/ui/adapters/ClipboardHistoryAdapter$HistoryViewHolderJcom/clipsy/android/ui/adapters/ClipboardHistoryAdapter$HistoryDiffCallback,com/clipsy/android/ui/adapters/DeviceAdapter=com/clipsy/android/ui/adapters/DeviceAdapter$DeviceViewHolderJcom/clipsy/android/ui/adapters/DeviceAdapter$DeviceViewHolder$WhenMappings?com/clipsy/android/ui/adapters/DeviceAdapter$DeviceDiffCallback/com/clipsy/android/ui/fragments/DevicesFragment]com/clipsy/android/ui/fragments/DevicesFragment$special$$inlined$activityViewModels$default$1]com/clipsy/android/ui/fragments/DevicesFragment$special$$inlined$activityViewModels$default$2]com/clipsy/android/ui/fragments/DevicesFragment$special$$inlined$activityViewModels$default$3Ccom/clipsy/android/ui/fragments/DevicesFragment$setupRecyclerView$1Ccom/clipsy/android/ui/fragments/DevicesFragment$setupRecyclerView$2Ccom/clipsy/android/ui/fragments/DevicesFragment$setupRecyclerView$3Ccom/clipsy/android/ui/fragments/DevicesFragment$setupRecyclerView$4@com/clipsy/android/ui/fragments/DevicesFragment$setupObservers$1@com/clipsy/android/ui/fragments/DevicesFragment$setupObservers$2@com/clipsy/android/ui/fragments/DevicesFragment$setupObservers$3Ecom/clipsy/android/ui/fragments/DevicesFragment$refreshDeviceStatus$19com/clipsy/android/ui/fragments/DevicesFragment$CompanionQcom/clipsy/android/ui/fragments/DevicesFragment$sam$androidx_lifecycle_Observer$0<com/clipsy/android/ui/fragments/DevicesFragment$WhenMappings/com/clipsy/android/ui/fragments/HistoryFragment]com/clipsy/android/ui/fragments/HistoryFragment$special$$inlined$activityViewModels$default$1]com/clipsy/android/ui/fragments/HistoryFragment$special$$inlined$activityViewModels$default$2]com/clipsy/android/ui/fragments/HistoryFragment$special$$inlined$activityViewModels$default$3;com/clipsy/android/ui/fragments/HistoryFragment$setupMenu$1Ccom/clipsy/android/ui/fragments/HistoryFragment$setupRecyclerView$1Ccom/clipsy/android/ui/fragments/HistoryFragment$setupRecyclerView$2Ccom/clipsy/android/ui/fragments/HistoryFragment$setupRecyclerView$3@com/clipsy/android/ui/fragments/HistoryFragment$setupObservers$1Qcom/clipsy/android/ui/fragments/HistoryFragment$sam$androidx_lifecycle_Observer$0.com/clipsy/android/ui/fragments/StatusFragment\com/clipsy/android/ui/fragments/StatusFragment$special$$inlined$activityViewModels$default$1\com/clipsy/android/ui/fragments/StatusFragment$special$$inlined$activityViewModels$default$2\com/clipsy/android/ui/fragments/StatusFragment$special$$inlined$activityViewModels$default$3?com/clipsy/android/ui/fragments/StatusFragment$setupObservers$1?com/clipsy/android/ui/fragments/StatusFragment$setupObservers$2?com/clipsy/android/ui/fragments/StatusFragment$setupObservers$3?com/clipsy/android/ui/fragments/StatusFragment$setupObservers$4?com/clipsy/android/ui/fragments/StatusFragment$setupObservers$5?com/clipsy/android/ui/fragments/StatusFragment$setupObservers$6Pcom/clipsy/android/ui/fragments/StatusFragment$sam$androidx_lifecycle_Observer$0,com/clipsy/android/ui/utils/ButtonStyleUtils8com/clipsy/android/ui/utils/ButtonStyleUtils$ButtonStyle9com/clipsy/android/ui/utils/ButtonStyleUtils$WhenMappings*com/clipsy/android/viewmodel/MainViewModel;com/clipsy/android/viewmodel/MainViewModel$refreshDevices$1<com/clipsy/android/viewmodel/MainViewModel$addManualDevice$19com/clipsy/android/viewmodel/MainViewModel$removeDevice$1Bcom/clipsy/android/viewmodel/MainViewModel$clearClipboardHistory$1@com/clipsy/android/viewmodel/MainViewModel$deleteClipboardItem$1>com/clipsy/android/viewmodel/MainViewModel$sendClipboardToPC$1Bcom/clipsy/android/viewmodel/MainViewModel$sendConnectionRequest$1<com/clipsy/android/viewmodel/MainViewModel$connectToDevice$1Acom/clipsy/android/viewmodel/MainViewModel$disconnectFromDevice$1Ecom/clipsy/android/viewmodel/MainViewModel$forceReconnectAllDevices$1Bcom/clipsy/android/viewmodel/MainViewModel$initializeWithContext$1Fcom/clipsy/android/service/ClipboardSyncService$updateLocalClipboard$1Fcom/clipsy/android/service/ClipboardSyncService$updateLocalClipboard$2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            