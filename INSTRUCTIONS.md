# How to Use Your Clipsy Logo

## Step 1: Save Your Logo
1. Save the logo image you provided as `logo_original.png` in this project directory
2. Make sure it's a high-quality PNG file

## Step 2: Generate All Icon Sizes
Run this command in your terminal/command prompt:

```bash
# On Windows
resize_logo.bat

# Or manually with Python
python resize_logo.py logo_original.png --all
```

## Step 3: The Generated Icons Will Be Placed In:

### Android App Icons
- `android/app/src/main/res/mipmap-*/ic_launcher.png` - Main app icons
- `android/app/src/main/res/drawable-*/ic_notification.png` - Notification icons

### Windows App Icons  
- `windows/app_icon.ico` - For Electron app
- `windows/icon_*.png` - Individual PNG sizes

### Web Icons
- `web/favicon.ico` - Browser favicon
- `web/icon_*.png` - Various web icon sizes

## Step 4: Update Your Apps

### For Android
The icons will be automatically placed in the correct Android resource directories. Your app should pick them up automatically.

### For Windows/Electron
Update your `package.json` build configuration to use `windows/app_icon.ico`

## Your Logo Design
Your logo has a beautiful flowing wave design with blue gradients on a dark background. It will work great for:
- ✅ App icons (the design is clear and recognizable at small sizes)
- ✅ Notification icons (distinctive shape)
- ✅ Branding consistency across platforms

The dark background with bright blue waves creates good contrast and will stand out well on device home screens and taskbars.
