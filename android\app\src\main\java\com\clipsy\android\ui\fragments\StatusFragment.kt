package com.clipsy.android.ui.fragments

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import com.clipsy.android.R
import com.clipsy.android.databinding.FragmentStatusBinding
import com.clipsy.android.service.ClipboardSyncService
import com.clipsy.android.ui.MainActivity

import com.clipsy.android.viewmodel.MainViewModel
/**
 * Fragment showing the current status of the Clipsy service and connections.
 */
class StatusFragment : Fragment() {
    
    private var _binding: FragmentStatusBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: MainViewModel by activityViewModels()
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentStatusBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupObservers()
        setupClickListeners()
    }

    override fun onResume() {
        super.onResume()
    }
    
    private fun setupObservers() {
        // Service status
        viewModel.isServiceRunning.observe(viewLifecycleOwner) { isRunning ->
            updateServiceStatus(isRunning)
        }

        // Connection status
        viewModel.connectionStatus.observe(viewLifecycleOwner) { status ->
            binding.textConnectionStatus.text = status
        }
        
        // Local IP address
        viewModel.localIpAddress.observe(viewLifecycleOwner) { ip ->
            binding.textLocalIp.text = if (ip.isNotEmpty()) ip else "Not available"
        }
        
        // Discovery status
        viewModel.isDiscovering.observe(viewLifecycleOwner) { isDiscovering ->
            updateDiscoveryStatus(isDiscovering)
        }
        
        // Connected devices count
        viewModel.connectedDevices?.observe(viewLifecycleOwner) { devices ->
            binding.textConnectedDevices.text = "${devices.size} device(s) connected"
        }

        // Discovered devices count
        viewModel.discoveredDevices?.observe(viewLifecycleOwner) { devices ->
            binding.textDiscoveredDevices.text = "${devices.size} device(s) found"
        }
    }
    
    private fun setupClickListeners() {
        binding.buttonRefreshDevices.setOnClickListener {
            viewModel.refreshDevices()
        }

        binding.cardServiceStatus.setOnClickListener {
            // Could open service settings or restart service
        }

        binding.cardNetworkInfo.setOnClickListener {
            // Could show detailed network information
        }



        // Force reconnect button removed from UI

        // Battery optimization and auto-start buttons removed from UI
    }


    
    private fun updateServiceStatus(isRunning: Boolean) {
        if (isRunning) {
            binding.textServiceStatus.text = "Service Running"
            binding.cardServiceStatus.setCardBackgroundColor(
                resources.getColor(R.color.clipsy_green_bright, null)
            )
        } else {
            binding.textServiceStatus.text = "Service Stopped"
            binding.cardServiceStatus.setCardBackgroundColor(
                resources.getColor(R.color.clipsy_card_dark, null)
            )
        }
    }
    
    private fun updateDiscoveryStatus(isDiscovering: Boolean) {
        if (isDiscovering) {
            binding.textDiscoveryStatus.text = getString(R.string.status_discovering)
            binding.progressDiscovery.visibility = View.VISIBLE
            binding.buttonRefreshDevices.isEnabled = false
        } else {
            // Show more meaningful status when not discovering
            val connectedCount = viewModel.getConnectedDeviceCount()
            val discoveredCount = viewModel.getDeviceCount()

            binding.textDiscoveryStatus.text = when {
                connectedCount > 0 -> "Ready - $connectedCount device(s) connected"
                discoveredCount > 0 -> "Ready - $discoveredCount device(s) found"
                else -> "Ready - No devices found"
            }
            binding.progressDiscovery.visibility = View.GONE
            binding.buttonRefreshDevices.isEnabled = true
        }
    }
    


    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
