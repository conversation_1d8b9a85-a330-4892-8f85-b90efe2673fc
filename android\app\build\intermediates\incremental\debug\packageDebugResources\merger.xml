<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="N:\new project\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="N:\new project\android\app\src\main\res"><file name="bottom_nav_color_selector" path="N:\new project\android\app\src\main\res\color\bottom_nav_color_selector.xml" qualifiers="" type="color"/><file name="button_background_primary_selector" path="N:\new project\android\app\src\main\res\color\button_background_primary_selector.xml" qualifiers="" type="color"/><file name="button_text_primary_selector" path="N:\new project\android\app\src\main\res\color\button_text_primary_selector.xml" qualifiers="" type="color"/><file name="button_text_secondary_selector" path="N:\new project\android\app\src\main\res\color\button_text_secondary_selector.xml" qualifiers="" type="color"/><file name="switch_thumb_color" path="N:\new project\android\app\src\main\res\color\switch_thumb_color.xml" qualifiers="" type="color"/><file name="switch_track_color" path="N:\new project\android\app\src\main\res\color\switch_track_color.xml" qualifiers="" type="color"/><file name="badge_background" path="N:\new project\android\app\src\main\res\drawable\badge_background.xml" qualifiers="" type="drawable"/><file name="button_secondary" path="N:\new project\android\app\src\main\res\drawable\button_secondary.xml" qualifiers="" type="drawable"/><file name="circle_background" path="N:\new project\android\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="circle_indicator" path="N:\new project\android\app\src\main\res\drawable\circle_indicator.xml" qualifiers="" type="drawable"/><file name="code_background" path="N:\new project\android\app\src\main\res\drawable\code_background.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="N:\new project\android\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="ic_arrow_right" path="N:\new project\android\app\src\main\res\drawable\ic_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_computer" path="N:\new project\android\app\src\main\res\drawable\ic_computer.xml" qualifiers="" type="drawable"/><file name="ic_devices_vector" path="N:\new project\android\app\src\main\res\drawable\ic_devices_vector.xml" qualifiers="" type="drawable"/><file name="ic_history_vector" path="N:\new project\android\app\src\main\res\drawable\ic_history_vector.xml" qualifiers="" type="drawable"/><file name="ic_info" path="N:\new project\android\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="N:\new project\android\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_phone" path="N:\new project\android\app\src\main\res\drawable\ic_phone.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="N:\new project\android\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_scan_animation" path="N:\new project\android\app\src\main\res\drawable\ic_scan_animation.xml" qualifiers="" type="drawable"/><file name="ic_search" path="N:\new project\android\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_send_arrow" path="N:\new project\android\app\src\main\res\drawable\ic_send_arrow.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="N:\new project\android\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_status_vector" path="N:\new project\android\app\src\main\res\drawable\ic_status_vector.xml" qualifiers="" type="drawable"/><file name="ic_sync" path="N:\new project\android\app\src\main\res\drawable\ic_sync.xml" qualifiers="" type="drawable"/><file name="ic_sync_to_pc" path="N:\new project\android\app\src\main\res\drawable\ic_sync_to_pc.xml" qualifiers="" type="drawable"/><file name="ic_clipsy_devices" path="N:\new project\android\app\src\main\res\drawable-hdpi\ic_clipsy_devices.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_clipsy_history" path="N:\new project\android\app\src\main\res\drawable-hdpi\ic_clipsy_history.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_clipsy_refresh" path="N:\new project\android\app\src\main\res\drawable-hdpi\ic_clipsy_refresh.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_clipsy_settings" path="N:\new project\android\app\src\main\res\drawable-hdpi\ic_clipsy_settings.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_clipsy_status" path="N:\new project\android\app\src\main\res\drawable-hdpi\ic_clipsy_status.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_notification" path="N:\new project\android\app\src\main\res\drawable-hdpi\ic_notification.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_clipsy_devices" path="N:\new project\android\app\src\main\res\drawable-mdpi\ic_clipsy_devices.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_clipsy_history" path="N:\new project\android\app\src\main\res\drawable-mdpi\ic_clipsy_history.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_clipsy_refresh" path="N:\new project\android\app\src\main\res\drawable-mdpi\ic_clipsy_refresh.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_clipsy_settings" path="N:\new project\android\app\src\main\res\drawable-mdpi\ic_clipsy_settings.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_clipsy_status" path="N:\new project\android\app\src\main\res\drawable-mdpi\ic_clipsy_status.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_notification" path="N:\new project\android\app\src\main\res\drawable-mdpi\ic_notification.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_clipsy_devices" path="N:\new project\android\app\src\main\res\drawable-xhdpi\ic_clipsy_devices.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_clipsy_history" path="N:\new project\android\app\src\main\res\drawable-xhdpi\ic_clipsy_history.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_clipsy_refresh" path="N:\new project\android\app\src\main\res\drawable-xhdpi\ic_clipsy_refresh.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_clipsy_settings" path="N:\new project\android\app\src\main\res\drawable-xhdpi\ic_clipsy_settings.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_clipsy_status" path="N:\new project\android\app\src\main\res\drawable-xhdpi\ic_clipsy_status.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_notification" path="N:\new project\android\app\src\main\res\drawable-xhdpi\ic_notification.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_clipsy_devices" path="N:\new project\android\app\src\main\res\drawable-xxhdpi\ic_clipsy_devices.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_clipsy_history" path="N:\new project\android\app\src\main\res\drawable-xxhdpi\ic_clipsy_history.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_clipsy_refresh" path="N:\new project\android\app\src\main\res\drawable-xxhdpi\ic_clipsy_refresh.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_clipsy_settings" path="N:\new project\android\app\src\main\res\drawable-xxhdpi\ic_clipsy_settings.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_clipsy_status" path="N:\new project\android\app\src\main\res\drawable-xxhdpi\ic_clipsy_status.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_notification" path="N:\new project\android\app\src\main\res\drawable-xxhdpi\ic_notification.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_clipsy_devices" path="N:\new project\android\app\src\main\res\drawable-xxxhdpi\ic_clipsy_devices.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_clipsy_history" path="N:\new project\android\app\src\main\res\drawable-xxxhdpi\ic_clipsy_history.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_clipsy_refresh" path="N:\new project\android\app\src\main\res\drawable-xxxhdpi\ic_clipsy_refresh.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_clipsy_settings" path="N:\new project\android\app\src\main\res\drawable-xxxhdpi\ic_clipsy_settings.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_clipsy_status" path="N:\new project\android\app\src\main\res\drawable-xxxhdpi\ic_clipsy_status.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_notification" path="N:\new project\android\app\src\main\res\drawable-xxxhdpi\ic_notification.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="activity_main" path="N:\new project\android\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_settings" path="N:\new project\android\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="dialog_add_manual_device" path="N:\new project\android\app\src\main\res\layout\dialog_add_manual_device.xml" qualifiers="" type="layout"/><file name="dialog_pairing" path="N:\new project\android\app\src\main\res\layout\dialog_pairing.xml" qualifiers="" type="layout"/><file name="fragment_devices" path="N:\new project\android\app\src\main\res\layout\fragment_devices.xml" qualifiers="" type="layout"/><file name="fragment_history" path="N:\new project\android\app\src\main\res\layout\fragment_history.xml" qualifiers="" type="layout"/><file name="fragment_status" path="N:\new project\android\app\src\main\res\layout\fragment_status.xml" qualifiers="" type="layout"/><file name="item_clipboard_history" path="N:\new project\android\app\src\main\res\layout\item_clipboard_history.xml" qualifiers="" type="layout"/><file name="item_device" path="N:\new project\android\app\src\main\res\layout\item_device.xml" qualifiers="" type="layout"/><file name="item_device_bluetooth_style" path="N:\new project\android\app\src\main\res\layout\item_device_bluetooth_style.xml" qualifiers="" type="layout"/><file name="bottom_navigation_menu" path="N:\new project\android\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file name="main_menu" path="N:\new project\android\app\src\main\res\menu\main_menu.xml" qualifiers="" type="menu"/><file name="menu_history" path="N:\new project\android\app\src\main\res\menu\menu_history.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="N:\new project\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="N:\new project\android\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="N:\new project\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="N:\new project\android\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="N:\new project\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="N:\new project\android\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="N:\new project\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="N:\new project\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="N:\new project\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="N:\new project\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="N:\new project\android\app\src\main\res\values\arrays.xml" qualifiers=""><string-array name="history_limit_entries">
        <item>10 items</item>
        <item>20 items</item>
        <item>50 items</item>
        <item>100 items</item>
        <item>Unlimited</item>
    </string-array><string-array name="history_limit_values">
        <item>10</item>
        <item>20</item>
        <item>50</item>
        <item>100</item>
        <item>-1</item>
    </string-array></file><file path="N:\new project\android\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="clipsy_primary">#FF2A2A2A</color><color name="clipsy_primary_dark">#FF1A1A1A</color><color name="clipsy_accent">#FF4A90E2</color><color name="bluetooth_blue">#FF2196F3</color><color name="clipsy_background">#FF121212</color><color name="clipsy_surface">#FF1E1E1E</color><color name="clipsy_card_background">#FF2A2A2A</color><color name="clipsy_card_dark">#FF1A1A1A</color><color name="clipsy_green_bright">#FF4CAF50</color><color name="clipsy_text_primary">#FFFFFFFF</color><color name="clipsy_text_secondary">#FFAAAAAA</color><color name="clipsy_error">#FFF44336</color><color name="status_connected">#FF4CAF50</color><color name="status_disconnected">#FFF44336</color><color name="status_connecting">#FFFF9800</color><color name="source_local">#FF2196F3</color><color name="source_remote">#FF4CAF50</color><color name="primary_color">#FF4A90E2</color><color name="secondary_color">#FF666666</color><color name="background_color">#FF1E1E1E</color><color name="text_primary">#FFFFFFFF</color><color name="text_secondary">#FFAAAAAA</color><color name="border_color">#FF444444</color><color name="code_background">#FF2A2A2A</color><color name="button_text_primary">#FFFFFFFF</color><color name="button_text_secondary">#FF4A90E2</color><color name="button_text_disabled">#FF666666</color><color name="button_background_primary">#FF4A90E2</color><color name="button_background_secondary">#FF2A2A2A</color><color name="button_background_disabled">#FF1A1A1A</color><color name="button_stroke_primary">#FF4A90E2</color><color name="button_stroke_secondary">#FF666666</color><color name="high_contrast_text">#FFFFFFFF</color><color name="high_contrast_background">#FF000000</color><color name="medium_contrast_text">#FFCCCCCC</color><color name="medium_contrast_background">#FF333333</color></file><file path="N:\new project\android\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="button_corner_radius">12dp</dimen><dimen name="button_corner_radius_small">8dp</dimen><dimen name="button_elevation">2dp</dimen><dimen name="button_stroke_width">2dp</dimen><dimen name="button_stroke_width_small">1dp</dimen><dimen name="text_size_button">14sp</dimen><dimen name="text_size_button_small">12sp</dimen><dimen name="button_padding_horizontal">24dp</dimen><dimen name="button_padding_horizontal_small">16dp</dimen><dimen name="button_padding_vertical">12dp</dimen><dimen name="button_padding_vertical_small">8dp</dimen><dimen name="button_min_height">48dp</dimen><dimen name="button_min_height_small">36dp</dimen><dimen name="button_min_width">80dp</dimen></file><file path="N:\new project\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Clipsy</string><string name="nav_status">Status</string><string name="nav_devices">Devices</string><string name="nav_history">History</string><string name="action_settings">Settings</string><string name="action_refresh">Refresh</string><string name="status_service_running">Service Running</string><string name="status_service_stopped">Service Stopped</string><string name="status_connected">Connected</string><string name="status_disconnected">Disconnected</string><string name="status_discovering">Discovering devices…</string><string name="devices_title">Discovered Devices</string><string name="devices_empty">No devices found</string><string name="device_connect">Connect</string><string name="device_disconnect">Disconnect</string><string name="device_connected">Connected</string><string name="device_disconnected">Disconnected</string><string name="device_add_manual">Add Manual</string><string name="device_manual_ip_hint">Enter IP address</string><string name="device_manual_name_hint">Device name (optional)</string><string name="history_title">Clipboard History</string><string name="history_empty">No clipboard history</string><string name="history_clear">Clear History</string><string name="history_clear_confirm">Are you sure you want to clear the clipboard history?</string><string name="history_item_copy">Copy to clipboard</string><string name="history_item_delete">Delete</string><string name="settings_title">Settings</string><string name="settings_clipboard">Clipboard</string><string name="settings_sync_enabled">Enable clipboard sync</string><string name="settings_sync_enabled_summary">Automatically sync clipboard with other devices</string><string name="settings_history_enabled">Enable clipboard history</string><string name="settings_history_enabled_summary">Store clipboard history locally</string><string name="settings_history_limit">History limit</string><string name="settings_history_limit_summary">Maximum number of items to store</string><string name="settings_network">Network</string><string name="settings_device_name">Device name</string><string name="settings_device_name_summary">Name shown to other devices</string><string name="settings_auto_discovery">Auto discovery</string><string name="settings_auto_discovery_summary">Automatically discover devices on network</string><string name="settings_startup">Startup</string><string name="settings_auto_start">Auto start</string><string name="settings_auto_start_summary">Start service automatically on boot</string><string name="notification_service_title">Clipsy is running</string><string name="notification_service_text">Clipboard sync is active</string><string name="notification_clipboard_synced">Clipboard synced from %s</string><string name="error_permission_denied">Permission denied</string><string name="error_network_unavailable">Network unavailable</string><string name="error_connection_failed">Connection failed</string><string name="error_invalid_ip">Invalid IP address</string><string name="ok">OK</string><string name="cancel">Cancel</string><string name="yes">Yes</string><string name="no">No</string><string name="add">Add</string><string name="delete">Delete</string><string name="copy">Copy</string><string name="share">Share</string><string name="action_clear_history">Clear History</string><string name="action_copy">Copy</string><string name="action_delete">Delete</string><string name="action_connect">Connect</string><string name="action_remove">Remove</string><string name="action_pair">Pair</string><string name="action_unpair">Unpair</string><string name="pref_category_sync">Synchronization</string><string name="pref_auto_sync_title">Auto Sync</string><string name="pref_auto_sync_summary">Automatically sync clipboard changes</string><string name="pref_sync_interval_title">Sync Interval</string><string name="pref_sync_interval_summary">Clipboard check interval in milliseconds</string><string name="pref_category_history">History</string><string name="pref_history_limit_title">History Limit</string><string name="pref_history_limit_summary">Maximum number of clipboard items to keep</string><string name="pref_auto_cleanup_title">Auto Cleanup</string><string name="pref_auto_cleanup_summary">Automatically remove old clipboard items</string><string name="pref_category_network">Network</string><string name="pref_discovery_port_title">Discovery Port</string><string name="pref_discovery_port_summary">UDP port for device discovery</string><string name="pref_websocket_port_title">WebSocket Port</string><string name="pref_websocket_port_summary">TCP port for WebSocket connections</string><string name="pref_category_about">About</string><string name="pref_app_version_title">Version</string><string name="app_version">1.0.0</string></file><file path="N:\new project\android\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Clipsy" parent="Theme.Material3.Dark.NoActionBar">
        
        <item name="colorPrimary">@color/clipsy_primary</item>
        <item name="colorPrimaryVariant">@color/clipsy_primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/clipsy_accent</item>
        <item name="colorSecondaryVariant">@color/clipsy_accent</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:colorBackground">@color/clipsy_background</item>
        <item name="colorSurface">@color/clipsy_surface</item>
        <item name="colorOnSurface">@color/white</item>
        <item name="colorOnBackground">@color/white</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/white</item>
        <item name="actionMenuTextColor">@color/white</item>

        
        <item name="materialButtonStyle">@style/Button.Clipsy.Primary</item>
        <item name="materialButtonOutlinedStyle">@style/Button.Clipsy.Secondary</item>
        <item name="borderlessButtonStyle">@style/Button.Clipsy.Tertiary</item>

        
        <item name="android:buttonStyle">@style/Button.Clipsy.Primary</item>

        
        <item name="alertDialogTheme">@style/AlertDialog.Clipsy</item>
        <item name="materialAlertDialogTheme">@style/AlertDialog.Clipsy</item>

        
    </style><style name="BottomNavActiveIndicator" parent="Widget.Material3.BottomNavigationView.ActiveIndicator">
        <item name="android:color">#FF3A3A3A</item>
        <item name="android:alpha">0.9</item>
    </style><style name="Theme.Clipsy.Settings" parent="Theme.Clipsy">
        <item name="preferenceTheme">@style/PreferenceThemeOverlay.Clipsy</item>
        <item name="android:textColorPrimary">@color/clipsy_text_primary</item>
        <item name="android:textColorSecondary">@color/clipsy_text_secondary</item>
    </style><style name="PreferenceThemeOverlay.Clipsy" parent="PreferenceThemeOverlay.v14.Material">
        <item name="android:textColorPrimary">@color/clipsy_text_primary</item>
        <item name="android:textColorSecondary">@color/clipsy_text_primary</item>
        <item name="android:colorBackground">@color/clipsy_background</item>
        <item name="colorSurface">@color/clipsy_card_dark</item>
        <item name="colorOnSurface">@color/clipsy_text_primary</item>
        <item name="colorPrimary">@color/clipsy_text_primary</item>
        <item name="colorOnPrimary">@color/clipsy_text_primary</item>
        <item name="colorAccent">@color/clipsy_text_primary</item>
    </style><style name="ShapeAppearanceOverlay.Clipsy.FloatingActionButton" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style><style name="Button.Clipsy.Primary" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/button_text_primary_selector</item>
        <item name="backgroundTint">@color/button_background_primary_selector</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:elevation">2dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="Button.Clipsy.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="strokeColor">@color/white</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="Button.Clipsy.Tertiary" parent="Widget.Material3.Button.TextButton">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="Button.Clipsy.Small" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/button_background_secondary</item>
        <item name="strokeColor">@color/white</item>
        <item name="strokeWidth">1dp</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:minHeight">36dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="Button.Clipsy.Danger" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/high_contrast_text</item>
        <item name="backgroundTint">@color/clipsy_error</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:elevation">2dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="Button.Clipsy.Success" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/high_contrast_text</item>
        <item name="backgroundTint">@color/status_connected</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:elevation">2dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="AlertDialog.Clipsy" parent="ThemeOverlay.Material3.MaterialAlertDialog">
        <item name="android:background">@color/clipsy_card_dark</item>
        <item name="backgroundTint">@color/clipsy_card_dark</item>
        <item name="colorPrimary">@color/white</item>
        <item name="colorOnSurface">@color/white</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/white</item>
        <item name="materialAlertDialogTitleTextStyle">@style/AlertDialog.Clipsy.Title</item>
        <item name="materialAlertDialogBodyTextStyle">@style/AlertDialog.Clipsy.Body</item>
        <item name="buttonBarPositiveButtonStyle">@style/AlertDialog.Clipsy.Button</item>
        <item name="buttonBarNegativeButtonStyle">@style/AlertDialog.Clipsy.Button</item>
        <item name="buttonBarNeutralButtonStyle">@style/AlertDialog.Clipsy.Button</item>
    </style><style name="AlertDialog.Clipsy.Title" parent="MaterialAlertDialog.Material3.Title.Text">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="AlertDialog.Clipsy.Body" parent="MaterialAlertDialog.Material3.Body.Text">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
    </style><style name="AlertDialog.Clipsy.Button" parent="Widget.Material3.Button.TextButton.Dialog">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="rippleColor">@color/white</item>
    </style></file><file name="backup_rules" path="N:\new project\android\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="N:\new project\android\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="N:\new project\android\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file name="preferences" path="N:\new project\android\app\src\main\res\xml\preferences.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="N:\new project\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="N:\new project\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="N:\new project\android\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="N:\new project\android\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>