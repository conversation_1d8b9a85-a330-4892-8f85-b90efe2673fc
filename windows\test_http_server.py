#!/usr/bin/env python3
"""
Simple HTTP Server Test
Tests if we can create a basic HTTP server on the same port
"""

import asyncio
import logging
import sys
from aiohttp import web, WSMsgType
import aiohttp_cors
import json
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def websocket_handler(request):
    """WebSocket handler using aiohttp"""
    ws = web.WebSocketResponse()
    await ws.prepare(request)
    
    client_ip = request.remote
    logger.info(f"✅ WebSocket client connected: {client_ip}")
    
    # Send welcome message
    welcome_msg = {
        "type": "welcome",
        "message": "Hello from aiohttp WebSocket server!",
        "timestamp": datetime.now().isoformat()
    }
    logger.info(f"📤 Sending welcome: {welcome_msg}")
    await ws.send_str(json.dumps(welcome_msg))
    
    try:
        async for msg in ws:
            if msg.type == WSMsgType.TEXT:
                logger.info(f"📥 Received: {msg.data}")
                
                try:
                    data = json.loads(msg.data)
                    response = {
                        "type": "echo",
                        "original": data,
                        "timestamp": datetime.now().isoformat()
                    }
                    logger.info(f"📤 Echoing back: {response}")
                    await ws.send_str(json.dumps(response))
                except json.JSONDecodeError:
                    logger.error(f"❌ Invalid JSON: {msg.data}")
                    
            elif msg.type == WSMsgType.ERROR:
                logger.error(f"❌ WebSocket error: {ws.exception()}")
                break
                
    except Exception as e:
        logger.error(f"❌ Error in WebSocket handler: {e}")
    finally:
        logger.info(f"Client disconnected: {client_ip}")
    
    return ws

async def http_handler(request):
    """Simple HTTP handler"""
    return web.Response(text="WebSocket test server is running!")

async def create_app():
    """Create the web application"""
    app = web.Application()
    
    # Add routes
    app.router.add_get('/', http_handler)
    app.router.add_get('/ws', websocket_handler)
    
    # Enable CORS
    cors = aiohttp_cors.setup(app, defaults={
        "*": aiohttp_cors.ResourceOptions(
            allow_credentials=True,
            expose_headers="*",
            allow_headers="*",
            allow_methods="*"
        )
    })
    
    # Add CORS to all routes
    for route in list(app.router.routes()):
        cors.add(route)
    
    return app

async def main():
    """Main function"""
    host = "*************"
    port = 8767
    
    logger.info(f"🚀 Starting aiohttp WebSocket server on {host}:{port}")
    
    try:
        app = await create_app()
        
        runner = web.AppRunner(app)
        await runner.setup()
        
        site = web.TCPSite(runner, host, port)
        await site.start()
        
        logger.info(f"✅ Server listening on {host}:{port}")
        logger.info(f"HTTP endpoint: http://{host}:{port}/")
        logger.info(f"WebSocket endpoint: ws://{host}:{port}/ws")
        logger.info("Server is ready to accept connections...")
        
        # Keep the server running
        while True:
            await asyncio.sleep(1)
            
    except Exception as e:
        logger.error(f"❌ Failed to start server: {e}")
        raise

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)
