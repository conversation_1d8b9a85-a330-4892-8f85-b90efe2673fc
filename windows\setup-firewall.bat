@echo off
REM Clipsy Windows Firewall Setup Script
REM This script configures Windows Firewall to allow Clipsy PC application
REM Run as Administrator for proper permissions

echo ========================================
echo    Clipsy Windows Firewall Setup
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Running with Administrator privileges...
    echo.
) else (
    echo [ERROR] This script must be run as Administrator!
    echo [INFO] Right-click on this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Define Clipsy application paths
set "CLIPSY_EXE=%~dp0src\dist\Clipsy.exe"
set "CLIPSY_CLEANED_EXE=%~dp0src\dist\ClipsyCleanedCode.exe"
set "CLIPSY_PYTHON=%~dp0src\main.py"

echo [INFO] Configuring Windows Firewall for Clipsy...
echo.

REM Remove any existing Clipsy firewall rules first
echo [STEP 1] Removing existing Clipsy firewall rules...
netsh advfirewall firewall delete rule name="Clipsy PC App" >nul 2>&1
netsh advfirewall firewall delete rule name="Clipsy PC App - Inbound" >nul 2>&1
netsh advfirewall firewall delete rule name="Clipsy PC App - Outbound" >nul 2>&1
netsh advfirewall firewall delete rule name="Clipsy Executable" >nul 2>&1
netsh advfirewall firewall delete rule name="Clipsy Python Script" >nul 2>&1
echo [OK] Existing rules removed.
echo.

REM Add firewall rules for Clipsy executable (if exists)
if exist "%CLIPSY_EXE%" (
    echo [STEP 2] Adding firewall rules for Clipsy.exe...
    
    REM Inbound rule for Clipsy.exe
    netsh advfirewall firewall add rule name="Clipsy PC App - Inbound" dir=in action=allow program="%CLIPSY_EXE%" enable=yes profile=any protocol=any
    if %errorLevel% == 0 (
        echo [OK] Inbound rule added for Clipsy.exe
    ) else (
        echo [ERROR] Failed to add inbound rule for Clipsy.exe
    )
    
    REM Outbound rule for Clipsy.exe
    netsh advfirewall firewall add rule name="Clipsy PC App - Outbound" dir=out action=allow program="%CLIPSY_EXE%" enable=yes profile=any protocol=any
    if %errorLevel% == 0 (
        echo [OK] Outbound rule added for Clipsy.exe
    ) else (
        echo [ERROR] Failed to add outbound rule for Clipsy.exe
    )
    echo.
) else (
    echo [INFO] Clipsy.exe not found at: %CLIPSY_EXE%
    echo.
)

REM Add firewall rules for ClipsyCleanedCode.exe (if exists)
if exist "%CLIPSY_CLEANED_EXE%" (
    echo [STEP 3] Adding firewall rules for ClipsyCleanedCode.exe...
    
    REM Inbound rule for ClipsyCleanedCode.exe
    netsh advfirewall firewall add rule name="Clipsy Cleaned - Inbound" dir=in action=allow program="%CLIPSY_CLEANED_EXE%" enable=yes profile=any protocol=any
    if %errorLevel% == 0 (
        echo [OK] Inbound rule added for ClipsyCleanedCode.exe
    ) else (
        echo [ERROR] Failed to add inbound rule for ClipsyCleanedCode.exe
    )
    
    REM Outbound rule for ClipsyCleanedCode.exe
    netsh advfirewall firewall add rule name="Clipsy Cleaned - Outbound" dir=out action=allow program="%CLIPSY_CLEANED_EXE%" enable=yes profile=any protocol=any
    if %errorLevel% == 0 (
        echo [OK] Outbound rule added for ClipsyCleanedCode.exe
    ) else (
        echo [ERROR] Failed to add outbound rule for ClipsyCleanedCode.exe
    )
    echo.
) else (
    echo [INFO] ClipsyCleanedCode.exe not found at: %CLIPSY_CLEANED_EXE%
    echo.
)

REM Add firewall rules for Python (for development/script mode)
echo [STEP 4] Adding firewall rules for Python (development mode)...

REM Find Python executable
for /f "tokens=*" %%i in ('where python 2^>nul') do set "PYTHON_EXE=%%i" & goto :found_python
for /f "tokens=*" %%i in ('where python3 2^>nul') do set "PYTHON_EXE=%%i" & goto :found_python
for /f "tokens=*" %%i in ('where py 2^>nul') do set "PYTHON_EXE=%%i" & goto :found_python

echo [INFO] Python not found in PATH, skipping Python firewall rules.
goto :skip_python

:found_python
echo [INFO] Found Python at: %PYTHON_EXE%

REM Inbound rule for Python
netsh advfirewall firewall add rule name="Clipsy Python - Inbound" dir=in action=allow program="%PYTHON_EXE%" enable=yes profile=any protocol=any
if %errorLevel% == 0 (
    echo [OK] Inbound rule added for Python
) else (
    echo [ERROR] Failed to add inbound rule for Python
)

REM Outbound rule for Python
netsh advfirewall firewall add rule name="Clipsy Python - Outbound" dir=out action=allow program="%PYTHON_EXE%" enable=yes profile=any protocol=any
if %errorLevel% == 0 (
    echo [OK] Outbound rule added for Python
) else (
    echo [ERROR] Failed to add outbound rule for Python
)

:skip_python
echo.

REM Add specific port rules for Clipsy communication
echo [STEP 5] Adding port-specific firewall rules...

REM WebSocket port (8765) - Inbound
netsh advfirewall firewall add rule name="Clipsy WebSocket Port - Inbound" dir=in action=allow protocol=TCP localport=8765 enable=yes profile=any
if %errorLevel% == 0 (
    echo [OK] Inbound rule added for WebSocket port 8765
) else (
    echo [ERROR] Failed to add inbound rule for port 8765
)

REM WebSocket port (8765) - Outbound
netsh advfirewall firewall add rule name="Clipsy WebSocket Port - Outbound" dir=out action=allow protocol=TCP localport=8765 enable=yes profile=any
if %errorLevel% == 0 (
    echo [OK] Outbound rule added for WebSocket port 8765
) else (
    echo [ERROR] Failed to add outbound rule for port 8765
)

REM Discovery port (12345) - Inbound UDP
netsh advfirewall firewall add rule name="Clipsy Discovery Port - Inbound" dir=in action=allow protocol=UDP localport=12345 enable=yes profile=any
if %errorLevel% == 0 (
    echo [OK] Inbound rule added for Discovery port 12345 (UDP)
) else (
    echo [ERROR] Failed to add inbound rule for port 12345 (UDP)
)

REM Discovery port (12345) - Outbound UDP
netsh advfirewall firewall add rule name="Clipsy Discovery Port - Outbound" dir=out action=allow protocol=UDP localport=12345 enable=yes profile=any
if %errorLevel% == 0 (
    echo [OK] Outbound rule added for Discovery port 12345 (UDP)
) else (
    echo [ERROR] Failed to add outbound rule for port 12345 (UDP)
)

echo.

REM Display current Clipsy firewall rules
echo [STEP 6] Verifying firewall rules...
echo.
echo Current Clipsy firewall rules:
netsh advfirewall firewall show rule name=all | findstr /i "clipsy"
echo.

echo ========================================
echo    Firewall Setup Complete!
echo ========================================
echo.
echo [SUCCESS] Windows Firewall has been configured for Clipsy.
echo.
echo The following rules have been added:
echo - Clipsy executable programs (inbound/outbound)
echo - Python interpreter (for development mode)
echo - WebSocket port 8765 (TCP)
echo - Discovery port 12345 (UDP)
echo.
echo You can now run Clipsy without firewall blocking issues.
echo.
echo To remove these rules later, run: remove-firewall.bat
echo.
pause
