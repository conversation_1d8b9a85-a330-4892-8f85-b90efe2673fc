from PIL import Image, ImageDraw, ImageFont
import os

# Create a simple test logo
def create_test_logo():
    # Create a 512x512 image with a blue background
    size = 512
    img = Image.new('RGBA', (size, size), (0, 120, 215, 255))  # Blue background
    draw = ImageDraw.Draw(img)
    
    # Draw a white circle
    circle_margin = 50
    draw.ellipse([circle_margin, circle_margin, size-circle_margin, size-circle_margin], 
                 fill=(255, 255, 255, 255), outline=(0, 0, 0, 255), width=5)
    
    # Try to add text
    try:
        # Try to use a system font
        font_size = 80
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", font_size)
            except:
                font = ImageFont.load_default()
        
        # Add "C" for Clipsy
        text = "C"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - 20
        
        draw.text((x, y), text, fill=(0, 120, 215, 255), font=font)
        
    except Exception as e:
        print(f"Could not add text: {e}")
    
    # Save the test logo
    img.save('logo_original.png', 'PNG')
    print("Test logo created as logo_original.png")
    print(f"Size: {img.size}")

if __name__ == "__main__":
    create_test_logo()
