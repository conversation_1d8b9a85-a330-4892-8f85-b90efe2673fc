{"logs": [{"outputFile": "com.clipsy.android.app-mergeDebugResources-55:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\60faa6f29802d848c65e1c1909fcb4a3\\transformed\\appcompat-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,435,541,646,732,842,963,1043,1120,1211,1304,1399,1493,1593,1686,1781,1889,1980,2071,2154,2268,2376,2476,2590,2697,2805,2965,10064", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "430,536,641,727,837,958,1038,1115,1206,1299,1394,1488,1588,1681,1776,1884,1975,2066,2149,2263,2371,2471,2585,2692,2800,2960,3059,10143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f959e0cc303079f1bb4b6f18f6d78dd7\\transformed\\preference-1.2.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,345,485,654,736", "endColumns": "71,87,79,139,168,81,77", "endOffsets": "172,260,340,480,649,731,809"}, "to": {"startLines": "48,50,111,113,116,117,118", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4595,4733,9758,9924,10249,10418,10500", "endColumns": "71,87,79,139,168,81,77", "endOffsets": "4662,4816,9833,10059,10413,10495,10573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\724090e78494501a803eec31dba02c36\\transformed\\material-1.10.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,423,506,628,738,833,966,1055,1121,1218,1298,1360,1449,1512,1577,1636,1709,1772,1826,1954,2011,2073,2127,2200,2343,2427,2515,2651,2739,2827,2963,3048,3125,3178,3229,3295,3370,3446,3532,3611,3688,3764,3841,3915,4027,4118,4193,4284,4376,4450,4537,4628,4683,4765,4831,4914,5000,5062,5126,5189,5259,5376,5488,5599,5709,5766,5821", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,78,82,121,109,94,132,88,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,87,135,87,87,135,84,76,52,50,65,74,75,85,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85", "endOffsets": "260,339,418,501,623,733,828,961,1050,1116,1213,1293,1355,1444,1507,1572,1631,1704,1767,1821,1949,2006,2068,2122,2195,2338,2422,2510,2646,2734,2822,2958,3043,3120,3173,3224,3290,3365,3441,3527,3606,3683,3759,3836,3910,4022,4113,4188,4279,4371,4445,4532,4623,4678,4760,4826,4909,4995,5057,5121,5184,5254,5371,5483,5594,5704,5761,5816,5902"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3064,3143,3222,3305,3427,4278,4373,4506,4667,4821,4918,4998,5060,5149,5212,5277,5336,5409,5472,5526,5654,5711,5773,5827,5900,6043,6127,6215,6351,6439,6527,6663,6748,6825,6878,6929,6995,7070,7146,7232,7311,7388,7464,7541,7615,7727,7818,7893,7984,8076,8150,8237,8328,8383,8465,8531,8614,8700,8762,8826,8889,8959,9076,9188,9299,9409,9466,9838", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "endColumns": "12,78,78,82,121,109,94,132,88,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,87,135,87,87,135,84,76,52,50,65,74,75,85,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85", "endOffsets": "310,3138,3217,3300,3422,3532,4368,4501,4590,4728,4913,4993,5055,5144,5207,5272,5331,5404,5467,5521,5649,5706,5768,5822,5895,6038,6122,6210,6346,6434,6522,6658,6743,6820,6873,6924,6990,7065,7141,7227,7306,7383,7459,7536,7610,7722,7813,7888,7979,8071,8145,8232,8323,8378,8460,8526,8609,8695,8757,8821,8884,8954,9071,9183,9294,9404,9461,9516,9919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\18d5a599756ea47edbaa6b32ae2eb3d5\\transformed\\navigation-ui-2.7.5\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,123", "endOffsets": "163,287"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "9521,9634", "endColumns": "112,123", "endOffsets": "9629,9753"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\345ccf188a6f49c4c91ca3120313e197\\transformed\\core-1.12.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "38,39,40,41,42,43,44,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3537,3634,3744,3846,3947,4054,4159,10148", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3629,3739,3841,3942,4049,4154,4273,10244"}}]}]}