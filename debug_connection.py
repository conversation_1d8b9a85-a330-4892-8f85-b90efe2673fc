#!/usr/bin/env python3
"""
Debug connection issues between Android and Windows
"""

import socket
import json
import asyncio
import websockets
from datetime import datetime

async def test_websocket_connection():
    """Test WebSocket connection like Android app would."""
    print("Testing WebSocket connection...")
    
    try:
        uri = "ws://*************:8766"
        print(f"Connecting to {uri}")
        
        # Test with same timeout as Android (10 seconds)
        async with websockets.connect(uri, timeout=10) as websocket:
            print("SUCCESS: WebSocket connected")
            
            # Receive welcome message
            try:
                welcome = await asyncio.wait_for(websocket.recv(), timeout=5)
                welcome_data = json.loads(welcome)
                print(f"Welcome received: {welcome_data.get('type')}")
                print(f"Device name: {welcome_data.get('server_info', {}).get('device_name')}")
            except asyncio.TimeoutError:
                print("No welcome message received")
            
            # Test ping-pong
            ping_msg = {
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(ping_msg))
            print("Ping sent")
            
            try:
                pong = await asyncio.wait_for(websocket.recv(), timeout=5)
                pong_data = json.loads(pong)
                print(f"Pong received: {pong_data.get('type')}")
                return True
            except asyncio.TimeoutError:
                print("No pong response")
                return False
                
    except Exception as e:
        print(f"WebSocket connection failed: {e}")
        return False

def test_udp_broadcast():
    """Test UDP broadcast discovery."""
    print("\nTesting UDP broadcast...")
    
    try:
        # Create UDP socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        
        # Create discovery message like Windows app
        message = {
            "type": "discovery",
            "device_name": "Test-Windows-PC",
            "device_type": "windows",
            "ip": "*************",
            "websocket_port": 8766,
            "timestamp": datetime.now().isoformat()
        }
        
        data = json.dumps(message).encode('utf-8')
        
        # Send to broadcast address
        sock.sendto(data, ('*************', 8765))
        print("UDP broadcast sent to *************:8765")
        
        sock.close()
        return True
        
    except Exception as e:
        print(f"UDP broadcast failed: {e}")
        return False

def check_network_connectivity():
    """Check basic network connectivity."""
    print("\nChecking network connectivity...")
    
    # Test TCP connection to port 8766
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(("*************", 8766))
        sock.close()
        
        if result == 0:
            print("TCP connection to *************:8766: SUCCESS")
            return True
        else:
            print(f"TCP connection to *************:8766: FAILED (error {result})")
            return False
    except Exception as e:
        print(f"TCP connection test failed: {e}")
        return False

async def main():
    """Main test function."""
    print("Connection Debug Test")
    print("=" * 30)
    
    # Test 1: Basic network connectivity
    tcp_ok = check_network_connectivity()
    
    # Test 2: WebSocket connection
    ws_ok = await test_websocket_connection()
    
    # Test 3: UDP broadcast
    udp_ok = test_udp_broadcast()
    
    print("\n" + "=" * 30)
    print("RESULTS:")
    print(f"TCP connectivity: {'OK' if tcp_ok else 'FAILED'}")
    print(f"WebSocket connection: {'OK' if ws_ok else 'FAILED'}")
    print(f"UDP broadcast: {'OK' if udp_ok else 'FAILED'}")
    
    if tcp_ok and ws_ok:
        print("\nWebSocket should work from Android!")
        print("If Android still fails, check:")
        print("- Android app permissions")
        print("- Android WebSocket implementation")
        print("- Network security policies")
    else:
        print("\nConnection issues detected:")
        if not tcp_ok:
            print("- Basic TCP connection failed")
        if not ws_ok:
            print("- WebSocket protocol issues")

if __name__ == "__main__":
    asyncio.run(main())
