package com.clipsy.android.viewmodel

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.clipsy.android.data.model.Device
import com.clipsy.android.data.model.ClipboardItem
import com.clipsy.android.data.model.ConnectionState
import com.clipsy.android.data.model.ConnectionStatus
import com.clipsy.android.data.repository.ClipboardRepository
import com.clipsy.android.data.repository.DeviceRepository
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
/**
 * Main ViewModel for the application.
 */
class MainViewModel(
    private val clipboardRepository: ClipboardRepository = ClipboardRepository.getInstance(),
    private var deviceRepository: DeviceRepository? = null,
    private val connectionManager: com.clipsy.android.pairing.SimpleConnectionManager? = null
) : ViewModel() {

    // Removed init block to prevent crashes

    // Service state
    private val _isServiceRunning = MutableLiveData<Boolean>(false)
    val isServiceRunning: LiveData<Boolean> = _isServiceRunning
    
    // Connection status
    private val _connectionStatus = MutableLiveData<String>("Disconnected")
    val connectionStatus: LiveData<String> = _connectionStatus
    
    // Error messages
    private val _errorMessage = MutableLiveData<String>("")
    val errorMessage: LiveData<String> = _errorMessage
    
    // Local IP address
    private val _localIpAddress = MutableLiveData<String>("")
    val localIpAddress: LiveData<String> = _localIpAddress
    
    // Device discovery state
    private val _isDiscovering = MutableLiveData<Boolean>(false)
    val isDiscovering: LiveData<Boolean> = _isDiscovering
    
    // Repository LiveData (will be initialized when context is provided)
    var discoveredDevices: LiveData<List<Device>>? = null
    var connectedDevices: LiveData<List<Device>>? = null
    var connectionStates: LiveData<Map<String, ConnectionState>>? = null
    
    /**
     * Set service running state.
     */
    fun setServiceRunning(running: Boolean) {
        _isServiceRunning.value = running
        updateConnectionStatus()
    }
    
    /**
     * Set local IP address.
     */
    fun setLocalIpAddress(ip: String) {
        _localIpAddress.value = ip
    }
    
    /**
     * Set discovery state.
     */
    fun setDiscovering(discovering: Boolean) {
        _isDiscovering.value = discovering
    }
    
    /**
     * Show error message.
     */
    fun showError(message: String) {
        _errorMessage.value = message
    }
    
    /**
     * Clear error message.
     */
    fun clearErrorMessage() {
        _errorMessage.value = ""
    }
    
    /**
     * Refresh devices (trigger discovery).
     */
    fun refreshDevices() {
        viewModelScope.launch {
            try {
                setDiscovering(true)
                // Trigger device discovery through repository
                deviceRepository?.startDiscovery()
                // Stop discovery after 5 seconds
                kotlinx.coroutines.delay(5000)
                setDiscovering(false)
            } catch (e: Exception) {
                setDiscovering(false)
                // Don't show error, just fail silently
            }
        }
    }
    
    /**
     * Add manual device.
     */
    fun addManualDevice(ip: String, name: String) {
        viewModelScope.launch {
            try {
                if (ip.isBlank()) {
                    showError("IP address cannot be empty")
                    return@launch
                }
                
                // Basic IP validation
                if (!isValidIpAddress(ip)) {
                    showError("Invalid IP address format")
                    return@launch
                }
                
                deviceRepository?.addManualDevice(ip, name)
            } catch (e: Exception) {
                showError("Failed to add device: ${e.message}")
            }
        }
    }
    
    /**
     * Remove device.
     */
    fun removeDevice(device: Device) {
        viewModelScope.launch {
            try {
                deviceRepository?.removeDevice(device)
            } catch (e: Exception) {
                showError("Failed to remove device: ${e.message}")
            }
        }
    }
    
    /**
     * Get clipboard history with limit.
     */
    fun getClipboardHistory(limit: Int): LiveData<List<ClipboardItem>> {
        return clipboardRepository.getItemsWithLimit(limit)
    }
    
    /**
     * Clear clipboard history.
     */
    fun clearClipboardHistory() {
        viewModelScope.launch {
            try {
                clipboardRepository.clearAllItems()
            } catch (e: Exception) {
                showError("Failed to clear history: ${e.message}")
            }
        }
    }
    
    /**
     * Delete clipboard item.
     */
    fun deleteClipboardItem(item: ClipboardItem) {
        viewModelScope.launch {
            try {
                clipboardRepository.deleteItem(item)
            } catch (e: Exception) {
                showError("Failed to delete item: ${e.message}")
            }
        }
    }

    /**
     * Send clipboard content to PC.
     */
    fun sendClipboardToPC(content: String) {
        viewModelScope.launch {
            try {
                // Update all connected devices to SYNCING status for immediate feedback
                deviceRepository?.let { repo ->
                    repo.connectedDevices.value?.forEach { device ->
                        if (repo.getConnectionStatus(device) == ConnectionStatus.CONNECTED) {
                            repo.updateConnectionStatus(device, ConnectionStatus.SYNCING)
                        }
                    }
                }

                // Add to local clipboard history
                clipboardRepository.addLocalItem(content)

                // Send to connected devices via WebSocket
                deviceRepository?.getWebSocketClient()?.sendClipboardContent(content)

                // Reset status back to CONNECTED after sync
                delay(2000) // Show "Syncing..." for 2 seconds
                deviceRepository?.let { repo ->
                    repo.connectedDevices.value?.forEach { device ->
                        if (repo.getConnectionStatus(device) == ConnectionStatus.SYNCING) {
                            repo.updateConnectionStatus(device, ConnectionStatus.CONNECTED)
                        }
                    }
                }

            } catch (e: Exception) {
                showError("Failed to send clipboard: ${e.message}")
            }
        }
    }
    
    /**
     * Get connection status for device.
     */
    fun getDeviceConnectionStatus(device: Device): ConnectionState? {
        return deviceRepository?.getConnectionState(device)
    }

    /**
     * Send connection request to device (simplified).
     */
    fun sendConnectionRequest(device: Device) {
        viewModelScope.launch {
            try {
                connectToDevice(device)
            } catch (e: Exception) {
                showError("Error connecting to device: ${e.message}")
            }
        }
    }

    /**
     * Connect to a device.
     */
    fun connectToDevice(device: Device) {
        viewModelScope.launch {
            try {
                deviceRepository?.connectToDevice(device)
                updateConnectionStatus()
            } catch (e: Exception) {
                _errorMessage.value = "Connection failed: ${e.message}"
            }
        }
    }

    /**
     * Disconnect from a device.
     */
    fun disconnectFromDevice(device: Device) {
        viewModelScope.launch {
            try {
                deviceRepository?.disconnectFromDevice(device)
                updateConnectionStatus()
            } catch (e: Exception) {
                _errorMessage.value = "Disconnection failed: ${e.message}"
            }
        }
    }

    /**
     * Allow reconnection to a manually disconnected device.
     */
    fun allowReconnection(device: Device) {
        deviceRepository?.allowReconnection(device)
    }

    /**
     * Force reconnect all devices (useful for clearing stuck connections).
     */
    fun forceReconnectAllDevices() {
        viewModelScope.launch {
            try {
                deviceRepository?.forceReconnectAllDevices()
                updateConnectionStatus()
                _errorMessage.value = "Reconnecting to all devices..."
            } catch (e: Exception) {
                _errorMessage.value = "Force reconnect failed: ${e.message}"
            }
        }
    }

    /**
     * Update connection status display.
     */
    private fun updateConnectionStatus() {
        val connectedCount = deviceRepository?.getConnectedDeviceCount() ?: 0
        val status = when {
            !_isServiceRunning.value!! -> "Service Stopped"
            connectedCount == 0 -> "No Connections"
            connectedCount == 1 -> "1 Device Connected"
            else -> "$connectedCount Devices Connected"
        }
        _connectionStatus.value = status
    }
    
    /**
     * Basic IP address validation.
     */
    private fun isValidIpAddress(ip: String): Boolean {
        return try {
            val parts = ip.split(".")
            if (parts.size != 4) return false
            
            parts.all { part ->
                val num = part.toIntOrNull()
                num != null && num in 0..255
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get device count.
     */
    fun getDeviceCount(): Int = deviceRepository?.getDeviceCount() ?: 0

    /**
     * Get connected device count.
     */
    fun getConnectedDeviceCount(): Int = deviceRepository?.getConnectedDeviceCount() ?: 0

    /**
     * Check if any devices are connected.
     */
    fun hasConnectedDevices(): Boolean = getConnectedDeviceCount() > 0

    /**
     * Get device repository for direct access (for debugging).
     */
    fun getDeviceRepository(): DeviceRepository? = deviceRepository

    /**
     * Initialize networking components.
     */
    private fun initializeNetworking() {
        // This will be called by MainActivity with proper context
        // For now, just set initial states
        _connectionStatus.value = "Initializing..."
        _localIpAddress.value = ""
        _isDiscovering.value = false
    }

    /**
     * Initialize networking with context (called from MainActivity).
     */
    fun initializeWithContext(context: android.content.Context) {
        try {
            // Initialize device repository with context
            if (deviceRepository == null) {
                deviceRepository = DeviceRepository(context, connectionManager)

                // Initialize LiveData references
                discoveredDevices = deviceRepository!!.discoveredDevices
                connectedDevices = deviceRepository!!.connectedDevices
                connectionStates = deviceRepository!!.connectionStates

                // Start device discovery automatically
                viewModelScope.launch {
                    try {
                        deviceRepository!!.startDiscovery()
                    } catch (e: Exception) {
                        // Ignore discovery errors
                    }
                }
            }

            // Detect local IP address safely
            detectLocalIpAddress(context)
        } catch (e: Exception) {
            _localIpAddress.value = "*************"
            _connectionStatus.value = "Ready"
        }
    }

    /**
     * Detect local IP address.
     */
    private fun detectLocalIpAddress(context: android.content.Context) {
        try {
            val wifiManager = context.applicationContext.getSystemService(android.content.Context.WIFI_SERVICE) as android.net.wifi.WifiManager
            val wifiInfo = wifiManager.connectionInfo
            val ipInt = wifiInfo.ipAddress

            if (ipInt != 0) {
                val ip = String.format(
                    "%d.%d.%d.%d",
                    (ipInt and 0xff),
                    (ipInt shr 8 and 0xff),
                    (ipInt shr 16 and 0xff),
                    (ipInt shr 24 and 0xff)
                )
                _localIpAddress.value = ip
                _connectionStatus.value = "Network detected"
            } else {
                _localIpAddress.value = "*************"
                _connectionStatus.value = "Ready"
            }
        } catch (e: Exception) {
            _localIpAddress.value = "*************"
            _connectionStatus.value = "Ready"
        }
    }
}
