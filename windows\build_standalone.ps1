# PowerShell build script for Clipsy Standalone Windows Application
# This script creates a single executable with all dependencies bundled

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Building Clipsy Standalone Application" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Change to the source directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$srcPath = Join-Path $scriptPath "src"
Set-Location $srcPath

# Check if Python is available
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python 3.8 or later" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Checking dependencies..." -ForegroundColor Yellow

# Install/upgrade required packages
Write-Host "Installing required packages..." -ForegroundColor Yellow

$packages = @(
    "pyinstaller",
    "websockets",
    "pywin32",
    "pillow",
    "psutil",
    "aiohttp",
    "aiofiles",
    "requests",
    "certifi"
)

try {
    # Upgrade pip first
    python -m pip install --upgrade pip
    
    # Install packages
    foreach ($package in $packages) {
        Write-Host "Installing $package..." -ForegroundColor Gray
        python -m pip install --upgrade $package
    }
    
    Write-Host "Dependencies installed successfully." -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to install required packages" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Clean previous builds
Write-Host "Cleaning previous builds..." -ForegroundColor Yellow

$dirsToClean = @("build", "dist", "__pycache__")
foreach ($dir in $dirsToClean) {
    if (Test-Path $dir) {
        Remove-Item $dir -Recurse -Force
        Write-Host "Removed $dir" -ForegroundColor Gray
    }
}

# Create logs directory
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Name "logs" | Out-Null
    Write-Host "Created logs directory" -ForegroundColor Gray
}

Write-Host "Building standalone executable..." -ForegroundColor Yellow

# Build the executable using PyInstaller
try {
    pyinstaller --clean ClipsyStandalone.spec
    
    if ($LASTEXITCODE -ne 0) {
        throw "PyInstaller failed with exit code $LASTEXITCODE"
    }
} catch {
    Write-Host "ERROR: Build failed" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if the executable was created
$exePath = "dist\ClipsyStandalone.exe"
if (Test-Path $exePath) {
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "BUILD SUCCESSFUL!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Executable created: $exePath" -ForegroundColor Green
    Write-Host ""
    Write-Host "Features included:" -ForegroundColor Cyan
    Write-Host "- Automatic firewall configuration" -ForegroundColor White
    Write-Host "- All dependencies bundled" -ForegroundColor White
    Write-Host "- No external files required" -ForegroundColor White
    Write-Host "- Administrator privilege handling" -ForegroundColor White
    Write-Host "- Comprehensive error handling" -ForegroundColor White
    Write-Host ""
    
    # Get file size
    $fileSize = (Get-Item $exePath).Length
    $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
    Write-Host "Executable size: $fileSizeMB MB" -ForegroundColor Gray
    
    # Copy to parent directory for easy access
    $parentExePath = "..\ClipsyStandalone.exe"
    try {
        Copy-Item $exePath $parentExePath -Force
        Write-Host "Executable also copied to: windows\ClipsyStandalone.exe" -ForegroundColor Green
    } catch {
        Write-Host "Warning: Could not copy to parent directory" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "You can now distribute the single ClipsyStandalone.exe file" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Build completed successfully!" -ForegroundColor Green
    
} else {
    Write-Host "ERROR: Executable not found after build" -ForegroundColor Red
    Write-Host "Check the build output above for errors" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Press Enter to exit..." -ForegroundColor Gray
Read-Host
