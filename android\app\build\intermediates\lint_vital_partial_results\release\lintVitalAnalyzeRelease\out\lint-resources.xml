http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/arrays.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/switch_thumb_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/button_text_primary_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/button_background_primary_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/bottom_nav_color_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/switch_track_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/button_text_secondary_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_computer.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_history_vector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_clipsy_settings.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_clipsy_settings.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_clipsy_settings.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_clipsy_settings.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/ic_clipsy_settings.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_clipsy_refresh.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_clipsy_refresh.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_clipsy_refresh.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_clipsy_refresh.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/ic_clipsy_refresh.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_clipsy_status.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_clipsy_status.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_clipsy_status.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_clipsy_status.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/ic_clipsy_status.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_scan_animation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_clipsy_history.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_clipsy_history.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_clipsy_history.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_clipsy_history.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/ic_clipsy_history.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_clipsy_devices.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_clipsy_devices.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_clipsy_devices.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_clipsy_devices.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/ic_clipsy_devices.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_phone.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_right.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/dialog_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_secondary.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/badge_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_notification.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_notification.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_notification.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_notification.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/ic_notification.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_refresh.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sync.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_send_arrow.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_indicator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_devices_vector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_status_vector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_info.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sync_to_pc.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/code_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_device.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_pairing.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_device_bluetooth_style.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_clipboard_history.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_devices.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_status.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_history.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_history.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/main_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_manual_device.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_foreground.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_foreground.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_foreground.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_foreground.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_foreground.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/preferences.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+array:history_limit_entries,0,V40003005a,**********,;10 items,20 items,50 items,100 items,Unlimited,;history_limit_values,0,V4000b0137,13001101ee,;10,20,50,100,-1,;+color:clipsy_green_bright,1,V400130361,**********,;"#FF4CAF50";switch_thumb_color,2,F;clipsy_card_background,1,V4001102f1,3a00110327,;"#FF2A2A2A";clipsy_primary_dark,1,V4000c01eb,37000c021e,;"#FF1A1A1A";text_primary,1,V40025063d,**********,;"#FFFFFFFF";high_contrast_text,1,V400350952,**********,;"#FFFFFFFF";medium_contrast_text,1,V4003709c6,38003709fa,;"#FFCCCCCC";button_background_disabled,1,V400300868,3e003008a2,;"#FF1A1A1A";white,1,V400080148,290008016d,;"#FFFFFFFF";teal_700,1,V4000600f1,2c00060119,;"#FF018786";purple_700,1,V400040095,2e000400bf,;"#FF3700B3";purple_500,1,V400030066,2e00030090,;"#FF6200EE";status_disconnected,1,V4001a0491,37001a04c4,;"#FFF44336";button_text_primary,1,V4002b073f,37002b0772,;"#FFFFFFFF";clipsy_error,1,V40016040b,**********,;"#FFF44336";bluetooth_blue,1,V4000e0255,32000e0283,;"#FF2196F3";border_color,1,V4002706a1,30002706cd,;"#FF444444";high_contrast_background,1,V400360989,3c003609c1,;"#FF000000";primary_color,1,V4002205a2,31002205cf,;"#FF4A90E2";button_background_secondary,1,V4002f0828,3f002f0863,;"#FF2A2A2A";clipsy_card_dark,1,V40012032c,340012035c,;"#FF1A1A1A";button_text_disabled,1,V4002d07b1,38002d07e5,;"#FF666666";clipsy_accent,1,V4000d0223,31000d0250,;"#FF4A90E2";button_text_secondary,1,V4002c0777,39002c07ac,;"#FF4A90E2";clipsy_text_primary,1,V400140399,37001403cc,;"#FFFFFFFF";purple_200,1,V400020037,2e00020061,;"#FFBB86FC";clipsy_surface,1,V4001002be,32001002ec,;"#FF1E1E1E";button_stroke_secondary,1,V4003208e1,3b00320918,;"#FF666666";button_text_primary_selector,3,F;button_background_primary_selector,4,F;teal_200,1,V4000500c4,2c000500ec,;"#FF03DAC5";source_remote,1,V4001f054c,31001f0579,;"#FF4CAF50";secondary_color,1,V4002305d4,3300230603,;"#FF666666";clipsy_background,1,V4000f0288,35000f02b9,;"#FF121212";bottom_nav_color_selector,5,F;clipsy_primary,1,V4000b01b8,32000b01e6,;"#FF2A2A2A";medium_contrast_background,1,V4003809ff,3e00380a39,;"#FF333333";switch_track_color,6,F;clipsy_text_secondary,1,V4001503d1,3900150406,;"#FFAAAAAA";black,1,V40007011e,2900070143,;"#FF000000";source_local,1,V4001e051b,30001e0547,;"#FF2196F3";button_stroke_primary,1,V4003108a7,39003108dc,;"#FF4A90E2";button_text_secondary_selector,7,F;background_color,1,V400240608,3400240638,;"#FF1E1E1E";button_background_primary,1,V4002e07ea,3d002e0823,;"#FF4A90E2";status_connected,1,V40019045c,340019048c,;"#FF4CAF50";status_connecting,1,V4001b04c9,35001b04fa,;"#FFFF9800";text_secondary,1,V40026066e,320026069c,;"#FFAAAAAA";code_background,1,V4002806d2,3300280701,;"#FF2A2A2A";+dimen:button_min_height,8,V400140304,3000140330,;"48dp";button_padding_vertical_small,8,V4001102a8,3b001102df,;"8dp";button_min_width,8,V40016036c,2f00160397,;"80dp";button_stroke_width,8,V4000600f2,310006011f,;"2dp";button_stroke_width_small,8,V400070124,3700070157,;"1dp";button_padding_horizontal,8,V4000e01f9,38000e022d,;"24dp";text_size_button,8,V4000a0179,2f000a01a4,;"14sp";button_min_height_small,8,V400150335,3600150367,;"36dp";button_corner_radius_small,8,V40004008a,38000400be,;"8dp";button_elevation,8,V4000500c3,2e000500ed,;"2dp";button_padding_horizontal_small,8,V4000f0232,3e000f026c,;"16dp";button_corner_radius,8,V400030056,3300030085,;"12dp";text_size_button_small,8,V4000b01a9,35000b01da,;"12sp";button_padding_vertical,8,V400100271,36001002a3,;"12dp";+drawable:ic_computer,9,F;ic_history_vector,10,F;ic_clipsy_settings,11,F;ic_clipsy_settings,12,F;ic_clipsy_settings,13,F;ic_clipsy_settings,14,F;ic_clipsy_settings,15,F;ic_clipsy_refresh,16,F;ic_clipsy_refresh,17,F;ic_clipsy_refresh,18,F;ic_clipsy_refresh,19,F;ic_clipsy_refresh,20,F;ic_clipsy_status,21,F;ic_clipsy_status,22,F;ic_clipsy_status,23,F;ic_clipsy_status,24,F;ic_clipsy_status,25,F;ic_scan_animation,26,F;ic_clipsy_history,27,F;ic_clipsy_history,28,F;ic_clipsy_history,29,F;ic_clipsy_history,30,F;ic_clipsy_history,31,F;ic_clipsy_devices,32,F;ic_clipsy_devices,33,F;ic_clipsy_devices,34,F;ic_clipsy_devices,35,F;ic_clipsy_devices,36,F;ic_phone,37,F;ic_arrow_right,38,F;dialog_background,39,F;button_secondary,40,F;badge_background,41,F;ic_notification,42,F;ic_notification,43,F;ic_notification,44,F;ic_notification,45,F;ic_notification,46,F;ic_notification,47,F;ic_refresh,48,F;ic_sync,49,F;circle_background,50,F;ic_send_arrow,51,F;circle_indicator,52,F;ic_settings,53,F;ic_devices_vector,54,F;ic_status_vector,55,F;ic_info,56,F;ic_sync_to_pc,57,F;ic_search,58,F;code_background,59,F;+id:text_device_type,60,F;btn_cancel_pairing,61,F;button_info,62,F;text_content,63,F;text_device_info,63,F;nav_status,64,F;nav_history,64,F;recycler_view_connected_devices,65,F;tv_pairing_code,61,F;text_connection_status,66,F;text_connection_status,60,F;text_connection_status,62,F;progress_discovery,66,F;action_clear_history,67,F;bottom_navigation,68,F;button_refresh,65,F;text_timestamp,63,F;icon_device_type,60,F;icon_device_type,62,F;progress_scanning,65,F;button_unpair,60,F;indicator_online,60,F;text_last_seen,60,F;settings_container,69,F;swipe_refresh_layout,65,F;switch_sync,65,F;fab_add_device,65,F;button_connect,60,F;text_discovery_status,66,F;text_device_name,60,F;text_device_name,62,F;fab_manual_sync,68,F;header_connected_devices,65,F;recycler_view_history,70,F;text_device_ip,60,F;action_settings,71,F;text_description,65,F;edit_text_message,70,F;text_current_device_name,65,F;card_network_info,66,F;action_refresh,71,F;card_device_name,65,F;tv_pairing_instruction,61,F;button_pair,60,F;button_remove,60,F;text_source,63,F;recycler_view_available_devices,65,F;button_delete,63,F;edit_text_ip,72,F;header_available_devices,65,F;text_service_status,66,F;text_local_ip,66,F;nav_devices,64,F;button_send_message,70,F;layout_empty,70,F;toolbar,68,F;card_service_status,66,F;tv_pairing_title,61,F;fragment_container,68,F;text_connected_devices,66,F;text_discovered_devices,66,F;button_refresh_devices,66,F;edit_text_name,72,F;card_no_available_devices,65,F;icon_content_type,63,F;+layout:activity_main,68,F;fragment_devices,65,F;fragment_history,70,F;fragment_status,66,F;item_clipboard_history,63,F;activity_settings,69,F;dialog_pairing,61,F;item_device_bluetooth_style,62,F;item_device,60,F;dialog_add_manual_device,72,F;+menu:main_menu,71,F;bottom_navigation_menu,64,F;menu_history,67,F;+mipmap:ic_launcher_foreground,73,F;ic_launcher_foreground,74,F;ic_launcher_foreground,75,F;ic_launcher_foreground,76,F;ic_launcher_foreground,77,F;ic_launcher,78,F;ic_launcher,79,F;ic_launcher,80,F;ic_launcher,81,F;ic_launcher,82,F;+string:cancel,83,V400470ddf,2900470e04,;"Cancel";action_copy,83,V400510f43,2c00510f6b,;"Copy";notification_clipboard_synced,83,V4003d0c22,52003d0c70,;"Clipboard synced from %s";error_permission_denied,83,V400400c8e,4500400ccf,;"Permission denied";app_version,83,V4006d1559,2d006d1582,;"1.0.0";action_delete,83,V400520f70,3000520f9c,;"Delete";pref_auto_sync_title,83,V4005a10bc,3a005a10f2,;"Auto Sync";nav_status,83,V400040059,2d00040082,;"Status";nav_history,83,V4000600b7,2f000600e2,;"History";pref_auto_sync_summary,83,V4005b10f7,57005b114a,;"Automatically sync clipboard changes";devices_empty,83,V400150314,3a0015034a,;"No devices found";device_add_manual,83,V4001a042f,38001a0463,;"Add Manual";settings_title,83,V4002706bd,33002706ec,;"Settings";settings_device_name_summary,83,V40032099b,54003209eb,;"Name shown to other devices";notification_service_title,83,V4003b0b8a,48003b0bce,;"Clipsy is running";pref_category_about,83,V4006b14e8,35006b1519,;"About";settings_clipboard,83,V4002806f1,3800280725,;"Clipboard";error_invalid_ip,83,V400430d64,3f00430d9f,;"Invalid IP address";device_disconnect,83,V400170382,38001703b6,;"Disconnect";settings_auto_discovery,83,V4003309f0,4200330a2e,;"Auto discovery";settings_auto_start,83,V400370ad3,3a00370b09,;"Auto start";pref_auto_cleanup_summary,83,V400631312,5e0063136c,;"Automatically remove old clipboard items";action_clear_history,83,V400500f04,3e00500f3e,;"Clear History";settings_device_name,83,V40031095e,3c00310996,;"Device name";status_discovering,83,V400110279,43001102b8,;"Discovering devices…";add,83,V4004a0e4f,23004a0e6e,;"Add";pref_websocket_port_summary,83,V40069148c,5a006914e2,;"TCP port for WebSocket connections";pref_auto_cleanup_title,83,V4006212d1,400062130d,;"Auto Cleanup";settings_history_limit_summary,83,V4002e08c8,5b002e091f,;"Maximum number of items to store";status_disconnected,83,V40010023c,3c00100274,;"Disconnected";pref_discovery_port_summary,83,V4006713f1,5500671442,;"UDP port for device discovery";action_pair,83,V400551005,2c0055102d,;"Pair";settings_auto_discovery_summary,83,V400340a33,6500340a94,;"Automatically discover devices on network";history_item_delete,83,V40024066b,360024069d,;"Delete";device_connect,83,V40016034f,320016037d,;"Connect";status_service_stopped,83,V4000e01c2,42000e0200,;"Service Stopped";history_item_copy,83,V40023062b,3f00230666,;"Copy to clipboard";device_manual_name_hint,83,V4001c04ab,4a001c04f1,;"Device name (optional)";pref_category_network,83,V400651372,39006513a7,;"Network";settings_startup,83,V400360a9e,3400360ace,;"Startup";settings_sync_enabled_summary,83,V4002a0772,69002a07d7,;"Automatically sync clipboard with other devices";settings_network,83,V400300929,3400300959,;"Network";error_connection_failed,83,V400420d1e,4500420d5f,;"Connection failed";pref_history_limit_summary,83,V400611270,60006112cc,;"Maximum number of clipboard items to keep";settings_history_limit,83,V4002d0887,40002d08c3,;"History limit";no,83,V400490e2d,2100490e4a,;"No";history_clear,83,V40021058b,37002105be,;"Clear History";action_unpair,83,V400561032,300056105e,;"Unpair";action_settings,83,V4000900fe,340009012e,;"Settings";settings_history_enabled,83,V4002b07dc,4d002b0825,;"Enable clipboard history";device_connected,83,V4001803bb,36001803ed,;"Connected";delete,83,V4004b0e73,29004b0e98,;"Delete";history_clear_confirm,83,V4002205c3,6700220626,;"Are you sure you want to clear the clipboard history?";pref_discovery_port_title,83,V4006613ac,44006613ec,;"Discovery Port";devices_title,83,V4001402d7,3c0014030f,;"Discovered Devices";action_refresh,83,V4000a0133,32000a0161,;"Refresh";pref_category_sync,83,V40059107d,3e005910b7,;"Synchronization";pref_websocket_port_title,83,V400681447,4400681487,;"WebSocket Port";pref_history_limit_title,83,V40060122d,420060126b,;"History Limit";share,83,V4004d0ec3,27004d0ee6,;"Share";notification_service_text,83,V4003c0bd3,4e003c0c1d,;"Clipboard sync is active";copy,83,V4004c0e9d,25004c0ebe,;"Copy";history_empty,83,V40020054c,3e00200586,;"No clipboard history";ok,83,V400460dbd,2100460dda,;"OK";settings_auto_start_summary,83,V400380b0e,5b00380b65,;"Start service automatically on boot";pref_sync_interval_title,83,V4005c114f,42005c118d,;"Sync Interval";history_title,83,V4001f0510,3b001f0547,;"Clipboard History";device_manual_ip_hint,83,V4001b0468,42001b04a6,;"Enter IP address";device_disconnected,83,V4001903f2,3c0019042a,;"Disconnected";action_connect,83,V400530fa1,3200530fcf,;"Connect";pref_sync_interval_summary,83,V4005d1192,5f005d11ed,;"Clipboard check interval in milliseconds";yes,83,V400480e09,2300480e28,;"Yes";status_service_running,83,V4000d017f,42000d01bd,;"Service Running";action_remove,83,V400540fd4,3000541000,;"Remove";pref_app_version_title,83,V4006c151e,3a006c1554,;"Version";nav_devices,83,V400050087,2f000500b2,;"Devices";app_name,83,V400010010,2b00010037,;"Clipsy";settings_sync_enabled,83,V40029072a,470029076d,;"Enable clipboard sync";settings_history_enabled_summary,83,V4002c082a,5c002c0882,;"Store clipboard history locally";pref_category_history,83,V4005f11f3,39005f1228,;"History";error_network_unavailable,83,V400410cd4,4900410d19,;"Network unavailable";status_connected,83,V4000f0205,36000f0237,;"Connected";+style:Button.Clipsy.Secondary,84,V40056112b,c006213d6,;DWidget.Material3.Button.OutlinedButton,android\:textColor:@color/white,android\:textSize:14sp,android\:textStyle:bold,android\:minHeight:48dp,android\:paddingStart:24dp,android\:paddingEnd:24dp,android\:textAllCaps:false,backgroundTint:@android\:color/transparent,strokeColor:@color/white,strokeWidth:2dp,cornerRadius:12dp,;Button.Clipsy.Small,84,V400711648,c007d18f8,;DWidget.Material3.Button.OutlinedButton,android\:textColor:@color/white,android\:textSize:12sp,android\:textStyle:normal,android\:minHeight:36dp,android\:paddingStart:16dp,android\:paddingEnd:16dp,android\:textAllCaps:false,backgroundTint:@color/button_background_secondary,strokeColor:@color/white,strokeWidth:1dp,cornerRadius:8dp,;BottomNavActiveIndicator,84,V400270803,c002a08d8,;DWidget.Material3.BottomNavigationView.ActiveIndicator,android\:color:#FF3A3A3A,android\:alpha:0.9,;Theme.Clipsy,84,V400020064,c002407c7,;DTheme.Material3.Dark.NoActionBar,colorPrimary:@color/clipsy_primary,colorPrimaryVariant:@color/clipsy_primary_dark,colorOnPrimary:@color/white,colorSecondary:@color/clipsy_accent,colorSecondaryVariant:@color/clipsy_accent,colorOnSecondary:@color/white,colorSurface:@color/clipsy_surface,colorOnSurface:@color/white,colorOnBackground:@color/white,actionMenuTextColor:@color/white,materialButtonStyle:@style/Button.Clipsy.Primary,materialButtonOutlinedStyle:@style/Button.Clipsy.Secondary,borderlessButtonStyle:@style/Button.Clipsy.Tertiary,alertDialogTheme:@style/AlertDialog.Clipsy,materialAlertDialogTheme:@style/AlertDialog.Clipsy,android\:colorBackground:@color/clipsy_background,android\:statusBarColor:?attr/colorPrimaryVariant,android\:textColorPrimary:@color/white,android\:textColorSecondary:@color/white,android\:buttonStyle:@style/Button.Clipsy.Primary,;AlertDialog.Clipsy.Button,84,V400b82492,c00be2605,;DWidget.Material3.Button.TextButton.Dialog,android\:textColor:@color/white,android\:textSize:14sp,android\:textStyle:bold,android\:textAllCaps:false,rippleColor:@color/white,;AlertDialog.Clipsy.Title,84,V400ab2265,c00af236b,;DMaterialAlertDialog.Material3.Title.Text,android\:textColor:@color/white,android\:textSize:18sp,android\:textStyle:bold,;Button.Clipsy.Tertiary,84,V400651417,c006e1616,;DWidget.Material3.Button.TextButton,android\:textColor:@color/white,android\:textSize:14sp,android\:textStyle:bold,android\:minHeight:48dp,android\:paddingStart:16dp,android\:paddingEnd:16dp,android\:textAllCaps:false,cornerRadius:12dp,;Button.Clipsy.Danger,84,V400801933,c008b1ba3,;DWidget.Material3.Button,android\:textColor:@color/high_contrast_text,android\:textSize:14sp,android\:textStyle:bold,android\:minHeight:48dp,android\:paddingStart:24dp,android\:paddingEnd:24dp,android\:elevation:2dp,android\:textAllCaps:false,backgroundTint:@color/clipsy_error,cornerRadius:12dp,;Button.Clipsy.Success,84,V4008e1bdc,c00991e51,;DWidget.Material3.Button,android\:textColor:@color/high_contrast_text,android\:textSize:14sp,android\:textStyle:bold,android\:minHeight:48dp,android\:paddingStart:24dp,android\:paddingEnd:24dp,android\:elevation:2dp,android\:textAllCaps:false,backgroundTint:@color/status_connected,cornerRadius:12dp,;Button.Clipsy.Primary,84,V400480e59,c005310ea,;DWidget.Material3.Button,android\:textColor:@color/button_text_primary_selector,android\:textSize:14sp,android\:textStyle:bold,android\:minHeight:48dp,android\:paddingStart:24dp,android\:paddingEnd:24dp,android\:elevation:2dp,android\:textAllCaps:false,backgroundTint:@color/button_background_primary_selector,cornerRadius:12dp,;PreferenceThemeOverlay.Clipsy,84,V400340a6b,c003d0d14,;DPreferenceThemeOverlay.v14.Material,android\:textColorPrimary:@color/clipsy_text_primary,android\:textColorSecondary:@color/clipsy_text_primary,android\:colorBackground:@color/clipsy_background,colorSurface:@color/clipsy_card_dark,colorOnSurface:@color/clipsy_text_primary,colorPrimary:@color/clipsy_text_primary,colorOnPrimary:@color/clipsy_text_primary,colorAccent:@color/clipsy_text_primary,;Theme.Clipsy.Settings,84,V4002d0903,c00310a3f,;DTheme.Clipsy,preferenceTheme:@style/PreferenceThemeOverlay.Clipsy,android\:textColorPrimary:@color/clipsy_text_primary,android\:textColorSecondary:@color/clipsy_text_secondary,;AlertDialog.Clipsy.Body,84,V400b22395,c00b52466,;DMaterialAlertDialog.Material3.Body.Text,android\:textColor:@color/white,android\:textSize:14sp,;ShapeAppearanceOverlay.Clipsy.FloatingActionButton,84,V400400d37,c00430deb,;EcornerFamily:rounded,cornerSize:50%,;AlertDialog.Clipsy,84,V4009c1e93,c00a8223a,;DThemeOverlay.Material3.MaterialAlertDialog,android\:background:@color/clipsy_card_dark,android\:textColorPrimary:@color/white,android\:textColorSecondary:@color/white,backgroundTint:@color/clipsy_card_dark,colorPrimary:@color/white,colorOnSurface:@color/white,materialAlertDialogTitleTextStyle:@style/AlertDialog.Clipsy.Title,materialAlertDialogBodyTextStyle:@style/AlertDialog.Clipsy.Body,buttonBarPositiveButtonStyle:@style/AlertDialog.Clipsy.Button,buttonBarNegativeButtonStyle:@style/AlertDialog.Clipsy.Button,buttonBarNeutralButtonStyle:@style/AlertDialog.Clipsy.Button,;+xml:preferences,85,F;network_security_config,86,F;data_extraction_rules,87,F;backup_rules,88,F;