<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Clipsy" parent="Theme.Material3.Dark.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/clipsy_primary</item>
        <item name="colorPrimaryVariant">@color/clipsy_primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/clipsy_accent</item>
        <item name="colorSecondaryVariant">@color/clipsy_accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Background colors -->
        <item name="android:colorBackground">@color/clipsy_background</item>
        <item name="colorSurface">@color/clipsy_surface</item>
        <item name="colorOnSurface">@color/white</item>
        <item name="colorOnBackground">@color/white</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Toolbar text colors -->
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/white</item>
        <item name="actionMenuTextColor">@color/white</item>

        <!-- Button styling defaults for better visibility -->
        <item name="materialButtonStyle">@style/Button.Clipsy.Primary</item>
        <item name="materialButtonOutlinedStyle">@style/Button.Clipsy.Secondary</item>
        <item name="borderlessButtonStyle">@style/Button.Clipsy.Tertiary</item>

        <!-- Ensure button text is always visible -->
        <item name="android:buttonStyle">@style/Button.Clipsy.Primary</item>

        <!-- Dialog button styling for white text -->
        <item name="alertDialogTheme">@style/AlertDialog.Clipsy</item>
        <item name="materialAlertDialogTheme">@style/AlertDialog.Clipsy</item>

        <!-- Customize your theme here. -->
    </style>

    <!-- Bottom Navigation Active Indicator Style -->
    <style name="BottomNavActiveIndicator" parent="Widget.Material3.BottomNavigationView.ActiveIndicator">
        <item name="android:color">#FF3A3A3A</item>
        <item name="android:alpha">0.9</item>
    </style>

    <!-- Settings Activity Theme -->
    <style name="Theme.Clipsy.Settings" parent="Theme.Clipsy">
        <item name="preferenceTheme">@style/PreferenceThemeOverlay.Clipsy</item>
        <item name="android:textColorPrimary">@color/clipsy_text_primary</item>
        <item name="android:textColorSecondary">@color/clipsy_text_secondary</item>
    </style>

    <!-- Preference Theme Overlay -->
    <style name="PreferenceThemeOverlay.Clipsy" parent="PreferenceThemeOverlay.v14.Material">
        <item name="android:textColorPrimary">@color/clipsy_text_primary</item>
        <item name="android:textColorSecondary">@color/clipsy_text_primary</item>
        <item name="android:colorBackground">@color/clipsy_background</item>
        <item name="colorSurface">@color/clipsy_card_dark</item>
        <item name="colorOnSurface">@color/clipsy_text_primary</item>
        <item name="colorPrimary">@color/clipsy_text_primary</item>
        <item name="colorOnPrimary">@color/clipsy_text_primary</item>
        <item name="colorAccent">@color/clipsy_text_primary</item>
    </style>

    <!-- Round FAB Shape -->
    <style name="ShapeAppearanceOverlay.Clipsy.FloatingActionButton" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <!-- Custom Button Styles for Dark Mode -->

    <!-- Primary Button - Filled with accent color -->
    <style name="Button.Clipsy.Primary" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/button_text_primary_selector</item>
        <item name="backgroundTint">@color/button_background_primary_selector</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:elevation">2dp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!-- Secondary Button - Outlined with accent color -->
    <style name="Button.Clipsy.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="strokeColor">@color/white</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!-- Tertiary Button - Text only with accent color -->
    <style name="Button.Clipsy.Tertiary" parent="Widget.Material3.Button.TextButton">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!-- Small Button - Compact version -->
    <style name="Button.Clipsy.Small" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/button_background_secondary</item>
        <item name="strokeColor">@color/white</item>
        <item name="strokeWidth">1dp</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:minHeight">36dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!-- Danger Button - For destructive actions -->
    <style name="Button.Clipsy.Danger" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/high_contrast_text</item>
        <item name="backgroundTint">@color/clipsy_error</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:elevation">2dp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!-- Success Button - For positive actions -->
    <style name="Button.Clipsy.Success" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/high_contrast_text</item>
        <item name="backgroundTint">@color/status_connected</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:elevation">2dp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!-- Custom AlertDialog Theme for white button text -->
    <style name="AlertDialog.Clipsy" parent="ThemeOverlay.Material3.MaterialAlertDialog">
        <item name="android:background">@color/clipsy_card_dark</item>
        <item name="backgroundTint">@color/clipsy_card_dark</item>
        <item name="colorPrimary">@color/white</item>
        <item name="colorOnSurface">@color/white</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/white</item>
        <item name="materialAlertDialogTitleTextStyle">@style/AlertDialog.Clipsy.Title</item>
        <item name="materialAlertDialogBodyTextStyle">@style/AlertDialog.Clipsy.Body</item>
        <item name="buttonBarPositiveButtonStyle">@style/AlertDialog.Clipsy.Button</item>
        <item name="buttonBarNegativeButtonStyle">@style/AlertDialog.Clipsy.Button</item>
        <item name="buttonBarNeutralButtonStyle">@style/AlertDialog.Clipsy.Button</item>
    </style>

    <!-- AlertDialog Title Style -->
    <style name="AlertDialog.Clipsy.Title" parent="MaterialAlertDialog.Material3.Title.Text">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- AlertDialog Body Style -->
    <style name="AlertDialog.Clipsy.Body" parent="MaterialAlertDialog.Material3.Body.Text">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
    </style>

    <!-- AlertDialog Button Style -->
    <style name="AlertDialog.Clipsy.Button" parent="Widget.Material3.Button.TextButton.Dialog">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="rippleColor">@color/white</item>
    </style>

</resources>
