"""
Simple Connection Manager for Clipsy - Handles direct device connections without complex pairing
"""

import json
import os
import logging
import time
from typing import Dict, Callable
import uuid


class SimpleConnectionManager:
    """Simple connection manager for Clipsy without complex pairing."""

    def __init__(self, config_dir: str = "config"):
        self.logger = logging.getLogger(__name__)
        self.config_dir = config_dir
        self.device_id = self._get_or_create_device_id()
        self.device_name = "Windows-PC"

        # Connection state
        self.connected_devices = {}
        self.connection_callback = None

        # Device storage
        self.known_devices_file = os.path.join(config_dir, "known_devices.json")
        self.known_devices = self._load_known_devices()

        # Ensure config directory exists
        os.makedirs(config_dir, exist_ok=True)

        self.logger.info(f"Simple connection manager initialized for {self.device_name}")
    
    def _get_or_create_device_id(self) -> str:
        """Get or create a unique device ID."""
        device_id_file = os.path.join(self.config_dir, "device_id.txt")

        if os.path.exists(device_id_file):
            try:
                with open(device_id_file, 'r') as f:
                    return f.read().strip()
            except Exception as e:
                self.logger.warning(f"Could not read device ID file: {e}")

        # Generate new device ID
        device_id = str(uuid.uuid4())
        try:
            os.makedirs(self.config_dir, exist_ok=True)
            with open(device_id_file, 'w') as f:
                f.write(device_id)
        except Exception as e:
            self.logger.error(f"Could not save device ID: {e}")

        return device_id

    def set_connection_callback(self, callback: Callable):
        """Set callback for connection events."""
        self.connection_callback = callback

    def _load_known_devices(self):
        """Load known devices from file."""
        try:
            if os.path.exists(self.known_devices_file):
                with open(self.known_devices_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading known devices: {e}")
        return {}

    def _save_known_devices(self):
        """Save known devices to file."""
        try:
            os.makedirs(os.path.dirname(self.known_devices_file), exist_ok=True)
            with open(self.known_devices_file, 'w') as f:
                json.dump(self.known_devices, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving known devices: {e}")

    def add_known_device(self, device_id: str, device_name: str, device_ip: str):
        """Add device to known devices list."""
        self.known_devices[device_id] = {
            "device_id": device_id,
            "device_name": device_name,
            "device_ip": device_ip,
            "last_seen": time.time()
        }
        self._save_known_devices()
        self.logger.info(f"Added known device: {device_name} ({device_ip})")

    def remove_known_device(self, device_id: str) -> bool:
        """Remove device from known devices list."""
        if device_id in self.known_devices:
            device_name = self.known_devices[device_id].get("device_name", "Unknown")
            del self.known_devices[device_id]
            self._save_known_devices()
            self.logger.info(f"Removed known device: {device_name} ({device_id})")
            return True
        return False

    def get_known_devices(self):
        """Get list of known devices."""
        return list(self.known_devices.values())

    def connect_to_device(self, device_ip: str, device_name: str = None) -> bool:
        """Connect to a device directly."""
        try:
            # Generate device ID from IP if not provided
            device_id = f"device_{device_ip.replace('.', '_')}"
            if not device_name:
                device_name = f"Device at {device_ip}"

            # Add to known devices
            self.add_known_device(device_id, device_name, device_ip)

            # Mark as connected
            self.connected_devices[device_id] = {
                "device_id": device_id,
                "device_name": device_name,
                "device_ip": device_ip,
                "connected_at": time.time()
            }

            # Notify callback
            if self.connection_callback:
                self.connection_callback("connected", {
                    "device_id": device_id,
                    "device_name": device_name,
                    "device_ip": device_ip
                })

            self.logger.info(f"Connected to device: {device_name} ({device_ip})")
            return True

        except Exception as e:
            self.logger.error(f"Error connecting to device {device_ip}: {e}")
            return False

    def disconnect_from_device(self, device_id: str) -> bool:
        """Disconnect from a device."""
        try:
            if device_id in self.connected_devices:
                device_info = self.connected_devices[device_id]
                del self.connected_devices[device_id]

                # Notify callback
                if self.connection_callback:
                    self.connection_callback("disconnected", device_info)

                self.logger.info(f"Disconnected from device: {device_info.get('device_name', 'Unknown')}")
                return True
            return False

        except Exception as e:
            self.logger.error(f"Error disconnecting from device {device_id}: {e}")
            return False

    def get_connected_devices(self):
        """Get list of connected devices."""
        return list(self.connected_devices.values())

    def is_device_connected(self, device_id: str) -> bool:
        """Check if device is connected."""
        return device_id in self.connected_devices

    def get_device_info(self) -> Dict:
        """Get this device's information."""
        return {
            "device_id": self.device_id,
            "device_name": self.device_name,
            "device_type": "pc"
        }

    def cleanup(self):
        """Clean up resources."""
        self.connected_devices.clear()
        self.logger.info("Connection manager cleaned up")
