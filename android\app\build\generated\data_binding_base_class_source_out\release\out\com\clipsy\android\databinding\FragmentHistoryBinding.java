// Generated by view binder compiler. Do not edit!
package com.clipsy.android.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.clipsy.android.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentHistoryBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton buttonSendMessage;

  @NonNull
  public final TextInputEditText editTextMessage;

  @NonNull
  public final LinearLayout layoutEmpty;

  @NonNull
  public final RecyclerView recyclerViewHistory;

  private FragmentHistoryBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton buttonSendMessage, @NonNull TextInputEditText editTextMessage,
      @NonNull LinearLayout layoutEmpty, @NonNull RecyclerView recyclerViewHistory) {
    this.rootView = rootView;
    this.buttonSendMessage = buttonSendMessage;
    this.editTextMessage = editTextMessage;
    this.layoutEmpty = layoutEmpty;
    this.recyclerViewHistory = recyclerViewHistory;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHistoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHistoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_history, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHistoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_send_message;
      ImageButton buttonSendMessage = ViewBindings.findChildViewById(rootView, id);
      if (buttonSendMessage == null) {
        break missingId;
      }

      id = R.id.edit_text_message;
      TextInputEditText editTextMessage = ViewBindings.findChildViewById(rootView, id);
      if (editTextMessage == null) {
        break missingId;
      }

      id = R.id.layout_empty;
      LinearLayout layoutEmpty = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmpty == null) {
        break missingId;
      }

      id = R.id.recycler_view_history;
      RecyclerView recyclerViewHistory = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewHistory == null) {
        break missingId;
      }

      return new FragmentHistoryBinding((LinearLayout) rootView, buttonSendMessage, editTextMessage,
          layoutEmpty, recyclerViewHistory);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
