"""
Simple device manager for Clipsy - Direct connection without pairing.
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Callable
import logging


class SimpleDeviceManager:
    """Simple device manager interface for direct connections."""
    
    def __init__(self, parent_frame, app):
        self.parent_frame = parent_frame
        self.app = app
        self.logger = logging.getLogger(__name__)

        # Set up connection callback for simple connection manager
        self.app.connection_manager.set_connection_callback(self._handle_connection_event)

        # Device state tracking
        self.devices = {}  # device_id -> device_info
        self.device_widgets = {}  # device_id -> widget_frame
        self.sync_states = {}  # device_id -> sync_enabled
        
        # UI components
        self.main_frame = None
        self.devices_container = None
        self.no_devices_label = None
        
        # Callbacks
        self.on_device_connect = None
        self.on_device_disconnect = None
        self.on_sync_toggle = None
        
        self._create_ui()
        
    def _create_ui(self):
        """Create the simple device manager UI."""
        # Main container
        self.main_frame = ttk.Frame(self.parent_frame)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Header
        header_frame = ttk.Frame(self.main_frame)
        header_frame.pack(fill="x", pady=(0, 20))
        
        title_label = ttk.Label(header_frame, text="Device Manager", font=("Segoe UI", 16, "bold"))
        title_label.pack(side="left")
        
        # Refresh button
        refresh_btn = ttk.Button(header_frame, text="🔄 Refresh", command=self._refresh_devices)
        refresh_btn.pack(side="right")
        
        # Devices container with scrollbar
        container_frame = ttk.Frame(self.main_frame)
        container_frame.pack(fill="both", expand=True)
        
        # Canvas and scrollbar for scrolling
        canvas = tk.Canvas(container_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(container_frame, orient="vertical", command=canvas.yview)
        self.devices_container = ttk.Frame(canvas)
        
        self.devices_container.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.devices_container, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # No devices message
        self.no_devices_label = ttk.Label(
            self.devices_container,
            text="No Clipsy devices found.\n\nMake sure other devices are running Clipsy and connected to the same network.\nClick 'Refresh' to scan for devices.",
            font=("Segoe UI", 10),
            foreground="gray",
            justify="center"
        )
        self.no_devices_label.pack(pady=50)
        
    def _create_device_widget(self, device_id: str, device_info: dict) -> ttk.Frame:
        """Create a widget for a device."""
        device_frame = ttk.LabelFrame(self.devices_container, text="", padding=10)
        device_frame.pack(fill="x", pady=5)
        
        # Device header
        header_frame = ttk.Frame(device_frame)
        header_frame.pack(fill="x", pady=(0, 10))
        
        # Device icon and name
        info_frame = ttk.Frame(header_frame)
        info_frame.pack(side="left", fill="x", expand=True)
        
        device_name = device_info.get("name") or device_info.get("device_name", "Unknown Device")
        device_type = device_info.get("type") or device_info.get("device_type", "unknown")
        device_ip = device_info.get("ip") or device_info.get("ip_address", "Unknown")
        
        # Device icon
        icon = self._get_device_icon(device_type)
        icon_label = ttk.Label(info_frame, text=icon, font=("Segoe UI", 16))
        icon_label.pack(side="left", padx=(0, 10))
        
        # Device details
        details_frame = ttk.Frame(info_frame)
        details_frame.pack(side="left", fill="x", expand=True)
        
        name_label = ttk.Label(details_frame, text=device_name, font=("Segoe UI", 12, "bold"))
        name_label.pack(anchor="w")
        
        ip_label = ttk.Label(details_frame, text=f"IP: {device_ip}", font=("Segoe UI", 9))
        ip_label.pack(anchor="w")
        
        # Connection status
        is_connected = self._is_device_connected_simple(device_id)
        status_text = "🟢 Connected" if is_connected else "🔴 Disconnected"
        status_label = ttk.Label(details_frame, text=status_text, font=("Segoe UI", 9))
        status_label.pack(anchor="w")
        
        # Action buttons
        button_frame = ttk.Frame(header_frame)
        button_frame.pack(side="right")
        
        if is_connected:
            disconnect_btn = ttk.Button(
                button_frame, 
                text="Disconnect", 
                command=lambda: self._disconnect_device(device_id)
            )
            disconnect_btn.pack(side="right", padx=(5, 0))
        else:
            connect_btn = ttk.Button(
                button_frame, 
                text="Connect", 
                command=lambda: self._connect_device(device_id)
            )
            connect_btn.pack(side="right", padx=(5, 0))
        
        # Info button
        info_btn = ttk.Button(
            button_frame, 
            text="Info", 
            command=lambda: self._show_device_info(device_id)
        )
        info_btn.pack(side="right", padx=(5, 0))
        
        # Remove button
        remove_btn = ttk.Button(
            button_frame, 
            text="Remove", 
            command=lambda: self._remove_device(device_id)
        )
        remove_btn.pack(side="right", padx=(5, 0))
        
        return device_frame
        
    def _get_device_icon(self, device_type: str) -> str:
        """Get icon for device type."""
        icons = {
            "android": "📱",
            "ios": "📱", 
            "windows": "💻",
            "mac": "💻",
            "linux": "💻",
            "unknown": "📟"
        }
        return icons.get(device_type.lower(), "📟")
        
    def _is_device_connected_simple(self, device_id: str) -> bool:
        """Check if device is connected via multiple connection indicators."""
        if not device_id:
            return False

        # Check multiple connection indicators for more reliable status
        connection_indicators = []

        # 1. Check connection manager (logical connection)
        if self.app.connection_manager:
            if device_id and '.' in device_id:  # Looks like an IP address
                connection_device_id = f"device_{device_id.replace('.', '_')}"
                connection_indicators.append(self.app.connection_manager.is_device_connected(connection_device_id))
            else:
                connection_indicators.append(self.app.connection_manager.is_device_connected(device_id))

        # 2. Check sync server (incoming connections)
        if self.app.sync_server:
            connected_devices = self.app.sync_server.get_connected_devices()
            server_connected = device_id in connected_devices
            connection_indicators.append(server_connected)

        # 3. Check sync client (outgoing connections)
        if self.app.sync_client:
            client_connected = self.app.sync_client.is_connected_to(device_id)
            connection_indicators.append(client_connected)

        # Device is considered connected if ANY of the indicators show connection
        # This provides more resilient status display even with unstable WebSocket connections
        is_connected = any(connection_indicators)

        # Log for debugging if there's a mismatch
        if len(set(connection_indicators)) > 1:  # Different indicators disagree
            self.logger.debug(f"Connection status mismatch for {device_id}: {connection_indicators}")

        return is_connected
        
    def _connect_device(self, device_id: str):
        """Connect to a device directly."""
        device_info = self.devices.get(device_id)
        if not device_info:
            return

        device_ip = device_info.get("ip") or device_info.get("ip_address")
        device_name = device_info.get("name") or device_info.get("device_name", "Unknown Device")

        if not device_ip:
            messagebox.showerror("Error", "Device IP not available")
            return

        try:
            # First mark as connected in connection manager
            success = self.app.connection_manager.connect_to_device(device_ip, device_name)
            if success:
                # Now establish real WebSocket connection using sync client
                if self.app.sync_client:
                    import asyncio
                    import threading

                    def connect_async():
                        try:
                            # Get the event loop from the main app
                            if hasattr(self.app, 'loop') and self.app.loop:
                                future = asyncio.run_coroutine_threadsafe(
                                    self.app.sync_client.connect_to_device(device_ip, device_info),
                                    self.app.loop
                                )
                                future.result(timeout=10)  # Wait up to 10 seconds
                                self.logger.info(f"WebSocket connection established to {device_name}")
                            else:
                                self.logger.warning("No event loop available for WebSocket connection")
                        except Exception as e:
                            self.logger.error(f"Failed to establish WebSocket connection: {e}")

                    # Run the async connection in a separate thread
                    thread = threading.Thread(target=connect_async)
                    thread.daemon = True
                    thread.start()

                messagebox.showinfo("Success", f"Connected to {device_name}")
                self._refresh_devices()
            else:
                messagebox.showerror("Error", f"Failed to connect to {device_name}")
        except Exception as e:
            messagebox.showerror("Connection Error", f"Failed to connect: {e}")
        
    def _disconnect_device(self, device_id: str):
        """Disconnect from a device."""
        try:
            device_info = self.devices.get(device_id, {})
            device_ip = device_info.get("ip") or device_info.get("ip_address")
            device_name = device_info.get("name") or device_info.get("device_name", "Unknown Device")

            # First disconnect WebSocket connection if sync client is available
            if device_ip:
                import asyncio
                import threading

                def disconnect_async():
                    try:
                        # Get the event loop from the main app
                        if hasattr(self.app, 'loop') and self.app.loop:
                            # Disconnect from client side
                            if self.app.sync_client:
                                future = asyncio.run_coroutine_threadsafe(
                                    self.app.sync_client.disconnect_from_device(device_ip),
                                    self.app.loop
                                )
                                future.result(timeout=5)  # Wait up to 5 seconds
                                self.logger.info(f"WebSocket client disconnected from {device_name}")

                            # Disconnect from server side
                            if self.app.sync_server:
                                self.app.sync_server.disconnect_device(device_ip)
                                self.logger.info(f"WebSocket server disconnected from {device_name}")
                        else:
                            self.logger.warning("No event loop available for WebSocket disconnection")
                    except Exception as e:
                        self.logger.error(f"Failed to disconnect WebSocket: {e}")

                # Run the async disconnection in a separate thread
                thread = threading.Thread(target=disconnect_async)
                thread.daemon = True
                thread.start()
                thread.join(timeout=5)  # Wait for disconnection to complete

            # Then remove from connection manager
            # Convert IP address to connection manager device ID format
            if device_id and '.' in device_id:  # Looks like an IP address
                connection_device_id = f"device_{device_id.replace('.', '_')}"
            else:
                connection_device_id = device_id

            success = self.app.connection_manager.disconnect_from_device(connection_device_id)
            if success:
                messagebox.showinfo("Success", f"Disconnected from {device_name}")
                self._refresh_devices()
            else:
                messagebox.showerror("Error", "Failed to disconnect from device")
        except Exception as e:
            messagebox.showerror("Disconnection Error", f"Failed to disconnect: {e}")
        
    def _remove_device(self, device_id: str):
        """Remove a device from known devices."""
        if messagebox.askyesno("Remove Device", "Are you sure you want to remove this device?"):
            try:
                success = self.app.connection_manager.remove_known_device(device_id)
                if success:
                    self._refresh_devices()
                    messagebox.showinfo("Success", "Device removed successfully")
                else:
                    messagebox.showerror("Error", "Failed to remove device")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to remove device: {e}")
                
    def _show_device_info(self, device_id: str):
        """Show detailed device information."""
        device_info = self.devices.get(device_id)
        if not device_info:
            return
            
        info_text = f"""Device Information:

Name: {device_info.get('device_name', 'Unknown')}
Type: {device_info.get('device_type', 'Unknown')}
IP Address: {device_info.get('ip', 'Unknown')}
Device ID: {device_info.get('device_id', 'Unknown')}
Connected: {'Yes' if self._is_device_connected_simple(device_id) else 'No'}"""

        messagebox.showinfo("Device Information", info_text)
    
    def _handle_connection_event(self, event_type: str, data: dict):
        """Handle connection events from connection manager."""
        if event_type == "connected":
            device_name = data.get("device_name", "Unknown Device")
            self.logger.info(f"Device connected: {device_name}")
            self._refresh_devices()
        elif event_type == "disconnected":
            device_name = data.get("device_name", "Unknown Device")
            self.logger.info(f"Device disconnected: {device_name}")
            self._refresh_devices()
            
    def update_devices(self, discovered_devices: dict, connected_devices: dict):
        """Update the device list with discovered and connected devices."""
        all_devices = {}
        seen_ips = set()

        # Only show discovered Clipsy devices (no manual/known devices)
        # This ensures we only display devices that are actually running Clipsy

        # Add connected devices first (they take priority)
        for device_id, device_info in connected_devices.items():
            device_ip = device_info.get('ip') or device_info.get('ip_address')
            if device_ip and device_ip not in seen_ips:
                all_devices[device_id] = device_info
                seen_ips.add(device_ip)

        # Add discovered devices (deduplicate by IP)
        for device_id, device_info in discovered_devices.items():
            device_ip = device_info.get('ip') or device_info.get('ip_address')
            if device_ip and device_ip not in seen_ips:
                # Only add if it's not a manual device or if it's a verified Clipsy device
                is_manual = device_info.get('manual', False)
                is_scanned = device_info.get('scanned', False)

                # Include device if:
                # 1. It's not manual (discovered via broadcast/response)
                # 2. It's manual but was verified via scan (has 'scanned' flag)
                if not is_manual or is_scanned:
                    all_devices[device_id] = device_info
                    seen_ips.add(device_ip)

        # Skip adding known devices from connection manager
        # We only want to show devices that are actually running Clipsy

        self.devices = all_devices
        self._update_device_widgets()
        
    def _update_device_widgets(self):
        """Update the device widgets display."""
        # Clear existing widgets
        for widget in self.device_widgets.values():
            widget.destroy()
        self.device_widgets.clear()
        
        # Hide/show no devices message
        if self.devices:
            self.no_devices_label.pack_forget()
            
            # Create widgets for each device
            for device_id, device_info in self.devices.items():
                widget = self._create_device_widget(device_id, device_info)
                self.device_widgets[device_id] = widget
        else:
            self.no_devices_label.pack(pady=50)
            
    def _refresh_devices(self):
        """Refresh the device list."""
        # Get discovered devices from discovery service
        discovered = {}
        if hasattr(self.app, 'device_discovery') and self.app.device_discovery:
            discovered = self.app.device_discovery.get_discovered_devices()
        elif hasattr(self.app, 'discovery_service') and self.app.discovery_service:
            discovered = self.app.discovery_service.get_discovered_devices()

        # Get connected devices from sync server
        connected = {}
        if self.app.sync_server:
            connected = self.app.sync_server.get_connected_devices()

        self.update_devices(discovered, connected)
        
    def set_callbacks(self, on_connect: Callable = None, on_disconnect: Callable = None, on_sync_toggle: Callable = None):
        """Set callback functions for device actions."""
        self.on_device_connect = on_connect
        self.on_device_disconnect = on_disconnect
        self.on_sync_toggle = on_sync_toggle
