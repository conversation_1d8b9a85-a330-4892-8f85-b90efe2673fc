# Clipsy Setup Guide

## Prerequisites

### Windows
- **Operating System**: Windows 10 or later
- **Python**: Python 3.8 or later
- **Network**: Wi-Fi connection

### Android
- **Operating System**: Android 6.0 (API level 23) or later
- **Development**: Android Studio (for building from source)
- **Network**: Wi-Fi connection

## Installation

### Windows Installation

#### Option 1: From Source
1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-repo/clipsy.git
   cd clipsy/windows
   ```

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python src/main.py
   ```

#### Option 2: Executable (Future)
1. Download the latest release from GitHub
2. Run the installer
3. Launch Clipsy from Start Menu

### Android Installation

#### Option 1: From Source
1. **Open in Android Studio**:
   - Open Android Studio
   - Select "Open an existing project"
   - Navigate to `clipsy/android` directory

2. **Build and install**:
   - Connect your Android device or start an emulator
   - Click "Run" or use `Ctrl+R`

#### Option 2: APK Installation (Future)
1. Download the APK from GitHub releases
2. Enable "Install from unknown sources" in Android settings
3. Install the APK file

## Initial Configuration

### Windows Setup

1. **Launch Clipsy**
2. **Configure device name**:
   - Go to Settings tab
   - Set a recognizable device name
   - Click "Apply Settings"

3. **Network configuration**:
   - Ensure Windows Firewall allows the application
   - Default ports: 8765 (discovery), 8766 (sync)

4. **Clipboard permissions**:
   - No special permissions required
   - Application will automatically monitor clipboard

### Android Setup

1. **Launch Clipsy**
2. **Grant permissions**:
   - Allow notification access (required for Android 13+)
   - The app will request necessary permissions

3. **Configure device name**:
   - Go to Settings
   - Set device name under "Network" section

4. **Enable background service**:
   - Ensure the app can run in background
   - Disable battery optimization for Clipsy

## Network Configuration

### Firewall Settings

#### Windows Firewall
1. Open Windows Defender Firewall
2. Click "Allow an app or feature through Windows Defender Firewall"
3. Add Clipsy or allow Python through firewall
4. Ensure both "Private" and "Public" are checked

#### Router Configuration
- No special router configuration required
- Ensure devices are on the same Wi-Fi network
- Some enterprise networks may block the required ports

### Port Configuration

Default ports used by Clipsy:
- **8765**: UDP discovery port
- **8766**: WebSocket sync port

To change ports:
1. Edit `config.json` (Windows) or app settings (Android)
2. Restart the application
3. Ensure all devices use the same ports

## Device Pairing

### Automatic Discovery
1. **Start Clipsy on both devices**
2. **Wait for discovery**:
   - Devices should appear in the "Devices" tab/screen
   - Discovery may take up to 30 seconds

3. **Connect to device**:
   - Select device from list
   - Click "Connect"
   - Connection status will update

### Manual Connection
If automatic discovery fails:

1. **Find IP addresses**:
   - Windows: Check "Local IP Address" in Devices tab
   - Android: Check device settings or use network scanner

2. **Add manual device**:
   - Click "Add Manual" button
   - Enter the IP address of the target device
   - Optionally enter a device name

3. **Connect**:
   - Select the manually added device
   - Click "Connect"

## Troubleshooting Setup

### Common Issues

#### Devices Not Discovering Each Other
- **Check network**: Ensure both devices are on same Wi-Fi
- **Check firewall**: Disable firewall temporarily to test
- **Check ports**: Ensure ports 8765 and 8766 are not blocked
- **Restart discovery**: Refresh device list or restart app

#### Connection Failed
- **Check IP address**: Verify the IP address is correct
- **Check service**: Ensure Clipsy is running on target device
- **Check network**: Test basic connectivity (ping)
- **Check ports**: Ensure WebSocket port (8766) is accessible

#### Clipboard Not Syncing
- **Check permissions**: Ensure clipboard access is granted
- **Check connection**: Verify devices are connected
- **Check settings**: Ensure clipboard sync is enabled
- **Test manually**: Try copying simple text

#### Android Service Issues
- **Battery optimization**: Disable for Clipsy
- **Background restrictions**: Allow background activity
- **Notification permissions**: Required for foreground service
- **Auto-start**: Enable in device settings

### Network Diagnostics

#### Test Network Connectivity
```bash
# Windows Command Prompt
ping <target_device_ip>
telnet <target_device_ip> 8766

# Android (using terminal app)
ping <target_device_ip>
```

#### Check Port Availability
```bash
# Windows PowerShell
Test-NetConnection -ComputerName <target_ip> -Port 8766

# Check if port is listening locally
netstat -an | findstr :8766
```

### Log Files

#### Windows Logs
- Location: `clipsy.log` in application directory
- Level: INFO (default) or DEBUG (in config)

#### Android Logs
- Use Android Studio Logcat
- Filter by "Clipsy" tag
- Check system logs for service issues

## Performance Optimization

### Windows
- **Minimize to tray**: Enable in settings to reduce resource usage
- **Adjust monitor interval**: Increase clipboard monitor interval if needed
- **History limit**: Reduce history limit for better performance

### Android
- **Background optimization**: Ensure proper background permissions
- **Battery settings**: Add to battery optimization whitelist
- **Memory management**: Monitor memory usage in developer options

## Security Considerations

### Network Security
- **Local network only**: Clipsy is designed for local network use
- **No encryption**: Currently no encryption (planned for future)
- **Firewall**: Use firewall to restrict access if needed

### Data Privacy
- **Local storage**: Clipboard history stored locally only
- **No cloud sync**: No data sent to external servers
- **Temporary storage**: Consider clearing history regularly

## Next Steps

After successful setup:
1. **Test clipboard sync**: Copy text on one device, verify on another
2. **Configure settings**: Adjust history limits, notifications, etc.
3. **Set up auto-start**: Enable automatic startup if desired
4. **Add more devices**: Repeat setup process for additional devices

For advanced configuration and troubleshooting, see the [Troubleshooting Guide](troubleshooting.md).
