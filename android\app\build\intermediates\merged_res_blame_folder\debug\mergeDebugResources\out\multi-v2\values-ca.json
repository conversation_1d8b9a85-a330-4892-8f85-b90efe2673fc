{"logs": [{"outputFile": "com.clipsy.android.app-mergeDebugResources-55:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f959e0cc303079f1bb4b6f18f6d78dd7\\transformed\\preference-1.2.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,270,347,490,659,746", "endColumns": "68,95,76,142,168,86,80", "endOffsets": "169,265,342,485,654,741,822"}, "to": {"startLines": "48,50,111,113,116,117,118", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4537,4671,9802,9965,10291,10460,10547", "endColumns": "68,95,76,142,168,86,80", "endOffsets": "4601,4762,9874,10103,10455,10542,10623"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\60faa6f29802d848c65e1c1909fcb4a3\\transformed\\appcompat-1.6.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,447,552,659,742,848,974,1058,1137,1228,1321,1414,1509,1607,1700,1793,1887,1978,2069,2150,2261,2369,2467,2577,2682,2790,2950,10108", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "442,547,654,737,843,969,1053,1132,1223,1316,1409,1504,1602,1695,1788,1882,1973,2064,2145,2256,2364,2462,2572,2677,2785,2945,3044,10185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\345ccf188a6f49c4c91ca3120313e197\\transformed\\core-1.12.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3493,3589,3691,3790,3887,3993,4098,10190", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "3584,3686,3785,3882,3988,4093,4219,10286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\18d5a599756ea47edbaa6b32ae2eb3d5\\transformed\\navigation-ui-2.7.5\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "9569,9680", "endColumns": "110,121", "endOffsets": "9675,9797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\724090e78494501a803eec31dba02c36\\transformed\\material-1.10.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,626,718,819,947,1031,1096,1193,1273,1338,1433,1497,1569,1631,1707,1770,1827,1948,2006,2067,2124,2204,2341,2428,2512,2651,2729,2808,2960,3049,3125,3182,3238,3304,3382,3463,3551,3639,3717,3794,3868,3947,4057,4147,4239,4331,4432,4506,4588,4689,4739,4822,4888,4980,5067,5129,5193,5256,5329,5452,5565,5669,5777,5838,5898", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,87,102,91,100,127,83,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,83,138,77,78,151,88,75,56,55,65,77,80,87,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85", "endOffsets": "269,350,430,518,621,713,814,942,1026,1091,1188,1268,1333,1428,1492,1564,1626,1702,1765,1822,1943,2001,2062,2119,2199,2336,2423,2507,2646,2724,2803,2955,3044,3120,3177,3233,3299,3377,3458,3546,3634,3712,3789,3863,3942,4052,4142,4234,4326,4427,4501,4583,4684,4734,4817,4883,4975,5062,5124,5188,5251,5324,5447,5560,5664,5772,5833,5893,5979"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3049,3130,3210,3298,3401,4224,4325,4453,4606,4767,4864,4944,5009,5104,5168,5240,5302,5378,5441,5498,5619,5677,5738,5795,5875,6012,6099,6183,6322,6400,6479,6631,6720,6796,6853,6909,6975,7053,7134,7222,7310,7388,7465,7539,7618,7728,7818,7910,8002,8103,8177,8259,8360,8410,8493,8559,8651,8738,8800,8864,8927,9000,9123,9236,9340,9448,9509,9879", "endLines": "5,33,34,35,36,37,45,46,47,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,112", "endColumns": "12,80,79,87,102,91,100,127,83,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,83,138,77,78,151,88,75,56,55,65,77,80,87,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85", "endOffsets": "319,3125,3205,3293,3396,3488,4320,4448,4532,4666,4859,4939,5004,5099,5163,5235,5297,5373,5436,5493,5614,5672,5733,5790,5870,6007,6094,6178,6317,6395,6474,6626,6715,6791,6848,6904,6970,7048,7129,7217,7305,7383,7460,7534,7613,7723,7813,7905,7997,8098,8172,8254,8355,8405,8488,8554,8646,8733,8795,8859,8922,8995,9118,9231,9335,9443,9504,9564,9960"}}]}]}