"""
Clipboard monitoring module for Windows.
Monitors system clipboard for changes and triggers sync events.
"""

import time
import threading
import logging
import pyperclip
import hashlib
from typing import Callable
from datetime import datetime


class ClipboardMonitor:
    """Monitors system clipboard for changes."""
    
    def __init__(self, config: dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Monitoring state
        self.is_running = False
        self.monitor_thread = None
        self.last_content = ""
        self.monitor_interval = config.get("monitor_interval", 0.5)
        
        # Callbacks
        self.on_clipboard_change = None
        self.on_history_update = None
        
        # History storage
        self.history = []
        self.history_limit = config.get("history_limit", 50)
        self.history_enabled = config.get("history_enabled", True)

        # Sync state management to prevent infinite loops
        self.is_setting_from_remote = False
        self.last_synced_content_hash = ""
        self.sync_debounce_time = 1.0  # 1 second debounce
        self.last_sync_timestamp = 0.0
        
    def set_clipboard_change_callback(self, callback: Callable[[str], None]):
        """Set callback function for clipboard changes."""
        self.on_clipboard_change = callback

    def set_history_update_callback(self, callback: Callable[[dict], None]):
        """Set callback function for history updates."""
        self.on_history_update = callback

    def _generate_content_hash(self, content: str) -> str:
        """Generate content hash for duplicate detection."""
        try:
            return hashlib.sha256(content.encode()).hexdigest()
        except Exception as e:
            self.logger.error(f"Failed to generate content hash: {e}")
            return str(hash(content))

    def _should_sync_content(self, content: str) -> bool:
        """Check if content should be synced (avoid loops and duplicates)."""
        current_time = time.time()
        content_hash = self._generate_content_hash(content)

        # Check if we're setting clipboard from remote (avoid loop)
        if self.is_setting_from_remote:
            return False

        # Check if content is the same as last synced (avoid duplicates)
        if content_hash == self.last_synced_content_hash:
            return False

        # Check debounce time (avoid rapid-fire syncs)
        if current_time - self.last_sync_timestamp < self.sync_debounce_time:
            return False

        return True

    def _update_sync_tracking(self, content: str):
        """Update sync tracking after successful sync."""
        self.last_synced_content_hash = self._generate_content_hash(content)
        self.last_sync_timestamp = time.time()
    
    def start(self):
        """Start clipboard monitoring."""
        if self.is_running:
            self.logger.warning("Clipboard monitor already running")
            return
        
        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("Clipboard monitor started")
    
    def stop(self):
        """Stop clipboard monitoring."""
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        self.logger.info("Clipboard monitor stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop."""
        while self.is_running:
            try:
                current_content = pyperclip.paste()
                
                # Check if content has changed
                if current_content != self.last_content and current_content.strip():
                    # Check if we should sync this content (prevent loops/duplicates)
                    if self._should_sync_content(current_content):
                        # Update history
                        if self.history_enabled:
                            self._add_to_history(current_content)

                        # Trigger callback
                        if self.on_clipboard_change:
                            self.on_clipboard_change(current_content)

                        # Update sync tracking
                        self._update_sync_tracking(current_content)

                    self.last_content = current_content
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                self.logger.error(f"Error in clipboard monitor: {e}")
                time.sleep(1)  # Wait before retrying
    
    def _add_to_history(self, content: str, source: str = "local"):
        """Add content to clipboard history."""
        timestamp = datetime.now().isoformat()

        # Remove duplicate if exists
        self.history = [item for item in self.history if item["content"] != content]

        # Create new history item
        new_item = {
            "content": content,
            "timestamp": timestamp,
            "source": source
        }

        # Add new item at the beginning
        self.history.insert(0, new_item)

        # Trim history to limit
        if len(self.history) > self.history_limit:
            self.history = self.history[:self.history_limit]

        # Notify UI about the new history item
        if self.on_history_update:
            self.on_history_update(new_item)
    
    def get_history(self) -> list:
        """Get clipboard history."""
        return self.history.copy()
    
    def clear_history(self):
        """Clear clipboard history."""
        self.history.clear()
        self.logger.info("Clipboard history cleared")
    
    def set_clipboard_content(self, content: str, source: str = "remote"):
        """Set clipboard content (from remote sync)."""
        try:
            # Set sync lock to prevent infinite loop
            self.is_setting_from_remote = True

            pyperclip.copy(content)
            self.last_content = content

            # Update sync tracking to prevent duplicate detection
            self._update_sync_tracking(content)

            # Add to history if from remote source
            if source == "remote" and self.history_enabled:
                self._add_to_history(content, source)

            # Release sync lock after a short delay
            def release_lock():
                time.sleep(0.1)  # 100ms delay to ensure clipboard is set - faster
                self.is_setting_from_remote = False

            # Start release timer in background thread
            release_thread = threading.Thread(target=release_lock, daemon=True)
            release_thread.start()

        except Exception as e:
            self.logger.error(f"Failed to set clipboard content: {e}")
            # Ensure sync lock is released even on error
            self.is_setting_from_remote = False
    
    def get_current_content(self) -> str:
        """Get current clipboard content."""
        try:
            return pyperclip.paste()
        except Exception as e:
            self.logger.error(f"Failed to get clipboard content: {e}")
            return ""
    
    def is_enabled(self) -> bool:
        """Check if clipboard sync is enabled."""
        return self.config.get("sync_enabled", True)
    
    def set_enabled(self, enabled: bool):
        """Enable or disable clipboard sync."""
        self.config["sync_enabled"] = enabled
        if enabled and not self.is_running:
            self.start()
        elif not enabled and self.is_running:
            self.stop()
    
    def set_history_enabled(self, enabled: bool):
        """Enable or disable clipboard history."""
        self.history_enabled = enabled
        self.config["history_enabled"] = enabled
        if not enabled:
            self.clear_history()
    
    def set_history_limit(self, limit: int):
        """Set clipboard history limit."""
        self.history_limit = max(1, limit)
        self.config["history_limit"] = self.history_limit
        
        # Trim current history if needed
        if len(self.history) > self.history_limit:
            self.history = self.history[:self.history_limit]
