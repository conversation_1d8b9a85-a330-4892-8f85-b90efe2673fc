#!/usr/bin/env python3
"""
Clipsy - Cross-Platform Clipboard Manager
Modern Windows Application Entry Point with PyQt6 UI
"""

import sys
import os
import json
import logging
import asyncio
import threading
from pathlib import Path

# Add src directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Check if PyQt6 is available
try:
    from PyQt6.QtWidgets import QApplication, QMessageBox
    from PyQt6.QtGui import QIcon
    PYQT6_AVAILABLE = True
except ImportError:
    PYQT6_AVAILABLE = False

from ui.modern_window import ClipsyModernWindow
from clipboard.monitor import ClipboardMonitor
from network.discovery import DeviceDiscovery
from network.sync_server import SyncServer
from network.sync_client import SyncClient


class ModernClipsyApp:
    """Modern application class for Clipsy Windows client with PyQt6."""
    
    def __init__(self):
        self.config = self.load_config()
        self.setup_logging()
        
        # Core components
        self.clipboard_monitor = None
        self.device_discovery = None
        self.sync_server = None
        self.sync_client = None
        self.main_window = None
        self.qt_app = None
        
        # Event loop for async operations
        self.loop = None
        self.loop_thread = None
    
    def load_config(self):
        """Load configuration from file."""
        config_path = Path(__file__).parent.parent / "config.json"
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            # Return default config
            return {
                "app": {"name": "Clipsy", "version": "1.0.0", "device_name": "Windows-PC"},
                "network": {
                    "discovery_port": 8765, "websocket_port": 8766,
                    "broadcast_interval": 5, "connection_timeout": 10,
                    "auto_discovery": True, "manual_ips": []
                },
                "clipboard": {
                    "sync_enabled": True, "auto_sync": True, "history_enabled": True,
                    "history_limit": 100, "monitor_interval": 0.5
                },
                "ui": {
                    "window_width": 1000, "window_height": 700,
                    "minimize_to_tray": True, "show_notifications": True, "theme": "light"
                },
                "startup": {"auto_start": False, "start_minimized": False},
                "security": {
                    "require_pairing": False, "device_whitelist": [], "max_content_size": 1048576
                }
            }
    
    def setup_logging(self):
        """Setup logging configuration."""
        log_level = logging.INFO
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # Create logs directory if it doesn't exist
        log_dir = Path(__file__).parent / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # Setup file and console logging
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.FileHandler(log_dir / "clipsy_modern.log"),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def start_async_loop(self):
        """Start the async event loop in a separate thread."""
        try:
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.loop.run_forever()
        except Exception as e:
            self.logger.error(f"Error in async loop: {e}")
    
    def stop_async_loop(self):
        """Stop the async event loop."""
        if self.loop and self.loop.is_running():
            self.loop.call_soon_threadsafe(self.loop.stop)
    
    async def initialize_components(self):
        """Initialize all application components."""
        try:
            # Initialize clipboard monitor
            self.clipboard_monitor = ClipboardMonitor(self.config["clipboard"])
            
            # Initialize network components
            self.device_discovery = DeviceDiscovery(self.config["network"])
            self.sync_server = SyncServer(self.config["network"])
            self.sync_client = SyncClient(self.config["network"])
            
            # Start services
            await self.sync_server.start()
            await self.device_discovery.start()
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            raise
    
    def run(self):
        """Run the modern application."""
        if not PYQT6_AVAILABLE:
            print("Error: PyQt6 is required for the modern UI.")
            print("Please install it with: pip install PyQt6")
            return
        
        try:
            # Create Qt application
            self.qt_app = QApplication(sys.argv)
            self.qt_app.setApplicationName("Clipsy")
            self.qt_app.setApplicationVersion(self.config["app"]["version"])
            
            # Set application icon
            icon_path = Path(__file__).parent.parent / "app_icon.ico"
            if icon_path.exists():
                self.qt_app.setWindowIcon(QIcon(str(icon_path)))
            
            # Start async event loop in background thread
            self.loop_thread = threading.Thread(target=self.start_async_loop, daemon=True)
            self.loop_thread.start()

            import time
            time.sleep(0.5)

            # Initialize async components
            if self.loop:
                future = asyncio.run_coroutine_threadsafe(
                    self.initialize_components(), self.loop
                )
                future.result(timeout=10)  # Wait for initialization
            else:
                raise RuntimeError("Failed to start async event loop")

            # Create and show main window
            self.main_window = ClipsyModernWindow(self.config, self)
            self.main_window.show()
            
            # Start clipboard monitor
            if self.clipboard_monitor:
                self.clipboard_monitor.start()
            
            # Run Qt event loop
            sys.exit(self.qt_app.exec())
            
        except KeyboardInterrupt:
            self.logger.info("Application interrupted by user")
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            if PYQT6_AVAILABLE and self.qt_app:
                QMessageBox.critical(None, "Error", f"Application error: {e}")
            raise
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Cleanup resources before exit."""
        self.logger.info("Cleaning up modern application resources...")
        
        if self.clipboard_monitor:
            self.clipboard_monitor.stop()
        
        if self.loop:
            # Stop async services
            if self.sync_server:
                asyncio.run_coroutine_threadsafe(
                    self.sync_server.stop(), self.loop
                )
            if self.device_discovery:
                asyncio.run_coroutine_threadsafe(
                    self.device_discovery.stop(), self.loop
                )
        
        self.stop_async_loop()
        self.logger.info("Modern application cleanup complete")


def main():
    """Main entry point for modern UI."""
    try:
        app = ModernClipsyApp()
        app.run()
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
